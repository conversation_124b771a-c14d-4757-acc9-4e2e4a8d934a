package com.icss.wallet.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Wallet;
import com.icss.wallet.entity.Transaction;
import com.icss.wallet.mapper.WalletMapper;
import com.icss.wallet.mapper.TransactionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

@Service
public class WalletService {
    
    @Autowired
    private WalletMapper walletMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;
    
    /**
     * 根据用户ID获取钱包信息
     */
    public Wallet getWalletByUserId(Long userId) {
        return walletMapper.findByUserId(userId);
    }
    
    /**
     * 钱包充值（从银行卡充值到钱包）
     */
    @Transactional
    public boolean recharge(Long userId, BigDecimal amount, String bankCardNumber, String remark) {
        try {
            // 增加钱包余额
            int result = walletMapper.addBalance(userId, amount);
            if (result <= 0) {
                return false;
            }
            
            // 记录交易
            Transaction transaction = new Transaction();
            transaction.setTransNo(generateTransNo());
            transaction.setUserId(userId);
            transaction.setAmount(amount);
            transaction.setType(1); // 1-充值
            transaction.setStatus(1); // 1-成功
            transaction.setTargetInfo(bankCardNumber);
            transaction.setRemark(remark);
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            
            transactionMapper.insert(transaction);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 钱包提现（从钱包提现到银行卡）
     */
    @Transactional
    public boolean withdraw(Long userId, BigDecimal amount, String bankCardNumber, String remark) {
        try {
            // 减少钱包余额
            int result = walletMapper.subtractBalance(userId, amount);
            if (result <= 0) {
                return false; // 余额不足或钱包状态异常
            }
            
            // 记录交易
            Transaction transaction = new Transaction();
            transaction.setTransNo(generateTransNo());
            transaction.setUserId(userId);
            transaction.setAmount(amount);
            transaction.setType(2); // 2-提现
            transaction.setStatus(1); // 1-成功
            transaction.setTargetInfo(bankCardNumber);
            transaction.setRemark(remark);
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            
            transactionMapper.insert(transaction);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成交易流水号
     */
    private String generateTransNo() {
        return "TXN" + System.currentTimeMillis();
    }

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询钱包信息（包含用户信息）
     */
    public IPage<Wallet> getWalletsWithUserInfo(int pageNum, int pageSize, String phone, String realName, Integer status) {
        Page<Wallet> page = new Page<>(pageNum, pageSize);
        return walletMapper.selectWalletsWithUserInfo(page, phone, realName, status);
    }

    /**
     * 管理员获取钱包统计信息
     */
    public java.util.Map<String, Object> getWalletStatistics() {
        return walletMapper.getWalletStatistics();
    }

    /**
     * 管理员调整钱包余额
     */
    @Transactional
    public boolean adjustWalletBalance(java.util.List<Long> walletIds, String type, BigDecimal amount, String reason) {
        for (Long walletId : walletIds) {
            Wallet wallet = walletMapper.selectById(walletId);
            if (wallet == null) {
                throw new RuntimeException("钱包不存在: " + walletId);
            }

            BigDecimal newBalance;
            switch (type) {
                case "add":
                    newBalance = wallet.getBalance().add(amount);
                    break;
                case "subtract":
                    newBalance = wallet.getBalance().subtract(amount);
                    if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
                        throw new RuntimeException("余额不足，无法减少: " + walletId);
                    }
                    break;
                case "set":
                    newBalance = amount;
                    break;
                default:
                    throw new RuntimeException("不支持的调整类型: " + type);
            }

            wallet.setBalance(newBalance);
            wallet.setUpdateTime(new Date());
            walletMapper.updateById(wallet);

            // 记录调整日志（这里可以扩展为专门的日志表）
            // TODO: 添加余额调整日志记录
        }
        return true;
    }

    /**
     * 管理员切换钱包状态
     */
    @Transactional
    public boolean toggleWalletStatus(Long walletId, Integer status) {
        Wallet wallet = walletMapper.selectById(walletId);
        if (wallet == null) {
            throw new RuntimeException("钱包不存在");
        }

        wallet.setStatus(status);
        wallet.setUpdateTime(new Date());
        return walletMapper.updateById(wallet) > 0;
    }
}
