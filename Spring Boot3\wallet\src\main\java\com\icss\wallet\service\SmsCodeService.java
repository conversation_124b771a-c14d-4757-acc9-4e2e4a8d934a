package com.icss.wallet.service;

import com.icss.wallet.entity.SmsCode;
import com.icss.wallet.mapper.SmsCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.Random;

@Service
public class SmsCodeService {
    
    @Autowired
    private SmsCodeMapper smsCodeMapper;
    
    /**
     * 发送验证码
     * @param phone 手机号
     * @param type 验证码类型 1-注册登录,2-修改密码,3-支付验证
     * @return 验证码
     */
    public String sendCode(String phone, Integer type) {
        // 生成6位随机验证码
        String code = generateCode();
        
        // 设置过期时间（5分钟后）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, 5);
        Date expireTime = calendar.getTime();
        
        // 保存验证码到数据库
        SmsCode smsCode = new SmsCode();
        smsCode.setPhone(phone);
        smsCode.setCode(code);
        smsCode.setType(type);
        smsCode.setExpireTime(expireTime);
        smsCode.setCreateTime(new Date());
        
        smsCodeMapper.insert(smsCode);
        
        // 这里应该调用短信服务发送验证码，暂时模拟
        System.out.println("发送验证码到手机号: " + phone + ", 验证码: " + code);
        
        return code;
    }
    
    /**
     * 验证验证码
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否验证成功
     */
    public boolean verifyCode(String phone, String code, Integer type) {
        SmsCode smsCode = smsCodeMapper.findValidCode(phone, type);
        
        if (smsCode == null) {
            return false;
        }
        
        // 检查验证码是否匹配
        if (!smsCode.getCode().equals(code)) {
            return false;
        }
        
        // 检查是否过期
        if (smsCode.getExpireTime().before(new Date())) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 生成6位随机验证码
     */
    private String generateCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
}
