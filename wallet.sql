/*
Navicat MySQL Data Transfer

Source Server         : MySQL
Source Server Version : 50528
Source Host           : localhost:3306
Source Database       : wallet

Target Server Type    : MYSQL
Target Server Version : 50528
File Encoding         : 65001

Date: 2025-06-20 18:14:04
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `admin_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `role` varchar(20) DEFAULT 'operator',
  `status` int(11) DEFAULT '1',
  `last_login_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PR<PERSON>AR<PERSON> (`admin_id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES ('1', 'admin', '0192023a7bbd73250516f069df18b500', '张建国', 'admin', '1', '2024-01-20 09:15:30', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('2', 'limanager', 'fe1a10e4576c9db0b40e26b9ffa38ea5', '李雪梅', 'admin', '1', '2024-01-19 14:22:15', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('3', 'wangop', '9d34de276eaa015a937f5c90365b1581', '王志强', 'operator', '1', '2024-01-18 16:45:20', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('4', 'chenop', '1581f91b362ed2f90bf892aaaedc75b2', '陈美丽', 'operator', '1', '2024-01-17 11:30:45', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('5', 'zhaoop', '5bfdad151f5a624c3b137cc91686e359', '赵小明', 'operator', '0', null, '2025-06-20 17:58:08', '2025-06-20 17:58:08');

-- ----------------------------
-- Table structure for bank_account
-- ----------------------------
DROP TABLE IF EXISTS `bank_account`;
CREATE TABLE `bank_account` (
  `account_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户ID',
  `account_number` varchar(30) NOT NULL COMMENT '银行账号',
  `account_type` tinyint(4) NOT NULL COMMENT '账户类型(1-储蓄账户,2-支票账户,3-信用卡账户)',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `branch_name` varchar(100) DEFAULT NULL COMMENT '支行名称',
  `account_holder` varchar(50) NOT NULL COMMENT '账户持有人姓名',
  `phone` varchar(20) NOT NULL COMMENT '绑定手机号',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `available_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `credit_limit` decimal(15,2) DEFAULT '0.00' COMMENT '信用额度(信用卡专用)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-冻结,1-正常,2-销户)',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认账户(0-否,1-是)',
  `open_date` date NOT NULL COMMENT '开户日期',
  `last_transaction_time` datetime DEFAULT NULL COMMENT '最后交易时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `idx_account_number` (`account_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  CONSTRAINT `fk_bank_account_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='银行账户表';

-- ----------------------------
-- Records of bank_account
-- ----------------------------
INSERT INTO `bank_account` VALUES ('1', '1', '6222021985051512345001', '1', '中国工商银行', '北京朝阳支行', '刘德华', '***********', 'CNY', '2580000.50', '2580000.50', '0.00', '1', '1', '2020-01-15', '2024-01-20 10:30:00', '2025-06-20 18:06:28', '2025-06-20 18:06:28');
INSERT INTO `bank_account` VALUES ('2', '1', '6225881985051512346001', '3', '招商银行', '北京分行', '刘德华', '***********', 'CNY', '0.00', '450000.00', '500000.00', '1', '0', '2020-03-20', '2024-01-19 15:20:00', '2025-06-20 18:06:28', '2025-06-20 18:06:28');
INSERT INTO `bank_account` VALUES ('3', '2', '6227001992032015678001', '1', '中国建设银行', '上海浦东支行', '张曼玉', '***********', 'HKD', '1890000.75', '1890000.75', '0.00', '1', '1', '2019-05-10', '2024-01-19 14:15:00', '2025-06-20 18:06:28', '2025-06-20 18:13:44');
INSERT INTO `bank_account` VALUES ('4', '2', '6013821992032015679001', '3', '中国银行', '上海分行', '张曼玉', '***********', 'CNY', '0.00', '270000.00', '300000.00', '1', '0', '2019-08-25', '2024-01-18 16:45:00', '2025-06-20 18:06:28', '2025-06-20 18:06:28');
INSERT INTO `bank_account` VALUES ('5', '1', '6225771985051512347001', '2', '浦发银行', '北京海淀支行', '刘德华', '***********', 'CNY', '856000.60', '856000.60', '0.00', '1', '0', '2021-03-15', '2024-01-18 10:45:00', '2025-06-20 18:06:28', '2025-06-20 18:06:28');
INSERT INTO `bank_account` VALUES ('6', '3', '6228481978121234567001', '1', '中国农业银行', '深圳福田支行', '周星驰', '***********', 'HKD', '3560000.00', '3560000.00', '0.00', '1', '1', '2018-12-01', '2024-01-18 11:30:00', '2025-06-20 18:06:36', '2025-06-20 18:13:14');
INSERT INTO `bank_account` VALUES ('7', '3', '6222601978121234568001', '3', '交通银行', '深圳分行', '周星驰', '***********', 'HKD', '0.00', '720000.00', '800000.00', '1', '0', '2019-02-14', '2024-01-17 09:20:00', '2025-06-20 18:06:36', '2025-06-20 18:13:25');
INSERT INTO `bank_account` VALUES ('8', '3', '6230581978121234569001', '2', '平安银行', '深圳南山支行', '周星驰', '***********', 'CNY', '1234000.45', '1234000.45', '0.00', '1', '0', '2020-11-05', '2024-01-16 12:20:00', '2025-06-20 18:06:36', '2025-06-20 18:06:36');
INSERT INTO `bank_account` VALUES ('9', '4', '6217001989061567890001', '1', '中信银行', '广州天河支行', '林青霞', '***********', 'CNY', '1678000.25', '0.00', '0.00', '0', '1', '2020-06-18', '2024-01-15 14:30:00', '2025-06-20 18:06:44', '2025-06-20 18:06:44');
INSERT INTO `bank_account` VALUES ('10', '4', '6226201989061567891001', '3', '光大银行', '广州分行', '林青霞', '***********', 'CNY', '0.00', '180000.00', '200000.00', '1', '0', '2020-09-10', '2024-01-14 16:45:00', '2025-06-20 18:06:44', '2025-06-20 18:06:44');
INSERT INTO `bank_account` VALUES ('11', '4', '6225801989061567892001', '1', '广发银行', '广州越秀支行', '林青霞', '***********', 'CNY', '445000.00', '445000.00', '0.00', '2', '0', '2022-01-10', '2024-01-10 14:15:00', '2025-06-20 18:06:44', '2025-06-20 18:06:44');
INSERT INTO `bank_account` VALUES ('12', '5', '6226221990010198765001', '1', '民生银行', '杭州西湖支行', '梁朝伟', '***********', 'HKD', '2987500.80', '2987500.80', '0.00', '1', '1', '2019-01-08', '2024-01-20 09:15:00', '2025-06-20 18:06:51', '2025-06-20 18:13:32');
INSERT INTO `bank_account` VALUES ('13', '5', '6226851990010198766001', '3', '兴业银行', '杭州分行', '梁朝伟', '***********', 'USD', '0.00', '540000.00', '600000.00', '1', '0', '2019-04-22', '2024-01-19 13:25:00', '2025-06-20 18:06:51', '2025-06-20 18:13:40');
INSERT INTO `bank_account` VALUES ('14', '2', '6226201992032015680001', '2', '华夏银行', '上海静安支行', '张曼玉', '***********', 'USD', '534000.00', '534000.00', '0.00', '1', '0', '2021-07-20', '2024-01-17 15:30:00', '2025-06-20 18:06:51', '2025-06-20 18:13:03');
INSERT INTO `bank_account` VALUES ('15', '5', '6221881990010198767001', '1', '中国邮政储蓄银行', '杭州滨江支行', '梁朝伟', '***********', 'CNY', '1120000.90', '1120000.90', '0.00', '1', '0', '2021-09-18', '2024-01-15 16:40:00', '2025-06-20 18:06:51', '2025-06-20 18:06:51');

-- ----------------------------
-- Table structure for bank_card
-- ----------------------------
DROP TABLE IF EXISTS `bank_card`;
CREATE TABLE `bank_card` (
  `card_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '银行卡ID',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户ID',
  `card_number` varchar(20) NOT NULL DEFAULT '',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `card_type` tinyint(4) NOT NULL COMMENT '卡类型(1-借记卡,2-信用卡)',
  `card_holder` varchar(50) NOT NULL COMMENT '持卡人姓名',
  `phone` varchar(20) NOT NULL COMMENT '预留手机号',
  `expiry_date` varchar(10) DEFAULT NULL COMMENT '有效期(信用卡专用)',
  `cvv` varchar(10) DEFAULT NULL COMMENT '安全码(信用卡专用)',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认(0-否,1-是)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-解绑,1-绑定)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`card_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_bank_card_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COMMENT='银行卡表';

-- ----------------------------
-- Records of bank_card
-- ----------------------------
INSERT INTO `bank_card` VALUES ('1', '1', '6222021985051512345', '中国工商银行', '1', '刘德华', '***********', null, null, '1', '1', '2025-06-20 17:58:57', '2025-06-20 18:09:30');
INSERT INTO `bank_card` VALUES ('2', '1', '6225881985051512346', '招商银行', '2', '刘德华', '***********', '12/26', '123', '0', '1', '2025-06-20 17:58:57', '2025-06-20 17:58:57');
INSERT INTO `bank_card` VALUES ('3', '2', '6227001992032015678', '中国建设银行', '1', '张曼玉', '***********', null, null, '1', '1', '2025-06-20 17:58:57', '2025-06-20 17:58:57');
INSERT INTO `bank_card` VALUES ('4', '2', '6013821992032015679', '中国银行', '2', '张曼玉', '***********', '03/27', '456', '0', '0', '2025-06-20 17:58:57', '2025-06-20 17:58:57');
INSERT INTO `bank_card` VALUES ('5', '1', '6222021985051512345', '中国工商银行', '1', '刘德华', '***********', null, null, '1', '1', '2025-06-20 17:59:12', '2025-06-20 17:59:12');
INSERT INTO `bank_card` VALUES ('6', '1', '6225881985051512346', '招商银行', '2', '刘德华', '***********', '12/26', '123', '0', '1', '2025-06-20 17:59:12', '2025-06-20 17:59:12');
INSERT INTO `bank_card` VALUES ('7', '2', '6227001992032015678', '中国建设银行', '1', '张曼玉', '***********', null, null, '1', '1', '2025-06-20 17:59:12', '2025-06-20 17:59:12');
INSERT INTO `bank_card` VALUES ('8', '2', '6013821992032015679', '中国银行', '2', '张曼玉', '***********', '03/27', '456', '0', '0', '2025-06-20 17:59:12', '2025-06-20 17:59:12');
INSERT INTO `bank_card` VALUES ('9', '4', '6217001989061567890', '中信银行', '1', '林青霞', '***********', null, null, '1', '0', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('10', '4', '6226201989061567891', '光大银行', '2', '林青霞', '***********', '11/26', '321', '0', '0', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('11', '5', '6226221990010198765', '民生银行', '1', '梁朝伟', '***********', null, null, '1', '1', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('12', '5', '6226851990010198766', '兴业银行', '2', '梁朝伟', '***********', '05/28', '654', '0', '1', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('13', '1', '6225771985051512347', '浦发银行', '1', '刘德华', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('14', '2', '6226201992032015680', '华夏银行', '1', '张曼玉', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('15', '3', '6230581978121234569', '平安银行', '2', '周星驰', '***********', '09/27', '987', '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('16', '4', '6225801989061567892', '广发银行', '1', '林青霞', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('17', '5', '6221881990010198767', '中国邮政储蓄银行', '1', '梁朝伟', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('18', '1', '6225881985051512648', '招商银行', '1', '孙燕姿', '***********', null, null, '0', '0', '2025-06-20 18:11:25', '2025-06-20 18:11:25');

-- ----------------------------
-- Table structure for sms_code
-- ----------------------------
DROP TABLE IF EXISTS `sms_code`;
CREATE TABLE `sms_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` tinyint(4) NOT NULL COMMENT '类型(1-注册登录,2-修改密码,3-支付验证)',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone_type` (`phone`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='短信验证码表';

-- ----------------------------
-- Records of sms_code
-- ----------------------------

-- ----------------------------
-- Table structure for transaction
-- ----------------------------
DROP TABLE IF EXISTS `transaction`;
CREATE TABLE `transaction` (
  `trans_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '交易ID',
  `trans_no` varchar(32) NOT NULL COMMENT '交易流水号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
  `type` tinyint(4) NOT NULL COMMENT '交易类型(1-充值,2-提现,3-转账,4-消费)',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态(0-处理中,1-成功,2-失败)',
  `target_info` varchar(100) DEFAULT NULL COMMENT '目标信息(银行卡号/手机号等)',
  `remark` varchar(255) DEFAULT NULL COMMENT '交易备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`trans_id`),
  UNIQUE KEY `idx_trans_no` (`trans_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_transaction_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='交易记录表';

-- ----------------------------
-- Records of transaction
-- ----------------------------
INSERT INTO `transaction` VALUES ('1', 'TXN202401200001', '1', '500000.00', '1', '1', '6222021985051512345', '电影票房分红充值', '2025-06-20 18:06:58', '2025-06-20 18:06:58');
INSERT INTO `transaction` VALUES ('2', 'TXN202401190001', '1', '80000.00', '2', '1', '6225881985051512346', '购买豪车提现', '2025-06-20 18:06:58', '2025-06-20 18:06:58');
INSERT INTO `transaction` VALUES ('3', 'TXN202401180001', '1', '150000.00', '3', '1', '***********', '慈善捐款转账给张曼玉', '2025-06-20 18:06:58', '2025-06-20 18:06:58');
INSERT INTO `transaction` VALUES ('4', 'TXN202401200002', '2', '300000.00', '1', '1', '6227001992032015678', '广告代言费充值', '2025-06-20 18:07:13', '2025-06-20 18:07:13');
INSERT INTO `transaction` VALUES ('5', 'TXN202401190002', '2', '50000.00', '4', '1', '珠宝店', '购买珠宝消费', '2025-06-20 18:07:13', '2025-06-20 18:07:13');
INSERT INTO `transaction` VALUES ('6', 'TXN202401180002', '2', '200000.00', '3', '1', '***********', '投资收益转账给周星驰', '2025-06-20 18:07:13', '2025-06-20 18:07:13');
INSERT INTO `transaction` VALUES ('7', 'TXN202401200003', '3', '800000.00', '1', '1', '6228481978121234567', '电影制作分成充值', '2025-06-20 18:07:18', '2025-06-20 18:07:18');
INSERT INTO `transaction` VALUES ('8', 'TXN202401190003', '3', '120000.00', '2', '1', '6222601978121234568', '购买房产提现', '2025-06-20 18:07:18', '2025-06-20 18:07:18');
INSERT INTO `transaction` VALUES ('9', 'TXN202401180003', '3', '100000.00', '3', '1', '***********', '员工奖金转账给林青霞', '2025-06-20 18:07:18', '2025-06-20 18:07:18');
INSERT INTO `transaction` VALUES ('10', 'TXN202401200004', '4', '280000.00', '1', '2', '6217001989061567890', '演出费用充值失败', '2025-06-20 18:07:24', '2025-06-20 18:07:24');
INSERT INTO `transaction` VALUES ('11', 'TXN202401190004', '4', '45000.00', '2', '2', '6226201989061567891', '账户冻结提现失败', '2025-06-20 18:07:24', '2025-06-20 18:07:24');
INSERT INTO `transaction` VALUES ('12', 'TXN202401180004', '4', '180000.00', '3', '1', '***********', '版权收入转账给梁朝伟', '2025-06-20 18:07:24', '2025-06-20 18:07:24');
INSERT INTO `transaction` VALUES ('13', 'TXN202401200005', '5', '450000.00', '1', '1', '6226221990010198765', '影视投资回报充值', '2025-06-20 18:07:30', '2025-06-20 18:07:30');
INSERT INTO `transaction` VALUES ('14', 'TXN202401190005', '5', '75000.00', '4', '1', '艺术品拍卖行', '艺术品收藏消费', '2025-06-20 18:07:30', '2025-06-20 18:07:30');
INSERT INTO `transaction` VALUES ('15', 'TXN202401180005', '5', '200000.00', '3', '1', '公益基金会', '公益基金转账', '2025-06-20 18:07:30', '2025-06-20 18:07:30');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `password` varchar(50) NOT NULL COMMENT '密码(明文存储)',
  `pay_password` varchar(50) NOT NULL DEFAULT '123456' COMMENT '支付密码(明文存储)',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-禁用,1-正常)',
  `pay_limit` decimal(15,2) DEFAULT '50000.00' COMMENT '支付限额',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='用户表';

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('1', '***********', 'user123', 'pay123', '刘德华', '******************', null, '1', '100000.00', '2024-01-20 08:30:15', '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('2', '***********', 'user456', 'pay456', '张曼玉', '110108199203201567', null, '1', '80000.00', '2024-01-19 19:45:30', '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('3', '***********', 'user789', 'pay789', '周星驰', '310115197812123456', null, '1', '150000.00', '2024-01-18 12:20:45', '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('4', '***********', 'user321', 'pay321', '林青霞', '320102198906156789', null, '0', '60000.00', '2024-01-15 16:10:20', '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('5', '***********', 'user654', 'pay654', '梁朝伟', '440304199001019876', null, '1', '120000.00', null, '2025-06-20 17:58:23', '2025-06-20 17:58:23');

-- ----------------------------
-- Table structure for wallet
-- ----------------------------
DROP TABLE IF EXISTS `wallet`;
CREATE TABLE `wallet` (
  `wallet_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '钱包ID',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户ID',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-冻结,1-正常)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`wallet_id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_wallet_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='钱包表';

-- ----------------------------
-- Records of wallet
-- ----------------------------
INSERT INTO `wallet` VALUES ('1', '1', '2580000.50', '1', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
INSERT INTO `wallet` VALUES ('2', '2', '1890000.75', '1', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
INSERT INTO `wallet` VALUES ('3', '3', '3560000.00', '1', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
INSERT INTO `wallet` VALUES ('4', '4', '1678000.25', '0', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
INSERT INTO `wallet` VALUES ('5', '5', '2987500.80', '1', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
DROP TRIGGER IF EXISTS `update_bank_account_time`;
DELIMITER ;;
CREATE TRIGGER `update_bank_account_time` BEFORE UPDATE ON `bank_account` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_bank_card_time`;
DELIMITER ;;
CREATE TRIGGER `update_bank_card_time` BEFORE UPDATE ON `bank_card` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_user_time`;
DELIMITER ;;
CREATE TRIGGER `update_user_time` BEFORE UPDATE ON `user` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_wallet_time`;
DELIMITER ;;
CREATE TRIGGER `update_wallet_time` BEFORE UPDATE ON `wallet` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
