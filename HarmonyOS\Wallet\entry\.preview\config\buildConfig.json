{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29901", "checkEntry": "true", "localPropertiesPath": "D:\\HarmonyOSProject\\Wallet\\local.properties", "Path": "D:\\HarmonyOS\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\HarmonyOSProject\\Wallet\\build-profile.json5", "watchMode": "true", "appResource": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets", "aceSoPath": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/LoginPage\",\"pages/BarPage\",\"pages/WalletPage\",\"pages/HomePage\",\"pages/TransactionPage\",\"pages/BankCardPage\",\"pages/SettingsPage\",\"pages/CardDetailPage\",\"pages/BankAccountTransferPage\",\"pages/ChangePasswordPage\"]}"]}}