package com.icss.wallet.controller;

import com.icss.wallet.result.R;
import com.icss.wallet.service.TransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@CrossOrigin
@RestController
@RequestMapping("/transfer")
public class TransferController {
    @Autowired
    private TransferService transferService;

    @PostMapping
    public R transfer(
            @RequestParam String fromAccountNumber,
            @RequestParam String toAccountNumber,
            @RequestParam BigDecimal amount) {
        transferService.transfer(fromAccountNumber, toAccountNumber, amount);
        return R.success("转账成功");
    }
}