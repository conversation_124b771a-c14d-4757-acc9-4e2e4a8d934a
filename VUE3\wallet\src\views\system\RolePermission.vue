<template>
  <div class="role-permission-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><UserFilled /></el-icon>
            角色权限管理
          </h2>
          <p>管理系统管理员角色和权限，包括添加、编辑、删除管理员等操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleAdd" size="large">
            <el-icon><Plus /></el-icon>
            添加管理员
          </el-button>
        </div>
      </div>
    </div>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>管理员列表</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入真实姓名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select
            v-model="searchForm.role"
            placeholder="请选择角色"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="管理员" value="admin" />
            <el-option label="操作员" value="operator" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" :value="null" />
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="adminId" label="管理员ID" width="100" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">
              {{ getRoleName(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginTime) || '暂无记录' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) || '暂无记录' }}
          </template>
        </el-table-column>
        <el-table-column label="权限说明" width="200">
          <template #default="{ row }">
            <div class="permission-desc">
              {{ getPermissionDesc(row.role) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              :type="row.status === 1 ? 'warning' : 'success'" 
              size="small" 
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            :disabled="!!form.adminId"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="form.realName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-radio-group v-model="form.role">
            <el-radio label="admin">管理员</el-radio>
            <el-radio label="operator">操作员</el-radio>
          </el-radio-group>
          <div class="role-help-text">
            <p><strong>管理员：</strong>拥有所有权限，可以管理用户、角色、系统设置等</p>
            <p><strong>操作员：</strong>只能查看和操作业务数据，不能管理用户和系统设置</p>
          </div>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限说明卡片 -->
    <el-card class="permission-info-card" shadow="never">
      <template #header>
        <div class="info-header">
          <el-icon><InfoFilled /></el-icon>
          <span>权限说明</span>
        </div>
      </template>
      <div class="permission-info">
        <div class="permission-item">
          <h4>管理员 (admin)</h4>
          <ul>
            <li>用户管理：查看、添加、编辑、删除用户</li>
            <li>角色权限：管理管理员账户和权限</li>
            <li>银行账户：管理所有银行账户</li>
            <li>银行卡：管理所有银行卡</li>
            <li>交易管理：查看和管理所有交易记录</li>
            <li>系统设置：修改系统配置</li>
          </ul>
        </div>
        <div class="permission-item">
          <h4>操作员 (operator)</h4>
          <ul>
            <li>用户管理：仅查看用户信息</li>
            <li>银行账户：查看和基本操作</li>
            <li>银行卡：查看和基本操作</li>
            <li>交易管理：查看交易记录</li>
            <li>无权限：用户管理、角色权限、系统设置</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, InfoFilled } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  username: '',
  realName: '',
  role: '',
  status: null
})

// 表单数据
const form = reactive({
  adminId: null,
  username: '',
  password: '',
  realName: '',
  role: 'operator',
  status: 1
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 获取管理员列表
const fetchAdmins = async () => {
  try {
    loading.value = true
    const response = await axios.get('http://localhost:8091/admin/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value
      }
    })

    if (response.data && response.data.code === 0) {
      let records = response.data.data?.records || []

      // 前端过滤
      if (searchForm.username) {
        records = records.filter(record =>
          record.username && record.username.toLowerCase().includes(searchForm.username.toLowerCase())
        )
      }
      if (searchForm.realName) {
        records = records.filter(record =>
          record.realName && record.realName.includes(searchForm.realName)
        )
      }
      if (searchForm.role) {
        records = records.filter(record => record.role === searchForm.role)
      }
      if (searchForm.status !== null) {
        records = records.filter(record => record.status === searchForm.status)
      }

      tableData.value = records
      total.value = records.length
    } else {
      ElMessage.error('获取管理员列表失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchAdmins()
}

// 重置
const handleReset = () => {
  searchForm.username = ''
  searchForm.realName = ''
  searchForm.role = ''
  searchForm.status = null
  currentPage.value = 1
  fetchAdmins()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchAdmins()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchAdmins()
}

// 添加管理员
const handleAdd = () => {
  dialogTitle.value = '添加管理员'
  Object.assign(form, {
    adminId: null,
    username: '',
    password: '',
    realName: '',
    role: 'operator',
    status: 1
  })
  dialogVisible.value = true
}

// 编辑管理员
const handleEdit = (row) => {
  dialogTitle.value = '编辑管理员'
  Object.assign(form, {
    adminId: row.adminId,
    username: row.username,
    password: '', // 编辑时不显示原密码
    realName: row.realName,
    role: row.role,
    status: row.status
  })
  dialogVisible.value = true
}

// 切换管理员状态
const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确认${action}管理员 "${row.realName}" 吗？`,
      `${action}管理员`,
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const newStatus = row.status === 1 ? 0 : 1
    const response = await axios.put('http://localhost:8091/admin', {
      adminId: row.adminId,
      status: newStatus
    })

    if (response.data && response.data.code === 0) {
      ElMessage.success(`${action}成功`)
      fetchAdmins()
    } else {
      ElMessage.error(`${action}失败: ` + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}管理员失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除管理员
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认删除管理员 "${row.realName}" 吗？此操作不可恢复！`,
      '删除管理员',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const response = await axios.delete(`http://localhost:8091/admin/${row.adminId}`)

    if (response.data && response.data.code === 0) {
      ElMessage.success('删除成功')
      fetchAdmins()
    } else {
      ElMessage.error('删除失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除管理员失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    const adminData = { ...form }
    if (adminData.adminId) {
      // 编辑管理员
      const response = await axios.put('http://localhost:8091/admin', adminData)
      if (response.data && response.data.code === 0) {
        ElMessage.success('更新成功')
        handleDialogClose()
        fetchAdmins()
      } else {
        ElMessage.error('更新失败: ' + (response.data?.msg || '未知错误'))
      }
    } else {
      // 添加管理员
      const response = await axios.post('http://localhost:8091/admin', adminData)
      if (response.data && response.data.code === 0) {
        ElMessage.success('添加成功')
        handleDialogClose()
        fetchAdmins()
      } else {
        ElMessage.error('添加失败: ' + (response.data?.msg || '未知错误'))
      }
    }
  } catch (error) {
    if (error.message && error.message.includes('validate')) {
      return
    }
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取角色名称
const getRoleName = (role) => {
  const roleMap = {
    admin: '管理员',
    operator: '操作员'
  }
  return roleMap[role] || '未知'
}

// 获取角色标签类型
const getRoleTagType = (role) => {
  const typeMap = {
    admin: 'danger',
    operator: 'primary'
  }
  return typeMap[role] || ''
}

// 获取权限描述
const getPermissionDesc = (role) => {
  const descMap = {
    admin: '拥有所有权限，可管理用户、角色、系统设置',
    operator: '只能查看和操作业务数据，无管理权限'
  }
  return descMap[role] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 初始化
onMounted(() => {
  fetchAdmins()
})
</script>

<style scoped>
.role-permission-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.permission-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.role-help-text {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.role-help-text p {
  margin: 4px 0;
}

.permission-info-card {
  margin-top: 20px;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #409eff;
}

.permission-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.permission-item h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.permission-item ul {
  margin: 0;
  padding-left: 20px;
}

.permission-item li {
  margin-bottom: 5px;
  color: #666;
  line-height: 1.4;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
