{"modulePathMap": {"entry": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry"}, "compileMode": "esmodule", "projectRootPath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet", "nodeModulesPath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "buildConfigPath": ".preview\\config\\buildConfig.json"}