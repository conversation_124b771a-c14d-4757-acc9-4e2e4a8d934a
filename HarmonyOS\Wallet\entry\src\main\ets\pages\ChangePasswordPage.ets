import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';
import preferences from '@ohos.data.preferences';

// 定义API响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

@Entry
@Component
export struct ChangePasswordPage {
  @State oldPassword: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';
  @State isLoading: boolean = false;
  @State preferencesStore: preferences.Preferences | null = null;
  @State userId: number = 0;

  aboutToAppear() {
    this.initPreferences();
  }

  // 初始化preferences并获取用户ID
  async initPreferences() {
    try {
      this.preferencesStore = await preferences.getPreferences(getContext(), 'user_prefs');
      this.userId = await this.preferencesStore.get('userId', 0) as number;
      console.log('当前用户ID:', this.userId);
    } catch (error) {
      console.error('初始化preferences失败:', error);
    }
  }

  build() {
    Column() {
      // 标题栏
      this.TitleBar()

      // 主内容区域
      Column() {
        // 页面标题
        Text('修改登录密码')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
          .margin({ top: 30, bottom: 40 })

        // 密码输入表单
        this.PasswordForm()

        // 修改按钮
        this.ChangeButton()

        // 温馨提示
        this.Tips()
      }
      .width('100%')
      .padding({ left: 20, right: 20 })
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }

  @Builder
  TitleBar() {
    Row() {
      Image($r('app.media.back'))
        .width(24)
        .height(24)
        .onClick(() => {
          router.back();
        })

      Text('修改密码')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .fontColor('#333333')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 占位，保持标题居中
      Row().width(24).height(24)
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor('#ffffff')
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }

  @Builder
  PasswordForm() {
    Column() {
      // 原密码输入
      Column() {
        Text('原密码')
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入原密码' })
          .width('100%')
          .height(50)
          .type(InputType.Password)
          .backgroundColor('#ffffff')
          .borderRadius(8)
          .border({ width: 1, color: '#e0e0e0' })
          .onChange((value: string) => {
            this.oldPassword = value;
          })
      }
      .width('100%')
      .margin({ bottom: 20 })

      // 新密码输入
      Column() {
        Text('新密码')
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入新密码' })
          .width('100%')
          .height(50)
          .type(InputType.Password)
          .backgroundColor('#ffffff')
          .borderRadius(8)
          .border({ width: 1, color: '#e0e0e0' })
          .onChange((value: string) => {
            this.newPassword = value;
          })
      }
      .width('100%')
      .margin({ bottom: 20 })

      // 确认密码输入
      Column() {
        Text('确认新密码')
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请再次输入新密码' })
          .width('100%')
          .height(50)
          .type(InputType.Password)
          .backgroundColor('#ffffff')
          .borderRadius(8)
          .border({ width: 1, color: '#e0e0e0' })
          .onChange((value: string) => {
            this.confirmPassword = value;
          })
      }
      .width('100%')
      .margin({ bottom: 30 })
    }
    .width('100%')
  }

  @Builder
  ChangeButton() {
    Button() {
      if (this.isLoading) {
        Row() {
          LoadingProgress()
            .width(20)
            .height(20)
            .color('#ffffff')
            .margin({ right: 8 })
          Text('修改中...')
            .fontSize(16)
            .fontColor('#ffffff')
        }
      } else {
        Text('确认修改')
          .fontSize(16)
          .fontColor('#ffffff')
      }
    }
    .width('100%')
    .height(50)
    .backgroundColor('#1f98e5')
    .borderRadius(8)
    .enabled(!this.isLoading)
    .onClick(() => {
      this.changePassword();
    })
    .margin({ bottom: 30 })
  }

  @Builder
  Tips() {
    Column() {
      Text('温馨提示')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 10 })

      Text('• 密码长度至少6位')
        .fontSize(14)
        .fontColor('#666666')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 5 })

      Text('• 建议使用字母、数字组合')
        .fontSize(14)
        .fontColor('#666666')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 5 })

      Text('• 请妥善保管您的密码')
        .fontSize(14)
        .fontColor('#666666')
        .alignSelf(ItemAlign.Start)
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#ffffff')
    .borderRadius(8)
  }

  // 修改密码
  async changePassword() {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.isLoading = true;

    try {
      const response: AxiosResponse<ApiResponse<void>> = await axios({
        url: 'http://localhost:8091/auth/changePassword',
        method: 'post',
        params: {
          userId: this.userId,
          oldPassword: this.oldPassword,
          newPassword: this.newPassword
        }
      });

      console.log('修改密码响应:', JSON.stringify(response.data));

      if (response.data.code === 0) {
        promptAction.showToast({
          message: '密码修改成功',
          duration: 2000
        });

        // 延迟返回上一页
        setTimeout(() => {
          router.back();
        }, 1000);
      } else {
        promptAction.showToast({
          message: response.data.msg || '密码修改失败',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('修改密码错误:', error);
      promptAction.showToast({
        message: '网络错误，请稍后重试',
        duration: 2000
      });
    } finally {
      this.isLoading = false;
    }
  }

  // 表单验证
  validateForm(): boolean {
    if (!this.oldPassword.trim()) {
      promptAction.showToast({
        message: '请输入原密码',
        duration: 2000
      });
      return false;
    }

    if (!this.newPassword.trim()) {
      promptAction.showToast({
        message: '请输入新密码',
        duration: 2000
      });
      return false;
    }

    if (this.newPassword.length < 6) {
      promptAction.showToast({
        message: '新密码长度至少6位',
        duration: 2000
      });
      return false;
    }

    if (!this.confirmPassword.trim()) {
      promptAction.showToast({
        message: '请确认新密码',
        duration: 2000
      });
      return false;
    }

    if (this.newPassword !== this.confirmPassword) {
      promptAction.showToast({
        message: '两次输入的密码不一致',
        duration: 2000
      });
      return false;
    }

    if (this.oldPassword === this.newPassword) {
      promptAction.showToast({
        message: '新密码不能与原密码相同',
        duration: 2000
      });
      return false;
    }

    return true;
  }
}
