{"version": "2.0", "ppid": 13468, "events": [{"head": {"id": "86051955-5242-4f02-9649-36b3fdc49fbb", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692917435100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ade46fd-f148-44bf-880a-1b9d39b6fcdc", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11823020663700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16d9efd3-3d09-44ab-a2e3-52f4aa954364", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11823027710700, "endTime": 11823027763600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "6c925dcf-2f76-4db8-9aff-c0ccf7adc3e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c925dcf-2f76-4db8-9aff-c0ccf7adc3e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11823027710700, "endTime": 11823027763600}, "additional": {"logType": "info", "children": [], "durationId": "16d9efd3-3d09-44ab-a2e3-52f4aa954364"}}, {"head": {"id": "7476006d-e452-4145-b1dd-63338da6b2fc", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11826324793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "905ab98e-25d1-43e2-9ca8-9eea90f99474", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11826325143100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b34b97b2-45fb-4982-926c-2e22c6779a8d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833582629600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833588531300, "endTime": 11833741035800}, "additional": {"children": ["8649ff4d-8dde-4007-82c2-a523f52d24ae", "717d46da-1150-4ce1-978c-d7897fc89ca5", "54892d0e-6e15-4b17-a502-358a4e6243d8", "160af27a-f83b-4935-bf39-064db7837a07", "785a7352-4155-46ce-b2c6-ea974cc1ff82", "4ccb6bc1-87d7-43c0-818c-510a94ce158d", "550fd9db-200e-4bd6-8ec8-e3accf785be6"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8649ff4d-8dde-4007-82c2-a523f52d24ae", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833588532500, "endTime": 11833600258900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "622f0431-20be-4fdb-b557-e5af99585985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "717d46da-1150-4ce1-978c-d7897fc89ca5", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833600271700, "endTime": 11833739867000}, "additional": {"children": ["f82c9437-36d4-470d-a28e-281bde01cb88", "8372ad20-4019-4c7c-bae8-2e7498e6609a", "92cf3951-7d86-4a24-9996-7082160cab11", "989c78b0-8c85-44bc-b78e-7bb0849b6037", "80f1ad05-8c88-45d4-982a-c55b0fc5edb6", "b90fb935-3710-493b-b4a6-a94ca9c985a8", "678db236-6bd1-46e2-b5d6-2a5a39a4d67b", "2983ab7f-7515-4957-bc45-3978f61bf38a", "ef162886-21f6-46af-8735-4411b87763de"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "74de65e1-c8b5-4fc5-af41-99329660d989"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54892d0e-6e15-4b17-a502-358a4e6243d8", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833739893600, "endTime": 11833741018100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "a0b7caff-fd66-4563-ae2c-37ce554b41b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "160af27a-f83b-4935-bf39-064db7837a07", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833741024400, "endTime": 11833741031000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "e6514944-f37b-4b73-ad39-62f6585abf64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "785a7352-4155-46ce-b2c6-ea974cc1ff82", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833591306500, "endTime": 11833591331900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "41ea9e35-3fae-44dc-a8a9-0815471151f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41ea9e35-3fae-44dc-a8a9-0815471151f5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833591306500, "endTime": 11833591331900}, "additional": {"logType": "info", "children": [], "durationId": "785a7352-4155-46ce-b2c6-ea974cc1ff82", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "4ccb6bc1-87d7-43c0-818c-510a94ce158d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833595977700, "endTime": 11833595991400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "6c63e6e1-d5a1-4eca-812e-668f09568af5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c63e6e1-d5a1-4eca-812e-668f09568af5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833595977700, "endTime": 11833595991400}, "additional": {"logType": "info", "children": [], "durationId": "4ccb6bc1-87d7-43c0-818c-510a94ce158d", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "b3beef27-5c86-478a-9457-81dc308d74f6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833596045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ff4818b-4fb6-41ae-9a9a-8c23047acf21", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833600171500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "622f0431-20be-4fdb-b557-e5af99585985", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833588532500, "endTime": 11833600258900}, "additional": {"logType": "info", "children": [], "durationId": "8649ff4d-8dde-4007-82c2-a523f52d24ae", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "f82c9437-36d4-470d-a28e-281bde01cb88", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833605416700, "endTime": 11833605426900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "2e3608ec-b387-4170-a5fc-24d8d101564c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8372ad20-4019-4c7c-bae8-2e7498e6609a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833605440100, "endTime": 11833608909400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "22211d04-27c0-49e9-ac0f-cb2a34fa1552"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92cf3951-7d86-4a24-9996-7082160cab11", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833608921000, "endTime": 11833681833500}, "additional": {"children": ["fe983fb6-3131-486c-824d-283e912f6394", "4b71d2d3-ede4-404d-a06b-be533fd5d813", "dffde690-2612-42e2-994f-632c3e89deda"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "aed22087-217d-439f-82c6-cd29570c9054"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "989c78b0-8c85-44bc-b78e-7bb0849b6037", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833681850500, "endTime": 11833704341200}, "additional": {"children": ["b337ed6d-96a6-444c-ae06-0b71e75111e4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "98e5122b-a229-46de-bfdb-b361e26ca24c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80f1ad05-8c88-45d4-982a-c55b0fc5edb6", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833704353700, "endTime": 11833718287400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "e22920e8-05d2-4668-9f3d-d9eb13316dff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b90fb935-3710-493b-b4a6-a94ca9c985a8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833719244400, "endTime": 11833726774600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "436151c6-c498-401f-8953-2e2ca112f440"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "678db236-6bd1-46e2-b5d6-2a5a39a4d67b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833726795300, "endTime": 11833739703500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "7aebf9d4-cb53-4611-a4fa-18d759fb559f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2983ab7f-7515-4957-bc45-3978f61bf38a", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833739724400, "endTime": 11833739826700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "b3af2edd-8b33-45f5-ac7b-411ebf410902"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e3608ec-b387-4170-a5fc-24d8d101564c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833605416700, "endTime": 11833605426900}, "additional": {"logType": "info", "children": [], "durationId": "f82c9437-36d4-470d-a28e-281bde01cb88", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "22211d04-27c0-49e9-ac0f-cb2a34fa1552", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833605440100, "endTime": 11833608909400}, "additional": {"logType": "info", "children": [], "durationId": "8372ad20-4019-4c7c-bae8-2e7498e6609a", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "fe983fb6-3131-486c-824d-283e912f6394", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833609490600, "endTime": 11833609505200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92cf3951-7d86-4a24-9996-7082160cab11", "logId": "b73d95b8-774e-4fc6-94a8-077cd28994ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b73d95b8-774e-4fc6-94a8-077cd28994ef", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833609490600, "endTime": 11833609505200}, "additional": {"logType": "info", "children": [], "durationId": "fe983fb6-3131-486c-824d-283e912f6394", "parent": "aed22087-217d-439f-82c6-cd29570c9054"}}, {"head": {"id": "4b71d2d3-ede4-404d-a06b-be533fd5d813", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833611834200, "endTime": 11833681144800}, "additional": {"children": ["355d2e87-83f4-4ac5-a503-cb6a495a820e", "51845b26-651f-4bfa-92db-f4e3c713ad8b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92cf3951-7d86-4a24-9996-7082160cab11", "logId": "2ca8a068-bf0a-4e9e-a1bd-e538626f843c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "355d2e87-83f4-4ac5-a503-cb6a495a820e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833611835600, "endTime": 11833617082300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b71d2d3-ede4-404d-a06b-be533fd5d813", "logId": "66e13408-3912-4b2b-b33a-527169571468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51845b26-651f-4bfa-92db-f4e3c713ad8b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833617098300, "endTime": 11833681131100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b71d2d3-ede4-404d-a06b-be533fd5d813", "logId": "8b203168-d5b3-4647-b292-b46b20259ed7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5cf54e0-dbe0-4fef-bb83-b6a0b67bd0ef", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833611842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5638cf7b-ee1d-4aa5-99f6-b6aed7c4910f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833616977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e13408-3912-4b2b-b33a-527169571468", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833611835600, "endTime": 11833617082300}, "additional": {"logType": "info", "children": [], "durationId": "355d2e87-83f4-4ac5-a503-cb6a495a820e", "parent": "2ca8a068-bf0a-4e9e-a1bd-e538626f843c"}}, {"head": {"id": "a0a820fe-2ce5-45db-a4a8-f3096c8b15c0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833617115300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc69cc80-71f7-4eb2-bce3-83373ab37b1d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833623662300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2974264-f96c-4a26-8a8d-2f37f80927db", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833623780100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75448589-faae-4af0-aa55-632c3e32950b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833623923400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8143d6f5-710c-4444-b0a0-ed11a2439584", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833624030900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5877678d-95c1-42fb-8b60-663e8d53c92f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833625419200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd7ec7b-a025-4e1a-83bf-f15477079c0e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833629778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5748ffa6-f814-4e9f-bc41-8ee74f6cc32b", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833638492200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9d6c8c-58ff-4fee-a1e6-6a9988bd6bf1", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833660682500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "731ab758-a0b0-49e9-9299-66e2d619fdbb", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833660860200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 56}, "markType": "other"}}, {"head": {"id": "2cf01003-02b0-4030-a064-f24d42fc4c70", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833661004400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 56}, "markType": "other"}}, {"head": {"id": "e87f4764-bcb4-4ffc-9c2b-c31676b1c3a9", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833680844900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e429da46-ba81-4579-9be6-5e85ddb9ff6d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833680970000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2409b4c9-fded-48f4-adac-16c56fe9ab93", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833681035000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e7466d-ca8d-4f3e-bcbc-ed340e58b112", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833681085900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b203168-d5b3-4647-b292-b46b20259ed7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833617098300, "endTime": 11833681131100}, "additional": {"logType": "info", "children": [], "durationId": "51845b26-651f-4bfa-92db-f4e3c713ad8b", "parent": "2ca8a068-bf0a-4e9e-a1bd-e538626f843c"}}, {"head": {"id": "2ca8a068-bf0a-4e9e-a1bd-e538626f843c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833611834200, "endTime": 11833681144800}, "additional": {"logType": "info", "children": ["66e13408-3912-4b2b-b33a-527169571468", "8b203168-d5b3-4647-b292-b46b20259ed7"], "durationId": "4b71d2d3-ede4-404d-a06b-be533fd5d813", "parent": "aed22087-217d-439f-82c6-cd29570c9054"}}, {"head": {"id": "dffde690-2612-42e2-994f-632c3e89deda", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833681796600, "endTime": 11833681817500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92cf3951-7d86-4a24-9996-7082160cab11", "logId": "def7ec4b-b2c5-44ad-bfb1-e12fe87cdd18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "def7ec4b-b2c5-44ad-bfb1-e12fe87cdd18", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833681796600, "endTime": 11833681817500}, "additional": {"logType": "info", "children": [], "durationId": "dffde690-2612-42e2-994f-632c3e89deda", "parent": "aed22087-217d-439f-82c6-cd29570c9054"}}, {"head": {"id": "aed22087-217d-439f-82c6-cd29570c9054", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833608921000, "endTime": 11833681833500}, "additional": {"logType": "info", "children": ["b73d95b8-774e-4fc6-94a8-077cd28994ef", "2ca8a068-bf0a-4e9e-a1bd-e538626f843c", "def7ec4b-b2c5-44ad-bfb1-e12fe87cdd18"], "durationId": "92cf3951-7d86-4a24-9996-7082160cab11", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "b337ed6d-96a6-444c-ae06-0b71e75111e4", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833682443900, "endTime": 11833704323200}, "additional": {"children": ["4c38295e-e357-4e95-9743-83ee116bb388", "8310944f-de92-48b6-807d-cf3bf82037d0", "9ed9706d-3c50-4210-9b98-bb983b05f53e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "989c78b0-8c85-44bc-b78e-7bb0849b6037", "logId": "838e4fd3-29a3-4365-bcee-0a6eb6875c17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c38295e-e357-4e95-9743-83ee116bb388", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833685493200, "endTime": 11833685507300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b337ed6d-96a6-444c-ae06-0b71e75111e4", "logId": "602046ed-67fd-4236-9e79-477da1796bad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "602046ed-67fd-4236-9e79-477da1796bad", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833685493200, "endTime": 11833685507300}, "additional": {"logType": "info", "children": [], "durationId": "4c38295e-e357-4e95-9743-83ee116bb388", "parent": "838e4fd3-29a3-4365-bcee-0a6eb6875c17"}}, {"head": {"id": "8310944f-de92-48b6-807d-cf3bf82037d0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833687368900, "endTime": 11833702871800}, "additional": {"children": ["0efb43bc-9a35-497d-afaf-7a3e18ee56c0", "83bf6717-afdc-4527-b8da-56b3b16e72bf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b337ed6d-96a6-444c-ae06-0b71e75111e4", "logId": "22201a4d-51a3-41d7-ab4a-55efd9e321ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0efb43bc-9a35-497d-afaf-7a3e18ee56c0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833687370100, "endTime": 11833692028200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8310944f-de92-48b6-807d-cf3bf82037d0", "logId": "53a313e2-9e99-4a4d-91b1-7feacf6a2a37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83bf6717-afdc-4527-b8da-56b3b16e72bf", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833692044200, "endTime": 11833702854400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8310944f-de92-48b6-807d-cf3bf82037d0", "logId": "eefed72b-da91-4acd-bada-58e1797b70e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5e429e0-8cf7-46d4-bdcf-2c7a7ac835e0", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833687375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0918d46-f790-4fe3-a6f8-0bbd7bb84863", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833691927100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a313e2-9e99-4a4d-91b1-7feacf6a2a37", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833687370100, "endTime": 11833692028200}, "additional": {"logType": "info", "children": [], "durationId": "0efb43bc-9a35-497d-afaf-7a3e18ee56c0", "parent": "22201a4d-51a3-41d7-ab4a-55efd9e321ee"}}, {"head": {"id": "35f5f4aa-42b7-4855-b28a-63cd0dd06e44", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833692057700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1177dada-baa5-431c-8f1d-050d708a24f0", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833698902800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62477c37-dc26-4dab-a114-ef8dbb9e4e4b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833699078500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d7f69ee-e08a-435f-b2e2-a0b8c2dea85d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833699351200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6436624-ba2f-4a44-aaee-feb834e4852a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833699523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "452e4879-9bf1-4af6-a672-a9eab704a26c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833699596200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99ba7a9-0d55-40aa-b42e-5b4ec35fa87f", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833699651200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb18484e-ca09-4b7a-8ac4-b21b88052c55", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833699739500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1173477b-947c-4dbd-87bc-94a5ac4efd7d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833702540900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50fc535f-ef4f-4252-93c6-66db00175c15", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833702675400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d045a8-cd19-47d0-bd0a-7858098a03b3", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833702750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e915c826-7f97-4196-97b3-28f9570fe49b", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833702804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eefed72b-da91-4acd-bada-58e1797b70e9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833692044200, "endTime": 11833702854400}, "additional": {"logType": "info", "children": [], "durationId": "83bf6717-afdc-4527-b8da-56b3b16e72bf", "parent": "22201a4d-51a3-41d7-ab4a-55efd9e321ee"}}, {"head": {"id": "22201a4d-51a3-41d7-ab4a-55efd9e321ee", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833687368900, "endTime": 11833702871800}, "additional": {"logType": "info", "children": ["53a313e2-9e99-4a4d-91b1-7feacf6a2a37", "eefed72b-da91-4acd-bada-58e1797b70e9"], "durationId": "8310944f-de92-48b6-807d-cf3bf82037d0", "parent": "838e4fd3-29a3-4365-bcee-0a6eb6875c17"}}, {"head": {"id": "9ed9706d-3c50-4210-9b98-bb983b05f53e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833704282300, "endTime": 11833704299900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b337ed6d-96a6-444c-ae06-0b71e75111e4", "logId": "b8435c99-f96d-4e54-86fe-81ae109f151b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8435c99-f96d-4e54-86fe-81ae109f151b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833704282300, "endTime": 11833704299900}, "additional": {"logType": "info", "children": [], "durationId": "9ed9706d-3c50-4210-9b98-bb983b05f53e", "parent": "838e4fd3-29a3-4365-bcee-0a6eb6875c17"}}, {"head": {"id": "838e4fd3-29a3-4365-bcee-0a6eb6875c17", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833682443900, "endTime": 11833704323200}, "additional": {"logType": "info", "children": ["602046ed-67fd-4236-9e79-477da1796bad", "22201a4d-51a3-41d7-ab4a-55efd9e321ee", "b8435c99-f96d-4e54-86fe-81ae109f151b"], "durationId": "b337ed6d-96a6-444c-ae06-0b71e75111e4", "parent": "98e5122b-a229-46de-bfdb-b361e26ca24c"}}, {"head": {"id": "98e5122b-a229-46de-bfdb-b361e26ca24c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833681850500, "endTime": 11833704341200}, "additional": {"logType": "info", "children": ["838e4fd3-29a3-4365-bcee-0a6eb6875c17"], "durationId": "989c78b0-8c85-44bc-b78e-7bb0849b6037", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "cd8b6068-fc4e-443f-9e35-6acd2e0649fc", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833717531400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69936355-083a-4868-b060-d4f5860e49e9", "name": "hvigorfile, resolve hvigorfile dependencies in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833718192100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e22920e8-05d2-4668-9f3d-d9eb13316dff", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833704353700, "endTime": 11833718287400}, "additional": {"logType": "info", "children": [], "durationId": "80f1ad05-8c88-45d4-982a-c55b0fc5edb6", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "ef162886-21f6-46af-8735-4411b87763de", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833719053800, "endTime": 11833719230600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "717d46da-1150-4ce1-978c-d7897fc89ca5", "logId": "5fc08c22-8fc3-4583-a6c7-12a0a306d259"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65cd19d5-aa75-4256-8e5c-ab50ce8cfc07", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833719077700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fc08c22-8fc3-4583-a6c7-12a0a306d259", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833719053800, "endTime": 11833719230600}, "additional": {"logType": "info", "children": [], "durationId": "ef162886-21f6-46af-8735-4411b87763de", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "404d3613-4345-478c-9996-b31f1c70daf2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833720633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d74f4e0-c4db-42b7-b674-cb150a444c63", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833726071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "436151c6-c498-401f-8953-2e2ca112f440", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833719244400, "endTime": 11833726774600}, "additional": {"logType": "info", "children": [], "durationId": "b90fb935-3710-493b-b4a6-a94ca9c985a8", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "737358e2-e21f-4021-ac58-d59dfa73094c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833726809800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a623fb88-72f9-4e76-aed2-183dfd6d3456", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833731900200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17f7e48f-daca-49a7-95ad-01de2d712ea4", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833732010900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f302f441-9a52-4400-85dd-c075240c91a9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833732259600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e275e72-2d87-4f70-b5d1-ae0f575fd977", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833736868300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2667efb-f829-40e2-9477-1722f4c7e956", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833736969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aebf9d4-cb53-4611-a4fa-18d759fb559f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833726795300, "endTime": 11833739703500}, "additional": {"logType": "info", "children": [], "durationId": "678db236-6bd1-46e2-b5d6-2a5a39a4d67b", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "91cb2244-e897-491d-a733-9f67cc189f4c", "name": "Configuration phase cost:135 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833739748300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3af2edd-8b33-45f5-ac7b-411ebf410902", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833739724400, "endTime": 11833739826700}, "additional": {"logType": "info", "children": [], "durationId": "2983ab7f-7515-4957-bc45-3978f61bf38a", "parent": "74de65e1-c8b5-4fc5-af41-99329660d989"}}, {"head": {"id": "74de65e1-c8b5-4fc5-af41-99329660d989", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833600271700, "endTime": 11833739867000}, "additional": {"logType": "info", "children": ["2e3608ec-b387-4170-a5fc-24d8d101564c", "22211d04-27c0-49e9-ac0f-cb2a34fa1552", "aed22087-217d-439f-82c6-cd29570c9054", "98e5122b-a229-46de-bfdb-b361e26ca24c", "e22920e8-05d2-4668-9f3d-d9eb13316dff", "436151c6-c498-401f-8953-2e2ca112f440", "7aebf9d4-cb53-4611-a4fa-18d759fb559f", "b3af2edd-8b33-45f5-ac7b-411ebf410902", "5fc08c22-8fc3-4583-a6c7-12a0a306d259"], "durationId": "717d46da-1150-4ce1-978c-d7897fc89ca5", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "550fd9db-200e-4bd6-8ec8-e3accf785be6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833740994400, "endTime": 11833741007700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b7b7e98-a824-4242-bc72-52d17668bf4b", "logId": "288622de-ebef-48e6-83a9-481f0573519b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "288622de-ebef-48e6-83a9-481f0573519b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833740994400, "endTime": 11833741007700}, "additional": {"logType": "info", "children": [], "durationId": "550fd9db-200e-4bd6-8ec8-e3accf785be6", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "a0b7caff-fd66-4563-ae2c-37ce554b41b6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833739893600, "endTime": 11833741018100}, "additional": {"logType": "info", "children": [], "durationId": "54892d0e-6e15-4b17-a502-358a4e6243d8", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "e6514944-f37b-4b73-ad39-62f6585abf64", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833741024400, "endTime": 11833741031000}, "additional": {"logType": "info", "children": [], "durationId": "160af27a-f83b-4935-bf39-064db7837a07", "parent": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3"}}, {"head": {"id": "624a678f-f6cc-4d2e-9579-8c9ab2f471b3", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833588531300, "endTime": 11833741035800}, "additional": {"logType": "info", "children": ["622f0431-20be-4fdb-b557-e5af99585985", "74de65e1-c8b5-4fc5-af41-99329660d989", "a0b7caff-fd66-4563-ae2c-37ce554b41b6", "e6514944-f37b-4b73-ad39-62f6585abf64", "41ea9e35-3fae-44dc-a8a9-0815471151f5", "6c63e6e1-d5a1-4eca-812e-668f09568af5", "288622de-ebef-48e6-83a9-481f0573519b"], "durationId": "5b7b7e98-a824-4242-bc72-52d17668bf4b"}}, {"head": {"id": "addc0656-1a20-424e-abaf-52c59c06cfe0", "name": "Configuration task cost before running: 156 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833741275100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48a91e8-4626-46b4-bee3-ca9023d4691d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833745845700, "endTime": 11833753878300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d8501646-f0bc-447b-ad74-52ffb4ef7264", "logId": "2e158277-7211-4dd9-8778-c8102b86b273"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8501646-f0bc-447b-ad74-52ffb4ef7264", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833742634700}, "additional": {"logType": "detail", "children": [], "durationId": "d48a91e8-4626-46b4-bee3-ca9023d4691d"}}, {"head": {"id": "17800372-8c57-4ed8-8ec3-065713ea64f9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833743087800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e20dc2d-2b2b-426f-bd77-d9601774457c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833743172500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f027419b-6999-49dc-b34a-79ae601ce321", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833743224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d81304-3e42-4895-8789-091642c9e910", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833745867600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2088d75f-b7bf-4b14-8105-92731cef193c", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833753607500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01be570e-8d9a-4d6b-834a-965395f4a718", "name": "entry : default@PreBuild cost memory 0.294586181640625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833753777300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e158277-7211-4dd9-8778-c8102b86b273", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833745845700, "endTime": 11833753878300}, "additional": {"logType": "info", "children": [], "durationId": "d48a91e8-4626-46b4-bee3-ca9023d4691d"}}, {"head": {"id": "a7f7bccf-7d18-4e6b-b48c-13f7c8f6c0d4", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833758585400, "endTime": 11833761309500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f518b3d7-b83f-437d-b118-2e6b8d6ec4af", "logId": "eadbfc1a-0dd6-4f08-b557-dd1f165f7eb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f518b3d7-b83f-437d-b118-2e6b8d6ec4af", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833757362800}, "additional": {"logType": "detail", "children": [], "durationId": "a7f7bccf-7d18-4e6b-b48c-13f7c8f6c0d4"}}, {"head": {"id": "edbe1125-18af-48c1-aed1-237f8ae2807b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833757822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74493475-64f3-466b-9447-18bfa207d421", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833757908500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad570fa6-e93c-4b2b-aa41-f5fac75b96c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833757970600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def048b0-ae6f-4a36-90e0-60a9093dbdc2", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833758595200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3f97454-5a63-4522-895b-efa5cbfcf9d5", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833761143300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f48a538-4a3c-4c23-b26e-f7265916bc7d", "name": "entry : default@MergeProfile cost memory 0.1310272216796875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833761236700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eadbfc1a-0dd6-4f08-b557-dd1f165f7eb8", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833758585400, "endTime": 11833761309500}, "additional": {"logType": "info", "children": [], "durationId": "a7f7bccf-7d18-4e6b-b48c-13f7c8f6c0d4"}}, {"head": {"id": "668391f5-6807-4952-9c33-a21a8cd77365", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833764793100, "endTime": 11833766884700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "54f71a51-a1b1-4335-bfea-c3a3ddeb3d2e", "logId": "bb574749-5a9f-4704-971e-7bdf5c61f379"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54f71a51-a1b1-4335-bfea-c3a3ddeb3d2e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833763031600}, "additional": {"logType": "detail", "children": [], "durationId": "668391f5-6807-4952-9c33-a21a8cd77365"}}, {"head": {"id": "053764a5-ce97-4860-be3a-c775c64361fd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833763487400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a27da9b-0fae-4b5b-a22d-70d02c4def7c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833763571200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87a788f-7520-4c2d-8812-be76ca8d6c02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833763624200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8036486-a731-4d91-8bcf-2cb1c5f46f44", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833764801900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c8f8a33-0b60-4b0b-9871-bf3bdf3ceb25", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833765608800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e45b3d-0b4f-4581-a4d2-a716bf9f0978", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833766716800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2c55b2-24ae-47ae-8851-421262bfc7a6", "name": "entry : default@CreateBuildProfile cost memory 0.09677886962890625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833766819000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb574749-5a9f-4704-971e-7bdf5c61f379", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833764793100, "endTime": 11833766884700}, "additional": {"logType": "info", "children": [], "durationId": "668391f5-6807-4952-9c33-a21a8cd77365"}}, {"head": {"id": "8352858a-2ae9-480f-aeba-305a95551dcf", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833769695700, "endTime": 11833770144000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9bd1037f-9b58-4296-9ef4-26bd7753923b", "logId": "ca24ad31-51e6-4ed5-b420-24cf37d560ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bd1037f-9b58-4296-9ef4-26bd7753923b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833768381200}, "additional": {"logType": "detail", "children": [], "durationId": "8352858a-2ae9-480f-aeba-305a95551dcf"}}, {"head": {"id": "c7eb13f1-5165-4f16-a8e5-a7affe4da251", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833768845700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f0dd18-ef20-47d4-9480-1de26a4fd462", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833768937900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98575b8c-f72e-4a1d-8ee0-bf7cc1667532", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833768997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f7b8e73-2249-4f12-8603-27c8e373a315", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833769704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7ea158-4e74-4780-a17a-1534af925061", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833769816800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa67d7bf-0c38-4c30-97ac-be92b15c20d7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833769872900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "355f40db-b57d-464d-a870-b3b5486ad95d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833769919300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae62f78f-8ef3-4015-85ac-cd8e50bafd4c", "name": "entry : default@PreCheckSyscap cost memory 0.05088043212890625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833770002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d7c802-975a-4321-a686-0edee5ce1b18", "name": "runTaskFromQueue task cost before running: 185 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833770087200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca24ad31-51e6-4ed5-b420-24cf37d560ec", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833769695700, "endTime": 11833770144000, "totalTime": 369000}, "additional": {"logType": "info", "children": [], "durationId": "8352858a-2ae9-480f-aeba-305a95551dcf"}}, {"head": {"id": "fbb58131-44c2-4c9d-a2d1-c8181fd6605c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833780001900, "endTime": 11833781075900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d9f686ab-d5aa-4d42-a547-0c9be9b446fe", "logId": "b64389c5-6a73-4b1d-bcf2-a0e7dad870a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9f686ab-d5aa-4d42-a547-0c9be9b446fe", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833771551500}, "additional": {"logType": "detail", "children": [], "durationId": "fbb58131-44c2-4c9d-a2d1-c8181fd6605c"}}, {"head": {"id": "58ad34ae-54ce-4922-892f-1aca7caeec5e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833772016800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f60f68e7-2df3-4cec-bfce-1f1650bf5d5a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833772103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def4c071-b0d7-46cb-b8a2-3b2ebbcbaf8e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833772157500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bb8da4b-a1c1-4def-b9ec-21c0da6d1328", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833780020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5edc0b1-c9c4-4bae-9962-e5858829195f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833780266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915be63e-390f-430c-adfa-699e10d56722", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833780896200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ec803b-0a6e-4626-ac2c-9dc7301e5e22", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07645416259765625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833781003900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b64389c5-6a73-4b1d-bcf2-a0e7dad870a7", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833780001900, "endTime": 11833781075900}, "additional": {"logType": "info", "children": [], "durationId": "fbb58131-44c2-4c9d-a2d1-c8181fd6605c"}}, {"head": {"id": "66eef76e-7e57-482e-8ddd-506f755e6d28", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833784352400, "endTime": 11833785397100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2caa1116-8d8f-4a7e-a4ce-e82a1d1ed82b", "logId": "356ff6db-dc4f-4fdf-905a-b9ef83d7b859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2caa1116-8d8f-4a7e-a4ce-e82a1d1ed82b", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833782649700}, "additional": {"logType": "detail", "children": [], "durationId": "66eef76e-7e57-482e-8ddd-506f755e6d28"}}, {"head": {"id": "cb30dad3-1a54-45e3-8d6f-5bb2bf0ce5be", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833783141800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429b9abf-8a7b-4c57-83a9-a00ac6a64f9c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833783239200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd86874b-1043-47c6-a22b-eb9567870684", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833783294800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a886a419-c7a5-450f-9473-499b7c445930", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833784363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2830426-a46c-4b54-b70a-c55835a0e7fb", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833785235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a56e6581-12f1-431c-b345-624402614209", "name": "entry : default@ProcessProfile cost memory 0.058502197265625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833785329900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356ff6db-dc4f-4fdf-905a-b9ef83d7b859", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833784352400, "endTime": 11833785397100}, "additional": {"logType": "info", "children": [], "durationId": "66eef76e-7e57-482e-8ddd-506f755e6d28"}}, {"head": {"id": "a39ba136-4d1c-4cb5-9ee4-5bfac5b184fb", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833788932600, "endTime": 11833795537100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "55a6137c-e229-4774-a974-644b2992b307", "logId": "008d1e6e-cf33-41ba-ac10-4160102bd86d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55a6137c-e229-4774-a974-644b2992b307", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833786786300}, "additional": {"logType": "detail", "children": [], "durationId": "a39ba136-4d1c-4cb5-9ee4-5bfac5b184fb"}}, {"head": {"id": "1df053c6-594a-4347-bb4e-07c7d0c31fa4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833787248700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d02de5-bb85-4111-87d2-e4b347691cb3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833787337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79141890-5fcf-4e28-9a96-553cb0bb5dc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833787391300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155e0a16-a1be-4586-932b-37f9ccee16d5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833788942600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc384f1f-8184-4b89-9c2e-60b000cd60fd", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833795317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "180c3671-e3ea-4470-be58-f19aa145ebc8", "name": "entry : default@ProcessRouterMap cost memory -1.3883743286132812", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833795455700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008d1e6e-cf33-41ba-ac10-4160102bd86d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833788932600, "endTime": 11833795537100}, "additional": {"logType": "info", "children": [], "durationId": "a39ba136-4d1c-4cb5-9ee4-5bfac5b184fb"}}, {"head": {"id": "dffe2c84-f349-458b-90e3-a3c5190ede33", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833802054400, "endTime": 11833804783900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2f196ccb-7484-4a4e-b6e0-c3e64c730194", "logId": "cb9977ec-bd25-4852-b30b-083daa24be62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f196ccb-7484-4a4e-b6e0-c3e64c730194", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833798279200}, "additional": {"logType": "detail", "children": [], "durationId": "dffe2c84-f349-458b-90e3-a3c5190ede33"}}, {"head": {"id": "a35f34cc-a6a9-4fcd-a86f-64ce539208e2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833798793200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b5bdf1-58f5-45d9-85f1-38b351610f31", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833798887400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3813481a-854c-48ce-8910-8316594da93e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833798943300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab89dfd-564e-4996-b7e5-71407511519d", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833799777800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8242f21-c352-4540-9613-a6a95f1b696f", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833803171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52d5da86-78a8-4450-a169-1caeb011eb04", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833803305800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e416fcd-b4e7-4ca1-ace7-650038e06546", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833803361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e7d9f1-9a4e-4c71-a097-8298f32b2631", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833803413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5508342b-951d-4c5b-92b5-72aba243b0d1", "name": "entry : default@PreviewProcessResource cost memory 0.08844757080078125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833803503100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caf77d99-5c32-42af-b982-17d498935503", "name": "runTaskFromQueue task cost before running: 219 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833804697000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb9977ec-bd25-4852-b30b-083daa24be62", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833802054400, "endTime": 11833804783900, "totalTime": 1507200}, "additional": {"logType": "info", "children": [], "durationId": "dffe2c84-f349-458b-90e3-a3c5190ede33"}}, {"head": {"id": "da0044f1-329f-4b34-b96d-3308c6fd7a77", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833811623000, "endTime": 11833832009100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "44d56796-28fd-4f4a-a77c-e339057eff4b", "logId": "0dc305b9-29c4-43bf-bbc9-7a4687bff1d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44d56796-28fd-4f4a-a77c-e339057eff4b", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833807710300}, "additional": {"logType": "detail", "children": [], "durationId": "da0044f1-329f-4b34-b96d-3308c6fd7a77"}}, {"head": {"id": "9de4657b-3e57-4ef2-b24c-298a2f092ee1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833808157500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3af71487-5c36-4cd9-b512-017d3960219c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833808284800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bea0a87e-c4c1-4448-8a1d-5429cedd5e48", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833808340300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99be95f0-2737-48ad-b7a1-3f50baf5a775", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833811636700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eebc351a-67c8-42f2-a343-b791aee5eaf8", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833831812500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1628aecd-cacb-43d9-8e72-fe6a78e82b41", "name": "entry : default@GenerateLoaderJson cost memory -0.8736114501953125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833831941600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc305b9-29c4-43bf-bbc9-7a4687bff1d4", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833811623000, "endTime": 11833832009100}, "additional": {"logType": "info", "children": [], "durationId": "da0044f1-329f-4b34-b96d-3308c6fd7a77"}}, {"head": {"id": "b1f0b1a0-91be-4c3f-b591-ce78662c3833", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833842663100, "endTime": 11833881266000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "aa8190c2-3d67-4a33-9509-e5bd29740268", "logId": "bdd75357-0ee3-4997-86c4-558d883a8918"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa8190c2-3d67-4a33-9509-e5bd29740268", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833838949000}, "additional": {"logType": "detail", "children": [], "durationId": "b1f0b1a0-91be-4c3f-b591-ce78662c3833"}}, {"head": {"id": "2269d15e-f935-4777-92c6-bfad01bbc849", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833839391500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6222caaa-822d-42b6-8a99-9455a8151d55", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833839479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf2266c-ef7a-4fa5-bee8-39aca801cb07", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833839529700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258a2cd8-a23d-46d1-b530-ffe5be0002b5", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833840314100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95bf4849-7575-4fbf-90cd-c5044d676b1f", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833842688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0e6d76-3aa1-4823-a3e8-526e02be01df", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833880865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11d4dc5-5cf3-45e9-bb3e-2f2f28ac9cc1", "name": "entry : default@PreviewCompileResource cost memory -0.30266571044921875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833881131200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdd75357-0ee3-4997-86c4-558d883a8918", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833842663100, "endTime": 11833881266000}, "additional": {"logType": "info", "children": [], "durationId": "b1f0b1a0-91be-4c3f-b591-ce78662c3833"}}, {"head": {"id": "0f41bd0b-9901-48d1-927a-452fc3300169", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884160700, "endTime": 11833884544300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "637de424-c5b1-44bd-a403-2233461190af", "logId": "42e5dcc7-0467-4e24-9700-acd46b021add"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "637de424-c5b1-44bd-a403-2233461190af", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833883512900}, "additional": {"logType": "detail", "children": [], "durationId": "0f41bd0b-9901-48d1-927a-452fc3300169"}}, {"head": {"id": "e1e73855-32fd-4061-b974-13749300a339", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833883955300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05153684-b903-44b2-a441-fa93c4b33466", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884035300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489ebd10-7590-4cca-bc63-a0af21793860", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884086700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90213321-4bbe-45ef-8624-5f4d0bf25061", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a19539-2252-4820-bec2-2d8d20976cfc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884253400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c52b567-f8f8-4374-bf39-d703ff5be6d5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884308900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56af1951-de56-464e-a3e3-fbfbf773ad4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884349100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ec84d6-8e57-40e8-9b98-740f9df5eca9", "name": "entry : default@PreviewHookCompileResource cost memory 0.05146026611328125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18cde916-624e-4ee8-8663-df3d1191c268", "name": "runTaskFromQueue task cost before running: 299 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884481800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e5dcc7-0467-4e24-9700-acd46b021add", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833884160700, "endTime": 11833884544300, "totalTime": 301300}, "additional": {"logType": "info", "children": [], "durationId": "0f41bd0b-9901-48d1-927a-452fc3300169"}}, {"head": {"id": "a758d01d-fd49-4282-bada-707e5963d476", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833887002900, "endTime": 11833889117600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "e4c8d949-e79c-422b-acc0-d99cb446cdd9", "logId": "b8a9e663-2361-4876-a46e-b57addfcea74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4c8d949-e79c-422b-acc0-d99cb446cdd9", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833885898900}, "additional": {"logType": "detail", "children": [], "durationId": "a758d01d-fd49-4282-bada-707e5963d476"}}, {"head": {"id": "189c6d86-2065-411e-8b31-f61f61658476", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833886315500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f5c473-ee20-463d-b9c7-e15f968a1429", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833886394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2591637b-10fe-4183-8b4d-df6ef0cc75b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833886461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9681d0c3-a6b0-4ded-855a-d8e3a16d9f73", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833887011800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abec768f-2742-498a-a668-f0545620752d", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833888972300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d5af21-6428-4036-b5a6-122af235a278", "name": "entry : default@CopyPreviewProfile cost memory 0.0970306396484375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833889059200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a9e663-2361-4876-a46e-b57addfcea74", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833887002900, "endTime": 11833889117600}, "additional": {"logType": "info", "children": [], "durationId": "a758d01d-fd49-4282-bada-707e5963d476"}}, {"head": {"id": "78ce342d-2ae2-48ff-9495-98e7bfda0628", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833891973400, "endTime": 11833892394900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "0ca1cc35-ba1f-432c-8f62-4f6127a7160d", "logId": "75ce8b96-4e3c-4dd5-bd62-64c3848a1506"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ca1cc35-ba1f-432c-8f62-4f6127a7160d", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833890697100}, "additional": {"logType": "detail", "children": [], "durationId": "78ce342d-2ae2-48ff-9495-98e7bfda0628"}}, {"head": {"id": "c4ed6999-1897-4bdf-89be-6e1efa34550f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833891141300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ec039b-1386-4b2e-ae2e-e6e8f6c356aa", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833891229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23e915a-2356-4f4e-a502-27d4dc7b673a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833891292000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19a8384-adf0-458d-b758-f82784ae0d74", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833891983200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb2ed41-e78c-4047-afd1-08caff1ea571", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833892076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c805edb3-982c-4e3b-aa72-7c3232059ed5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833892127200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534675e8-69a7-46d1-8c42-7877e645e286", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833892171500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f44d2b4-15eb-4bee-a2fc-2429e4d4f2c4", "name": "entry : default@ReplacePreviewerPage cost memory 0.05141448974609375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833892254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "617db8ea-08b3-493e-9192-587170e809b3", "name": "runTaskFromQueue task cost before running: 307 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833892340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ce8b96-4e3c-4dd5-bd62-64c3848a1506", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833891973400, "endTime": 11833892394900, "totalTime": 345700}, "additional": {"logType": "info", "children": [], "durationId": "78ce342d-2ae2-48ff-9495-98e7bfda0628"}}, {"head": {"id": "e5e92a39-14df-4b37-beec-2e000f7c230f", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833893791200, "endTime": 11833894024500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "01669679-c1bf-4bda-9a43-cce96f60005a", "logId": "f3249f42-ea05-46c5-a160-c23473a7c8b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01669679-c1bf-4bda-9a43-cce96f60005a", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833893747100}, "additional": {"logType": "detail", "children": [], "durationId": "e5e92a39-14df-4b37-beec-2e000f7c230f"}}, {"head": {"id": "409d4bbb-957f-4f85-96a3-978937f26df3", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833893797400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8185354-f1a3-4c45-ae3a-7edc4bfdc9f8", "name": "entry : buildPreviewerResource cost memory 0.01184844970703125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833893898400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08bea2e0-086a-444c-8f3c-717bab71b3bb", "name": "runTaskFromQueue task cost before running: 309 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833893971200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3249f42-ea05-46c5-a160-c23473a7c8b9", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833893791200, "endTime": 11833894024500, "totalTime": 164100}, "additional": {"logType": "info", "children": [], "durationId": "e5e92a39-14df-4b37-beec-2e000f7c230f"}}, {"head": {"id": "6c996427-41ad-41ce-b69b-8ee313f132fd", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833896738800, "endTime": 11833898953900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "286cf30a-ac74-4a86-9ade-0d1c1f1a425d", "logId": "5b254221-aebf-4623-b301-fd5eefaac538"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "286cf30a-ac74-4a86-9ade-0d1c1f1a425d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833895446600}, "additional": {"logType": "detail", "children": [], "durationId": "6c996427-41ad-41ce-b69b-8ee313f132fd"}}, {"head": {"id": "31c3f21c-918a-4905-b385-d247959e280b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833895914700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e1ba02-dde8-467e-8cb8-e728a9af3c88", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833896006400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f56f3b1-b1fb-4c72-9e4a-c4f29d9e6541", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833896061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f6006ed-7dd0-4e99-ab48-c0a7e55cbe6f", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833896747000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f106de7-3c0d-413c-83b1-f61943d32869", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833898798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a90300f-69fa-434e-9fec-03d62fa8b530", "name": "entry : default@PreviewUpdateAssets cost memory 0.10992431640625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833898888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b254221-aebf-4623-b301-fd5eefaac538", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833896738800, "endTime": 11833898953900}, "additional": {"logType": "info", "children": [], "durationId": "6c996427-41ad-41ce-b69b-8ee313f132fd"}}, {"head": {"id": "dc4442c5-7c84-4120-b48c-9187c50129be", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833906251300, "endTime": 11845374658900}, "additional": {"children": ["fa4bc0c1-5dfc-42b7-821a-a09029d75355"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets' has been changed."], "detailId": "a91c7246-e39c-4cc7-a981-3235ef5c767d", "logId": "656bdb68-5694-4fb1-bd6c-8c412c387d0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a91c7246-e39c-4cc7-a981-3235ef5c767d", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833900831800}, "additional": {"logType": "detail", "children": [], "durationId": "dc4442c5-7c84-4120-b48c-9187c50129be"}}, {"head": {"id": "10b2ea63-85cf-4022-808f-beded2d97899", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833901283600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ee24ec8-7b8a-4ce6-8d22-2f83df50693f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833901369400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb2fd6ef-3e02-458c-b28f-38a67dac133b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833901425700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14200de0-3471-4a1d-8998-5301eb4102c9", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833906264400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f68239-e4aa-466e-a5b0-9b4602d93a57", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833940245200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6d4da50-1e6c-4639-a06e-a0bd6ec0dff8", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833940402600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11833953656000, "endTime": 11845372202800}, "additional": {"children": ["394b95aa-6aaf-48ec-b30c-16bcf311f739", "92ee54a3-738b-45bd-9405-1eabccb517e9", "06a855ab-97c7-4007-8e75-c355913b4bcd", "191a5a97-98c7-4d6b-86b7-60b035dccd9c", "cf9d282c-f9b9-4751-9f9a-6cc4c3349481", "6138804d-1bf5-47a7-b711-0065220250f7", "0ad67bab-5b4f-42e6-b99e-a6a9b10a297a", "101ade8c-b4ab-4883-aa36-6ec55264b61d", "ab20ae72-3a6d-4f6d-9be7-ea70aa8dbb02", "d9898b23-0a9f-4299-8de9-ef5161a98d0c", "3f78df29-87f3-4dfb-a6ee-d9e22a053477", "1d030885-77b1-4f99-88de-074f1f5f71aa", "ac0973c8-e3c5-46c4-93ce-22827f3d9c9c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "dc4442c5-7c84-4120-b48c-9187c50129be", "logId": "84168331-e02e-4d52-8885-a08a1e21e1d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b9e74ca-ecb9-468c-aa5d-79b03e65add5", "name": "entry : default@PreviewArkTS cost memory -1.2884521484375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833955547100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3c64a91-0348-4b5b-9e05-9e7ad4f4db53", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11837786103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "394b95aa-6aaf-48ec-b30c-16bcf311f739", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11837787293700, "endTime": 11837787316600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "446d2bf6-bfeb-46f4-b014-765de2dd98d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "446d2bf6-bfeb-46f4-b014-765de2dd98d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11837787293700, "endTime": 11837787316600}, "additional": {"logType": "info", "children": [], "durationId": "394b95aa-6aaf-48ec-b30c-16bcf311f739", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "2ce8c757-6392-4bfc-a8ec-68cd4e869b36", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842305729600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ee54a3-738b-45bd-9405-1eabccb517e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11842306836400, "endTime": 11842306858300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "b3a44eb1-ff47-44f3-9971-04b440b4f31f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3a44eb1-ff47-44f3-9971-04b440b4f31f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842306836400, "endTime": 11842306858300}, "additional": {"logType": "info", "children": [], "durationId": "92ee54a3-738b-45bd-9405-1eabccb517e9", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "e0badb45-7b5c-444e-af30-92bef9fe82ba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842609499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a855ab-97c7-4007-8e75-c355913b4bcd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11842610679500, "endTime": 11842610708900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "22e9c854-5358-4ffb-a05a-385783c2eb9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22e9c854-5358-4ffb-a05a-385783c2eb9d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842610679500, "endTime": 11842610708900}, "additional": {"logType": "info", "children": [], "durationId": "06a855ab-97c7-4007-8e75-c355913b4bcd", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "22fa9f52-6625-4f10-a7dc-25033a60b6e5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842732473700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "191a5a97-98c7-4d6b-86b7-60b035dccd9c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11842733526200, "endTime": 11842733550100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "deec4dd8-c840-4f73-8aeb-3d58cd894d2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "deec4dd8-c840-4f73-8aeb-3d58cd894d2e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842733526200, "endTime": 11842733550100}, "additional": {"logType": "info", "children": [], "durationId": "191a5a97-98c7-4d6b-86b7-60b035dccd9c", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "8021dd60-b614-40f9-bffe-2e9191ac52f7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842867052200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf9d282c-f9b9-4751-9f9a-6cc4c3349481", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11842868152300, "endTime": 11842868175900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "3909daf0-582a-4020-965b-c56d1c9fb9c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3909daf0-582a-4020-965b-c56d1c9fb9c5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11842868152300, "endTime": 11842868175900}, "additional": {"logType": "info", "children": [], "durationId": "cf9d282c-f9b9-4751-9f9a-6cc4c3349481", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "f26a0cd6-c993-4812-aed9-df5b4e2c2ced", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11843005111400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6138804d-1bf5-47a7-b711-0065220250f7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11843006226600, "endTime": 11843006252700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "68318b09-0804-4718-ba88-2d70b8d9e445"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68318b09-0804-4718-ba88-2d70b8d9e445", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11843006226600, "endTime": 11843006252700}, "additional": {"logType": "info", "children": [], "durationId": "6138804d-1bf5-47a7-b711-0065220250f7", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "7add8851-0772-4d11-b255-8c6961a03b52", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11843127646500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad67bab-5b4f-42e6-b99e-a6a9b10a297a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11843129046900, "endTime": 11843129072900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "68351258-ce49-4ef5-9bbd-ee91014036a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68351258-ce49-4ef5-9bbd-ee91014036a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11843129046900, "endTime": 11843129072900}, "additional": {"logType": "info", "children": [], "durationId": "0ad67bab-5b4f-42e6-b99e-a6a9b10a297a", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "d53a351c-a4eb-4e0a-a3c3-31f39b0aa4b2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11843206898400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "101ade8c-b4ab-4883-aa36-6ec55264b61d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11843208058300, "endTime": 11843208080100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "529cdbaf-098f-45cf-bd85-a0d5adc1743d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "529cdbaf-098f-45cf-bd85-a0d5adc1743d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11843208058300, "endTime": 11843208080100}, "additional": {"logType": "info", "children": [], "durationId": "101ade8c-b4ab-4883-aa36-6ec55264b61d", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "4a7243d8-df07-443d-8191-729a9d8783b7", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845370985600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab20ae72-3a6d-4f6d-9be7-ea70aa8dbb02", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11845372097400, "endTime": 11845372116300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "e6c0bf4e-1362-407c-b219-0357fda14b70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6c0bf4e-1362-407c-b219-0357fda14b70", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845372097400, "endTime": 11845372116300}, "additional": {"logType": "info", "children": [], "durationId": "ab20ae72-3a6d-4f6d-9be7-ea70aa8dbb02", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "84168331-e02e-4d52-8885-a08a1e21e1d6", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11833953656000, "endTime": 11845372202800}, "additional": {"logType": "info", "children": ["446d2bf6-bfeb-46f4-b014-765de2dd98d7", "b3a44eb1-ff47-44f3-9971-04b440b4f31f", "22e9c854-5358-4ffb-a05a-385783c2eb9d", "deec4dd8-c840-4f73-8aeb-3d58cd894d2e", "3909daf0-582a-4020-965b-c56d1c9fb9c5", "68318b09-0804-4718-ba88-2d70b8d9e445", "68351258-ce49-4ef5-9bbd-ee91014036a0", "529cdbaf-098f-45cf-bd85-a0d5adc1743d", "e6c0bf4e-1362-407c-b219-0357fda14b70", "e5dfc217-d05d-4981-be75-5fd994ddf5d5", "2f0601c2-6325-4a45-9fd8-1be22e95b492", "61816856-e444-4afa-b4e8-9a22ab7c7c30", "62b3a01c-8b56-4d6d-9a3f-7d92f63d5f53"], "durationId": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "parent": "656bdb68-5694-4fb1-bd6c-8c412c387d0c"}}, {"head": {"id": "d9898b23-0a9f-4299-8de9-ef5161a98d0c", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11836601257900, "endTime": 11837757021700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "e5dfc217-d05d-4981-be75-5fd994ddf5d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5dfc217-d05d-4981-be75-5fd994ddf5d5", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11836601257900, "endTime": 11837757021700}, "additional": {"logType": "info", "children": [], "durationId": "d9898b23-0a9f-4299-8de9-ef5161a98d0c", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "3f78df29-87f3-4dfb-a6ee-d9e22a053477", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11837757215500, "endTime": 11837761506800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "2f0601c2-6325-4a45-9fd8-1be22e95b492"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f0601c2-6325-4a45-9fd8-1be22e95b492", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11837757215500, "endTime": 11837761506800}, "additional": {"logType": "info", "children": [], "durationId": "3f78df29-87f3-4dfb-a6ee-d9e22a053477", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "1d030885-77b1-4f99-88de-074f1f5f71aa", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11837761594000, "endTime": 11837761599200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "61816856-e444-4afa-b4e8-9a22ab7c7c30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61816856-e444-4afa-b4e8-9a22ab7c7c30", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11837761594000, "endTime": 11837761599200}, "additional": {"logType": "info", "children": [], "durationId": "1d030885-77b1-4f99-88de-074f1f5f71aa", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "ac0973c8-e3c5-46c4-93ce-22827f3d9c9c", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker3", "startTime": 11837761657600, "endTime": 11845371057400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "62b3a01c-8b56-4d6d-9a3f-7d92f63d5f53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62b3a01c-8b56-4d6d-9a3f-7d92f63d5f53", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11837761657600, "endTime": 11845371057400}, "additional": {"logType": "info", "children": [], "durationId": "ac0973c8-e3c5-46c4-93ce-22827f3d9c9c", "parent": "84168331-e02e-4d52-8885-a08a1e21e1d6"}}, {"head": {"id": "656bdb68-5694-4fb1-bd6c-8c412c387d0c", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833906251300, "endTime": 11845374658900, "totalTime": 11468391900}, "additional": {"logType": "info", "children": ["84168331-e02e-4d52-8885-a08a1e21e1d6"], "durationId": "dc4442c5-7c84-4120-b48c-9187c50129be"}}, {"head": {"id": "38509dc0-4451-48d4-8015-cd026d5e41b2", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845378805700, "endTime": 11845379082400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d4b2504b-5402-4e1d-a784-1564457a6c00", "logId": "d86922f0-6c7f-4dbd-a6c3-74e8a415e6b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4b2504b-5402-4e1d-a784-1564457a6c00", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845378772000}, "additional": {"logType": "detail", "children": [], "durationId": "38509dc0-4451-48d4-8015-cd026d5e41b2"}}, {"head": {"id": "2ddbb73b-6653-4cd8-aa85-920a49cf8e99", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845378814900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7381b381-9fef-4cf9-8ea5-19c4d9af536a", "name": "entry : PreviewBuild cost memory 0.01175689697265625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845378933600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "198e07ce-2310-47e7-852e-6216c390d9d6", "name": "runTaskFromQueue task cost before running: 11 s 794 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845379020600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86922f0-6c7f-4dbd-a6c3-74e8a415e6b3", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845378805700, "endTime": 11845379082400, "totalTime": 192000}, "additional": {"logType": "info", "children": [], "durationId": "38509dc0-4451-48d4-8015-cd026d5e41b2"}}, {"head": {"id": "608416bd-24a6-43c4-81e3-f02d34cb1459", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388302500, "endTime": 11845388323700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2973822f-5c6f-4759-8430-24aae4fe8fdd", "logId": "7a1132ab-6872-4895-b9a2-d30288aaaa72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a1132ab-6872-4895-b9a2-d30288aaaa72", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388302500, "endTime": 11845388323700}, "additional": {"logType": "info", "children": [], "durationId": "608416bd-24a6-43c4-81e3-f02d34cb1459"}}, {"head": {"id": "4664f833-4636-47f1-bf00-1a235481ec64", "name": "BUILD SUCCESSFUL in 11 s 803 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388365200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8808f99a-10d5-40bb-8a91-a5c20c68b244", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11833585723900, "endTime": 11845388582900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "1865b8f8-c931-49f9-814e-2896c0f38db1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388609300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c29a7f-2451-4ac4-920c-cbb7b9b09aa3", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388672300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7213221-597c-43b3-969d-cc0d2286d4f7", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e1a2ba-87e8-4dfc-b630-cc67ca4dcc86", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388769100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa500e4-6ed5-4582-9b78-7da684e13678", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388812200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1765ef6b-0555-41ea-a0a3-d4d898ab8b2e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4ee9b7b-be8a-4e16-b537-d98db7bcc0a9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388897400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b41c01e-bf96-47a4-ae53-3264e52be07a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdea0a0c-2a06-43eb-8a1f-7dbd62aa99a9", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845388974600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67802b89-a5eb-444d-b1bb-098e09348764", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845389014500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae83427-062d-466e-a9aa-92bb9f4d4c59", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845391763700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821055bc-674c-4c64-960c-4e8402810a12", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845392519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b4939d-11af-4d2a-b19e-edfe1e2b2145", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845392784600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08dde4a3-b25c-4844-8100-5394d35fb76d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845407034700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d34a05-7f1c-42b7-a274-e89aebbdffff", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845408093800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8c86cc-a0c8-4f5e-a918-c9ea92cee92b", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845408393200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa3062a-759c-4e03-9297-a2d4c9c0751a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845408661300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc922598-2253-4573-b00d-11868ebf5549", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845409394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aab854b-b2c1-4424-be41-43ed2c266203", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845409468400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f344ce48-b10b-4f0d-9fc3-847bfd693685", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845409714000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15708844-f460-45c6-b46d-c9df8da1e27f", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845409970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "739ebcb0-b9e7-4c67-bf8f-e68d9eced426", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845410267300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "091d9981-589c-4ba7-b101-3f439b6edd60", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:22 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845410545700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}