package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icss.wallet.entity.BankCard;
import com.icss.wallet.result.R;
import com.icss.wallet.service.BankCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@CrossOrigin
@RestController
@RequestMapping("/bankCards")
public class BankCardController {
    @Autowired
    private BankCardService bankCardService;

    @GetMapping
    public R getAllCards() {
        List<BankCard> cards = bankCardService.getAllCards();
        return R.success("查询成功", cards);
    }

    @GetMapping("/user/{userId}")
    public R getCardsByUser(@PathVariable Long userId) {
        List<BankCard> cards = bankCardService.getCardsByUser(userId);
        return R.success("查询成功", cards);
    }

    @GetMapping("/bound/user/{userId}")
    public R getBoundCardsByUser(@PathVariable Long userId) {
        List<BankCard> cards = bankCardService.getBoundCardsByUser(userId);
        return R.success("查询成功", cards);
    }

    @PostMapping("/add")
    public R addCard(@RequestBody BankCard bankCard) {
        try {
            // 验证必要字段
            if (bankCard.getCardNumber() == null || bankCard.getBankName() == null) {
                return R.failure("银行卡号和银行名称不能为空");
            }

            bankCardService.addCard(bankCard);
            return R.success("添加成功", bankCard);
        } catch (Exception e) {
            return R.failure(e.getMessage());
        }
    }

    @PostMapping("/bind")
    public R bindCard(@RequestBody BankCard bankCard) {
        try {
            bankCardService.bindCard(bankCard);
            return R.success("绑定成功");
        } catch (Exception e) {
            return R.failure(e.getMessage());
        }
    }

    @PostMapping("/bind/{cardId}")
    public R bindCardById(@PathVariable Long cardId) {
        try {
            bankCardService.bindCardById(cardId);
            return R.success("绑定成功");
        } catch (Exception e) {
            return R.failure(e.getMessage());
        }
    }

    @PostMapping("/unbind/{cardId}")
    public R unbindCard(@PathVariable Long cardId) {
        bankCardService.unbindCard(cardId);
        return R.success("解绑成功");
    }

    @PutMapping("/{cardId}/default")
    public R setDefaultCard(@PathVariable Long cardId) {
        try {
            bankCardService.setDefaultCard(cardId);
            return R.success("设置默认卡成功");
        } catch (Exception e) {
            return R.failure(e.getMessage());
        }
    }
    // 新增：获取单个银行卡详情
    @GetMapping("/{cardId}")
    public R getCardById(@PathVariable Long cardId) {
        BankCard card = bankCardService.getCardById(cardId);
        if (card == null) {
            return R.failure("银行卡不存在");
        }
        return R.success("查询成功", card);
    }

    // 新增：删除银行卡
    @DeleteMapping("/{cardId}")
    public R deleteCard(@PathVariable Long cardId) {
        try {
            bankCardService.deleteCard(cardId);
            return R.success("删除成功");
        } catch (Exception e) {
            return R.failure(e.getMessage());
        }
    }

    // ==================== 管理员专用API ====================

    /**
     * 管理员分页查询银行卡信息（包含用户信息）
     */
    @GetMapping("/admin/page")
    public R getBankCardsWithUserInfo(@RequestParam(defaultValue = "1") int pageNum,
                                     @RequestParam(defaultValue = "10") int pageSize,
                                     @RequestParam(required = false) String phone,
                                     @RequestParam(required = false) String cardNumber,
                                     @RequestParam(required = false) Integer isBound) {
        try {
            IPage<BankCard> page = bankCardService.getBankCardsWithUserInfo(pageNum, pageSize, phone, cardNumber, isBound);
            return R.success("查询成功", page);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 管理员获取银行卡统计信息
     */
    @GetMapping("/admin/statistics")
    public R getBankCardStatistics() {
        try {
            java.util.Map<String, Object> statistics = bankCardService.getBankCardStatistics();
            return R.success("获取统计信息成功", statistics);
        } catch (Exception e) {
            return R.failure("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 管理员添加银行卡
     */
    @PostMapping("/admin/add")
    public R addBankCard(@RequestBody BankCard bankCard) {
        try {
            bankCard.setCreateTime(new Date());
            bankCard.setUpdateTime(new Date());
            boolean result = bankCardService.save(bankCard);
            if (result) {
                return R.success("添加银行卡成功");
            } else {
                return R.failure("添加银行卡失败");
            }
        } catch (Exception e) {
            return R.failure("添加银行卡失败: " + e.getMessage());
        }
    }

    /**
     * 管理员绑定银行卡
     */
    @PostMapping("/admin/bind/{cardId}")
    public R bindBankCard(@PathVariable Long cardId) {
        try {
            boolean result = bankCardService.bindCard(cardId);
            if (result) {
                return R.success("绑定成功");
            } else {
                return R.failure("绑定失败");
            }
        } catch (Exception e) {
            return R.failure("绑定失败: " + e.getMessage());
        }
    }

    /**
     * 管理员解绑银行卡
     */
    @PostMapping("/admin/unbind/{cardId}")
    public R unbindBankCard(@PathVariable Long cardId) {
        try {
            boolean result = bankCardService.unbindCard(cardId);
            if (result) {
                return R.success("解绑成功");
            } else {
                return R.failure("解绑失败");
            }
        } catch (Exception e) {
            return R.failure("解绑失败: " + e.getMessage());
        }
    }

    /**
     * 管理员设置默认银行卡
     */
    @PutMapping("/admin/{cardId}/default")
    public R setDefaultBankCard(@PathVariable Long cardId) {
        try {
            boolean result = bankCardService.setDefaultCard(cardId);
            if (result) {
                return R.success("设置默认卡片成功");
            } else {
                return R.failure("设置默认卡片失败");
            }
        } catch (Exception e) {
            return R.failure("设置默认卡片失败: " + e.getMessage());
        }
    }

    /**
     * 管理员删除银行卡
     */
    @DeleteMapping("/admin/{cardId}")
    public R deleteBankCard(@PathVariable Long cardId) {
        try {
            boolean result = bankCardService.removeById(cardId);
            if (result) {
                return R.success("删除成功");
            } else {
                return R.failure("删除失败");
            }
        } catch (Exception e) {
            return R.failure("删除失败: " + e.getMessage());
        }
    }
}