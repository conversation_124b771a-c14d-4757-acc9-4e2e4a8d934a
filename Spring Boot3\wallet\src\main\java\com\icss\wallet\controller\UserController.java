package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.User;
import com.icss.wallet.result.R;
import com.icss.wallet.service.UserService;
import com.icss.wallet.service.SmsCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

@RestController
@RequestMapping("/user")
@CrossOrigin
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private SmsCodeService smsCodeService;

    @PostMapping("/login")
    public R<String> login(@RequestParam("username") String username,
                           @RequestParam("password") String password) {
        User user = userService.findByPhone(username);
        if (user == null) {
            return R.failure("用户不存在");
        }
        if (!user.getPassword().equals(password)) {
            return R.failure("密码错误");
        }

        userService.updateLastLoginTime(user.getUserId());
        return R.success("登录成功", "token-" + user.getUserId());
    }

    @PostMapping("/loginWithCode")
    public R<String> loginWithCode(@RequestParam("phone") String phone,
                                   @RequestParam("code") String code) {
        // 验证验证码
        boolean isValidCode = smsCodeService.verifyCode(phone, code, 1);
        if (!isValidCode) {
            return R.failure("验证码错误或已过期");
        }

        // 查找用户
        User user = userService.findByPhone(phone);
        if (user == null) {
            return R.failure("用户不存在");
        }

        if (user.getStatus() == 0) {
            return R.failure("账户已被禁用");
        }

        userService.updateLastLoginTime(user.getUserId());
        return R.success("登录成功", "token-" + user.getUserId());
    }

    @PostMapping("/register")
    public R<Void> register(@RequestBody User user) {
        if (userService.findByPhone(user.getPhone()) != null) {
            return R.failure("手机号已注册");
        }
     // 设置默认值和当前时间
        user.setStatus(1);
        user.setPayLimit(new BigDecimal("50000.00"));
        user.setCreateTime(new Date());  // 设置当前时间为创建时间
        user.setUpdateTime(new Date());  // 设置当前时间为更新时间

        boolean result = userService.save(user);
        if (result) {
            return R.success("注册成功");
        } else {
            return R.failure("注册失败");
        }
    }

    // ========== 用户管理 ==========

    /**
     * 分页查询用户列表
     */
    @GetMapping("/users/page")
    public R page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) Integer status) {

        Page<User> page = new Page<>(pageNum, pageSize);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        // 添加查询条件
        if (phone != null && !phone.trim().isEmpty()) {
            queryWrapper.like("phone", phone);
        }
        if (realName != null && !realName.trim().isEmpty()) {
            queryWrapper.like("real_name", realName);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc("create_time");

        IPage<User> result = userService.page(page, queryWrapper);
        return R.success("查询成功", result);
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/users/{userId}")
    public R getById(@PathVariable Long userId) {
        User user = userService.getById(userId);
        if (user != null) {
            // 不返回密码信息
            user.setPassword(null);
            user.setPayPassword(null);
        }
        return R.success("查询成功", user);
    }

    /**
     * 添加用户
     */
    @PostMapping("/users")
    public R add(@RequestBody User user) {
        // 检查手机号是否已存在
        if (userService.findByPhone(user.getPhone()) != null) {
            return R.failure("手机号已存在");
        }

        // 设置默认值
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());

        boolean result = userService.save(user);
        if (result) {
            return R.success("添加成功");
        } else {
            return R.failure("添加失败");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/users")
    public R update(@RequestBody User user) {
        user.setUpdateTime(new Date());
        boolean result = userService.updateById(user);
        if (result) {
            return R.success("更新成功");
        } else {
            return R.failure("更新失败");
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/users/status")
    public R updateStatus(@RequestBody User user) {
        User updateUser = new User();
        updateUser.setUserId(user.getUserId());
        updateUser.setStatus(user.getStatus());
        updateUser.setUpdateTime(new Date());

        boolean result = userService.updateById(updateUser);
        if (result) {
            return R.success("状态更新成功");
        } else {
            return R.failure("状态更新失败");
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{userId}")
    public R delete(@PathVariable Long userId) {
        boolean result = userService.removeById(userId);
        if (result) {
            return R.success("删除成功");
        } else {
            return R.failure("删除失败");
        }
    }
}