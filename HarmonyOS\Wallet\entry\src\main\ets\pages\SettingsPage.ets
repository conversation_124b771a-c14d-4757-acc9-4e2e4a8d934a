import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';
import preferences from '@ohos.data.preferences';
import { UserStorage } from '../common/UserStorage';

// 定义API响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 定义通用响应类型（兼容R<T>格式）
interface R<T> {
  code: number;
  msg: string;
  data: T;
}

// 定义用户数据类型
interface UserData {
  userId: number;
  phone: string;
  realName?: string;
  username?: string;
  password?: string;
  payPassword?: string;
  payLimit?: number;
  status?: number;
  createTime?: string;
  updateTime?: string;
  lastLoginTime?: string;
}

// 定义用户更新请求类型
interface UserUpdateRequest {
  userId: number;
  payPassword?: string;
  payLimit?: number;
  updateTime: string;
}

interface UserInfo {
  name: string;
  phone: string;
  avatar: Resource;
}

@Entry
@Component
export struct SettingsPage {
  @State userInfo: UserInfo = {
    name: '加载中...',
    phone: '加载中...',
    avatar: $r('app.media.icon_avatar')
  };
  @State showPasswordDialog: boolean = false;
  @State showLimitDialog: boolean = false;
  @State showLogoutDialog: boolean = false;
  @State preferencesStore: preferences.Preferences | null = null;
  @State userId: number = 0;
  @State userName: string = '';
  @State userPhone: string = '';
  @State payLimit: string = '';

  aboutToAppear() {
    this.loadUserInfo();
  }

  // 从UserStorage加载用户信息
  async loadUserInfo() {
    try {
      console.log('开始从UserStorage加载用户信息...');

      // 检查用户是否已登录
      const isLoggedIn = await UserStorage.isLoggedIn();
      if (!isLoggedIn) {
        console.log('用户未登录，跳转到登录页面');
        router.replaceUrl({ url: 'pages/LoginPage' });
        return;
      }

      // 从UserStorage获取用户信息
      this.userId = await UserStorage.getCurrentUserId() || 0;
      this.userPhone = await UserStorage.getCurrentUserPhone() || '';
      this.userName = await UserStorage.getCurrentUserRealName() || '';

      console.log('从UserStorage加载的用户信息:', {
        userId: this.userId,
        userPhone: this.userPhone,
        userName: this.userName
      });

      // 如果有用户ID，通过API获取最新用户信息
      if (this.userId > 0) {
        await this.loadUserInfoFromAPI(this.userId);
      }

      // 更新显示的用户信息
      this.updateUserInfoDisplay();
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 设置默认显示信息
      this.userInfo = {
        name: '未知用户',
        phone: '未绑定手机',
        avatar: $r('app.media.icon_avatar')
      };
    }
  }

  // 通过用户ID从API加载用户信息
  async loadUserInfoFromAPI(userId: number) {
    try {
      console.log('通过API加载用户信息，userId:', userId);

      const response: AxiosResponse<ApiResponse<UserData>> = await axios({
        url: `http://localhost:8091/user/${userId}`,
        method: 'get'
      });

      if (response.data && response.data.code === 0) {
        const userData = response.data.data;
        console.log('从API获取的用户信息:', userData);

        // 更新本地数据
        this.userId = userData.userId;
        this.userName = userData.realName || userData.username || '未知用户';
        this.userPhone = userData.phone || '';
        if (userData.payLimit) {
          this.payLimit = userData.payLimit.toString();
        }

        // 如果真实姓名有更新，同步到UserStorage
        if (userData.realName && userData.realName !== await UserStorage.getCurrentUserRealName()) {
          await UserStorage.updateRealName(userData.realName);
        }
      }
    } catch (error) {
      console.error('通过API加载用户信息失败:', error);
    }
  }

  // 更新用户信息显示
  updateUserInfoDisplay() {
    // 处理手机号显示（脱敏）
    let displayPhone = this.userPhone;
    if (this.userPhone && this.userPhone.length === 11) {
      displayPhone = this.userPhone.substring(0, 3) + '****' + this.userPhone.substring(7);
    }

    this.userInfo = {
      name: this.userName || '未知用户',
      phone: displayPhone || '未绑定手机',
      avatar: $r('app.media.icon_avatar')
    };

    console.log('更新后的用户信息显示:', this.userInfo);
  }

  // 清除用户信息
  async clearUserInfo() {
    try {
      await UserStorage.clearUserInfo();
      console.log('用户信息已清除');
    } catch (error) {
      console.error('清除用户信息失败:', error);
    }
  }

  build() {
    // 根容器
    Column() {
      // 主内容区域
      Column() {
        // 用户信息卡片
        this.UserInfoCard()

        // 设置选项列表
        this.SettingsList()

        // 退出登录按钮
        this.LogoutButton()
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#f5f5f5')

      // 弹窗区域（覆盖在主内容之上）
      if (this.showPasswordDialog) {
        this.PasswordDialog()
      }

      if (this.showLimitDialog) {
        this.LimitDialog()
      }

      if (this.showLogoutDialog) {
        this.LogoutDialog()
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  UserInfoCard() {
    Row() {
      Image(this.userInfo.avatar)
        .width(60)
        .height(60)
        .borderRadius(30)
        .margin({right: 15})

      Column() {
        Text(this.userInfo.name)
          .fontSize(18)
          .fontColor('#333333')
        Text(this.userInfo.phone)
          .fontSize(14)
          .fontColor('#666666')
          .margin({top: 4})
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .margin({top: 20})
    .shadow({radius: 4, color: '#00000010', offsetX: 0, offsetY: 2})
  }

  @Builder
  SettingsList() {
    Column() {
      this.SettingItem('支付密码设置', $r('app.media.password'), () => {
        this.showPasswordDialog = true;
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('支付限额设置', $r('app.media.setting'), () => {
        this.showLimitDialog = true;
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('修改登录密码', $r('app.media.edit'), () => {
        router.pushUrl({url: 'pages/ChangePasswordPage'});
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('关于我们', $r('app.media.about'), () => {
        router.pushUrl({url: 'pages/AboutPage'});
      })
    }
    .width('90%')
    .margin({top: 20})
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .shadow({radius: 4, color: '#00000010', offsetX: 0, offsetY: 2})
  }

  @Builder
  SettingItem(title: string, icon: Resource, onClick: () => void) {
    Row() {
      Image(icon)
        .width(20)
        .height(20)
        .margin({right: 15})

      Text(title)
        .fontSize(16)
        .fontColor('#333333')
        .layoutWeight(1)

      Image($r('app.media.arrow'))
        .width(16)
        .height(16)
    }
    .width('100%')
    .height(60)
    .padding({left: 15, right: 15})
    .onClick(onClick)
  }

  @Builder
  LogoutButton() {
    Button() {
      Text('退出登录')
        .fontSize(16)
        .fontColor('#FF5252')
    }
    .width('90%')
    .height(50)
    .backgroundColor('#ffffff')
    .margin({top: 30})
    .onClick(() => {
      this.showLogoutDialog = true;
    })
  }

  @Builder
  PasswordDialog() {
    Column() {
      Text('设置支付密码')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Text('请输入6位数字支付密码')
        .fontSize(14)
        .fontColor('#666666')
        .margin({bottom: 10})

      TextInput({placeholder: '新支付密码'})
        .width('80%')
        .height(50)
        .type(InputType.Password)
        .maxLength(6)
        .margin({bottom: 10})

      TextInput({placeholder: '确认支付密码'})
        .width('80%')
        .height(50)
        .type(InputType.Password)
        .maxLength(6)
        .margin({bottom: 20})

      Row() {
        Button() {
          Text('取消')
            .fontSize(16)
            .fontColor('#333333')
        }
        .width('40%')
        .height(50)
        .backgroundColor('#f0f0f0')
        .onClick(() => {
          this.showPasswordDialog = false;
        })

        Button() {
          Text('确认')
            .fontSize(16)
            .fontColor(Color.White)
        }
        .width('40%')
        .height(50)
        .backgroundColor('#ee1f98e5')
        .margin({left: 10})
        .onClick(() => {
          this.showPasswordDialog = false;
          promptAction.showToast({message: '支付密码设置成功', duration: 1000});
        })
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '20%'})
    .zIndex(1)
  }

  @Builder
  LimitDialog() {
    Column() {
      Text('支付限额设置')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Text('单笔支付限额')
        .fontSize(16)
        .fontColor('#333333')
          // .alignSelf(HorizontalAlign.Start)
        .margin({left: '10%', bottom: 5})

      TextInput({placeholder: '请输入单笔限额'})
        .width('80%')
        .height(50)
        .type(InputType.Number)
        .margin({bottom: 10})

      Text('每日支付限额')
        .fontSize(16)
        .fontColor('#333333')
          // .alignSelf(HorizontalAlign.Start)
        .margin({left: '10%', bottom: 5})

      TextInput({placeholder: '请输入每日限额'})
        .width('80%')
        .height(50)
        .type(InputType.Number)
        .margin({bottom: 20})

      Row() {
        Button() {
          Text('取消')
            .fontSize(16)
            .fontColor('#333333')
        }
        .width('40%')
        .height(50)
        .backgroundColor('#f0f0f0')
        .onClick(() => {
          this.showLimitDialog = false;
        })

        Button() {
          Text('确认')
            .fontSize(16)
            .fontColor(Color.White)
        }
        .width('40%')
        .height(50)
        .backgroundColor('#ee1f98e5')
        .margin({left: 10})
        .onClick(() => {
          this.showLimitDialog = false;
          promptAction.showToast({message: '限额设置成功', duration: 1000});
        })
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '15%'})
    .zIndex(1)
  }

  @Builder
  LogoutDialog() {
    Column() {
      Text('确认退出登录？')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Button() {
        Text('确认退出')
          .fontSize(16)
          .fontColor(Color.White)
      }
      .width('80%')
      .height(50)
      .backgroundColor('#FF5252')
      .onClick(async () => {
        this.showLogoutDialog = false;
        // 清除本地存储的用户信息
        await this.clearUserInfo();
        router.replaceUrl({url: 'pages/LoginPage'});
      })

      Button() {
        Text('取消')
          .fontSize(16)
          .fontColor('#333333')
      }
      .width('80%')
      .height(50)
      .backgroundColor('#f0f0f0')
      .margin({top: 10})
      .onClick(() => {
        this.showLogoutDialog = false;
      })
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '30%'})
    .zIndex(1)
  }
}