<template>
  <div class="card-detail-container">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>银行卡详情</span>
          <div class="card-actions">
            <el-button
              v-if="cardData.status === 0"
              type="primary"
              @click="handleBind"
              :loading="bindLoading"
            >
              绑定银行卡
            </el-button>
            <el-button
              v-if="cardData.status === 1"
              type="warning"
              @click="handleUnbind"
              :loading="unbindLoading"
            >
              解绑银行卡
            </el-button>
            <el-button
              v-if="cardData.status === 1 && cardData.isDefault === 0"
              type="success"
              @click="handleSetDefault"
              :loading="setDefaultLoading"
            >
              设为默认卡
            </el-button>
            <el-button
              type="info"
              @click="goBack"
            >
              返回列表
            </el-button>
          </div>
        </div>
      </template>

      <div class="card-info" v-loading="pageLoading">
        <!-- 银行卡基本信息 -->
        <el-descriptions :column="2" border>
          <el-descriptions-item label="银行卡ID">{{ cardData.cardId }}</el-descriptions-item>
          <el-descriptions-item label="所属用户ID">{{ cardData.userId }}</el-descriptions-item>
          <el-descriptions-item label="银行卡号">
            <span class="card-number">{{ formatCardNumber(cardData.cardNumber) }}</span>
            <el-button
              type="text"
              size="small"
              @click="copyCardNumber"
              style="margin-left: 10px;"
            >
              复制
            </el-button>
          </el-descriptions-item>
          <el-descriptions-item label="银行名称">
            <el-tag type="primary">{{ cardData.bankName }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="卡类型">
            <el-tag :type="getCardTypeTagType(cardData.cardType)">
              {{ getCardTypeName(cardData.cardType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="持卡人姓名">{{ cardData.cardHolder }}</el-descriptions-item>
          <el-descriptions-item label="预留手机号">{{ formatPhoneNumber(cardData.phone) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(cardData.status)">
              {{ getStatusName(cardData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="默认卡">
            <el-tag :type="cardData.isDefault === 1 ? 'success' : 'info'">
              {{ cardData.isDefault === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 信用卡特有信息 -->
        <el-card v-if="cardData.cardType === 2" class="credit-card-info" shadow="never">
          <template #header>
            <div class="credit-card-header">信用卡信息</div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="有效期">{{ cardData.expiryDate }}</el-descriptions-item>
            <el-descriptions-item label="安全码">***</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 时间信息 -->
        <el-descriptions class="time-info" :column="2" border>
          <el-descriptions-item label="创建时间">{{ formatDateTime(cardData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(cardData.updateTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const cardId = ref(route.params.id)

// 调试信息
console.log('CardDetail页面加载')
console.log('路由参数:', route.params)
console.log('cardId:', cardId.value)

// 加载状态
const pageLoading = ref(false)
const bindLoading = ref(false)
const unbindLoading = ref(false)
const setDefaultLoading = ref(false)

const cardData = ref({
  cardId: '',
  userId: '',
  cardNumber: '',
  bankName: '',
  cardType: '',
  cardHolder: '',
  phone: '',
  expiryDate: '',
  cvv: '',
  isDefault: 0,
  status: 0,
  createTime: '',
  updateTime: ''
})

// 获取银行卡详情
const fetchCardDetail = async () => {
  // 验证cardId是否存在
  if (!cardId.value) {
    console.error('银行卡ID参数缺失，当前路由参数:', route.params)
    console.log('当前路由路径:', route.path)
    ElMessage.error('银行卡ID参数缺失')
    router.push('/home/<USER>')
    return
  }

  try {
    pageLoading.value = true
    console.log('正在获取银行卡详情，ID:', cardId.value)
    const response = await axios.get(`http://localhost:8091/bankCards/${cardId.value}`)
    if (response.data && response.data.code === 0) {
      cardData.value = response.data.data
      console.log('获取银行卡详情成功:', cardData.value)
    } else {
      ElMessage.error('获取银行卡详情失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取银行卡详情失败:', error)
    ElMessage.error('获取银行卡详情失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    pageLoading.value = false
  }
}

// 格式化银行卡号 (每4位加空格)
const formatCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  const digitsOnly = cardNumber.replace(/\s/g, '')
  return digitsOnly.replace(/(\d{4})(?=\d)/g, '$1 ')
}

// 格式化手机号 (3-4-4格式)
const formatPhoneNumber = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString()
}

// 获取卡类型名称
const getCardTypeName = (type) => {
  const types = {
    1: '借记卡',
    2: '信用卡'
  }
  return types[type] || '未知'
}

// 获取卡类型标签类型
const getCardTypeTagType = (type) => {
  const typeMap = {
    1: 'success',
    2: 'warning'
  }
  return typeMap[type] || ''
}

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    0: '未绑定',
    1: '已绑定'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success'
  }
  return typeMap[status] || ''
}

// 绑定银行卡
const handleBind = async () => {
  try {
    bindLoading.value = true
    const response = await axios.post(`http://localhost:8091/bankCards/bind/${cardData.value.cardId}`)
    if (response.data && response.data.code === 0) {
      ElMessage.success('绑定成功')
      fetchCardDetail()
    } else {
      ElMessage.error('绑定失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('绑定银行卡失败:', error)
    ElMessage.error('绑定银行卡失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    bindLoading.value = false
  }
}

// 解绑银行卡
const handleUnbind = async () => {
  try {
    await ElMessageBox.confirm('确定要解绑该银行卡吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    unbindLoading.value = true
    const response = await axios.post(`http://localhost:8091/bankCards/unbind/${cardData.value.cardId}`)
    if (response.data && response.data.code === 0) {
      ElMessage.success('解绑成功')
      fetchCardDetail()
    } else {
      ElMessage.error('解绑失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消解绑')
    } else {
      console.error('解绑银行卡失败:', error)
      ElMessage.error('解绑银行卡失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
    }
  } finally {
    unbindLoading.value = false
  }
}

// 设为默认卡
const handleSetDefault = async () => {
  try {
    await ElMessageBox.confirm('确定要将该银行卡设为默认卡吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    setDefaultLoading.value = true
    const response = await axios.put(`http://localhost:8091/bankCards/${cardData.value.cardId}/default`)
    if (response.data && response.data.code === 0) {
      ElMessage.success('设置默认卡成功')
      fetchCardDetail()
    } else {
      ElMessage.error('设置默认卡失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消设置')
    } else {
      console.error('设置默认卡失败:', error)
      ElMessage.error('设置默认卡失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
    }
  } finally {
    setDefaultLoading.value = false
  }
}

// 复制银行卡号
const copyCardNumber = async () => {
  try {
    await navigator.clipboard.writeText(cardData.value.cardNumber)
    ElMessage.success('银行卡号已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 返回列表
const goBack = () => {
  router.push('/home/<USER>')
}

// 初始化加载数据
onMounted(() => {
  fetchCardDetail()
})
</script>

<style scoped>
.card-detail-container {
  padding: 20px;
}

.detail-card {
  max-width: 900px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-number {
  font-family: monospace;
  font-size: 1.1em;
  letter-spacing: 1px;
}

.credit-card-info {
  margin-top: 20px;
}

.credit-card-header {
  font-weight: bold;
  color: #666;
}

.time-info {
  margin-top: 20px;
}

:deep(.el-descriptions__body) {
  background-color: #fafafa;
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: bold;
}

.card-actions .el-button {
  margin-left: 10px;
}

.card-actions .el-button:first-child {
  margin-left: 0;
}

:deep(.el-loading-mask) {
  border-radius: 4px;
}
</style>