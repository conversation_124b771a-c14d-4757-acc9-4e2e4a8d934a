{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"init": 418700, "PreBuild": 32943100, "PreCheckSyscap": 405400, "PreviewProcessResource": 4869400, "PreviewCompileResource": 591318100, "PreviewHookCompileResource": 852800, "CopyPreviewProfile": 15011300, "ReplacePreviewerPage": 719900, "buildPreviewerResource": 671600, "PreviewUpdateAssets": 5067800}, "APP": {"init": 281500}}, "TOTAL_TIME": 13071047400, "BUILD_ID": "202506261128234750", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750908516536"}}}