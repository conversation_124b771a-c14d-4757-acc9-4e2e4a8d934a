import preferences from '@ohos.data.preferences';
import common from '@ohos.app.ability.common';

/**
 * 用户信息接口
 */
interface UserInfo {
  userId: number;
  token: string;
  phone: string;
  realName?: string;
}

/**
 * 用户信息存储工具类
 * 管理用户登录状态和基本信息
 */
export class UserStorage {
  private static readonly STORE_NAME = 'user_preferences';
  private static readonly USER_ID_KEY = 'user_id';
  private static readonly TOKEN_KEY = 'user_token';
  private static readonly PHONE_KEY = 'user_phone';
  private static readonly REAL_NAME_KEY = 'user_real_name';
  private static readonly IS_LOGGED_IN_KEY = 'is_logged_in';

  private static store: preferences.Preferences | null = null;

  /**
   * 初始化存储
   */
  private static async initStore(): Promise<void> {
    if (!UserStorage.store) {
      try {
        console.log('开始初始化用户存储...');
        // 获取应用上下文
        const context = getContext() as common.UIAbilityContext;
        console.log('获取应用上下文成功');
        UserStorage.store = await preferences.getPreferences(context, UserStorage.STORE_NAME);
        console.log('用户存储初始化成功');
      } catch (error) {
        console.error('初始化用户存储失败:', error);
        console.error('错误详情:', JSON.stringify(error));
        throw new Error('初始化用户存储失败: ' + error.message);
      }
    }
  }

  /**
   * 保存用户登录信息
   * @param userId 用户ID
   * @param token 用户Token
   * @param phone 用户手机号
   * @param realName 用户真实姓名（可选）
   */
  static async saveUserInfo(userId: number, token: string, phone: string, realName?: string): Promise<void> {
    try {
      console.log('开始保存用户信息:', { userId, phone, realName });
      await UserStorage.initStore();

      if (UserStorage.store) {
        console.log('存储对象已准备就绪，开始保存数据...');

        await UserStorage.store.put(UserStorage.USER_ID_KEY, userId);
        console.log('用户ID保存成功');

        await UserStorage.store.put(UserStorage.TOKEN_KEY, token);
        console.log('Token保存成功');

        await UserStorage.store.put(UserStorage.PHONE_KEY, phone);
        console.log('手机号保存成功');

        await UserStorage.store.put(UserStorage.IS_LOGGED_IN_KEY, true);
        console.log('登录状态保存成功');

        if (realName) {
          await UserStorage.store.put(UserStorage.REAL_NAME_KEY, realName);
          console.log('真实姓名保存成功');
        }

        await UserStorage.store.flush();
        console.log('用户信息保存成功:', { userId, phone, realName });
      } else {
        throw new Error('存储对象初始化失败');
      }
    } catch (error) {
      console.error('保存用户信息失败:', error);
      console.error('错误详情:', JSON.stringify(error));
      throw new Error('保存用户信息失败: ' + error.message);
    }
  }

  /**
   * 获取当前用户ID
   * @returns 用户ID，如果未登录返回null
   */
  static async getCurrentUserId(): Promise<number | null> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        const userId = await UserStorage.store.get(UserStorage.USER_ID_KEY, 0);
        return userId === 0 ? null : userId as number;
      }
      return null;
    } catch (error) {
      console.error('获取用户ID失败:', error);
      return null;
    }
  }

  /**
   * 获取当前用户Token
   * @returns 用户Token，如果未登录返回null
   */
  static async getCurrentUserToken(): Promise<string | null> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        const token = await UserStorage.store.get(UserStorage.TOKEN_KEY, '');
        return token === '' ? null : token as string;
      }
      return null;
    } catch (error) {
      console.error('获取用户Token失败:', error);
      return null;
    }
  }

  /**
   * 获取当前用户手机号
   * @returns 用户手机号，如果未登录返回null
   */
  static async getCurrentUserPhone(): Promise<string | null> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        const phone = await UserStorage.store.get(UserStorage.PHONE_KEY, '');
        return phone === '' ? null : phone as string;
      }
      return null;
    } catch (error) {
      console.error('获取用户手机号失败:', error);
      return null;
    }
  }

  /**
   * 获取当前用户真实姓名
   * @returns 用户真实姓名，如果未设置返回null
   */
  static async getCurrentUserRealName(): Promise<string | null> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        const realName = await UserStorage.store.get(UserStorage.REAL_NAME_KEY, '');
        return realName === '' ? null : realName as string;
      }
      return null;
    } catch (error) {
      console.error('获取用户真实姓名失败:', error);
      return null;
    }
  }

  /**
   * 检查用户是否已登录
   * @returns 是否已登录
   */
  static async isLoggedIn(): Promise<boolean> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        const isLoggedIn = await UserStorage.store.get(UserStorage.IS_LOGGED_IN_KEY, false);
        const userId = await UserStorage.store.get(UserStorage.USER_ID_KEY, 0);
        const token = await UserStorage.store.get(UserStorage.TOKEN_KEY, '');

        // 只有当标记为已登录且有用户ID和Token时才认为是已登录状态
        return (isLoggedIn as boolean) && (userId as number) > 0 && (token as string) !== '';
      }
      return false;
    } catch (error) {
      console.error('检查登录状态失败:', error);
      return false;
    }
  }

  /**
   * 获取完整的用户信息
   * @returns 用户信息对象，如果未登录返回null
   */
  static async getCurrentUserInfo(): Promise<UserInfo | null> {
    try {
      const isLoggedIn = await UserStorage.isLoggedIn();
      if (!isLoggedIn) {
        return null;
      }

      const userId = await UserStorage.getCurrentUserId();
      const token = await UserStorage.getCurrentUserToken();
      const phone = await UserStorage.getCurrentUserPhone();
      const realName = await UserStorage.getCurrentUserRealName();

      if (userId && token && phone) {
        const userInfo: UserInfo = {
          userId,
          token,
          phone,
          realName: realName || undefined
        };
        return userInfo;
      }

      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 清除用户信息（退出登录）
   */
  static async clearUserInfo(): Promise<void> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        await UserStorage.store.delete(UserStorage.USER_ID_KEY);
        await UserStorage.store.delete(UserStorage.TOKEN_KEY);
        await UserStorage.store.delete(UserStorage.PHONE_KEY);
        await UserStorage.store.delete(UserStorage.REAL_NAME_KEY);
        await UserStorage.store.delete(UserStorage.IS_LOGGED_IN_KEY);
        await UserStorage.store.flush();
        console.log('用户信息清除成功');
      }
    } catch (error) {
      console.error('清除用户信息失败:', error);
      throw new Error('清除用户信息失败');
    }
  }

  /**
   * 更新用户真实姓名
   * @param realName 真实姓名
   */
  static async updateRealName(realName: string): Promise<void> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        await UserStorage.store.put(UserStorage.REAL_NAME_KEY, realName);
        await UserStorage.store.flush();
        console.log('用户真实姓名更新成功:', realName);
      }
    } catch (error) {
      console.error('更新用户真实姓名失败:', error);
      throw new Error('更新用户真实姓名失败');
    }
  }

  /**
   * 更新用户Token
   * @param token 新的Token
   */
  static async updateUserToken(token: string): Promise<void> {
    try {
      await UserStorage.initStore();
      if (UserStorage.store) {
        await UserStorage.store.put(UserStorage.TOKEN_KEY, token);
        await UserStorage.store.flush();
        console.log('用户Token更新成功');
      }
    } catch (error) {
      console.error('更新用户Token失败:', error);
      throw new Error('更新用户Token失败');
    }
  }
}
