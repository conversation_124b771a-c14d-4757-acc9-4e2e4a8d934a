package com.icss.wallet.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.BankAccount;
import com.icss.wallet.mapper.BankAccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class BankAccountService {
    @Autowired
    private BankAccountMapper bankAccountMapper;

    public void addBankAccount(BankAccount bankAccount) {
        bankAccount.setCreateTime(new Date());
        bankAccount.setUpdateTime(new Date());
        bankAccountMapper.insert(bankAccount);
    }

    public void deleteBankAccount(Long accountId) {
        bankAccountMapper.deleteById(accountId);
    }

    public void updateBankAccount(BankAccount bankAccount) {
        bankAccount.setUpdateTime(new Date());
        bankAccountMapper.updateById(bankAccount);
    }

    public BankAccount getById(Long accountId) {
        return bankAccountMapper.selectById(accountId);
    }

    public IPage<BankAccount> page(Page<BankAccount> page, String phone, Integer status) {
        QueryWrapper<BankAccount> queryWrapper = new QueryWrapper<>();
        if (phone != null && !phone.isEmpty()) {
            queryWrapper.eq("phone", phone);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("create_time");
        return bankAccountMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据用户ID查询银行账户
     */
    public List<BankAccount> getByUserId(Long userId) {
        QueryWrapper<BankAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.orderByDesc("create_time");
        return bankAccountMapper.selectList(queryWrapper);
    }

    /**
     * HarmonyOS前端专用：根据用户ID查询活跃银行账户
     */
    public List<BankAccount> getActiveByUserId(Long userId) {
        QueryWrapper<BankAccount> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("status", 1); // 只查询状态为1（活跃）的账户
        queryWrapper.orderByDesc("create_time");
        return bankAccountMapper.selectList(queryWrapper);
    }
}