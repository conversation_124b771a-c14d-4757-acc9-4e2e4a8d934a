import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';

/**
 * API响应结构
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 分页响应结构
 */
interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 银行账户信息
 */
interface BankAccount {
  accountId: number;
  userId: number;
  accountNumber: string;
  accountType: number; // 1-储蓄账户, 2-支票账户, 3-信用卡账户
  bankName: string;
  branchName?: string;
  accountHolder: string;
  phone: string;
  currency: string;
  balance: number;
  availableBalance: number;
  creditLimit: number;
  status: number; // 0-冻结, 1-正常, 2-注销
  isDefault: number; // 0-非默认, 1-默认
  openDate?: string;
  lastTransactionTime?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 转账请求
 */
interface TransferRequest {
  fromAccountNumber: string;
  toAccountNumber: string;
  amount: number;
}

@Entry
@Component
export struct BankAccountTransferPage {
  @State userId: number = 1; // 当前用户ID
  @State userAccounts: BankAccount[] = []; // 用户银行账户列表
  @State selectedFromAccount: BankAccount | null = null; // 选中的转出账户
  @State toAccountNumber: string = ''; // 转入账户号
  @State transferAmount: string = ''; // 转账金额
  @State transferRemark: string = ''; // 转账备注
  @State isLoading: boolean = false;
  @State isLoadingAccounts: boolean = false;

  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as Record<string, Object>;
    if (params && params['userId']) {
      this.userId = params['userId'] as number;
    }
    this.loadUserBankAccounts();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 转账表单
      this.buildTransferForm()

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f7fa')
  }

  @Builder
  buildHeader() {
    Row() {
      Image($r('app.media.back'))
        .width(24)
        .height(24)
        .onClick(() => {
          router.back();
        })

      Text('银行账户转账')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 占位符保持居中
      Row()
        .width(24)
        .height(24)
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor('#ffffff')
  }

  @Builder
  buildTransferForm() {
    Column() {
      // 转出账户选择
      Column() {
        Text('转出账户')
          .fontSize(14)
          .fontColor('#666')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        if (this.isLoadingAccounts) {
          Row() {
            LoadingProgress()
              .width(20)
              .height(20)
              .color('#4285f4')
              .margin({ right: 8 })
            Text('加载账户中...')
              .fontSize(14)
              .fontColor('#999')
          }
          .height(48)
          .width('100%')
          .justifyContent(FlexAlign.Start)
          .alignItems(VerticalAlign.Center)
          .backgroundColor('#f8f9fa')
          .borderRadius(8)
          .padding({ left: 12, right: 12 })
        } else {
          this.buildAccountSelector()
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 转入账户号
      Column() {
        Text('转入账户号')
          .fontSize(14)
          .fontColor('#666')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入转入账户号' })
          .onChange((value: string) => {
            this.toAccountNumber = value;
          })
          .height(48)
          .borderRadius(8)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 转账金额
      Column() {
        Row() {
          Text('转账金额')
            .fontSize(14)
            .fontColor('#666')

          if (this.selectedFromAccount) {
            Text(`余额: ¥${this.selectedFromAccount.balance.toFixed(2)}`)
              .fontSize(12)
              .fontColor('#999')
              .margin({ left: 8 })
          }
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({ bottom: 8 })

        Row() {
          Text('¥')
            .fontSize(16)
            .fontColor('#666')
            .margin({ right: 8 })

          TextInput({ placeholder: '请输入转账金额' })
            .onChange((value: string) => {
              this.transferAmount = value;
            })
            .type(InputType.Number)
            .layoutWeight(1)
            .borderWidth(0)
            .backgroundColor('transparent')

          if (this.selectedFromAccount) {
            Button('全部')
              .type(ButtonType.Normal)
              .backgroundColor('transparent')
              .fontColor('#4285f4')
              .fontSize(12)
              .height(32)
              .onClick(() => {
                this.transferAmount = this.selectedFromAccount!.balance.toString();
              })
          }
        }
        .width('100%')
        .height(48)
        .backgroundColor('#f8f9fa')
        .borderRadius(8)
        .padding({ left: 12, right: 12 })
        .alignItems(VerticalAlign.Center)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 转账备注
      Column() {
        Text('转账备注')
          .fontSize(14)
          .fontColor('#666')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入转账备注（可选）' })
          .onChange((value: string) => {
            this.transferRemark = value;
          })
          .height(48)
          .borderRadius(8)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 30 })

      // 转账按钮
      Button(this.isLoading ? '转账中...' : '确认转账')
        .type(ButtonType.Capsule)
        .backgroundColor('#4285f4')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .onClick(() => {
          this.handleTransfer();
        })
        .enabled(!this.isLoading && this.canTransfer())
    }
    .backgroundColor(Color.White)
    .borderRadius(12)
    .padding(24)
    .margin(16)
  }

  @Builder
  buildAccountSelector() {
    Column() {
      if (this.userAccounts.length === 0) {
        Text('暂无可用银行账户')
          .fontSize(14)
          .fontColor('#999')
          .height(48)
          .textAlign(TextAlign.Center)
      } else {
        ForEach(this.userAccounts, (account: BankAccount) => {
          this.buildAccountItem(account)
        }, (account: BankAccount) => account.accountId.toString())
      }
    }
    .width('100%')
  }

  @Builder
  buildAccountItem(account: BankAccount) {
    Row() {
      // 选择圆圈
      Stack() {
        Circle()
          .width(20)
          .height(20)
          .fill(this.selectedFromAccount?.accountId === account.accountId ? '#4285f4' : 'transparent')
          .border({
            width: 2,
            color: this.selectedFromAccount?.accountId === account.accountId ? '#4285f4' : '#ddd'
          })

        if (this.selectedFromAccount?.accountId === account.accountId) {
          Circle()
            .width(8)
            .height(8)
            .fill('#ffffff')
        }
      }
      .margin({ right: 12 })

      // 账户信息
      Column() {
        Text(`${account.bankName} (${this.getAccountTypeText(account.accountType)})`)
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
          .fontColor('#1a1a1a')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(this.maskAccountNumber(account.accountNumber))
          .fontSize(12)
          .fontColor('#666')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 2 })

        Text(`余额: ¥${account.balance.toFixed(2)}`)
          .fontSize(12)
          .fontColor('#34a853')
          .alignSelf(ItemAlign.Start)
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding(12)
    .backgroundColor(this.selectedFromAccount?.accountId === account.accountId ? '#f0f7ff' : '#f8f9fa')
    .borderRadius(8)
    .margin({ bottom: 8 })
    .onClick(() => {
      this.selectedFromAccount = account;
    })
  }

  // 加载用户银行账户
  loadUserBankAccounts() {
    this.isLoadingAccounts = true;

    axios({
      url: `http://localhost:8091/bankAccounts/user/${this.userId}/active`,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<BankAccount[]>>) => {
      console.log('加载用户银行账户结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        // 获取当前用户的正常状态银行账户
        this.userAccounts = res.data.data || [];

        // 如果有默认账户，自动选中
        const defaultAccount = this.userAccounts.find(account => account.isDefault === 1);
        if (defaultAccount) {
          this.selectedFromAccount = defaultAccount;
        }

        if (this.userAccounts.length === 0) {
          promptAction.showToast({
            message: '您还没有可用的银行账户',
            duration: 2000
          });
        }
      } else {
        promptAction.showToast({
          message: res.data.msg || '加载账户失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('加载银行账户错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isLoadingAccounts = false;
    });
  }

  // 处理转账
  handleTransfer() {
    // 验证输入
    if (!this.selectedFromAccount) {
      promptAction.showToast({
        message: '请选择转出账户',
        duration: 2000
      });
      return;
    }

    if (!this.toAccountNumber || this.toAccountNumber.trim().length === 0) {
      promptAction.showToast({
        message: '请输入转入账户号',
        duration: 2000
      });
      return;
    }

    if (!this.transferAmount || parseFloat(this.transferAmount) <= 0) {
      promptAction.showToast({
        message: '请输入正确的转账金额',
        duration: 2000
      });
      return;
    }

    const amount = parseFloat(this.transferAmount);
    if (amount > this.selectedFromAccount.balance) {
      promptAction.showToast({
        message: '转账金额超过账户余额',
        duration: 2000
      });
      return;
    }

    if (this.selectedFromAccount.accountNumber === this.toAccountNumber.trim()) {
      promptAction.showToast({
        message: '不能向同一账户转账',
        duration: 2000
      });
      return;
    }

    this.isLoading = true;

    // 发送转账请求
    axios({
      url: 'http://localhost:8091/transfer',
      method: 'post',
      params: {
        fromAccountNumber: this.selectedFromAccount.accountNumber,
        toAccountNumber: this.toAccountNumber.trim(),
        amount: this.transferAmount
      }
    }).then((res: AxiosResponse<ApiResponse<null>>) => {
      console.log('转账结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        // 转账成功，清空表单并返回
        this.clearForm();
        setTimeout(() => {
          router.back();
        }, 1500);
      }
    }).catch((err: AxiosError) => {
      console.error('转账错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isLoading = false;
    });
  }

  // 检查是否可以转账
  canTransfer(): boolean {
    return !!(this.selectedFromAccount &&
    this.toAccountNumber.trim() &&
    this.transferAmount &&
      parseFloat(this.transferAmount) > 0);
  }

  // 清空表单
  clearForm() {
    this.toAccountNumber = '';
    this.transferAmount = '';
    this.transferRemark = '';
    this.selectedFromAccount = null;
  }

  // 获取账户类型文本
  getAccountTypeText(accountType: number): string {
    switch(accountType) {
      case 1: return '储蓄账户';
      case 2: return '支票账户';
      case 3: return '信用卡账户';
      default: return '未知类型';
    }
  }

  // 掩码显示账户号
  maskAccountNumber(accountNumber: string): string {
    if (accountNumber.length <= 8) {
      return accountNumber;
    }
    const start = accountNumber.substring(0, 4);
    const end = accountNumber.substring(accountNumber.length - 4);
    const middle = '*'.repeat(accountNumber.length - 8);
    return `${start}${middle}${end}`;
  }
}
