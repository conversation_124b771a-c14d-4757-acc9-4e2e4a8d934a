<template>
  <div class="payment-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="header-text">
            <h1 class="page-title">支付管理</h1>
            <p class="page-description">管理系统支付记录，包括查看、处理、添加支付记录等操作</p>
          </div>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading" class="refresh-btn">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalPayments }}</div>
                <div class="stat-label">总支付数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.successPayments }}</div>
                <div class="stat-label">成功支付</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.failedPayments }}</div>
                <div class="stat-label">失败支付</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <el-icon><Wallet /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.pendingPayments }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20" class="search-row">
        <el-col :span="6">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.paymentMethod"
            placeholder="请选择支付方式"
            clearable
            style="width: 100%"
          >
            <el-option label="钱包支付" value="wallet" />
            <el-option label="银行卡支付" value="bankcard" />
            <el-option label="第三方支付" value="thirdparty" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 100%"
          >
            <el-option label="成功" :value="1" />
            <el-option label="失败" :value="0" />
            <el-option label="处理中" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加记录
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 支付记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="paymentId" label="支付ID" width="100" />
        <el-table-column prop="phone" label="用户手机" width="120" />
        <el-table-column prop="amount" label="支付金额" width="120" align="right">
          <template #default="{ row }">
            ¥{{ Number(row.amount).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentMethod" label="支付方式" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.paymentMethod === 'wallet'" type="primary">钱包支付</el-tag>
            <el-tag v-else-if="row.paymentMethod === 'bankcard'" type="success">银行卡</el-tag>
            <el-tag v-else type="info">第三方</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.status === 1" type="success">成功</el-tag>
            <el-tag v-else-if="row.status === 0" type="danger">失败</el-tag>
            <el-tag v-else type="warning">处理中</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewDetails(row)"
              >
                <el-icon><View /></el-icon>
              </el-button>
              <el-button
                v-if="row.status === 2"
                type="success"
                size="small"
                @click="processPayment(row, 1)"
              >
                <el-icon><Check /></el-icon>
              </el-button>
              <el-button
                v-if="row.status === 2"
                type="danger"
                size="small"
                @click="processPayment(row, 0)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 支付详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="支付详情"
      width="600px"
    >
      <div v-if="selectedPayment" class="payment-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="支付ID">{{ selectedPayment.paymentId }}</el-descriptions-item>
          <el-descriptions-item label="用户手机">{{ selectedPayment.phone }}</el-descriptions-item>
          <el-descriptions-item label="支付金额">¥{{ Number(selectedPayment.amount).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ selectedPayment.paymentMethod }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="selectedPayment.status === 1" type="success">成功</el-tag>
            <el-tag v-else-if="selectedPayment.status === 0" type="danger">失败</el-tag>
            <el-tag v-else type="warning">处理中</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedPayment.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ selectedPayment.description }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 添加支付记录对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加支付记录"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        label-width="100px"
      >
        <el-form-item label="用户ID" required>
          <el-input-number
            v-model="addForm.userId"
            :min="1"
            style="width: 100%"
            placeholder="请输入用户ID"
          />
        </el-form-item>
        <el-form-item label="支付金额" required>
          <el-input-number
            v-model="addForm.amount"
            :min="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入支付金额"
          />
        </el-form-item>
        <el-form-item label="支付方式" required>
          <el-select v-model="addForm.paymentMethod" style="width: 100%" placeholder="请选择支付方式">
            <el-option label="钱包支付" value="wallet" />
            <el-option label="银行卡支付" value="bankcard" />
            <el-option label="第三方支付" value="thirdparty" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="addForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入支付描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Money, Check, Close, Wallet, Plus, Refresh, Search, View
} from '@element-plus/icons-vue'

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:8091'

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const showAddDialog = ref(false)

// 搜索表单
const searchForm = reactive({
  phone: '',
  paymentMethod: '',
  status: null
})

// 添加表单
const addForm = reactive({
  userId: null,
  amount: null,
  paymentMethod: '',
  description: ''
})

// 表格数据
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedPayment = ref(null)
const statistics = ref({
  totalPayments: 0,
  successPayments: 0,
  failedPayments: 0,
  pendingPayments: 0
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await axios.get('/payment/admin/statistics')
    if (response.data.code === 0) {
      statistics.value = response.data.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

const fetchPayments = async () => {
  try {
    loading.value = true
    const response = await axios.get('/payment/admin/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        phone: searchForm.phone,
        paymentMethod: searchForm.paymentMethod,
        status: searchForm.status
      }
    })

    if (response.data.code === 0) {
      tableData.value = response.data.data.records
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.msg || '获取支付记录失败')
    }
  } catch (error) {
    console.error('获取支付记录失败:', error)
    ElMessage.error('获取支付记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchPayments()
}

// 重置
const handleReset = () => {
  searchForm.phone = ''
  searchForm.paymentMethod = ''
  searchForm.status = null
  currentPage.value = 1
  fetchPayments()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchPayments()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchPayments()
}

// 查看详情
const viewDetails = (row) => {
  selectedPayment.value = row
  showDetailDialog.value = true
}

// 处理支付状态
const processPayment = async (row, status) => {
  const action = status === 1 ? '确认成功' : '标记失败'
  try {
    await ElMessageBox.confirm(`确定要${action}该支付记录吗？`, '确认操作')

    await axios.post('/payment/admin/process', {
      paymentId: row.paymentId,
      status: status
    })

    ElMessage.success(`${action}成功`)
    fetchPayments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('处理支付状态失败:', error)
      ElMessage.error('处理支付状态失败')
    }
  }
}

// 确认添加
const confirmAdd = async () => {
  if (!addForm.userId || !addForm.amount || !addForm.paymentMethod) {
    ElMessage.warning('请填写完整信息')
    return
  }

  try {
    await axios.post('/payment/admin/add', addForm)
    ElMessage.success('添加支付记录成功')
    showAddDialog.value = false
    refreshData()

    // 重置表单
    Object.assign(addForm, {
      userId: null,
      amount: null,
      paymentMethod: '',
      description: ''
    })
  } catch (error) {
    console.error('添加支付记录失败:', error)
    ElMessage.error('添加支付记录失败')
  }
}

// 刷新数据
const refreshData = () => {
  loadStatistics()
  fetchPayments()
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
  fetchPayments()
})
</script>

<style scoped>
.payment-manage {
 padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
  position: relative;
  overflow: hidden;
}

.header-content {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex !important;
  align-items: center !important;
  gap: 20px !important;
}

.page-icon {
  width: 60px !important;
  height: 60px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-size: 28px !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-text {
  color: white !important;
}

.page-title {
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 8px 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
  color: white !important;
}

.page-description {
  font-size: 16px !important;
  margin: 0 !important;
  opacity: 0.9;
  font-weight: 400;
  line-height: 1.5;
  color: white !important;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .header-left {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 20px;
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-left {
    gap: 15px;
  }

  .page-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .refresh-btn {
    font-size: 14px !important;
    padding: 10px 20px !important;
  }
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.search-card, .table-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-row {
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.payment-details {
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-manage {
    padding: 10px;
  }

  .statistics-cards .el-col {
    margin-bottom: 10px;
  }

  .search-row .el-col {
    margin-bottom: 10px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}


</style>
