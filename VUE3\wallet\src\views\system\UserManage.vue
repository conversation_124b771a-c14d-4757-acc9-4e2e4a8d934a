<template>
  <div class="user-manage-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><User /></el-icon>
            用户管理
          </h2>
          <p>管理系统用户信息，包括查看、添加、编辑、删除用户等操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计面板 -->
    <el-row :gutter="20" class="statistics-panel">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.totalUsers }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <el-icon class="stat-icon total"><User /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.activeUsers }}</div>
            <div class="stat-label">正常用户</div>
          </div>
          <el-icon class="stat-icon active"><UserFilled /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.inactiveUsers }}</div>
            <div class="stat-label">禁用用户</div>
          </div>
          <el-icon class="stat-icon inactive"><Remove /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ statistics.newUsers }}</div>
            <div class="stat-label">新用户</div>
          </div>
          <el-icon class="stat-icon new"><Plus /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <div class="header-actions">
            <el-button
              v-if="selectedUsers.length > 0"
              type="warning"
              @click="handleBatchOperation"
            >
              批量操作 ({{ selectedUsers.length }})
            </el-button>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              添加用户
            </el-button>
          </div>
        </div>
      </template>

      <!-- 快速筛选按钮 -->
      <div class="quick-filters">
        <el-button-group>
          <el-button
            :type="searchForm.status === null ? 'primary' : ''"
            @click="quickFilter(null)"
          >
            全部用户
          </el-button>
          <el-button
            :type="searchForm.status === 1 ? 'success' : ''"
            @click="quickFilter(1)"
          >
            <el-icon><Check /></el-icon>
            正常用户
          </el-button>
          <el-button
            :type="searchForm.status === 0 ? 'danger' : ''"
            @click="quickFilter(0)"
          >
            <el-icon><Close /></el-icon>
            禁用用户
          </el-button>
        </el-button-group>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="手机号">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入真实姓名"
            clearable
            style="width: 150px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="userId" label="用户ID" width="80" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="realName" label="真实姓名" width="100" />
        <el-table-column prop="idCard" label="身份证号" width="200">
          <template #default="{ row }">
            <div class="sensitive-data">
              <span>{{ formatIdCard(row.idCard, showFullData[row.userId]?.idCard) }}</span>
              <el-button
                type="text"
                size="small"
                @click="toggleDataVisibility(row.userId, 'idCard')"
                class="toggle-btn"
              >
                <el-icon>
                  <View v-if="!showFullData[row.userId]?.idCard" />
                  <Hide v-else />
                </el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="payLimit" label="支付限额" width="120" align="right">
          <template #default="{ row }">
            ¥{{ Number(row.payLimit).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <div class="status-display">
              <el-icon
                :class="['status-icon', row.status === 1 ? 'status-active' : 'status-inactive']"
              >
                <Check v-if="row.status === 1" />
                <Close v-else />
              </el-icon>
              <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录时间" width="160">
          <template #default="{ row }">
            <span v-if="row.lastLoginTime" class="login-time">
              {{ formatDateTime(row.lastLoginTime) }}
            </span>
            <span v-else class="no-login">
              <el-tag type="info" size="small">未登录</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              :type="row.status === 1 ? 'warning' : 'success'" 
              size="small" 
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号"
                :disabled="!!form.userId"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="form.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="支付密码" prop="payPassword">
              <el-input
                v-model="form.payPassword"
                type="password"
                placeholder="请输入支付密码"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="realName">
              <el-input
                v-model="form.realName"
                placeholder="请输入真实姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input
                v-model="form.idCard"
                placeholder="请输入身份证号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付限额" prop="payLimit">
              <el-input-number
                v-model="form.payLimit"
                :min="0"
                :max="1000000"
                :step="100"
                :precision="2"
                placeholder="请输入支付限额"
                style="width: 100%"
                controls-position="right"
              />
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                💡 支付限额可以根据需要自定义设置，建议范围：1000-100000
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, Check, Close, View, Hide,
  User, UserFilled, Remove
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 批量操作相关
const selectedUsers = ref([])

// 数据脱敏显示控制
const showFullData = ref({})

// 搜索表单
const searchForm = reactive({
  phone: '',
  realName: '',
  status: null
})

// 表单数据
const form = reactive({
  userId: null,
  phone: '',
  password: '',
  payPassword: '',
  realName: '',
  idCard: '',
  payLimit: 0,
  status: 1
})

// 表单验证规则
const formRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' },
    { min: 6, max: 20, message: '支付密码长度为6-20位', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  payLimit: [
    { required: true, message: '请输入支付限额', trigger: 'blur' },
    { type: 'number', min: 0, message: '支付限额不能小于0', trigger: 'blur' }
  ]
}

// 统计数据计算
const statistics = computed(() => {
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

  return {
    totalUsers: total.value,
    activeUsers: tableData.value.filter(user => user.status === 1).length,
    inactiveUsers: tableData.value.filter(user => user.status === 0).length,
    newUsers: tableData.value.filter(user => {
      if (!user.createTime) return false
      const createTime = new Date(user.createTime)
      return createTime >= thirtyDaysAgo
    }).length
  }
})

// 身份证号脱敏格式化
const formatIdCard = (idCard, showFull = false) => {
  if (!idCard) return ''
  if (showFull) return idCard
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1****$2')
}

// 切换数据显示状态
const toggleDataVisibility = (userId, dataType) => {
  if (!showFullData.value[userId]) {
    showFullData.value[userId] = {}
  }
  showFullData.value[userId][dataType] = !showFullData.value[userId][dataType]
}

// 批量选择处理
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 快速筛选
const quickFilter = (status) => {
  searchForm.status = status
  currentPage.value = 1
  fetchUsers()
}

// 批量操作
const handleBatchOperation = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户')
    return
  }

  ElMessageBox.confirm(
    `确定要批量操作选中的 ${selectedUsers.value.length} 个用户吗？`,
    '批量操作确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 显示批量操作选项
    showBatchOperationDialog()
  }).catch(() => {
    ElMessage.info('已取消批量操作')
  })
}

// 显示批量操作对话框
const showBatchOperationDialog = () => {
  ElMessageBox.confirm(
    '请选择批量操作类型：',
    '批量操作',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: '批量启用',
      cancelButtonText: '批量禁用',
      type: 'info'
    }
  ).then(() => {
    // 批量启用
    batchUpdateStatus(1)
  }).catch((action) => {
    if (action === 'cancel') {
      // 批量禁用
      batchUpdateStatus(0)
    }
  })
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  try {
    loading.value = true
    const promises = selectedUsers.value.map(user =>
      axios.put('http://localhost:8091/user/users/status', {
        userId: user.userId,
        status: status
      })
    )

    await Promise.all(promises)
    ElMessage.success(`批量${status === 1 ? '启用' : '禁用'}成功`)
    selectedUsers.value = []
    fetchUsers()
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  } finally {
    loading.value = false
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true

    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.phone) {
      params.phone = searchForm.phone
    }
    if (searchForm.realName) {
      params.realName = searchForm.realName
    }
    if (searchForm.status !== null) {
      params.status = searchForm.status
    }

    const response = await axios.get('http://localhost:8091/user/users/page', { params })

    if (response.data && response.data.code === 0) {
      tableData.value = response.data.data?.records || []
      total.value = response.data.data?.total || 0
    } else {
      ElMessage.error('获取用户列表失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}



// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchUsers()
}

// 重置
const handleReset = () => {
  searchForm.phone = ''
  searchForm.realName = ''
  searchForm.status = null
  currentPage.value = 1
  fetchUsers()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchUsers()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchUsers()
}

// 添加用户
const handleAdd = () => {
  dialogTitle.value = '添加用户'
  Object.assign(form, {
    userId: null,
    phone: '',
    password: '',
    payPassword: '',
    realName: '',
    idCard: '',
    payLimit: null, // 设置为null，让用户自己输入
    status: 1
  })
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  dialogTitle.value = '编辑用户'
  Object.assign(form, {
    userId: row.userId,
    phone: row.phone,
    password: '', // 编辑时不显示原密码
    payPassword: '',
    realName: row.realName,
    idCard: row.idCard,
    payLimit: row.payLimit,
    status: row.status
  })
  dialogVisible.value = true
}

// 切换用户状态
const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(
      `确认${action}用户 "${row.realName}" 吗？`,
      `${action}用户`,
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const newStatus = row.status === 1 ? 0 : 1
    const response = await axios.put('http://localhost:8091/user/users/status', {
      userId: row.userId,
      status: newStatus
    })

    if (response.data && response.data.code === 0) {
      ElMessage.success(`${action}成功`)
      fetchUsers()
    } else {
      ElMessage.error(`${action}失败: ` + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}用户失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认删除用户 "${row.realName}" 吗？此操作不可恢复！`,
      '删除用户',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const response = await axios.delete(`http://localhost:8091/user/users/${row.userId}`)

    if (response.data && response.data.code === 0) {
      ElMessage.success('删除成功')
      fetchUsers()
    } else {
      ElMessage.error('删除失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    const userData = { ...form }
    if (userData.userId) {
      // 编辑用户
      const response = await axios.put('http://localhost:8091/user/users', userData)
      if (response.data && response.data.code === 0) {
        ElMessage.success('更新成功')
        handleDialogClose()
        fetchUsers()
      } else {
        ElMessage.error('更新失败: ' + (response.data?.msg || '未知错误'))
      }
    } else {
      // 添加用户
      const response = await axios.post('http://localhost:8091/user/users', userData)
      if (response.data && response.data.code === 0) {
        ElMessage.success('添加成功')
        handleDialogClose()
        fetchUsers()
      } else {
        ElMessage.error('添加失败: ' + (response.data?.msg || '未知错误'))
      }
    }
  } catch (error) {
    if (error.message && error.message.includes('validate')) {
      return
    }
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 刷新数据
const refreshData = () => {
  fetchUsers()
}

// 初始化
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-manage-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.login-time {
  color: #606266;
}

.no-login {
  color: #909399;
  font-style: italic;
}

/* 统计面板样式 */
.statistics-panel {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: white;
  color: #2c3e50;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  position: relative;
  z-index: 2;
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 40px;
  opacity: 0.2;
  z-index: 1;
}

.stat-icon.total {
  color: #3498db;
}

.stat-icon.active {
  color: #27ae60;
}

.stat-icon.inactive {
  color: #e74c3c;
}

.stat-icon.new {
  color: #f39c12;
}

.stat-icon.total {
  color: #409eff;
}

.stat-icon.active {
  color: #67c23a;
}

.stat-icon.inactive {
  color: #f56c6c;
}

.stat-icon.new {
  color: #e6a23c;
}

/* 快速筛选样式 */
.quick-filters {
  margin-bottom: 15px;
}

/* 状态显示样式 */
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.status-active {
  color: #67c23a;
}

.status-icon.status-inactive {
  color: #f56c6c;
}

/* 敏感数据显示样式 */
.sensitive-data {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-btn {
  padding: 0;
  font-size: 14px;
  color: #409eff;
}

.toggle-btn:hover {
  color: #66b1ff;
}

/* 头部操作区域 */
.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}
</style>
