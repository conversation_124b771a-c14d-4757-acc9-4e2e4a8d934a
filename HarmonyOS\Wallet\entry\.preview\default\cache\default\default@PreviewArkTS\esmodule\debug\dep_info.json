{"resolveConflictMode": true, "depName2RootPath": {"@ohos/axios": "D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "@ohos/hypium": "D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+hypium@1.0.21\\oh_modules\\@ohos\\hypium", "@ohos/hamock": "D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock"}, "depName2DepInfo": {"@ohos/axios": {"pkgRootPath": "D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "pkgName": "@ohos/axios", "pkgVersion": "2.2.6"}, "@ohos/hypium": {"pkgRootPath": "D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+hypium@1.0.21\\oh_modules\\@ohos\\hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.21"}, "@ohos/hamock": {"pkgRootPath": "D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock", "pkgName": "@ohos/hamock", "pkgVersion": "1.0.0"}}}