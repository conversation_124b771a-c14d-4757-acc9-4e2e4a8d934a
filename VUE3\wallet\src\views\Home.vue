<script setup>
import Header from "../components/Header.vue";
import Aside from "../components/Aside.vue";
import { RouterView } from 'vue-router'
</script>

<template>
  <div class="common-layout">
    <el-container style="min-height: 100vh;">
      <el-header style="padding: 0; height: 60px; position: fixed; width: 100%; z-index: 1000;">
        <Header />
      </el-header>
      <el-container style="margin-top: 60px;">
        <el-aside width="230px" style="position: fixed; height: calc(100vh - 60px);">
          <Aside />
        </el-aside>
        <el-main style="margin-left: 230px; padding: 20px; background-color: #f0f2f5; min-height: calc(100vh - 120px);">
          <RouterView />
        </el-main>
        <el-footer style="margin-left: 230px; height: 60px; display: flex; align-items: center; justify-content: center; background-color:#eae8e8f5">
© 2025 电子钱包管理系统 - 版权所有
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped>
:deep(.el-main) {
  padding: 20px;
}
:deep(.el-footer) {
  border-top: 1px solid #f1f1f1ec;
}
</style>
