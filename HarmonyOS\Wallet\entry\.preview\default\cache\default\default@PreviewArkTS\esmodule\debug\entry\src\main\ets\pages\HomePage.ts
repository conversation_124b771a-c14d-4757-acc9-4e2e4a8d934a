if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface HomePage_Params {
    userId?: number;
    user?: User | null;
    wallet?: Wallet | null;
    isLoading?: boolean;
    showBalance?: boolean;
    bannerItems?: BannerItem[];
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse, AxiosError } from "@normalized:N&&&@ohos/axios/index&2.2.6";
/**
 * API响应结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
/**
 * 用户信息
 */
interface User {
    userId: number;
    phone: string;
    realName: string;
    status: number;
    createTime: string;
}
/**
 * 钱包信息
 */
interface Wallet {
    walletId: number;
    userId: number;
    balance: number;
    status: number;
    createTime: string;
    updateTime: string;
}
/**
 * 轮播图项目
 */
interface BannerItem {
    id: number;
    image: Resource;
    title: string;
    subtitle?: string;
}
/**
 * 服务项目
 */
interface ServiceItem {
    id: number;
    title: string;
    subtitle: string;
    icon: Resource;
    route?: string;
    color: string;
    bgColor: string;
}
export class HomePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userId = new ObservedPropertySimplePU(1, this, "userId");
        this.__user = new ObservedPropertyObjectPU(null, this, "user");
        this.__wallet = new ObservedPropertyObjectPU(null, this, "wallet");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showBalance = new ObservedPropertySimplePU(true, this, "showBalance");
        this.__bannerItems = new ObservedPropertyObjectPU([
            {
                id: 1,
                image: { "id": 16777242, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" },
                title: '新用户专享红包',
                subtitle: '注册即送100元红包'
            },
            {
                id: 2,
                image: { "id": 16777241, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" },
                title: '限时特惠活动',
                subtitle: '转账免手续费'
            },
            {
                id: 3,
                image: { "id": 16777243, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" },
                title: '信用卡还款优惠',
                subtitle: '还款享受积分奖励'
            }
        ], this, "bannerItems");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: HomePage_Params) {
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
        if (params.user !== undefined) {
            this.user = params.user;
        }
        if (params.wallet !== undefined) {
            this.wallet = params.wallet;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showBalance !== undefined) {
            this.showBalance = params.showBalance;
        }
        if (params.bannerItems !== undefined) {
            this.bannerItems = params.bannerItems;
        }
    }
    updateStateVars(params: HomePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
        this.__user.purgeDependencyOnElmtId(rmElmtId);
        this.__wallet.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__bannerItems.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userId.aboutToBeDeleted();
        this.__user.aboutToBeDeleted();
        this.__wallet.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showBalance.aboutToBeDeleted();
        this.__bannerItems.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userId: ObservedPropertySimplePU<number>; // 当前用户ID
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    private __user: ObservedPropertyObjectPU<User | null>; // 用户信息
    get user() {
        return this.__user.get();
    }
    set user(newValue: User | null) {
        this.__user.set(newValue);
    }
    private __wallet: ObservedPropertyObjectPU<Wallet | null>; // 钱包信息
    get wallet() {
        return this.__wallet.get();
    }
    set wallet(newValue: Wallet | null) {
        this.__wallet.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showBalance: ObservedPropertySimplePU<boolean>; // 是否显示余额
    get showBalance() {
        return this.__showBalance.get();
    }
    set showBalance(newValue: boolean) {
        this.__showBalance.set(newValue);
    }
    // 轮播图数据
    private __bannerItems: ObservedPropertyObjectPU<BannerItem[]>;
    get bannerItems() {
        return this.__bannerItems.get();
    }
    set bannerItems(newValue: BannerItem[]) {
        this.__bannerItems.set(newValue);
    }
    aboutToAppear() {
        this.loadUserData();
        this.loadWalletData();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(96:5)", "entry");
            Column.width('100%');
            Column.height('100%');
        }, Column);
        // 顶部状态栏
        this.buildStatusBar.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主要内容区域
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/HomePage.ets(101:7)", "entry");
            // 主要内容区域
            Scroll.layoutWeight(1);
            // 主要内容区域
            Scroll.scrollable(ScrollDirection.Vertical);
            // 主要内容区域
            Scroll.scrollBar(BarState.Off);
            // 主要内容区域
            Scroll.backgroundColor('#f8f9fa');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(102:9)", "entry");
            Column.width('100%');
            Column.padding({ bottom: 20 });
        }, Column);
        // 用户信息卡片
        this.buildUserCard.bind(this)();
        // 轮播图
        this.buildBanner.bind(this)();
        // 快捷操作
        this.buildQuickActions.bind(this)();
        // 全部服务
        this.buildServices.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部间距
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(116:11)", "entry");
            // 底部间距
            Row.height(30);
        }, Row);
        // 底部间距
        Row.pop();
        Column.pop();
        // 主要内容区域
        Scroll.pop();
        Column.pop();
    }
    // 顶部状态栏
    buildStatusBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(133:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 20, right: 20 });
            Row.backgroundColor('#ffffff');
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('E-Wallet');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(134:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777240, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(140:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                promptAction.showToast({
                    message: '暂无新消息',
                    duration: 1500
                });
            });
        }, Image);
        Row.pop();
    }
    // 用户信息卡片
    buildUserCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(161:5)", "entry");
            Column.width('100%');
            Column.padding(24);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(16);
            Column.margin({ left: 16, right: 16, top: 16 });
            Column.shadow({
                radius: 12,
                color: '#10000000',
                offsetX: 0,
                offsetY: 4
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户基本信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(163:7)", "entry");
            // 用户基本信息
            Row.width('100%');
            // 用户基本信息
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(164:9)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`你好，${this.user?.realName || '用户'}`);
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(165:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.user?.phone || '138****5678');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(170:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 头像
            Image.create({ "id": 16777261, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(180:9)", "entry");
            // 头像
            Image.width(48);
            // 头像
            Image.height(48);
            // 头像
            Image.borderRadius(24);
            // 头像
            Image.border({ width: 2, color: '#f0f0f0' });
        }, Image);
        // 用户基本信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 余额显示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(190:7)", "entry");
            // 余额显示
            Column.width('100%');
            // 余额显示
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(191:9)", "entry");
            Row.justifyContent(FlexAlign.Start);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包余额');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(192:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.showBalance ? { "id": 16777262, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" } : { "id": 16777263, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(196:11)", "entry");
            Image.width(20);
            Image.height(20);
            Image.margin({ left: 8 });
            Image.onClick(() => {
                this.showBalance = !this.showBalance;
            });
        }, Image);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.showBalance ? `¥${(this.wallet?.balance || 0).toFixed(2)}` : '******');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(207:9)", "entry");
            Text.fontSize(36);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.letterSpacing(this.showBalance ? 0 : 6);
        }, Text);
        Text.pop();
        // 余额显示
        Column.pop();
        Column.pop();
    }
    // 轮播图
    buildBanner(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(232:5)", "entry");
            Column.width('100%');
            Column.padding({ left: 16, right: 16, top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Swiper.create();
            Swiper.debugLine("entry/src/main/ets/pages/HomePage.ets(233:7)", "entry");
            Swiper.width('100%');
            Swiper.height(160);
            Swiper.autoPlay(true);
            Swiper.interval(4000);
            Swiper.indicator(new DotIndicator()
                .itemWidth(8)
                .itemHeight(8)
                .selectedItemWidth(16)
                .selectedItemHeight(8)
                .color('#80ffffff')
                .selectedColor('#ffffff'));
            Swiper.borderRadius(16);
            Swiper.shadow({
                radius: 12,
                color: '#********',
                offsetX: 0,
                offsetY: 6
            });
        }, Swiper);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Stack.create();
                    Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(235:11)", "entry");
                    Stack.width('100%');
                    Stack.height(160);
                }, Stack);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create(item.image);
                    Image.debugLine("entry/src/main/ets/pages/HomePage.ets(236:13)", "entry");
                    Image.width('100%');
                    Image.height(160);
                    Image.borderRadius(16);
                    Image.objectFit(ImageFit.Cover);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 轮播图文字覆盖层
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/HomePage.ets(243:13)", "entry");
                    // 轮播图文字覆盖层
                    Column.alignItems(HorizontalAlign.Start);
                    // 轮播图文字覆盖层
                    Column.justifyContent(FlexAlign.End);
                    // 轮播图文字覆盖层
                    Column.width('100%');
                    // 轮播图文字覆盖层
                    Column.height('100%');
                    // 轮播图文字覆盖层
                    Column.padding(20);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(item.title);
                    Text.debugLine("entry/src/main/ets/pages/HomePage.ets(244:15)", "entry");
                    Text.fontSize(18);
                    Text.fontWeight(FontWeight.Bold);
                    Text.fontColor('#ffffff');
                    Text.textAlign(TextAlign.Start);
                    Text.margin({ bottom: 4 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (item.subtitle) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(item.subtitle);
                                Text.debugLine("entry/src/main/ets/pages/HomePage.ets(252:17)", "entry");
                                Text.fontSize(14);
                                Text.fontColor('#ffffff');
                                Text.opacity(0.9);
                                Text.textAlign(TextAlign.Start);
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                // 轮播图文字覆盖层
                Column.pop();
                Stack.pop();
            };
            this.forEachUpdateFunction(elmtId, this.bannerItems, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Swiper.pop();
        Column.pop();
    }
    // 快捷操作
    buildQuickActions(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(297:5)", "entry");
            Column.width('100%');
            Column.padding(24);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(20);
            Column.margin({ left: 16, right: 16, top: 16 });
            Column.shadow({
                radius: 16,
                color: '#********',
                offsetX: 0,
                offsetY: 8
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快捷操作');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(298:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(305:7)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(307:9)", "entry");
            // 充值
            Column.layoutWeight(1);
            // 充值
            Column.padding({ top: 20, bottom: 20, left: 12, right: 12 });
            // 充值
            Column.backgroundColor('#ffffff');
            // 充值
            Column.borderRadius(16);
            // 充值
            Column.justifyContent(FlexAlign.Center);
            // 充值
            Column.shadow({
                radius: 12,
                color: '#********',
                offsetX: 0,
                offsetY: 6
            });
            // 充值
            Column.onClick(() => {
                this.navigateToPage('pages/WalletPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(309:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 48, height: 48 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(310:13)", "entry");
            Circle.fill('#ff56b2fa');
            Circle.shadow({
                radius: 8,
                color: '#407BA7E1',
                offsetX: 0,
                offsetY: 4
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777235, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(319:13)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(326:11)", "entry");
            Text.fontSize(14);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 充值
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/HomePage.ets(346:9)", "entry");
            Blank.width(16);
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(349:9)", "entry");
            // 提现
            Column.layoutWeight(1);
            // 提现
            Column.padding({ top: 20, bottom: 20, left: 12, right: 12 });
            // 提现
            Column.backgroundColor('#ffffff');
            // 提现
            Column.borderRadius(16);
            // 提现
            Column.justifyContent(FlexAlign.Center);
            // 提现
            Column.shadow({
                radius: 12,
                color: '#********',
                offsetX: 0,
                offsetY: 6
            });
            // 提现
            Column.onClick(() => {
                this.navigateToPage('pages/WalletPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(351:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 48, height: 48 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(352:13)", "entry");
            Circle.fill('#fffac46b');
            Circle.shadow({
                radius: 8,
                color: '#40E6B566',
                offsetX: 0,
                offsetY: 4
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777251, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(361:13)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(368:11)", "entry");
            Text.fontSize(14);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 提现
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/HomePage.ets(388:9)", "entry");
            Blank.width(16);
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(391:9)", "entry");
            // 转账
            Column.layoutWeight(1);
            // 转账
            Column.padding({ top: 20, bottom: 20, left: 12, right: 12 });
            // 转账
            Column.backgroundColor('#ffffff');
            // 转账
            Column.borderRadius(16);
            // 转账
            Column.justifyContent(FlexAlign.Center);
            // 转账
            Column.shadow({
                radius: 12,
                color: '#********',
                offsetX: 0,
                offsetY: 6
            });
            // 转账
            Column.onClick(() => {
                this.navigateToPage('pages/BankAccountTransferPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(393:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 48, height: 48 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(394:13)", "entry");
            Circle.fill('#ffffec67');
            Circle.shadow({
                radius: 8,
                color: '#407BC97B',
                offsetX: 0,
                offsetY: 4
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(403:13)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(410:11)", "entry");
            Text.fontSize(14);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 转账
        Column.pop();
        Row.pop();
        Column.pop();
    }
    // 全部服务
    buildServices(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(448:5)", "entry");
            Column.width('100%');
            Column.padding(24);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(20);
            Column.margin({ left: 16, right: 16, top: 16 });
            Column.shadow({
                radius: 16,
                color: '#********',
                offsetX: 0,
                offsetY: 8
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('全部服务');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(449:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第一行服务
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(457:7)", "entry");
            // 第一行服务
            Row.width('100%');
            // 第一行服务
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包管理
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(459:9)", "entry");
            // 钱包管理
            Column.layoutWeight(1);
            // 钱包管理
            Column.height(110);
            // 钱包管理
            Column.padding(12);
            // 钱包管理
            Column.backgroundColor('#ffffff');
            // 钱包管理
            Column.borderRadius(16);
            // 钱包管理
            Column.justifyContent(FlexAlign.Center);
            // 钱包管理
            Column.shadow({
                radius: 10,
                color: '#********',
                offsetX: 0,
                offsetY: 4
            });
            // 钱包管理
            Column.onClick(() => {
                this.navigateToPage('pages/WalletPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(461:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 10 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 40, height: 40 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(462:13)", "entry");
            Circle.fill('#bef1afc4');
            Circle.shadow({
                radius: 6,
                color: '#307BA7E1',
                offsetX: 0,
                offsetY: 3
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777236, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(471:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包管理');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(478:11)", "entry");
            Text.fontSize(13);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 钱包管理
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/HomePage.ets(502:9)", "entry");
            Blank.width(12);
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(505:9)", "entry");
            // 银行卡
            Column.layoutWeight(1);
            // 银行卡
            Column.height(110);
            // 银行卡
            Column.padding(12);
            // 银行卡
            Column.backgroundColor('#ffffff');
            // 银行卡
            Column.borderRadius(16);
            // 银行卡
            Column.justifyContent(FlexAlign.Center);
            // 银行卡
            Column.shadow({
                radius: 10,
                color: '#********',
                offsetX: 0,
                offsetY: 4
            });
            // 银行卡
            Column.onClick(() => {
                this.navigateToPage('pages/BankCardPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(507:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 10 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 40, height: 40 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(508:13)", "entry");
            Circle.fill('#c4e28350');
            Circle.shadow({
                radius: 6,
                color: '#307BC97B',
                offsetX: 0,
                offsetY: 3
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(517:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(524:11)", "entry");
            Text.fontSize(13);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/HomePage.ets(548:9)", "entry");
            Blank.width(12);
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易记录
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(551:9)", "entry");
            // 交易记录
            Column.layoutWeight(1);
            // 交易记录
            Column.height(110);
            // 交易记录
            Column.padding(12);
            // 交易记录
            Column.backgroundColor('#ffffff');
            // 交易记录
            Column.borderRadius(16);
            // 交易记录
            Column.justifyContent(FlexAlign.Center);
            // 交易记录
            Column.shadow({
                radius: 10,
                color: '#********',
                offsetX: 0,
                offsetY: 4
            });
            // 交易记录
            Column.onClick(() => {
                this.navigateToPage('pages/TransactionPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(553:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 10 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 40, height: 40 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(554:13)", "entry");
            Circle.fill('#dc80a723');
            Circle.shadow({
                radius: 6,
                color: '#30E6B566',
                offsetX: 0,
                offsetY: 3
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777233, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(563:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(570:11)", "entry");
            Text.fontSize(13);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 交易记录
        Column.pop();
        // 第一行服务
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第二行服务
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HomePage.ets(598:7)", "entry");
            // 第二行服务
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行账户
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(600:9)", "entry");
            // 银行账户
            Column.layoutWeight(1);
            // 银行账户
            Column.height(110);
            // 银行账户
            Column.padding(12);
            // 银行账户
            Column.backgroundColor('#ffffff');
            // 银行账户
            Column.borderRadius(16);
            // 银行账户
            Column.justifyContent(FlexAlign.Center);
            // 银行账户
            Column.shadow({
                radius: 10,
                color: '#********',
                offsetX: 0,
                offsetY: 4
            });
            // 银行账户
            Column.onClick(() => {
                this.navigateToPage('pages/BankAccountTransferPage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(602:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 10 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 40, height: 40 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(603:13)", "entry");
            Circle.fill('#cdece170');
            Circle.shadow({
                radius: 6,
                color: '#30B885C9',
                offsetX: 0,
                offsetY: 3
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(612:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行账户');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(619:11)", "entry");
            Text.fontSize(13);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 银行账户
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/HomePage.ets(643:9)", "entry");
            Blank.width(12);
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(646:9)", "entry");
            // 设置
            Column.layoutWeight(1);
            // 设置
            Column.height(110);
            // 设置
            Column.padding(12);
            // 设置
            Column.backgroundColor('#ffffff');
            // 设置
            Column.borderRadius(16);
            // 设置
            Column.justifyContent(FlexAlign.Center);
            // 设置
            Column.shadow({
                radius: 10,
                color: '#********',
                offsetX: 0,
                offsetY: 4
            });
            // 设置
            Column.onClick(() => {
                this.navigateToPage('pages/ProfilePage');
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(648:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 10 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 40, height: 40 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(649:13)", "entry");
            Circle.fill('#c89ba8b0');
            Circle.shadow({
                radius: 6,
                color: '#308FA3AD',
                offsetX: 0,
                offsetY: 3
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777268, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(658:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(665:11)", "entry");
            Text.fontSize(13);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 设置
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/HomePage.ets(688:9)", "entry");
            Blank.width(12);
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 关于
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HomePage.ets(691:9)", "entry");
            // 关于
            Column.layoutWeight(1);
            // 关于
            Column.height(110);
            // 关于
            Column.padding(12);
            // 关于
            Column.backgroundColor('#ffffff');
            // 关于
            Column.borderRadius(16);
            // 关于
            Column.justifyContent(FlexAlign.Center);
            // 关于
            Column.shadow({
                radius: 10,
                color: '#********',
                offsetX: 0,
                offsetY: 4
            });
            // 关于
            Column.onClick(() => {
                promptAction.showToast({
                    message: '关于功能开发中',
                    duration: 1500
                });
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 图标容器
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/HomePage.ets(693:11)", "entry");
            // 图标容器
            Stack.margin({ bottom: 10 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 40, height: 40 });
            Circle.debugLine("entry/src/main/ets/pages/HomePage.ets(694:13)", "entry");
            Circle.fill('#ba6f7dde');
            Circle.shadow({
                radius: 6,
                color: '#30A68B7A',
                offsetX: 0,
                offsetY: 3
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777266, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/HomePage.ets(703:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
        }, Image);
        // 图标容器
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('关于');
            Text.debugLine("entry/src/main/ets/pages/HomePage.ets(710:11)", "entry");
            Text.fontSize(13);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        // 关于
        Column.pop();
        // 第二行服务
        Row.pop();
        Column.pop();
    }
    // 加载用户数据
    loadUserData() {
        this.isLoading = true;
        axios({
            url: `http://localhost:8091/auth/users/${this.userId}`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<User>>) => {
            console.log('用户信息:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                this.user = res.data.data;
            }
            else {
                console.error('加载用户信息失败:', res.data.msg);
                promptAction.showToast({
                    message: '加载用户信息失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('用户信息请求错误:', err.message);
            promptAction.showToast({
                message: '网络连接失败',
                duration: 2000
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 加载钱包数据
    loadWalletData() {
        axios({
            url: `http://localhost:8091/wallet/balance/${this.userId}`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<Wallet>>) => {
            console.log('钱包信息:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                this.wallet = res.data.data;
            }
            else {
                console.error('加载钱包信息失败:', res.data.msg);
                promptAction.showToast({
                    message: '加载钱包信息失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('钱包信息请求错误:', err.message);
            promptAction.showToast({
                message: '网络连接失败',
                duration: 2000
            });
        });
    }
    // 处理服务项点击
    handleServiceClick(item: ServiceItem) {
        if (item.route) {
            this.navigateToPage(item.route);
        }
        else {
            promptAction.showToast({
                message: `${item.title}功能开发中`,
                duration: 1500
            });
        }
    }
    // 页面导航
    navigateToPage(route: string) {
        router.pushUrl({
            url: route,
            params: {
                userId: this.userId
            }
        }).catch((err: Error) => {
            console.error('页面跳转失败:', err.message);
            promptAction.showToast({
                message: '页面跳转失败，请重试',
                duration: 2000
            });
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "HomePage";
    }
}
registerNamedRoute(() => new HomePage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/HomePage", pageFullPath: "entry/src/main/ets/pages/HomePage", integratedHsp: "false", moduleType: "followWithHap" });
