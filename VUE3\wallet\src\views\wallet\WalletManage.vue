<template>
  <div class="wallet-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><Wallet /></el-icon>
            钱包管理
          </h2>
          <p>管理您的钱包余额，进行充值和提现操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 钱包概览 -->
    <div class="wallet-overview">
      <!-- 主钱包卡片 -->
      <el-card class="main-wallet-card" shadow="always">
        <div class="wallet-header">
          <div class="wallet-info">
            <h3>我的钱包</h3>
            <p>钱包ID: {{ walletId }}</p>
          </div>
          <div class="wallet-status">
            <el-tag :type="walletStatus === 1 ? 'success' : 'danger'" size="large">
              {{ walletStatus === 1 ? '正常' : '冻结' }}
            </el-tag>
          </div>
        </div>

        <div class="balance-display">
          <div class="balance-main">
            <span class="currency">¥</span>
            <span class="amount">{{ formatAmount(walletBalance) }}</span>
          </div>
          <div class="balance-subtitle">可用余额</div>
        </div>

        <div class="wallet-actions">
          <el-button type="success" size="large" @click="showRechargeDialog" :disabled="walletStatus !== 1">
            <el-icon><Plus /></el-icon>
            充值
          </el-button>
          <el-button type="warning" size="large" @click="showWithdrawDialog" :disabled="walletStatus !== 1">
            <el-icon><Minus /></el-icon>
            提现
          </el-button>
          <el-button type="primary" size="large" @click="refreshBalance">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button type="info" size="large" @click="goToPayment">
            <el-icon><CreditCard /></el-icon>
            去支付
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 功能模块网格 -->
    <div class="function-grid">
      <!-- 资金流水 -->
      <el-card class="function-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><TrendCharts /></el-icon>
            <span>资金流水</span>
          </div>
        </template>
        <div class="function-content">
          <div class="flow-stats">
            <div class="flow-item">
              <span class="flow-label">今日收入</span>
              <span class="flow-value income">+¥{{ formatAmount(todayIncome) }}</span>
            </div>
            <div class="flow-item">
              <span class="flow-label">今日支出</span>
              <span class="flow-value expense">-¥{{ formatAmount(todayExpense) }}</span>
            </div>
            <div class="flow-item">
              <span class="flow-label">本月收入</span>
              <span class="flow-value income">+¥{{ formatAmount(monthIncome) }}</span>
            </div>
            <div class="flow-item">
              <span class="flow-label">本月支出</span>
              <span class="flow-value expense">-¥{{ formatAmount(monthExpense) }}</span>
            </div>
          </div>
          <el-button type="text" @click="showTransactionHistory" style="width: 100%; margin-top: 15px;">
            查看详细流水 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </el-card>

      <!-- 安全设置 -->
      <el-card class="function-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Lock /></el-icon>
            <span>安全设置</span>
          </div>
        </template>
        <div class="function-content">
          <div class="security-items">
            <div class="security-item">
              <span>支付密码</span>
              <el-button type="text" @click="showChangePayPasswordDialog">修改</el-button>
            </div>
            <div class="security-item">
              <span>交易限额</span>
              <el-button type="text" @click="showLimitSettingDialog">设置</el-button>
            </div>
            <div class="security-item">
              <span>钱包锁定</span>
              <el-switch v-model="walletLocked" @change="toggleWalletLock" />
            </div>
            <div class="security-item">
              <span>指纹支付</span>
              <el-switch v-model="fingerprintEnabled" @change="toggleFingerprint" />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 快捷功能 -->
      <el-card class="function-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Grid /></el-icon>
            <span>快捷功能</span>
          </div>
        </template>
        <div class="function-content">
          <div class="quick-functions">
            <div class="quick-function-item" @click="showTransferDialog">
              <el-icon><Share /></el-icon>
              <span>转账</span>
            </div>
            <div class="quick-function-item" @click="showReceiveDialog">
              <el-icon><Download /></el-icon>
              <span>收款</span>
            </div>
            <div class="quick-function-item" @click="showBillDialog">
              <el-icon><Document /></el-icon>
              <span>账单</span>
            </div>
            <div class="quick-function-item" @click="showReportDialog">
              <el-icon><PieChart /></el-icon>
              <span>报表</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 银行卡管理 -->
      <el-card class="function-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><CreditCard /></el-icon>
            <span>银行卡管理</span>
          </div>
        </template>
        <div class="function-content">
          <div class="bank-card-summary">
            <div class="card-count">
              <span class="count-number">{{ bankCards.length }}</span>
              <span class="count-label">张银行卡</span>
            </div>
            <div class="card-status">
              <div class="status-item">
                <span class="status-dot active"></span>
                <span>{{ activeBankCards }}张已绑定</span>
              </div>
              <div class="status-item">
                <span class="status-dot inactive"></span>
                <span>{{ inactiveBankCards }}张未绑定</span>
              </div>
            </div>
          </div>
          <el-button type="text" @click="goToBankCards" style="width: 100%; margin-top: 15px;">
            管理银行卡 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 充值对话框 -->
    <el-dialog v-model="rechargeDialogVisible" title="钱包充值" width="500px">
      <el-form :model="rechargeForm" :rules="rechargeRules" ref="rechargeFormRef" label-width="100px">
        <el-form-item label="充值金额" prop="amount">
          <el-input
            v-model="rechargeForm.amount"
            placeholder="请输入充值金额"
            type="number"
            step="0.01"
            min="0.01"
            max="100000"
          >
            <template #prepend>¥</template>
          </el-input>
          <div class="amount-tips">单次充值限额：¥100,000</div>
        </el-form-item>
        
        <el-form-item label="选择银行卡" prop="bankCardId">
          <el-select v-model="rechargeForm.bankCardId" placeholder="请选择银行卡" style="width: 100%">
            <el-option
              v-for="card in bankCards"
              :key="card.cardId"
              :label="`${card.bankName} (${maskCardNumber(card.cardNumber)})`"
              :value="card.cardId"
              :disabled="card.status !== 1"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="rechargeForm.remark"
            placeholder="请输入备注（可选）"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rechargeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRecharge" :loading="rechargeLoading">
            确认充值
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 提现对话框 -->
    <el-dialog v-model="withdrawDialogVisible" title="钱包提现" width="500px">
      <el-form :model="withdrawForm" :rules="withdrawRules" ref="withdrawFormRef" label-width="100px">
        <el-form-item label="提现金额" prop="amount">
          <el-input
            v-model="withdrawForm.amount"
            placeholder="请输入提现金额"
            type="number"
            step="0.01"
            min="0.01"
            :max="walletBalance"
          >
            <template #prepend>¥</template>
          </el-input>
          <div class="amount-tips">
            可提现余额：¥{{ formatAmount(walletBalance) }}，单次提现限额：¥50,000
          </div>
        </el-form-item>
        
        <el-form-item label="选择银行卡" prop="bankCardId">
          <el-select v-model="withdrawForm.bankCardId" placeholder="请选择银行卡" style="width: 100%">
            <el-option
              v-for="card in bankCards"
              :key="card.cardId"
              :label="`${card.bankName} (${maskCardNumber(card.cardNumber)})`"
              :value="card.cardId"
              :disabled="card.status !== 1"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="withdrawForm.remark"
            placeholder="请输入备注（可选）"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="withdrawDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleWithdraw" :loading="withdrawLoading">
            确认提现
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 最近交易记录 -->
    <el-card class="transaction-history-card" shadow="hover" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>最近交易记录</span>
          <el-button type="text" @click="showTransactionHistory">
            查看全部 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </template>

      <el-table :data="transactionHistory.slice(0, 5)" style="width: 100%" empty-text="暂无交易记录">
        <el-table-column prop="transNo" label="交易流水号" width="180" />
        <el-table-column prop="type" label="交易类型" width="100">
          <template #default="scope">
            <el-tag :type="getTransactionTypeColor(scope.row.type)">
              {{ getTransactionTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="交易金额" width="120">
          <template #default="scope">
            <span :class="scope.row.type === 1 || scope.row.type === 3 ? 'income' : 'expense'">
              {{ scope.row.type === 1 || scope.row.type === 3 ? '+' : '-' }}¥{{ formatAmount(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="交易时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Minus, Refresh, CreditCard, Wallet, DataAnalysis, TrendCharts, Lock, Grid, Share, Download, Document, PieChart, ArrowRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

// 路由
const router = useRouter()

// 数据
const walletId = ref('WLT' + Date.now())
const walletBalance = ref(0)
const walletStatus = ref(1)
const bankCards = ref([])
const transactionHistory = ref([])

// 统计数据
const todayIncome = ref(0)
const todayExpense = ref(0)
const monthIncome = ref(0)
const monthExpense = ref(0)
const paymentCount = ref(0)

// 安全设置
const walletLocked = ref(false)
const fingerprintEnabled = ref(false)

// 计算属性
const activeBankCards = computed(() => bankCards.value.filter(card => card.status === 1).length)
const inactiveBankCards = computed(() => bankCards.value.filter(card => card.status === 0).length)

// 对话框显示状态
const rechargeDialogVisible = ref(false)
const withdrawDialogVisible = ref(false)
const transferDialogVisible = ref(false)
const receiveDialogVisible = ref(false)
const changePayPasswordDialogVisible = ref(false)
const limitSettingDialogVisible = ref(false)
const rechargeLoading = ref(false)
const withdrawLoading = ref(false)

// 充值表单
const rechargeForm = reactive({
  amount: '',
  bankCardId: '',
  remark: ''
})

// 提现表单
const withdrawForm = reactive({
  amount: '',
  bankCardId: '',
  remark: ''
})

// 转账表单
const transferForm = reactive({
  amount: '',
  targetPhone: '',
  remark: '',
  payPassword: ''
})

// 修改支付密码表单
const changePayPasswordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 限额设置表单
const limitSettingForm = reactive({
  dailyLimit: '',
  monthlyLimit: '',
  singleLimit: ''
})

// 表单引用
const rechargeFormRef = ref()
const withdrawFormRef = ref()
const paymentFormRef = ref()
const bankCardPayFormRef = ref()

// 验证规则
const rechargeRules = reactive({
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value || isNaN(value) || parseFloat(value) <= 0) {
          callback(new Error('充值金额必须大于0'))
        } else if (parseFloat(value) > 100000) {
          callback(new Error('单次充值金额不能超过10万元'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  bankCardId: [
    { required: true, message: '请选择银行卡', trigger: 'change' }
  ]
})

const withdrawRules = reactive({
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || isNaN(value) || parseFloat(value) <= 0) {
          callback(new Error('提现金额必须大于0'))
        } else if (parseFloat(value) > walletBalance.value) {
          callback(new Error('提现金额不能超过钱包余额'))
        } else if (parseFloat(value) > 50000) {
          callback(new Error('单次提现金额不能超过5万元'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  bankCardId: [
    { required: true, message: '请选择银行卡', trigger: 'change' }
  ]
})

// 支付验证规则
const paymentRules = reactive({
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || isNaN(value) || parseFloat(value) <= 0) {
          callback(new Error('支付金额必须大于0'))
        } else if (parseFloat(value) > walletBalance.value) {
          callback(new Error('支付金额不能超过钱包余额'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  merchantName: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' }
  ]
})

// 银行卡支付验证规则
const bankCardPayRules = reactive({
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || isNaN(value) || parseFloat(value) <= 0) {
          callback(new Error('支付金额必须大于0'))
        } else if (parseFloat(value) > 100000) {
          callback(new Error('单次支付金额不能超过10万元'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  bankCardId: [
    { required: true, message: '请选择银行卡', trigger: 'change' }
  ],
  merchantName: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' }
  ]
})

// 方法
const getUserId = () => {
  // 从localStorage获取用户ID，这里简化处理
  return 1
}

const formatAmount = (amount) => {
  return parseFloat(amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const maskCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  return cardNumber.replace(/(\d{4})\d*(\d{4})/, '$1 **** **** $2')
}

// 获取钱包余额
const getWalletBalance = async () => {
  try {
    const userId = getUserId()
    const response = await axios.get(`http://localhost:8091/wallet/balance/${userId}`)
    if (response.data && response.data.code === 0) {
      const wallet = response.data.data
      walletBalance.value = wallet.balance
      walletStatus.value = wallet.status
    } else {
      ElMessage.error(response.data?.msg || '获取钱包余额失败')
    }
  } catch (error) {
    console.error('获取钱包余额失败:', error)
    ElMessage.error('获取钱包余额失败')
  }
}

// 获取银行卡列表
const getBankCards = async () => {
  try {
    const userId = getUserId()
    const response = await axios.get(`http://localhost:8091/bankCards/user/${userId}`)
    if (response.data && response.data.code === 0) {
      bankCards.value = response.data.data || []
    } else {
      ElMessage.error(response.data?.msg || '获取银行卡列表失败')
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error)
    ElMessage.error('获取银行卡列表失败')
  }
}

// 刷新余额
const refreshBalance = () => {
  getWalletBalance()
}

// 显示充值对话框
const showRechargeDialog = () => {
  rechargeForm.amount = ''
  rechargeForm.bankCardId = ''
  rechargeForm.remark = ''
  rechargeDialogVisible.value = true
}

// 显示提现对话框
const showWithdrawDialog = () => {
  withdrawForm.amount = ''
  withdrawForm.bankCardId = ''
  withdrawForm.remark = ''
  withdrawDialogVisible.value = true
}

// 显示转账对话框
const showTransferDialog = () => {
  transferForm.amount = ''
  transferForm.targetPhone = ''
  transferForm.remark = ''
  transferForm.payPassword = ''
  transferDialogVisible.value = true
}

// 显示收款对话框
const showReceiveDialog = () => {
  receiveDialogVisible.value = true
}

// 显示修改支付密码对话框
const showChangePayPasswordDialog = () => {
  changePayPasswordForm.oldPassword = ''
  changePayPasswordForm.newPassword = ''
  changePayPasswordForm.confirmPassword = ''
  changePayPasswordDialogVisible.value = true
}

// 显示限额设置对话框
const showLimitSettingDialog = () => {
  limitSettingForm.dailyLimit = ''
  limitSettingForm.monthlyLimit = ''
  limitSettingForm.singleLimit = ''
  limitSettingDialogVisible.value = true
}

// 显示交易历史
const showTransactionHistory = () => {
  router.push('/home/<USER>')
}

// 显示账单
const showBillDialog = () => {
  ElMessage.info('账单功能开发中...')
}

// 显示报表
const showReportDialog = () => {
  ElMessage.info('报表功能开发中...')
}

// 跳转到支付页面
const goToPayment = () => {
  router.push('/home/<USER>')
}

// 跳转到银行卡管理
const goToBankCards = () => {
  router.push('/home/<USER>')
}

// 切换钱包锁定状态
const toggleWalletLock = (value) => {
  ElMessage.success(value ? '钱包已锁定' : '钱包已解锁')
}

// 切换指纹支付
const toggleFingerprint = (value) => {
  ElMessage.success(value ? '指纹支付已开启' : '指纹支付已关闭')
}

// 处理充值
const handleRecharge = async () => {
  try {
    const valid = await rechargeFormRef.value.validate()
    if (!valid) return
    
    const selectedCard = bankCards.value.find(card => card.cardId === rechargeForm.bankCardId)
    if (!selectedCard) {
      ElMessage.error('请选择有效的银行卡')
      return
    }
    
    await ElMessageBox.confirm(
      `确认从银行卡 ${selectedCard.bankName} (${maskCardNumber(selectedCard.cardNumber)}) 充值 ¥${rechargeForm.amount} 到钱包？`,
      '确认充值',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    rechargeLoading.value = true
    
    const userId = getUserId()
    const response = await axios.post('http://localhost:8091/wallet/recharge', null, {
      params: {
        userId: userId,
        amount: rechargeForm.amount,
        bankCardNumber: selectedCard.cardNumber,
        remark: rechargeForm.remark || '钱包充值'
      }
    })
    
    if (response.data && response.data.code === 0) {
      ElMessage.success('充值成功')
      rechargeDialogVisible.value = false
      getWalletBalance() // 刷新余额
    } else {
      ElMessage.error(response.data?.msg || '充值失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('充值失败:', error)
      ElMessage.error('充值失败')
    }
  } finally {
    rechargeLoading.value = false
  }
}

// 处理提现
const handleWithdraw = async () => {
  try {
    const valid = await withdrawFormRef.value.validate()
    if (!valid) return
    
    const selectedCard = bankCards.value.find(card => card.cardId === withdrawForm.bankCardId)
    if (!selectedCard) {
      ElMessage.error('请选择有效的银行卡')
      return
    }
    
    await ElMessageBox.confirm(
      `确认从钱包提现 ¥${withdrawForm.amount} 到银行卡 ${selectedCard.bankName} (${maskCardNumber(selectedCard.cardNumber)})？`,
      '确认提现',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    withdrawLoading.value = true
    
    const userId = getUserId()
    const response = await axios.post('http://localhost:8091/wallet/withdraw', null, {
      params: {
        userId: userId,
        amount: withdrawForm.amount,
        bankCardNumber: selectedCard.cardNumber,
        remark: withdrawForm.remark || '钱包提现'
      }
    })
    
    if (response.data && response.data.code === 0) {
      ElMessage.success('提现成功')
      withdrawDialogVisible.value = false
      getWalletBalance() // 刷新余额
    } else {
      ElMessage.error(response.data?.msg || '提现失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提现失败:', error)
      ElMessage.error('提现失败')
    }
  } finally {
    withdrawLoading.value = false
  }
}

// 计算交易统计
const calculateTransactionStats = () => {
  if (!transactionHistory.value || transactionHistory.value.length === 0) {
    todayIncome.value = 0
    todayExpense.value = 0
    monthIncome.value = 0
    monthExpense.value = 0
    return
  }

  const today = new Date()
  const todayStr = today.toDateString()
  const currentMonth = today.getMonth()
  const currentYear = today.getFullYear()

  let todayIncomeSum = 0
  let todayExpenseSum = 0
  let monthIncomeSum = 0
  let monthExpenseSum = 0

  transactionHistory.value.forEach(transaction => {
    if (transaction.status !== 1) return // 只统计成功的交易

    const transactionDate = new Date(transaction.createTime)
    const amount = parseFloat(transaction.amount) || 0

    // 判断收入还是支出
    // 1-充值, 3-转账收入 为收入
    // 2-提现, 4-消费 为支出
    const isIncome = transaction.type === 1 || transaction.type === 3
    const isExpense = transaction.type === 2 || transaction.type === 4

    // 今日统计
    if (transactionDate.toDateString() === todayStr) {
      if (isIncome) {
        todayIncomeSum += amount
      } else if (isExpense) {
        todayExpenseSum += amount
      }
    }

    // 本月统计
    if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
      if (isIncome) {
        monthIncomeSum += amount
      } else if (isExpense) {
        monthExpenseSum += amount
      }
    }
  })

  todayIncome.value = todayIncomeSum
  todayExpense.value = todayExpenseSum
  monthIncome.value = monthIncomeSum
  monthExpense.value = monthExpenseSum
}



// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 0: return 'warning'
    case 1: return 'success'
    case 2: return 'danger'
    case 3: return 'info'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '处理中'
    case 1: return '成功'
    case 2: return '失败'
    case 3: return '已取消'
    default: return '未知'
  }
}

// 获取交易记录
const getTransactionHistory = async () => {
  try {
    const userId = getUserId()
    // 获取更多记录用于统计，设置较大的pageSize
    const response = await axios.get(`http://localhost:8091/transactions/user/${userId}?pageSize=100`)
    if (response.data && response.data.code === 0) {
      // 后端返回的是分页数据，需要取records字段
      const pageData = response.data.data
      transactionHistory.value = pageData.records || []
      // 获取数据后计算统计
      calculateTransactionStats()
    } else {
      console.error('获取交易记录失败:', response.data?.msg)
      ElMessage.error('获取交易记录失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取交易记录失败:', error)
    ElMessage.error('获取交易记录失败')
  }
}

// 获取交易类型颜色
const getTransactionTypeColor = (type) => {
  switch (type) {
    case 1: return 'success' // 充值
    case 2: return 'warning' // 提现
    case 3: return 'info'    // 转账
    case 4: return 'danger'  // 消费
    default: return 'info'
  }
}

// 获取交易类型文本
const getTransactionTypeText = (type) => {
  switch (type) {
    case 1: return '充值'
    case 2: return '提现'
    case 3: return '转账'
    case 4: return '消费'
    default: return '未知'
  }
}

// 刷新数据
const refreshData = () => {
  getWalletBalance()
  getBankCards()
  getTransactionHistory()
}

// 生命周期
onMounted(() => {
  getWalletBalance()
  getBankCards()
  getTransactionHistory() // 这个方法内部会调用calculateTransactionStats()
})
</script>

<style scoped>
.wallet-manage {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #909399;
  margin: 0;
}

/* 钱包概览样式 */
.wallet-overview {
  margin-bottom: 30px;
}

.main-wallet-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.main-wallet-card :deep(.el-card__header) {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
}

.main-wallet-card :deep(.el-card__body) {
  padding: 30px 20px;
}

.wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.wallet-info h3 {
  margin: 0 0 5px 0;
  font-size: 20px;
  font-weight: bold;
}

.wallet-info p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.balance-display {
  text-align: center;
  margin: 30px 0;
}

.balance-main {
  margin-bottom: 10px;
}

.balance-main .currency {
  font-size: 28px;
  margin-right: 8px;
  opacity: 0.9;
}

.balance-main .amount {
  font-size: 48px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.balance-subtitle {
  font-size: 16px;
  opacity: 0.8;
}

.wallet-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.wallet-actions .el-button {
  min-width: 100px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.wallet-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 功能网格样式 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.function-card {
  min-height: 250px;
}

.function-card .card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.function-content {
  padding: 20px 0;
}

/* 资金流水样式 */
.flow-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.flow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.flow-label {
  color: #666;
  font-size: 14px;
}

.flow-value {
  font-weight: bold;
  font-size: 16px;
}

.flow-value.income {
  color: #67c23a;
}

.flow-value.expense {
  color: #f56c6c;
}

/* 安全设置样式 */
.security-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
  border-bottom: none;
}

/* 快捷功能样式 */
.quick-functions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.quick-function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-function-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.quick-function-item .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #409eff;
}

.quick-function-item span {
  font-size: 14px;
  color: #333;
}

/* 银行卡管理样式 */
.bank-card-summary {
  text-align: center;
}

.card-count {
  margin-bottom: 20px;
}

.count-number {
  font-size: 36px;
  font-weight: bold;
  color: #409eff;
}

.count-label {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.card-status {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.active {
  background: #67c23a;
}

.status-dot.inactive {
  background: #f56c6c;
}

/* 交易记录样式 */
.transaction-history-card {
  margin-top: 20px;
}

.income {
  color: #67c23a;
  font-weight: bold;
}

.expense {
  color: #f56c6c;
  font-weight: bold;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-content {
  text-align: center;
  padding: 30px 0;
}

.balance-amount {
  margin-bottom: 15px;
}

.currency {
  font-size: 24px;
  color: #909399;
  margin-right: 5px;
}

.amount {
  font-size: 48px;
  font-weight: bold;
  color: #67c23a;
}

.balance-status {
  margin-bottom: 30px;
}

.balance-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.balance-actions .el-button {
  padding: 12px 30px;
  font-size: 16px;
}

.quick-pay-content {
  padding: 20px 0;
}

.stats-content {
  padding: 20px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-value {
  color: #303133;
  font-size: 16px;
  font-weight: bold;
}

.payment-history-card {
  margin-top: 20px;
}

.amount-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wallet-manage {
    padding: 15px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .balance-amount .amount {
    font-size: 36px;
  }

  .balance-actions {
    flex-direction: column;
    align-items: center;
  }

  .balance-actions .el-button {
    width: 200px;
  }

  .quick-pay-content .el-button {
    margin-bottom: 10px;
  }
}
</style>
