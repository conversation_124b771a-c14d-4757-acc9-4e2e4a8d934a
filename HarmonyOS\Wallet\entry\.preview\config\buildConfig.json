{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29920", "checkEntry": "true", "localPropertiesPath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\local.properties", "Path": "D:\\HarmonyOS\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5", "watchMode": "true", "appResource": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets", "aceSoPath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/LoginPage\",\"pages/BarPage\",\"pages/WalletPage\",\"pages/HomePage\",\"pages/TransactionPage\",\"pages/SettingsPage\",\"pages/CardDetailPage\",\"pages/BankCardPage\"]}"]}}