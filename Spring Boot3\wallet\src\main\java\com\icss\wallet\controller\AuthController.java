package com.icss.wallet.controller;

import com.icss.wallet.entity.User;
import com.icss.wallet.result.R;
import com.icss.wallet.service.UserService;
import com.icss.wallet.service.SmsCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@CrossOrigin
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private SmsCodeService smsCodeService;
    
    /**
     * HarmonyOS前端专用：密码登录
     */
    @PostMapping("/login")
    public R login(@RequestParam String username, @RequestParam String password) {
        try {
            // 根据手机号或用户名查找用户
            User user = userService.findByPhone(username);
            if (user == null) {
                return R.failure("用户不存在");
            }
            
            // 验证密码（这里简化处理，实际应该使用加密验证）
            if (!password.equals(user.getPassword())) {
                return R.failure("密码错误");
            }
            
            // 检查用户状态
            if (user.getStatus() != 1) {
                return R.failure("账户已被禁用");
            }
            
            // 生成token（这里简化处理，返回用户ID作为token）
            String token = "TOKEN_" + user.getUserId() + "_" + System.currentTimeMillis();

            // 返回用户信息和token
            java.util.Map<String, Object> loginResult = new java.util.HashMap<>();
            loginResult.put("token", token);
            loginResult.put("userId", user.getUserId());
            loginResult.put("phone", user.getPhone());
            loginResult.put("realName", user.getRealName());

            return R.success("登录成功", loginResult);
        } catch (Exception e) {
            return R.failure("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * HarmonyOS前端专用：验证码登录
     */
    @PostMapping("/loginWithCode")
    public R loginWithCode(@RequestParam String phone, @RequestParam String code) {
        try {
            // 验证验证码
            boolean isValidCode = smsCodeService.verifyCode(phone, code, 1); // 1表示登录验证码
            if (!isValidCode) {
                return R.failure("验证码错误或已过期");
            }
            
            // 查找用户
            User user = userService.findByPhone(phone);
            if (user == null) {
                return R.failure("用户不存在");
            }
            
            // 检查用户状态
            if (user.getStatus() != 1) {
                return R.failure("账户已被禁用");
            }
            
            // 生成token（这里简化处理，返回用户ID作为token）
            String token = "TOKEN_" + user.getUserId() + "_" + System.currentTimeMillis();

            // 返回用户信息和token
            java.util.Map<String, Object> loginResult = new java.util.HashMap<>();
            loginResult.put("token", token);
            loginResult.put("userId", user.getUserId());
            loginResult.put("phone", user.getPhone());
            loginResult.put("realName", user.getRealName());

            return R.success("登录成功", loginResult);
        } catch (Exception e) {
            return R.failure("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * HarmonyOS前端专用：注册
     */
    @PostMapping("/register")
    public R register(@RequestParam String phone, 
                     @RequestParam String password,
                     @RequestParam String code,
                     @RequestParam(required = false) String realName) {
        try {
            // 验证验证码
            boolean isValidCode = smsCodeService.verifyCode(phone, code, 2); // 2表示注册验证码
            if (!isValidCode) {
                return R.failure("验证码错误或已过期");
            }
            
            // 检查手机号是否已注册
            User existingUser = userService.findByPhone(phone);
            if (existingUser != null) {
                return R.failure("手机号已注册");
            }
            
            // 创建新用户
            User newUser = new User();
            newUser.setPhone(phone);
            newUser.setPassword(password); // 实际应该加密存储
            newUser.setRealName(realName != null ? realName : "用户" + phone.substring(7));
            newUser.setStatus(1); // 正常状态
            newUser.setCreateTime(new java.util.Date());
            newUser.setUpdateTime(new java.util.Date());
            
            boolean result = userService.save(newUser);
            if (result) {
                // 生成token
                String token = "TOKEN_" + newUser.getUserId() + "_" + System.currentTimeMillis();
                return R.success("注册成功", token);
            } else {
                return R.failure("注册失败");
            }
        } catch (Exception e) {
            return R.failure("注册失败: " + e.getMessage());
        }
    }
}
