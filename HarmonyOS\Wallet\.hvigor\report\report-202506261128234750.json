{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "e934bf61-eb05-472f-8b00-5b1cf7ab4b37", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10086237106500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3385c5-dad2-4323-9655-50a985c28e69", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10086242604100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51dfa23c-955f-4cfb-ba5a-15a898486946", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10086243059700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b807786e-d0f5-4bc7-b361-53fc80968261", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163340821900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc4a9b92-9694-4950-8679-5b8587c910cd", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163346707500, "endTime": 10163504038200}, "additional": {"children": ["52d981e0-d374-44aa-9961-4cbab11f0725", "1d1c3de9-afa8-464e-8f46-8853cff831f7", "08d3ea4d-6d16-4b2a-9f53-9c88bb10e469", "9d09f7c5-e024-486e-b036-37fdc3101052", "039d6f09-3b51-42e8-8874-86c76fbf0354", "93fc0dd0-1027-44f3-ab75-8f66758cb357", "d7d6c5fa-8ba0-4805-a4c5-636448c21776"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52d981e0-d374-44aa-9961-4cbab11f0725", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163346712600, "endTime": 10163357370300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "38ddf765-1139-450b-9f98-82ec5a14408f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163357415500, "endTime": 10163502853900}, "additional": {"children": ["12215647-c93d-41c3-8641-2471cf88e082", "9eb619e2-1121-499b-8533-b66cb1dc4708", "8ae29181-bd94-4406-9744-7147aecb5c08", "f185ba4a-7b2e-4f57-8306-7a0ffc53c950", "6814ae9f-aa34-404b-929f-1fadf98942ba", "34064e4a-0892-4930-9a25-6ce00f9500b2", "a5a51767-c655-4ac1-a4cf-bcbc5c15a4f3", "41670794-3448-4c54-8c29-aeb0095346e7", "e02b6aaf-84ee-4bb9-bc0c-538bd0169ba8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "a88dd052-fd2f-4755-90ed-47687731ef39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08d3ea4d-6d16-4b2a-9f53-9c88bb10e469", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163502876700, "endTime": 10163504014000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "c82f2219-7aa5-47f8-b509-18e304b001db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d09f7c5-e024-486e-b036-37fdc3101052", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163504019400, "endTime": 10163504033700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "22a7bdf1-3bb9-427a-97ab-69749c79d4db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "039d6f09-3b51-42e8-8874-86c76fbf0354", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163349493200, "endTime": 10163349534900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "9c7a47fc-69a7-4560-9d90-1a7c3f5bb625"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c7a47fc-69a7-4560-9d90-1a7c3f5bb625", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163349493200, "endTime": 10163349534900}, "additional": {"logType": "info", "children": [], "durationId": "039d6f09-3b51-42e8-8874-86c76fbf0354", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "93fc0dd0-1027-44f3-ab75-8f66758cb357", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163354107400, "endTime": 10163354118400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "b0ee3905-74ac-457a-bc5d-cddb836148c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0ee3905-74ac-457a-bc5d-cddb836148c5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163354107400, "endTime": 10163354118400}, "additional": {"logType": "info", "children": [], "durationId": "93fc0dd0-1027-44f3-ab75-8f66758cb357", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "bfbb9f95-c788-412c-8ffd-e41d7787cde6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163354161400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c0359f-8d8f-49fe-be5c-30b61a7dc105", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163357204000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ddf765-1139-450b-9f98-82ec5a14408f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163346712600, "endTime": 10163357370300}, "additional": {"logType": "info", "children": [], "durationId": "52d981e0-d374-44aa-9961-4cbab11f0725", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "12215647-c93d-41c3-8641-2471cf88e082", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163362966900, "endTime": 10163362977300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "0de66722-7491-43dd-a85d-ea735f7d5bc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9eb619e2-1121-499b-8533-b66cb1dc4708", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163362992200, "endTime": 10163367095300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "22f85166-106e-4705-9504-8033de254332"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ae29181-bd94-4406-9744-7147aecb5c08", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163367106400, "endTime": 10163438212800}, "additional": {"children": ["922e7c09-29a9-4b5a-846b-74295f0956ff", "aef56161-4ce5-4c75-8ed3-e01d3a35f081", "f2c2632f-2eeb-40c5-800d-0db999a3de08"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "bf6ad70e-6469-4dd6-a877-d2b13dab49d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f185ba4a-7b2e-4f57-8306-7a0ffc53c950", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163438227000, "endTime": 10163461817200}, "additional": {"children": ["e946f71a-c892-4d58-b6aa-ef94c720db2b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "21ed6d73-5e33-49d4-92d1-fd15b227dd62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6814ae9f-aa34-404b-929f-1fadf98942ba", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163461824300, "endTime": 10163475071900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "5de1d55c-aab0-454d-9c55-fb65a5664c85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34064e4a-0892-4930-9a25-6ce00f9500b2", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163476136800, "endTime": 10163489857900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "541e03cf-fb11-46bb-ae38-fd73d98f02d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5a51767-c655-4ac1-a4cf-bcbc5c15a4f3", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163489884600, "endTime": 10163502717100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "f85ea334-54a4-4c43-aa29-a4648f7ba3cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41670794-3448-4c54-8c29-aeb0095346e7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163502747200, "endTime": 10163502842900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "bfa68d18-439b-4bd6-930b-e19e9cec7fc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0de66722-7491-43dd-a85d-ea735f7d5bc4", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163362966900, "endTime": 10163362977300}, "additional": {"logType": "info", "children": [], "durationId": "12215647-c93d-41c3-8641-2471cf88e082", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "22f85166-106e-4705-9504-8033de254332", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163362992200, "endTime": 10163367095300}, "additional": {"logType": "info", "children": [], "durationId": "9eb619e2-1121-499b-8533-b66cb1dc4708", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "922e7c09-29a9-4b5a-846b-74295f0956ff", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163367658300, "endTime": 10163367677000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ae29181-bd94-4406-9744-7147aecb5c08", "logId": "b7a22f29-da44-4aea-ae9e-81c9f4d09e96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7a22f29-da44-4aea-ae9e-81c9f4d09e96", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163367658300, "endTime": 10163367677000}, "additional": {"logType": "info", "children": [], "durationId": "922e7c09-29a9-4b5a-846b-74295f0956ff", "parent": "bf6ad70e-6469-4dd6-a877-d2b13dab49d3"}}, {"head": {"id": "aef56161-4ce5-4c75-8ed3-e01d3a35f081", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163369364200, "endTime": 10163437264500}, "additional": {"children": ["ec04f870-6165-4ce0-8ac6-8136839999e3", "04b81ae2-a40a-4a12-b521-8066fb135114"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ae29181-bd94-4406-9744-7147aecb5c08", "logId": "a1e1ff10-9ba8-4c0d-9bb8-975d2f5315e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec04f870-6165-4ce0-8ac6-8136839999e3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163369365200, "endTime": 10163375672100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aef56161-4ce5-4c75-8ed3-e01d3a35f081", "logId": "4b16de82-e367-439b-bedb-42c8c968a24a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04b81ae2-a40a-4a12-b521-8066fb135114", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163375687000, "endTime": 10163437250200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aef56161-4ce5-4c75-8ed3-e01d3a35f081", "logId": "bcc63893-3ce2-4b43-a6e7-e1f56b46c216"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5169951-6d12-4d76-9659-99f395e08a55", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163369369900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8de2f6-6f5e-4638-ac49-d2564a594b46", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163375541400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b16de82-e367-439b-bedb-42c8c968a24a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163369365200, "endTime": 10163375672100}, "additional": {"logType": "info", "children": [], "durationId": "ec04f870-6165-4ce0-8ac6-8136839999e3", "parent": "a1e1ff10-9ba8-4c0d-9bb8-975d2f5315e7"}}, {"head": {"id": "9c48b54c-d3c1-4567-905c-fe82d8ab58c1", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163375699000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ddd703-62d3-4d63-9987-4335708a089d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163381494000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "694f6e9a-02e6-4d14-9ff9-d5b8ebcc51a2", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163381606400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd49d05-45f1-44c1-abcc-36de13645c5f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163381736800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e34a753-7de4-4b00-b764-2b03d332ef40", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163381840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c26a2d-e8b5-4e39-928a-0fe8417b76ea", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163383802900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f62e1d-da25-49a0-855f-a68a16ec4ad2", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163387745300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82050229-5e6b-4030-801e-6ce9a349a867", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163396206800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f41f19-5aef-4f9a-900e-f9efb965b30a", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163417760700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf19e94-05ed-4fb1-a340-7f9a5c9fb930", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163417894900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 28}, "markType": "other"}}, {"head": {"id": "2a3b48fc-ff5f-4f72-9fa7-180940c418b8", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163417939800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 28}, "markType": "other"}}, {"head": {"id": "3f8336a0-a4e2-475f-b2b7-9b53c9932538", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163436953900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5225ea5-1f94-4726-84f6-6ab98e7e865e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163437083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1ecdaa-dbbe-48ae-ba92-477df78f1560", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163437146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ef121b2-14fb-4e40-9a19-37c76ef8ff4b", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163437193000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcc63893-3ce2-4b43-a6e7-e1f56b46c216", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163375687000, "endTime": 10163437250200}, "additional": {"logType": "info", "children": [], "durationId": "04b81ae2-a40a-4a12-b521-8066fb135114", "parent": "a1e1ff10-9ba8-4c0d-9bb8-975d2f5315e7"}}, {"head": {"id": "a1e1ff10-9ba8-4c0d-9bb8-975d2f5315e7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163369364200, "endTime": 10163437264500}, "additional": {"logType": "info", "children": ["4b16de82-e367-439b-bedb-42c8c968a24a", "bcc63893-3ce2-4b43-a6e7-e1f56b46c216"], "durationId": "aef56161-4ce5-4c75-8ed3-e01d3a35f081", "parent": "bf6ad70e-6469-4dd6-a877-d2b13dab49d3"}}, {"head": {"id": "f2c2632f-2eeb-40c5-800d-0db999a3de08", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163438062700, "endTime": 10163438194800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ae29181-bd94-4406-9744-7147aecb5c08", "logId": "2f0ef935-7bba-41ba-a792-66455d62136b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f0ef935-7bba-41ba-a792-66455d62136b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163438062700, "endTime": 10163438194800}, "additional": {"logType": "info", "children": [], "durationId": "f2c2632f-2eeb-40c5-800d-0db999a3de08", "parent": "bf6ad70e-6469-4dd6-a877-d2b13dab49d3"}}, {"head": {"id": "bf6ad70e-6469-4dd6-a877-d2b13dab49d3", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163367106400, "endTime": 10163438212800}, "additional": {"logType": "info", "children": ["b7a22f29-da44-4aea-ae9e-81c9f4d09e96", "a1e1ff10-9ba8-4c0d-9bb8-975d2f5315e7", "2f0ef935-7bba-41ba-a792-66455d62136b"], "durationId": "8ae29181-bd94-4406-9744-7147aecb5c08", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "e946f71a-c892-4d58-b6aa-ef94c720db2b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163438896500, "endTime": 10163461804300}, "additional": {"children": ["96e2716c-cb15-46f9-a3cc-809f3c462e36", "27965ce7-8f5f-4f7b-af01-032c24ba72bf", "d36af172-a86f-463e-9edd-ed88688fe15c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f185ba4a-7b2e-4f57-8306-7a0ffc53c950", "logId": "ba61e844-e937-49e3-9fca-3479606d439c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96e2716c-cb15-46f9-a3cc-809f3c462e36", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163442102200, "endTime": 10163442603900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e946f71a-c892-4d58-b6aa-ef94c720db2b", "logId": "0ba5890e-ba15-471b-b6ab-94c8dfb97275"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ba5890e-ba15-471b-b6ab-94c8dfb97275", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163442102200, "endTime": 10163442603900}, "additional": {"logType": "info", "children": [], "durationId": "96e2716c-cb15-46f9-a3cc-809f3c462e36", "parent": "ba61e844-e937-49e3-9fca-3479606d439c"}}, {"head": {"id": "27965ce7-8f5f-4f7b-af01-032c24ba72bf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163444524400, "endTime": 10163459927600}, "additional": {"children": ["f26cefa2-aad4-4563-acbd-a066879546f7", "3879678e-5ea4-4484-8200-f46acc10a4b8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e946f71a-c892-4d58-b6aa-ef94c720db2b", "logId": "d47abe5d-ba48-42e4-b366-fd2623abca38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f26cefa2-aad4-4563-acbd-a066879546f7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163444525600, "endTime": 10163448215400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27965ce7-8f5f-4f7b-af01-032c24ba72bf", "logId": "435c4ecd-90fb-4c6c-b010-48a39fd78464"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3879678e-5ea4-4484-8200-f46acc10a4b8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163448235200, "endTime": 10163459913200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27965ce7-8f5f-4f7b-af01-032c24ba72bf", "logId": "7724b98b-d469-4886-bb5c-8c173a01a997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7937405-b4d5-4de9-8f69-e12ae1f3417a", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163444530700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1255cab-ea2b-4924-8e8f-cd2fbdcedd73", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163448081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435c4ecd-90fb-4c6c-b010-48a39fd78464", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163444525600, "endTime": 10163448215400}, "additional": {"logType": "info", "children": [], "durationId": "f26cefa2-aad4-4563-acbd-a066879546f7", "parent": "d47abe5d-ba48-42e4-b366-fd2623abca38"}}, {"head": {"id": "b4abc132-e592-466b-bb96-a182201aef58", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163448248300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3925893b-23a2-4e0f-a413-3944a400b4fd", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163455734000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a24b2448-da9b-4ee2-b6a9-76feeba6d13d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163455865900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74c4449b-aa2c-4897-8fe3-ddb2df495ec7", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163456067800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d1d09e6-40fb-4a51-a112-28671553f815", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163456220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "366a7b13-6066-4cfc-ab86-e56b591b65b4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163456292500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d80cfa-7f71-4326-b577-68d3e37fc220", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163456355200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8588f21f-e8d0-41e3-969b-609ee615eea8", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163456440200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60e76a6-5291-4a4e-8fb4-ca8164034dca", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163459431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf841c1-e200-41c4-b240-0331b1763c22", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163459682000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f8c33e4-5423-4bab-9283-be51a0263c9b", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163459765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f13e0a-61b3-4239-a210-f89ee129fa3b", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163459848600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7724b98b-d469-4886-bb5c-8c173a01a997", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163448235200, "endTime": 10163459913200}, "additional": {"logType": "info", "children": [], "durationId": "3879678e-5ea4-4484-8200-f46acc10a4b8", "parent": "d47abe5d-ba48-42e4-b366-fd2623abca38"}}, {"head": {"id": "d47abe5d-ba48-42e4-b366-fd2623abca38", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163444524400, "endTime": 10163459927600}, "additional": {"logType": "info", "children": ["435c4ecd-90fb-4c6c-b010-48a39fd78464", "7724b98b-d469-4886-bb5c-8c173a01a997"], "durationId": "27965ce7-8f5f-4f7b-af01-032c24ba72bf", "parent": "ba61e844-e937-49e3-9fca-3479606d439c"}}, {"head": {"id": "d36af172-a86f-463e-9edd-ed88688fe15c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163461773600, "endTime": 10163461788100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e946f71a-c892-4d58-b6aa-ef94c720db2b", "logId": "d797adff-cc63-49aa-854b-cfaa53f605ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d797adff-cc63-49aa-854b-cfaa53f605ae", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163461773600, "endTime": 10163461788100}, "additional": {"logType": "info", "children": [], "durationId": "d36af172-a86f-463e-9edd-ed88688fe15c", "parent": "ba61e844-e937-49e3-9fca-3479606d439c"}}, {"head": {"id": "ba61e844-e937-49e3-9fca-3479606d439c", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163438896500, "endTime": 10163461804300}, "additional": {"logType": "info", "children": ["0ba5890e-ba15-471b-b6ab-94c8dfb97275", "d47abe5d-ba48-42e4-b366-fd2623abca38", "d797adff-cc63-49aa-854b-cfaa53f605ae"], "durationId": "e946f71a-c892-4d58-b6aa-ef94c720db2b", "parent": "21ed6d73-5e33-49d4-92d1-fd15b227dd62"}}, {"head": {"id": "21ed6d73-5e33-49d4-92d1-fd15b227dd62", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163438227000, "endTime": 10163461817200}, "additional": {"logType": "info", "children": ["ba61e844-e937-49e3-9fca-3479606d439c"], "durationId": "f185ba4a-7b2e-4f57-8306-7a0ffc53c950", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "8929d286-3be1-4282-aeec-fec624939289", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163474425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18011e67-1560-48e0-bf0b-4c2b872f5951", "name": "hvigorfile, resolve hvigorfile dependencies in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163474963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de1d55c-aab0-454d-9c55-fb65a5664c85", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163461824300, "endTime": 10163475071900}, "additional": {"logType": "info", "children": [], "durationId": "6814ae9f-aa34-404b-929f-1fadf98942ba", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "e02b6aaf-84ee-4bb9-bc0c-538bd0169ba8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163475931500, "endTime": 10163476122800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "logId": "c735ade6-7778-4ff5-a270-bb5a815254fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3b61816-069d-4d99-bea8-30c39a185bb1", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163475955000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c735ade6-7778-4ff5-a270-bb5a815254fc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163475931500, "endTime": 10163476122800}, "additional": {"logType": "info", "children": [], "durationId": "e02b6aaf-84ee-4bb9-bc0c-538bd0169ba8", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "ddac71e0-80df-4bd9-93d3-3d524b7cc8ea", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163477591800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7851275-8aa4-4eeb-b670-76fa0a189b66", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163488935600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541e03cf-fb11-46bb-ae38-fd73d98f02d8", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163476136800, "endTime": 10163489857900}, "additional": {"logType": "info", "children": [], "durationId": "34064e4a-0892-4930-9a25-6ce00f9500b2", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "f109b09b-d44c-457e-9f11-65fa59704be2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163489910600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27b120f3-44ef-4623-8e6b-f2d735b97f8a", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163495010200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbdffde9-9897-4856-b1d0-4f2d9ded8d56", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163495110600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82113f6-bbf4-46c8-9641-74c5c23f4844", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163495371800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51edd9a0-a566-48dd-a04b-c4f4fcc724f6", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163500017400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a5bfd0-3d99-47a5-a631-033a12ba284a", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163500121600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f85ea334-54a4-4c43-aa29-a4648f7ba3cb", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163489884600, "endTime": 10163502717100}, "additional": {"logType": "info", "children": [], "durationId": "a5a51767-c655-4ac1-a4cf-bcbc5c15a4f3", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "b6bdf51b-9d84-4646-94ba-1cdc8f489af1", "name": "Configuration phase cost:140 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163502770300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa68d18-439b-4bd6-930b-e19e9cec7fc4", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163502747200, "endTime": 10163502842900}, "additional": {"logType": "info", "children": [], "durationId": "41670794-3448-4c54-8c29-aeb0095346e7", "parent": "a88dd052-fd2f-4755-90ed-47687731ef39"}}, {"head": {"id": "a88dd052-fd2f-4755-90ed-47687731ef39", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163357415500, "endTime": 10163502853900}, "additional": {"logType": "info", "children": ["0de66722-7491-43dd-a85d-ea735f7d5bc4", "22f85166-106e-4705-9504-8033de254332", "bf6ad70e-6469-4dd6-a877-d2b13dab49d3", "21ed6d73-5e33-49d4-92d1-fd15b227dd62", "5de1d55c-aab0-454d-9c55-fb65a5664c85", "541e03cf-fb11-46bb-ae38-fd73d98f02d8", "f85ea334-54a4-4c43-aa29-a4648f7ba3cb", "bfa68d18-439b-4bd6-930b-e19e9cec7fc4", "c735ade6-7778-4ff5-a270-bb5a815254fc"], "durationId": "1d1c3de9-afa8-464e-8f46-8853cff831f7", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "d7d6c5fa-8ba0-4805-a4c5-636448c21776", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163503989600, "endTime": 10163504002700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc4a9b92-9694-4950-8679-5b8587c910cd", "logId": "6085ced8-a1eb-45f7-8d4f-383aad4a3385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6085ced8-a1eb-45f7-8d4f-383aad4a3385", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163503989600, "endTime": 10163504002700}, "additional": {"logType": "info", "children": [], "durationId": "d7d6c5fa-8ba0-4805-a4c5-636448c21776", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "c82f2219-7aa5-47f8-b509-18e304b001db", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163502876700, "endTime": 10163504014000}, "additional": {"logType": "info", "children": [], "durationId": "08d3ea4d-6d16-4b2a-9f53-9c88bb10e469", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "22a7bdf1-3bb9-427a-97ab-69749c79d4db", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163504019400, "endTime": 10163504033700}, "additional": {"logType": "info", "children": [], "durationId": "9d09f7c5-e024-486e-b036-37fdc3101052", "parent": "ad272bdb-6b08-40ef-be07-6c61a18fc61a"}}, {"head": {"id": "ad272bdb-6b08-40ef-be07-6c61a18fc61a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163346707500, "endTime": 10163504038200}, "additional": {"logType": "info", "children": ["38ddf765-1139-450b-9f98-82ec5a14408f", "a88dd052-fd2f-4755-90ed-47687731ef39", "c82f2219-7aa5-47f8-b509-18e304b001db", "22a7bdf1-3bb9-427a-97ab-69749c79d4db", "9c7a47fc-69a7-4560-9d90-1a7c3f5bb625", "b0ee3905-74ac-457a-bc5d-cddb836148c5", "6085ced8-a1eb-45f7-8d4f-383aad4a3385"], "durationId": "dc4a9b92-9694-4950-8679-5b8587c910cd"}}, {"head": {"id": "d5ebc163-f7fe-4789-842d-c3222b0eb406", "name": "Configuration task cost before running: 161 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163504171200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28fc387f-81e4-409c-a682-fbf713d10073", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163508883100, "endTime": 10163539349100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json' has been changed."], "detailId": "19fbda0c-fbdc-4823-b82b-1ae97b89d8b8", "logId": "25acbcfd-918d-4f88-a421-68a48c07686c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19fbda0c-fbdc-4823-b82b-1ae97b89d8b8", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163505501600}, "additional": {"logType": "detail", "children": [], "durationId": "28fc387f-81e4-409c-a682-fbf713d10073"}}, {"head": {"id": "cda5a563-2528-438b-84c0-b7e9d01b3bd8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163506018200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6151cfc-6c7c-4cc2-b8f9-7615c21c1793", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163506104500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b75b47-4f46-4adf-acd3-2039bc3504c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163506157700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "808c6188-dd9f-4f19-8b97-fdf88b463214", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163508895000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74fbcc9d-c778-4b62-a3c6-af6b708ccbab", "name": "entry:default@PreBuild is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163515204000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a09f5e77-5011-4c25-b787-002f65d4a2f8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163515354600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c7beb6d-9879-4216-89eb-d906e020a065", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163515452300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12667333-c265-4e1b-a553-11c6368dfe23", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163515506300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ee23c7-075d-49bd-b149-2ab2d75f5acb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163515553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c2bf827-e58d-4a83-9a87-4379862725b5", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163537800400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c76f9687-a31e-4167-978d-b124f28cc26a", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\HarmonyOS\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163538472800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "739bbf3e-bb9e-4993-8620-9c5216edeed8", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\HarmonyOS\\\\DevEco Studio\\\\tools\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163538635500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6805eb60-6e32-46f0-b2f2-561df917dfdb", "name": "entry : default@PreBuild cost memory -0.6684112548828125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163539128100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33bfcc74-e1b8-4715-89e7-9cf864481f5a", "name": "runTaskFromQueue task cost before running: 196 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163539241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25acbcfd-918d-4f88-a421-68a48c07686c", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163508883100, "endTime": 10163539349100, "totalTime": 30333500}, "additional": {"logType": "info", "children": [], "durationId": "28fc387f-81e4-409c-a682-fbf713d10073"}}, {"head": {"id": "118e0701-b9db-4161-b9b9-c34ca053a5c9", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163545175500, "endTime": 10163548172800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b581bf43-c12b-4f26-a1bb-769ded1a95db", "logId": "3532be45-ac4b-4125-a7ef-739dd263ae66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b581bf43-c12b-4f26-a1bb-769ded1a95db", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163543862900}, "additional": {"logType": "detail", "children": [], "durationId": "118e0701-b9db-4161-b9b9-c34ca053a5c9"}}, {"head": {"id": "8ef005de-41c3-4852-a2f3-2df433dd713e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163544362600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c94986f1-4326-445d-8f94-bece11f42190", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163544446700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b3c41b-c355-4f81-a7c5-d9faff052275", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163544496200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea6bc6b-fdf6-4ca7-a473-02c9f7476664", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163545184800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d996f5f-f0c4-4131-bb12-86a9ae82160d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163548010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52dc0107-8b59-45cf-8ae5-e7151c737470", "name": "entry : default@MergeProfile cost memory 0.13349151611328125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163548107200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3532be45-ac4b-4125-a7ef-739dd263ae66", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163545175500, "endTime": 10163548172800}, "additional": {"logType": "info", "children": [], "durationId": "118e0701-b9db-4161-b9b9-c34ca053a5c9"}}, {"head": {"id": "13d52331-61c5-4102-8dda-8a5aa56f6773", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163551194600, "endTime": 10163553451600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3e52a1b5-aedd-46c7-953c-9cd04f55841f", "logId": "7bfadf39-9762-4205-8100-5c3decdc4127"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e52a1b5-aedd-46c7-953c-9cd04f55841f", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163549815000}, "additional": {"logType": "detail", "children": [], "durationId": "13d52331-61c5-4102-8dda-8a5aa56f6773"}}, {"head": {"id": "51cdb0a7-190f-441d-8d60-cf904d331164", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163550312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70df1b0-0624-4b6f-bece-3bcc9e6e81bf", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163550401000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c914aef0-526a-4bd4-ad6f-54dca52b8dc6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163550457800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0cf961-9c79-48d4-8c99-190eee0d0e8f", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163551203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b144e089-d500-4207-aac1-1ce1fc17ce5a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163552143300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f8d6bc-421b-432f-b2c0-771c081dc784", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163553292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf59a17-03b7-4ea0-8893-42dea6f20f25", "name": "entry : default@CreateBuildProfile cost memory 0.1010589599609375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163553385600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bfadf39-9762-4205-8100-5c3decdc4127", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163551194600, "endTime": 10163553451600}, "additional": {"logType": "info", "children": [], "durationId": "13d52331-61c5-4102-8dda-8a5aa56f6773"}}, {"head": {"id": "1b82e0c1-d3c1-4595-90db-54ee7326247d", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556111200, "endTime": 10163556506200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4c5ae7fc-ae8b-4937-95fc-f8b600f0cd7f", "logId": "e6142515-ae65-4b16-9a70-42b7835d3e63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c5ae7fc-ae8b-4937-95fc-f8b600f0cd7f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163554838700}, "additional": {"logType": "detail", "children": [], "durationId": "1b82e0c1-d3c1-4595-90db-54ee7326247d"}}, {"head": {"id": "ecf4724d-044c-4eab-8856-fd19e2e9b2af", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163555309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb53e92-5bbd-4c2b-8e1b-a43468f8f37f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163555384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca861969-c8d6-4c54-bfe0-5eccd8032d91", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163555433500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43098217-2b6c-4ae4-be27-0c8f7158cd1b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f535037-fc1e-4581-82cb-1ab5599d909f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd93ae6-62ed-47a6-baf4-502966499b84", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d75a3e-c8b2-41fc-bd47-4a4a342657ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556318500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010ef9a5-672a-4261-a127-1e244a970ec1", "name": "entry : default@PreCheckSyscap cost memory 0.05040740966796875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34639a79-80da-423c-ad29-91207ee14566", "name": "runTaskFromQueue task cost before running: 213 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556456900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6142515-ae65-4b16-9a70-42b7835d3e63", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163556111200, "endTime": 10163556506200, "totalTime": 327200}, "additional": {"logType": "info", "children": [], "durationId": "1b82e0c1-d3c1-4595-90db-54ee7326247d"}}, {"head": {"id": "9bda0018-93bb-4fe6-9fa5-bc51482b1d0a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163567294700, "endTime": 10163568359300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "20f8dc14-f8c1-4f84-ac9b-cec49fc357a3", "logId": "a173e7a8-cfd9-4f84-84ca-05742deb7d65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20f8dc14-f8c1-4f84-ac9b-cec49fc357a3", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163557904600}, "additional": {"logType": "detail", "children": [], "durationId": "9bda0018-93bb-4fe6-9fa5-bc51482b1d0a"}}, {"head": {"id": "4f28924e-c0a3-487a-8460-f6d76f7c530f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163558382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ae857f-8fd3-468d-925a-3ae21dbb0efa", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163558475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57c485d9-df6a-424f-b811-1f9765330389", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163558526500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "989cb22d-dd53-4485-93b2-d38ba34906ec", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163567311500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae61d94e-3b6f-4b1d-a042-b32ed60273fe", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163567557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a903dc3-9417-43a6-a8b7-ecedf341ed86", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163568194000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be6f938-4754-4150-ab0a-b50234332171", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0698089599609375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163568292100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a173e7a8-cfd9-4f84-84ca-05742deb7d65", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163567294700, "endTime": 10163568359300}, "additional": {"logType": "info", "children": [], "durationId": "9bda0018-93bb-4fe6-9fa5-bc51482b1d0a"}}, {"head": {"id": "361bd588-ba5d-437f-a54a-e8dc9bde6abe", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163571740300, "endTime": 10163572841200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1d147c79-6c73-47de-9bc8-47015a7492e3", "logId": "0950271c-80fa-4164-b41c-a344080948a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d147c79-6c73-47de-9bc8-47015a7492e3", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163569966000}, "additional": {"logType": "detail", "children": [], "durationId": "361bd588-ba5d-437f-a54a-e8dc9bde6abe"}}, {"head": {"id": "f35028df-b01c-4ddc-9e08-586599a7cc1f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163570456700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec926d23-ef47-45a8-97f3-38093f948210", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163570542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dca046a-3575-4ac1-a68c-08b0b3a0bfe5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163570604400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f978fb3d-747a-42e2-849e-11c36d3173f2", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163571750500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9027fe-f6d1-4692-8b93-59ae0a5502a9", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163572685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44ba44d-02f6-420f-b3f4-235ebb954805", "name": "entry : default@ProcessProfile cost memory 0.05738067626953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163572775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0950271c-80fa-4164-b41c-a344080948a7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163571740300, "endTime": 10163572841200}, "additional": {"logType": "info", "children": [], "durationId": "361bd588-ba5d-437f-a54a-e8dc9bde6abe"}}, {"head": {"id": "4da95357-ead1-4db9-938d-07bc4697e62a", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163576674400, "endTime": 10163582576500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1a25dd17-037d-4fcb-bd8e-56496dc25c6b", "logId": "d02fec27-7a5d-4a7f-8de2-689c4267e78a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a25dd17-037d-4fcb-bd8e-56496dc25c6b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163574382200}, "additional": {"logType": "detail", "children": [], "durationId": "4da95357-ead1-4db9-938d-07bc4697e62a"}}, {"head": {"id": "68b662a9-d1ff-44f1-b266-4fc9685b08ab", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163574883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe7031b-cf21-4445-bbb0-ac338d8ae6c8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163574967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49792c5b-d5e8-4d3a-bad2-315c9e76db76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163575019200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f3ce291-070f-4e0b-96d0-77340fb7fe5b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163576690800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfbfe21-1161-4844-b199-697000ea5b82", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163582367700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe286748-f705-4284-af9c-a4b55f5adf73", "name": "entry : default@ProcessRouterMap cost memory 0.2286529541015625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163582501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d02fec27-7a5d-4a7f-8de2-689c4267e78a", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163576674400, "endTime": 10163582576500}, "additional": {"logType": "info", "children": [], "durationId": "4da95357-ead1-4db9-938d-07bc4697e62a"}}, {"head": {"id": "0eaa9428-7109-4ec6-8896-876e331bfccb", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163589307100, "endTime": 10163592300700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "dbf9d14a-6e6a-49a2-9be3-e844eeacc006", "logId": "bb0c113c-e256-415e-813d-005054f2ea4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbf9d14a-6e6a-49a2-9be3-e844eeacc006", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163585415100}, "additional": {"logType": "detail", "children": [], "durationId": "0eaa9428-7109-4ec6-8896-876e331bfccb"}}, {"head": {"id": "19d4fe35-a2e1-49f3-8f38-f64845260f2b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163585920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29050414-b0f1-4968-8215-0ad080f5e094", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163586004700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f063adde-82c0-438c-902f-5ea36efec0ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163586055400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2978162-2443-4a67-b3f8-69cb358277ca", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163586926600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5abf58a9-004b-4168-8924-63dbca51e988", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163590673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378fb95d-b35f-4ac6-a486-03b6c2961aba", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163590804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14783117-89df-4ab6-aead-b5dc9f430c07", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163590859800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2435ca65-cc24-43a1-8d82-e17dc931b7e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163590903300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "406f9b84-f630-40a8-b9bc-da47a39a843d", "name": "entry : default@PreviewProcessResource cost memory 0.09444427490234375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163590976100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb91aaf-7f02-4292-b054-d55ea0c1b8e5", "name": "runTaskFromQueue task cost before running: 249 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163592218000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb0c113c-e256-415e-813d-005054f2ea4c", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163589307100, "endTime": 10163592300700, "totalTime": 1727300}, "additional": {"logType": "info", "children": [], "durationId": "0eaa9428-7109-4ec6-8896-876e331bfccb"}}, {"head": {"id": "96cb3f59-1f55-496a-9dcc-49976495d061", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163600455400, "endTime": 10163619389600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9f73beb2-de61-478c-a566-10392de0bd34", "logId": "502069f3-39a0-4184-ba3a-d20f6f4e527b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f73beb2-de61-478c-a566-10392de0bd34", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163595895200}, "additional": {"logType": "detail", "children": [], "durationId": "96cb3f59-1f55-496a-9dcc-49976495d061"}}, {"head": {"id": "d17b265b-3f19-4058-99cf-585dd9361427", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163596461500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a522ddd-0dbe-4a34-9cdb-68630e46c6ca", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163596565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54365dc9-47e2-4933-8cef-f3289d18fd14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163596629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0c5e45-7434-4c2b-a474-d7891d612907", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163600467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3924a09-2c80-4851-9131-bf2dceeb8489", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163619191700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b745409-aad3-4500-b2ea-f85eff9e75d9", "name": "entry : default@GenerateLoaderJson cost memory 0.**********03125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163619319600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502069f3-39a0-4184-ba3a-d20f6f4e527b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163600455400, "endTime": 10163619389600}, "additional": {"logType": "info", "children": [], "durationId": "96cb3f59-1f55-496a-9dcc-49976495d061"}}, {"head": {"id": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163630738700, "endTime": 10164221057500}, "additional": {"children": ["3df286dc-235b-48a2-84d2-fb2e2131160f", "a081ea80-3d3e-41bd-b095-b4133b648fff", "7f9bd464-ab29-4b53-90a0-d71c225677aa", "e65cc0a6-9a14-441c-9912-be4df95cefb7", "447846d0-12b9-4922-ac1c-ca80f2a1a416"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources' has been changed."], "detailId": "718a531c-99d3-4034-8e91-06f3b6bc75bd", "logId": "bfd3879e-4730-48b7-93c3-e37ae55e4262"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "718a531c-99d3-4034-8e91-06f3b6bc75bd", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163626212700}, "additional": {"logType": "detail", "children": [], "durationId": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377"}}, {"head": {"id": "564e5839-1072-45d9-a9ea-9ae4c0881484", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163626738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1783e1e-5c4e-4ce9-b09b-4755cc09b3d7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163626846100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15633f6-3de1-4fe8-b969-c641b223d224", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163626901700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bca715-96b9-4649-86f9-c471d60ec566", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163627773900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e15dcf-3ca2-496a-9dc9-9319eb0c8051", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163630769000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db8d78d-8f00-4c17-b1c3-791acd293ba3", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163648125600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e991c220-bf20-4115-98dc-03758e54d288", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163648321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df286dc-235b-48a2-84d2-fb2e2131160f", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163649387500, "endTime": 10163674040400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377", "logId": "ede8a999-bcf6-48e3-b9fd-b4d2ee451b70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ede8a999-bcf6-48e3-b9fd-b4d2ee451b70", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163649387500, "endTime": 10163674040400}, "additional": {"logType": "info", "children": [], "durationId": "3df286dc-235b-48a2-84d2-fb2e2131160f", "parent": "bfd3879e-4730-48b7-93c3-e37ae55e4262"}}, {"head": {"id": "16bd7470-b7aa-4351-84a1-e3827e38c36e", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163674336500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a081ea80-3d3e-41bd-b095-b4133b648fff", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163675169800, "endTime": 10163788139700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377", "logId": "6ff6cccc-d553-47b5-9de0-bc498da76407"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5df2bf0-d9c2-4682-97a6-a4b316a652e0", "name": "current process  memoryUsage: {\n  rss: 167403520,\n  heapTotal: 116744192,\n  heapUsed: 109096576,\n  external: 3117374,\n  arrayBuffers: 111271\n} os memoryUsage :11.091697692871094", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163676017400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77bcda6f-4730-4072-aa41-5b9538a31ab9", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163786275700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff6cccc-d553-47b5-9de0-bc498da76407", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163675169800, "endTime": 10163788139700}, "additional": {"logType": "info", "children": [], "durationId": "a081ea80-3d3e-41bd-b095-b4133b648fff", "parent": "bfd3879e-4730-48b7-93c3-e37ae55e4262"}}, {"head": {"id": "c8d81253-4df6-4efb-8d33-c3c78a43e123", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163788290200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f9bd464-ab29-4b53-90a0-d71c225677aa", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163789204400, "endTime": 10163917886500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377", "logId": "f4eeb0a2-e89a-4262-b7bc-788a0df87106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "905b5494-7c98-40b0-b041-2ee6b3980ec9", "name": "current process  memoryUsage: {\n  rss: 167444480,\n  heapTotal: 116744192,\n  heapUsed: 109368664,\n  external: 3117500,\n  arrayBuffers: 111412\n} os memoryUsage :11.108829498291016", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163790077900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c220d193-ffcb-4f94-86e8-c14f481d03af", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163916021200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4eeb0a2-e89a-4262-b7bc-788a0df87106", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163789204400, "endTime": 10163917886500}, "additional": {"logType": "info", "children": [], "durationId": "7f9bd464-ab29-4b53-90a0-d71c225677aa", "parent": "bfd3879e-4730-48b7-93c3-e37ae55e4262"}}, {"head": {"id": "0a81b7a6-c46f-4a82-8bbc-057138c39795", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163918038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65cc0a6-9a14-441c-9912-be4df95cefb7", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163918941400, "endTime": 10164030692100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377", "logId": "7d6ec2f5-c232-4b01-9608-aea038013a7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baa7e29d-60bc-4b6d-a9da-7fbfb8f81826", "name": "current process  memoryUsage: {\n  rss: 167481344,\n  heapTotal: 116744192,\n  heapUsed: 109634624,\n  external: 3125818,\n  arrayBuffers: 119794\n} os memoryUsage :11.115657806396484", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163919753100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "847cea83-e400-410b-b924-958bf42d71ec", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164028507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6ec2f5-c232-4b01-9608-aea038013a7c", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163918941400, "endTime": 10164030692100}, "additional": {"logType": "info", "children": [], "durationId": "e65cc0a6-9a14-441c-9912-be4df95cefb7", "parent": "bfd3879e-4730-48b7-93c3-e37ae55e4262"}}, {"head": {"id": "90185795-6ee2-46e7-8b74-f2e544e600e6", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164031038800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447846d0-12b9-4922-ac1c-ca80f2a1a416", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164032663800, "endTime": 10164219588600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377", "logId": "c6b94917-8e46-48d5-8770-af2be0a847f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8bdec76-b1b1-472d-b767-efa1b06b7f5c", "name": "current process  memoryUsage: {\n  rss: 167575552,\n  heapTotal: 117006336,\n  heapUsed: 108126592,\n  external: 3107886,\n  arrayBuffers: 102865\n} os memoryUsage :11.123062133789062", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164033729400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d47614-ae13-48d9-8d1e-f1db7dfcbf47", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164215836900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b94917-8e46-48d5-8770-af2be0a847f9", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164032663800, "endTime": 10164219588600}, "additional": {"logType": "info", "children": [], "durationId": "447846d0-12b9-4922-ac1c-ca80f2a1a416", "parent": "bfd3879e-4730-48b7-93c3-e37ae55e4262"}}, {"head": {"id": "9118c1d5-b07e-4060-9de8-93a2b5da3bfb", "name": "entry : default@PreviewCompileResource cost memory 0.5313568115234375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164220762400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5901914b-e6b8-4518-88f2-17311668e1f1", "name": "runTaskFromQueue task cost before running: 877 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164220969200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd3879e-4730-48b7-93c3-e37ae55e4262", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163630738700, "endTime": 10164221057500, "totalTime": 590165300}, "additional": {"logType": "info", "children": ["ede8a999-bcf6-48e3-b9fd-b4d2ee451b70", "6ff6cccc-d553-47b5-9de0-bc498da76407", "f4eeb0a2-e89a-4262-b7bc-788a0df87106", "7d6ec2f5-c232-4b01-9608-aea038013a7c", "c6b94917-8e46-48d5-8770-af2be0a847f9"], "durationId": "a638ef81-7cb8-4c0d-9c1b-4e2e2c603377"}}, {"head": {"id": "79de1e7a-a26a-44de-97af-1209df7af003", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226497600, "endTime": 10164227310500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "210675a8-5cb3-4515-b31e-0cdea52bfdce", "logId": "2c135ead-6a6c-40cb-9132-19471e50c3cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "210675a8-5cb3-4515-b31e-0cdea52bfdce", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164225126700}, "additional": {"logType": "detail", "children": [], "durationId": "79de1e7a-a26a-44de-97af-1209df7af003"}}, {"head": {"id": "460e0e42-ba1d-48e2-96d0-9c77f7936de4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164225981700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1efce88-0e52-4b5b-80ec-155191a5cbd9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64bf8fa3-9b9a-46c8-9d3e-fa02aa195d81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226303800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "601f25a2-3e7e-4f32-b3fc-a6eacb5c3b28", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c91812-b54c-492d-9d78-741d5e6290d8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226730500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc37e2a3-f597-4fff-a5fa-66146ae1e149", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f807af-24a3-4a51-be9b-db7a13e3b708", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226921300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "441b9d1b-6f01-4fa6-b19b-370117e0cafb", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164227065500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68d4980a-1066-419c-a6a2-cdfe9483bf8b", "name": "runTaskFromQueue task cost before running: 884 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164227213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c135ead-6a6c-40cb-9132-19471e50c3cd", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164226497600, "endTime": 10164227310500, "totalTime": 690100}, "additional": {"logType": "info", "children": [], "durationId": "79de1e7a-a26a-44de-97af-1209df7af003"}}, {"head": {"id": "f2b1d08d-fd50-448c-825b-bdf92a78175d", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164231824700, "endTime": 10164246643800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile' has been changed."], "detailId": "cfc4760a-bfd6-4aa6-8530-06ce9c835226", "logId": "fb8bac51-7ceb-4522-bf5b-de6072f43a4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfc4760a-bfd6-4aa6-8530-06ce9c835226", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164230105300}, "additional": {"logType": "detail", "children": [], "durationId": "f2b1d08d-fd50-448c-825b-bdf92a78175d"}}, {"head": {"id": "33227c87-8277-4521-bf6d-c036c9d21da4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164230738400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479b0924-4b62-4ec6-8eec-5ed117663ab4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164230863100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebbe9266-2c68-4443-93a6-1c8b5aa731b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164230925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57eda0af-3f4b-4970-80c5-8ee9f552a916", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164231844300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e49590a4-360e-407e-8562-668b8a3ef222", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164234013000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56e26799-3375-41c8-aded-e1e6f3f226bd", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164234203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8bd5233-b5ed-4ea3-828e-a2247c4d2d67", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164234343300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2994df14-1878-4e2d-85e8-55fd8e474250", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164234434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d96f8a86-5143-44dd-850f-0f7bba060023", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164234534300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2baf75e-20f7-4b47-b593-2846e0184b95", "name": "entry : default@CopyPreviewProfile cost memory 0.24517059326171875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164246214200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f1900e1-6f85-4f47-9b02-6eda68caaad2", "name": "runTaskFromQueue task cost before running: 903 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164246513900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb8bac51-7ceb-4522-bf5b-de6072f43a4b", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164231824700, "endTime": 10164246643800, "totalTime": 14630300}, "additional": {"logType": "info", "children": [], "durationId": "f2b1d08d-fd50-448c-825b-bdf92a78175d"}}, {"head": {"id": "813804f9-7196-49d1-8963-d0fb21c5bf79", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253130600, "endTime": 10164253826800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "52a22ec2-174d-4c81-ba88-d45834916c38", "logId": "5e438a10-18e2-4255-b985-db0b242999b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52a22ec2-174d-4c81-ba88-d45834916c38", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164250633700}, "additional": {"logType": "detail", "children": [], "durationId": "813804f9-7196-49d1-8963-d0fb21c5bf79"}}, {"head": {"id": "6bce57a7-e542-495b-93d9-bc541cf46030", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164251537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451b9aba-eda4-4b52-bf15-70a4de6c03da", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164251752500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1b8afd-4790-45b3-8c99-97568c2e06be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164251870300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ef4d75-372c-4619-b9cb-1503bbeb4a94", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253151100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4258c723-c39f-4396-accf-7223cb9ee912", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd34e67-1a2b-4ac9-aa61-2e90345bb20f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87a0bca9-defc-4267-826f-f49dd5fa7033", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253515900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c67b5188-9f06-4e5b-90f3-957018b92cf1", "name": "entry : default@ReplacePreviewerPage cost memory 0.0514678955078125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db7387b-88b8-47b1-9c40-45cbe475efe7", "name": "runTaskFromQueue task cost before running: 910 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253758800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e438a10-18e2-4255-b985-db0b242999b3", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164253130600, "endTime": 10164253826800, "totalTime": 599300}, "additional": {"logType": "info", "children": [], "durationId": "813804f9-7196-49d1-8963-d0fb21c5bf79"}}, {"head": {"id": "8996d742-8517-40f7-a961-e8f69fe01f9c", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164256872000, "endTime": 10164257514700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f9d5e70d-8450-436b-b13e-9ea652b19a83", "logId": "9bcb3040-d9d3-4184-9a64-6cfc9f44fb40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9d5e70d-8450-436b-b13e-9ea652b19a83", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164256743100}, "additional": {"logType": "detail", "children": [], "durationId": "8996d742-8517-40f7-a961-e8f69fe01f9c"}}, {"head": {"id": "0990a301-ca01-4a85-a90f-ce261cde459d", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164256892200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "135ad35e-880c-4ec7-b610-094f16bc4868", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164257193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9261be25-2873-4562-8197-66b9c18de6a5", "name": "runTaskFromQueue task cost before running: 914 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164257395600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bcb3040-d9d3-4184-9a64-6cfc9f44fb40", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164256872000, "endTime": 10164257514700, "totalTime": 467600}, "additional": {"logType": "info", "children": [], "durationId": "8996d742-8517-40f7-a961-e8f69fe01f9c"}}, {"head": {"id": "2cceb50a-fb56-4ce2-988c-51e1b4c8efae", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164263559800, "endTime": 10164267846900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "108647c8-326a-4f20-8460-dca79de47d8d", "logId": "3b5ea171-4636-4959-ae1a-eb549a919e83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "108647c8-326a-4f20-8460-dca79de47d8d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164261362600}, "additional": {"logType": "detail", "children": [], "durationId": "2cceb50a-fb56-4ce2-988c-51e1b4c8efae"}}, {"head": {"id": "219d1932-8d3e-4364-bbdb-03314bfd4ae4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164262230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0adb8fcf-7e33-4daa-960c-0798b2aad8fd", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164262361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344ab4a3-8606-4345-aa37-8d8db402e053", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164262429600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1c8296-e1cb-4a4e-8458-a6b9788fc5dc", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164263576900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc65d746-0036-40ac-bbd9-59b2961f33d3", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164266165700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4282aca-6f4b-4313-b47c-5f84af6fdb00", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164266320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5148ab5e-7a3b-46b9-ab11-cb8e0af3959d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164266426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293c78ae-e975-428d-8894-56ab8b7e6e66", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164266482500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba1b211c-8ce7-4c11-b815-4c7e1c68a959", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164266528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ee8866-b78c-4739-bd9d-0f4dd2b83a7f", "name": "entry : default@PreviewUpdateAssets cost memory 0.1535186767578125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164267588300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc63dbf-d377-4a3c-bb98-9b9fb687d1c8", "name": "runTaskFromQueue task cost before running: 924 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164267740200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b5ea171-4636-4959-ae1a-eb549a919e83", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164263559800, "endTime": 10164267846900, "totalTime": 4140000}, "additional": {"logType": "info", "children": [], "durationId": "2cceb50a-fb56-4ce2-988c-51e1b4c8efae"}}, {"head": {"id": "33d82e22-3124-494e-8cd5-19b4f40c4ce4", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164278439100, "endTime": 10176404158700}, "additional": {"children": ["f40f1371-00ee-46a7-b69b-9731aeca0c2c"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6735af06-83b1-4466-abe5-2ffe67c45ea3", "logId": "d74f3451-39e9-46d9-a5d6-96bb989af763"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6735af06-83b1-4466-abe5-2ffe67c45ea3", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164270783000}, "additional": {"logType": "detail", "children": [], "durationId": "33d82e22-3124-494e-8cd5-19b4f40c4ce4"}}, {"head": {"id": "c4a1648c-904d-4e16-818e-ddd191770239", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164271559100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9aae41d-3ed2-475e-8f9e-ceb211fb89ca", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164271755000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096bedbb-94fa-4468-98ab-1bca2a38bbd6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164271852300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b85cf9ce-d690-465a-afa4-afa527be75b8", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164278500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker4", "startTime": 10164316413700, "endTime": 10176403840000}, "additional": {"children": ["27f5b961-07df-4a06-af4a-3f99103ce228", "883704f7-7177-475b-9998-2d53203b389c", "e61f1707-2589-410b-bffa-07214573e59b", "bd619de6-ef1e-4556-afb8-7f91494f3540", "e99cafa3-d1e6-4290-a2d7-1cbffdb6d298", "cbedcc2b-3a54-4e75-96a7-45972e99021f", "3e642a99-298c-4b12-b283-1bd221df5c6b", "d6bc78ae-00b1-46d9-9757-60355b2955e9", "0ac0f0f2-ebb6-4227-93f1-1c8882f2cf63", "e068143a-5bc3-4974-a800-b096fa79e1aa", "89fd2ed8-f5e4-4be4-aae5-bc18f8ce1924", "2820e82d-6955-4e3e-8d04-25faac14ef50", "b80bf34b-f923-4067-b747-a8ba992af06d", "4aae5d22-97f2-44b6-924f-0fa9dde3c5d7", "b4af567e-a76f-48ed-b126-05525f49171e", "f8e122bf-3b6c-49d9-9f76-33e63d23938b", "956cce6f-c29a-4493-9d4d-db9e512ca0ca", "8ef2904c-70fc-4572-9da7-00bd50bdd73a", "34c44cb4-6559-4a87-90f9-6258f4e64409", "2bc2eca1-c6fa-45bd-a57f-658526f6788a", "5a95bf4e-8ba1-47e4-96fa-12e9b56ae9b1", "aa588515-7e51-41f5-9675-4a50afb8d9ca", "b936bb65-f294-4ef1-9ec1-cbad79fafe6f", "5b209be3-71f3-49f2-82e0-d87171f4b88c", "04461808-e38d-4286-861b-2aa6eb323e55", "f1ce47df-e9e9-4cb2-84a5-972ad9e498b9", "e3390853-e467-41ec-9fbe-a2cf8c786adf", "2d47a26b-e82b-4025-85c6-b09e2b69af50", "e237c1c8-3170-44bd-add1-ded94129592c", "0d4ed4a1-f674-49c3-b5e7-eecb854f66bd", "6e47b602-56ac-462e-9458-77acfebf82d3", "bbd24ff7-4fda-49de-8561-f5a268bbe63c", "88b14b82-5fef-4c6e-a864-4c7a1f6791e6", "c4a54ef7-2123-4ccc-9856-32f710247c39", "703a9060-feac-4a05-b179-05caaa079c33", "9c4e28e9-9ee2-4989-b320-7de51e556e84", "e032f0f8-9456-4691-8225-48b48d1d4752", "40203317-e8fc-4ebe-952a-8b9777473c02", "6bdcbd7d-496f-4f09-83e4-d1eabfefcc0f", "60a3b431-870f-429a-90db-e8ef53ce1cc4", "b8d08ee2-9dae-4df9-8cd9-d1adc13c6a4a", "1b4e34b0-e821-4627-9747-04d4f4278fe4", "2104b7fe-78bb-4112-8872-c9f05acfe32f", "e29d9ea3-8cd7-47e5-9a1e-22ce9c662a21", "dd3c2a62-bfb9-4a20-9827-90cd05aa5900", "8a43b1a4-3295-46e0-8690-eaabb6615a3b", "46e1d41e-c896-4eaa-a664-283fe2f9d080", "ee558bf6-da3a-4a73-bbf9-89f8d5d0e3be", "a64352e4-a7fa-4e0c-a1c9-065003163501", "8af9d88f-843a-4abe-ac41-e814a21baa59", "aee24a81-5fc6-4c90-8cee-f85cf8b897d5", "e8fc20a9-187d-46ff-b319-f34b1d3259f8", "803bf125-aa37-419a-a139-9710dc14b0a3", "0dda4c2b-74ad-4efa-bf7d-3235c6c80e3a", "3c97534c-3103-4dea-88e1-2c9041d01884", "9b17a67e-f852-4a3c-bcf6-b5c4ac9d32dc", "726bbcd9-17e8-4933-97be-ca3311eced22", "d055a6e8-f5e0-46d8-839e-31e2ed7f7890", "d8229452-da4a-4240-bfa4-d80abdc112b4", "74260243-0920-4528-8e6c-6944e73f230a", "b228201c-9266-4596-b66b-477f7eb0ad3d", "c7eb115d-ac0d-4f38-9095-2cd99f6555f8", "32a7abb2-48ec-4b2d-aa75-8ef8b1172657", "493c0212-88bd-484d-81bc-d98070f87c3a", "df246845-7db3-4b2f-9a03-02b52aa7ce30", "7efc9f82-9358-441f-8832-47a0c5765e46", "2931d74b-03a0-4e11-8b72-5b75012c22ba", "e083db18-1d21-48e4-8699-3f0115d80f9b", "fecca482-11e3-44ae-9eda-718a34b819c9", "13f4c0b8-ded5-4f0d-bf76-87fd46bd906e", "b347c143-a19a-4210-a3d1-f2489205d995", "419e29fc-70e9-47fc-a6f1-6d490cff399c", "31a994cf-7b16-4214-809b-b7bdd8cf59d6", "ce4514b6-d35d-4276-87aa-8ba8da071366", "35890463-1706-4b18-9d1b-9cb724b84240", "3f0e816a-ab64-472d-902b-6410dea336e8", "77528d54-d62a-4ab0-be9d-9288a02640a5", "2e85723f-aa62-48fd-aa45-b75e35e07adf", "dd8a5778-9396-4d92-9a6e-217d4aee126c", "0fbaf530-b4e2-4940-bf47-f878884c884f", "3409cb19-3239-471c-b69d-3852c8f3a7ce", "8985ea59-ae64-457c-91e6-a5f511a96102", "87b41d9c-4c8e-47b1-84cb-2ae8f9cacfbf"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "33d82e22-3124-494e-8cd5-19b4f40c4ce4", "logId": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "467af5f5-6cde-467d-970b-f82cbefa70e1", "name": "entry : default@PreviewArkTS cost memory -0.47492218017578125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164319669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb43ae19-3da5-4c19-8efd-5be0485aa6bb", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10167841318500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f5b961-07df-4a06-af4a-3f99103ce228", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10167842333900, "endTime": 10167842391800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "0eb0b1b0-6b79-4c8f-8ff6-9f6bf167be1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0eb0b1b0-6b79-4c8f-8ff6-9f6bf167be1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10167842333900, "endTime": 10167842391800}, "additional": {"logType": "info", "children": [], "durationId": "27f5b961-07df-4a06-af4a-3f99103ce228", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "29aca534-ebf5-4341-86c4-8edd9790cf31", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173381871400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "883704f7-7177-475b-9998-2d53203b389c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173382819400, "endTime": 10173382840300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "1aacbfd4-c503-401c-a321-ea9248209f37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1aacbfd4-c503-401c-a321-ea9248209f37", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173382819400, "endTime": 10173382840300}, "additional": {"logType": "info", "children": [], "durationId": "883704f7-7177-475b-9998-2d53203b389c", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "0865ff65-53d6-4c76-b158-bdbf13318a9d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173382919500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e61f1707-2589-410b-bffa-07214573e59b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173383621900, "endTime": 10173383636700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "b845884d-1d01-45e7-a04f-3c994ade3de3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b845884d-1d01-45e7-a04f-3c994ade3de3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173383621900, "endTime": 10173383636700}, "additional": {"logType": "info", "children": [], "durationId": "e61f1707-2589-410b-bffa-07214573e59b", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "a440f090-c9d6-4ab7-ae06-6f20130ac5c1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173383697700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd619de6-ef1e-4556-afb8-7f91494f3540", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173384383900, "endTime": 10173384400700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "212aed22-b599-4a17-a19f-a28c16fb3f34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "212aed22-b599-4a17-a19f-a28c16fb3f34", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173384383900, "endTime": 10173384400700}, "additional": {"logType": "info", "children": [], "durationId": "bd619de6-ef1e-4556-afb8-7f91494f3540", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "2435550c-4bb0-4b4a-871f-a409e6ebee47", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173384465100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99cafa3-d1e6-4290-a2d7-1cbffdb6d298", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173385160400, "endTime": 10173385174700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "dbd692bb-c19b-4282-a43d-f830aa8218a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbd692bb-c19b-4282-a43d-f830aa8218a6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173385160400, "endTime": 10173385174700}, "additional": {"logType": "info", "children": [], "durationId": "e99cafa3-d1e6-4290-a2d7-1cbffdb6d298", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "a16961fd-20de-49e4-8f57-a293655d52d4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173385241900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbedcc2b-3a54-4e75-96a7-45972e99021f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173385890900, "endTime": 10173385905400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "85cd6d19-b615-4817-9c65-978347489678"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85cd6d19-b615-4817-9c65-978347489678", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173385890900, "endTime": 10173385905400}, "additional": {"logType": "info", "children": [], "durationId": "cbedcc2b-3a54-4e75-96a7-45972e99021f", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "af5317ae-c215-4004-8e40-7de87378c178", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173385964100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e642a99-298c-4b12-b283-1bd221df5c6b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173386638800, "endTime": 10173386652600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "fb9622a2-2354-43aa-a353-9d857be07967"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb9622a2-2354-43aa-a353-9d857be07967", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173386638800, "endTime": 10173386652600}, "additional": {"logType": "info", "children": [], "durationId": "3e642a99-298c-4b12-b283-1bd221df5c6b", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e1ca94ae-8fd3-4067-a8fb-49ed4fcfc1bb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173386710600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6bc78ae-00b1-46d9-9757-60355b2955e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173387386900, "endTime": 10173387401900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "ef330baf-f918-4e0c-ba09-a67eebe00ab4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef330baf-f918-4e0c-ba09-a67eebe00ab4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173387386900, "endTime": 10173387401900}, "additional": {"logType": "info", "children": [], "durationId": "d6bc78ae-00b1-46d9-9757-60355b2955e9", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "9f1b47ab-2b00-4b6e-a8d3-ab38ca818ef8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173387463600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac0f0f2-ebb6-4227-93f1-1c8882f2cf63", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173388110000, "endTime": 10173388123500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "d3f57222-b503-421d-9353-27b077af9220"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3f57222-b503-421d-9353-27b077af9220", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173388110000, "endTime": 10173388123500}, "additional": {"logType": "info", "children": [], "durationId": "0ac0f0f2-ebb6-4227-93f1-1c8882f2cf63", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "1e1ecd8a-8cf9-4ade-a616-0554157c04fe", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173388179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e068143a-5bc3-4974-a800-b096fa79e1aa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173388843200, "endTime": 10173388857600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "aecaaed1-be7c-4b88-9d0c-94ed512e3678"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aecaaed1-be7c-4b88-9d0c-94ed512e3678", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173388843200, "endTime": 10173388857600}, "additional": {"logType": "info", "children": [], "durationId": "e068143a-5bc3-4974-a800-b096fa79e1aa", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "4a85c252-92ee-4f8d-97d9-01dc26593fc8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173388918000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89fd2ed8-f5e4-4be4-aae5-bc18f8ce1924", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173389626400, "endTime": 10173389644200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "038e3f72-a1ca-4ff2-8aaf-1f5264336c3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "038e3f72-a1ca-4ff2-8aaf-1f5264336c3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173389626400, "endTime": 10173389644200}, "additional": {"logType": "info", "children": [], "durationId": "89fd2ed8-f5e4-4be4-aae5-bc18f8ce1924", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "9abcefb0-9d02-4879-a986-ad3353e1135e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173389706500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2820e82d-6955-4e3e-8d04-25faac14ef50", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173390384200, "endTime": 10173390402400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "9854bb09-24da-4660-8312-b6cd04adfb51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9854bb09-24da-4660-8312-b6cd04adfb51", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173390384200, "endTime": 10173390402400}, "additional": {"logType": "info", "children": [], "durationId": "2820e82d-6955-4e3e-8d04-25faac14ef50", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "b7b2a482-5809-44fc-9a90-214674b82f9b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173390460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80bf34b-f923-4067-b747-a8ba992af06d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173391141700, "endTime": 10173391331000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "bee5b4bb-3e75-41d5-a5d5-c154597c1b80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bee5b4bb-3e75-41d5-a5d5-c154597c1b80", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173391141700, "endTime": 10173391331000}, "additional": {"logType": "info", "children": [], "durationId": "b80bf34b-f923-4067-b747-a8ba992af06d", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "bc9a1e45-53ba-43d2-8465-688688cbcd21", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173391418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aae5d22-97f2-44b6-924f-0fa9dde3c5d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173392109700, "endTime": 10173392122800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "4d46cc14-cd5d-4aed-9cbe-76098c47fa78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d46cc14-cd5d-4aed-9cbe-76098c47fa78", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173392109700, "endTime": 10173392122800}, "additional": {"logType": "info", "children": [], "durationId": "4aae5d22-97f2-44b6-924f-0fa9dde3c5d7", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e59b43af-ef55-4631-ab95-4d73b8cb9571", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173392183600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4af567e-a76f-48ed-b126-05525f49171e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173392886200, "endTime": 10173392902600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "03e4bdfe-5d10-49ca-9094-5e05a5a6d79e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03e4bdfe-5d10-49ca-9094-5e05a5a6d79e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173392886200, "endTime": 10173392902600}, "additional": {"logType": "info", "children": [], "durationId": "b4af567e-a76f-48ed-b126-05525f49171e", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "476d4ace-c6d5-45c2-9181-ae09f3208539", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173392973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8e122bf-3b6c-49d9-9f76-33e63d23938b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173393736800, "endTime": 10173393753200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "5cb9b0f7-e1a6-46ef-9aad-eac86b99a16a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cb9b0f7-e1a6-46ef-9aad-eac86b99a16a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173393736800, "endTime": 10173393753200}, "additional": {"logType": "info", "children": [], "durationId": "f8e122bf-3b6c-49d9-9f76-33e63d23938b", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "6550c1f3-1c3d-486f-a844-c653210c5ffc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173393821900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "956cce6f-c29a-4493-9d4d-db9e512ca0ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173394538500, "endTime": 10173394552700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "d2dc6c7b-b040-459d-b346-4e4effbe5742"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2dc6c7b-b040-459d-b346-4e4effbe5742", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173394538500, "endTime": 10173394552700}, "additional": {"logType": "info", "children": [], "durationId": "956cce6f-c29a-4493-9d4d-db9e512ca0ca", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "54f70eba-50fa-42e5-823c-d13aea3ed553", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173394615500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ef2904c-70fc-4572-9da7-00bd50bdd73a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173395302400, "endTime": 10173395317800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "0e052da5-1cae-4e3e-b8a5-c065ff8722bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e052da5-1cae-4e3e-b8a5-c065ff8722bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173395302400, "endTime": 10173395317800}, "additional": {"logType": "info", "children": [], "durationId": "8ef2904c-70fc-4572-9da7-00bd50bdd73a", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "7262d9bc-60bf-4f8d-989a-8a465ae4dd5b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173395385000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c44cb4-6559-4a87-90f9-6258f4e64409", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173396977900, "endTime": 10173397005300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "c20b504e-4e46-4a95-8021-428f282e3269"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c20b504e-4e46-4a95-8021-428f282e3269", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173396977900, "endTime": 10173397005300}, "additional": {"logType": "info", "children": [], "durationId": "34c44cb4-6559-4a87-90f9-6258f4e64409", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "bb5019bf-595a-4120-ba54-dc3391594c72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173397118000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc2eca1-c6fa-45bd-a57f-658526f6788a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173397972800, "endTime": 10173397988700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "c73d27b4-8068-42f3-b84e-3c3b9609e888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c73d27b4-8068-42f3-b84e-3c3b9609e888", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173397972800, "endTime": 10173397988700}, "additional": {"logType": "info", "children": [], "durationId": "2bc2eca1-c6fa-45bd-a57f-658526f6788a", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "0886db23-8ee7-4977-bb32-3c4d3169ba0a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173398058500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a95bf4e-8ba1-47e4-96fa-12e9b56ae9b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173398795700, "endTime": 10173398814100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "440427a1-3829-4751-aa6f-bd9636ec831a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "440427a1-3829-4751-aa6f-bd9636ec831a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173398795700, "endTime": 10173398814100}, "additional": {"logType": "info", "children": [], "durationId": "5a95bf4e-8ba1-47e4-96fa-12e9b56ae9b1", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "2dffc8a4-5880-433f-9309-3b44aa21c042", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173398879300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa588515-7e51-41f5-9675-4a50afb8d9ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173399623000, "endTime": 10173399638300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "07ed7c4f-ecc1-4fa7-894f-d141861c6d5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07ed7c4f-ecc1-4fa7-894f-d141861c6d5c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173399623000, "endTime": 10173399638300}, "additional": {"logType": "info", "children": [], "durationId": "aa588515-7e51-41f5-9675-4a50afb8d9ca", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "229a3b37-74d4-4523-803f-86e44f6a325a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173399713600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b936bb65-f294-4ef1-9ec1-cbad79fafe6f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173400528700, "endTime": 10173400548100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "e6ebc2ec-40eb-43b7-9149-ca45d9eff0d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6ebc2ec-40eb-43b7-9149-ca45d9eff0d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173400528700, "endTime": 10173400548100}, "additional": {"logType": "info", "children": [], "durationId": "b936bb65-f294-4ef1-9ec1-cbad79fafe6f", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "cc42fc9f-d7bf-4fe2-b10c-f801fe6d372c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173400703700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b209be3-71f3-49f2-82e0-d87171f4b88c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173401539200, "endTime": 10173401558800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "4c85bea2-01b6-430a-a830-00252c12cbaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c85bea2-01b6-430a-a830-00252c12cbaa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173401539200, "endTime": 10173401558800}, "additional": {"logType": "info", "children": [], "durationId": "5b209be3-71f3-49f2-82e0-d87171f4b88c", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "34f80b7a-0fbd-4927-ac42-df1d8422b927", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173401649600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04461808-e38d-4286-861b-2aa6eb323e55", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173402449100, "endTime": 10173402469400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "c7f65174-db9a-49e4-9f6b-e053e82ff33a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7f65174-db9a-49e4-9f6b-e053e82ff33a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173402449100, "endTime": 10173402469400}, "additional": {"logType": "info", "children": [], "durationId": "04461808-e38d-4286-861b-2aa6eb323e55", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "16f93dae-2936-4560-9f53-533ed2b4a4bd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173402549800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ce47df-e9e9-4cb2-84a5-972ad9e498b9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173403452900, "endTime": 10173403471600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "fd95d722-b04f-4aa6-944b-a214997a5603"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd95d722-b04f-4aa6-944b-a214997a5603", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173403452900, "endTime": 10173403471600}, "additional": {"logType": "info", "children": [], "durationId": "f1ce47df-e9e9-4cb2-84a5-972ad9e498b9", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "07a42acd-4d4e-495b-8f14-95b161670fbc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173403554000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3390853-e467-41ec-9fbe-a2cf8c786adf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173405631500, "endTime": 10173405674700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "45a1696f-aced-4e96-952b-357868611ad8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45a1696f-aced-4e96-952b-357868611ad8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173405631500, "endTime": 10173405674700}, "additional": {"logType": "info", "children": [], "durationId": "e3390853-e467-41ec-9fbe-a2cf8c786adf", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "92742f93-f04d-4d1e-858f-053fba1a742d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173405838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d47a26b-e82b-4025-85c6-b09e2b69af50", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173406982900, "endTime": 10173407006400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "aff5a139-27e8-4a1a-b418-e2787fa7a80b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aff5a139-27e8-4a1a-b418-e2787fa7a80b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173406982900, "endTime": 10173407006400}, "additional": {"logType": "info", "children": [], "durationId": "2d47a26b-e82b-4025-85c6-b09e2b69af50", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "0ccfc937-17d1-46c8-99f7-c1b8fce74efa", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173407100300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e237c1c8-3170-44bd-add1-ded94129592c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173407904500, "endTime": 10173407920100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "bdb2ae7b-cbc3-4aa2-91e7-ab34324f0092"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdb2ae7b-cbc3-4aa2-91e7-ab34324f0092", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173407904500, "endTime": 10173407920100}, "additional": {"logType": "info", "children": [], "durationId": "e237c1c8-3170-44bd-add1-ded94129592c", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "25aa7be5-c3cb-45e0-8b1c-0826c66f67e1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173407989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d4ed4a1-f674-49c3-b5e7-eecb854f66bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173408784200, "endTime": 10173408799100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "0073d8ce-adfd-482a-8eb0-0206a1a42e41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0073d8ce-adfd-482a-8eb0-0206a1a42e41", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173408784200, "endTime": 10173408799100}, "additional": {"logType": "info", "children": [], "durationId": "0d4ed4a1-f674-49c3-b5e7-eecb854f66bd", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "599adafe-4611-468e-93f7-2d8ea4f1f892", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173408867700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e47b602-56ac-462e-9458-77acfebf82d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173409720800, "endTime": 10173409737400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "94afd9bd-2d86-47ee-95c8-761065dc321a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94afd9bd-2d86-47ee-95c8-761065dc321a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173409720800, "endTime": 10173409737400}, "additional": {"logType": "info", "children": [], "durationId": "6e47b602-56ac-462e-9458-77acfebf82d3", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "08828c2e-0088-4805-a778-cdf934afacae", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173409832000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd24ff7-4fda-49de-8561-f5a268bbe63c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173410624500, "endTime": 10173410641300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "e25e767f-167a-4ba6-9f2b-8d380c29d915"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e25e767f-167a-4ba6-9f2b-8d380c29d915", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173410624500, "endTime": 10173410641300}, "additional": {"logType": "info", "children": [], "durationId": "bbd24ff7-4fda-49de-8561-f5a268bbe63c", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "506aca71-7a71-46b4-a6b4-c53d4021f25a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173410710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b14b82-5fef-4c6e-a864-4c7a1f6791e6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173411446300, "endTime": 10173411463900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "68884f82-4ad2-4adc-a69d-8362fc481244"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68884f82-4ad2-4adc-a69d-8362fc481244", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173411446300, "endTime": 10173411463900}, "additional": {"logType": "info", "children": [], "durationId": "88b14b82-5fef-4c6e-a864-4c7a1f6791e6", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "160a5b5f-4759-4df5-9d1b-6135ea448fe7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173411537600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a54ef7-2123-4ccc-9856-32f710247c39", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173412260400, "endTime": 10173412274600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "b572d083-e56a-4279-87bd-d246ed99e013"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b572d083-e56a-4279-87bd-d246ed99e013", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173412260400, "endTime": 10173412274600}, "additional": {"logType": "info", "children": [], "durationId": "c4a54ef7-2123-4ccc-9856-32f710247c39", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "4d05bf44-53a9-4714-8a03-66e630c57e72", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173412342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703a9060-feac-4a05-b179-05caaa079c33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173413092400, "endTime": 10173413117500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "79500efb-9157-4aad-b85c-4de80b4e9c53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79500efb-9157-4aad-b85c-4de80b4e9c53", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173413092400, "endTime": 10173413117500}, "additional": {"logType": "info", "children": [], "durationId": "703a9060-feac-4a05-b179-05caaa079c33", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e1aa254f-12c0-472f-a49f-c27862b3a3a8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173413200700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c4e28e9-9ee2-4989-b320-7de51e556e84", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173413998700, "endTime": 10173414014500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "ceedc21e-c473-4b4f-8ee2-48a7f4b92da4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ceedc21e-c473-4b4f-8ee2-48a7f4b92da4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173413998700, "endTime": 10173414014500}, "additional": {"logType": "info", "children": [], "durationId": "9c4e28e9-9ee2-4989-b320-7de51e556e84", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "b524e018-d33d-4f2c-a683-c246c740a232", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173414092400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e032f0f8-9456-4691-8225-48b48d1d4752", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173414843000, "endTime": 10173414860200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "ecdbfefe-a4ae-4b57-9722-135f2328c604"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecdbfefe-a4ae-4b57-9722-135f2328c604", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173414843000, "endTime": 10173414860200}, "additional": {"logType": "info", "children": [], "durationId": "e032f0f8-9456-4691-8225-48b48d1d4752", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "1afb180c-6932-4c12-93a6-cc9470a4d43d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173414931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40203317-e8fc-4ebe-952a-8b9777473c02", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173416154100, "endTime": 10173416172800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "34fa4d61-84b4-43e6-854b-4979c4929e09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34fa4d61-84b4-43e6-854b-4979c4929e09", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173416154100, "endTime": 10173416172800}, "additional": {"logType": "info", "children": [], "durationId": "40203317-e8fc-4ebe-952a-8b9777473c02", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "72780ab6-ddfd-48cd-b1e1-9294d7d2a975", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173416256200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdcbd7d-496f-4f09-83e4-d1eabfefcc0f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173417062600, "endTime": 10173417077400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "408740ba-3c00-403b-9550-56cf78fff099"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "408740ba-3c00-403b-9550-56cf78fff099", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173417062600, "endTime": 10173417077400}, "additional": {"logType": "info", "children": [], "durationId": "6bdcbd7d-496f-4f09-83e4-d1eabfefcc0f", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "fd804c95-180a-4f3e-9303-df7b920baa21", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173417144400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60a3b431-870f-429a-90db-e8ef53ce1cc4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173417850200, "endTime": 10173417864500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "7ff8ecc8-91b2-4291-b6bc-655da1d0b46b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ff8ecc8-91b2-4291-b6bc-655da1d0b46b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173417850200, "endTime": 10173417864500}, "additional": {"logType": "info", "children": [], "durationId": "60a3b431-870f-429a-90db-e8ef53ce1cc4", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "34afaa61-46f8-4a10-8bce-f2b233c68e2f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173417932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d08ee2-9dae-4df9-8cd9-d1adc13c6a4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173418620600, "endTime": 10173418633500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "858f8cb6-7ff6-4a64-9f3c-5aef994cc1f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "858f8cb6-7ff6-4a64-9f3c-5aef994cc1f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173418620600, "endTime": 10173418633500}, "additional": {"logType": "info", "children": [], "durationId": "b8d08ee2-9dae-4df9-8cd9-d1adc13c6a4a", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "f96c0a4d-6b20-42aa-9c42-9611d75a5bbe", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173418692500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4e34b0-e821-4627-9747-04d4f4278fe4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173419384800, "endTime": 10173419400300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "b533d3a5-7255-432a-bc05-9a04403716f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b533d3a5-7255-432a-bc05-9a04403716f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173419384800, "endTime": 10173419400300}, "additional": {"logType": "info", "children": [], "durationId": "1b4e34b0-e821-4627-9747-04d4f4278fe4", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e48a4034-c1ac-428c-b877-d3a3c2500890", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173419487400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2104b7fe-78bb-4112-8872-c9f05acfe32f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173420181600, "endTime": 10173420196200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "dcaa0aac-49c5-4cf8-bdb7-a3032fc53efc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcaa0aac-49c5-4cf8-bdb7-a3032fc53efc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173420181600, "endTime": 10173420196200}, "additional": {"logType": "info", "children": [], "durationId": "2104b7fe-78bb-4112-8872-c9f05acfe32f", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e578f41e-33e4-47ac-b32a-9d47726987ce", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173420259000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29d9ea3-8cd7-47e5-9a1e-22ce9c662a21", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173420949200, "endTime": 10173420964400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "7cf1e9ed-2425-409e-bb37-3cbf562ae0e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cf1e9ed-2425-409e-bb37-3cbf562ae0e2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173420949200, "endTime": 10173420964400}, "additional": {"logType": "info", "children": [], "durationId": "e29d9ea3-8cd7-47e5-9a1e-22ce9c662a21", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e861865a-6f78-444c-b70c-ace01d523cef", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173421025000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd3c2a62-bfb9-4a20-9827-90cd05aa5900", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173421706100, "endTime": 10173421721300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "422e747a-1c4d-437c-bb53-4dff080c5ce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "422e747a-1c4d-437c-bb53-4dff080c5ce6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173421706100, "endTime": 10173421721300}, "additional": {"logType": "info", "children": [], "durationId": "dd3c2a62-bfb9-4a20-9827-90cd05aa5900", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "235d9bc7-8291-49e0-8168-69b19eb4a1bf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173421787000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a43b1a4-3295-46e0-8690-eaabb6615a3b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173422629800, "endTime": 10173422648400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "69bea7f6-d831-4e6f-b9bd-c3048c46d5a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69bea7f6-d831-4e6f-b9bd-c3048c46d5a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173422629800, "endTime": 10173422648400}, "additional": {"logType": "info", "children": [], "durationId": "8a43b1a4-3295-46e0-8690-eaabb6615a3b", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "bbe4d849-35c0-441a-822b-4e2a10e72acb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173422729400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e1d41e-c896-4eaa-a664-283fe2f9d080", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173423484000, "endTime": 10173423501100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "ac5e7eb8-1204-4de1-878d-2879f70e8f30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac5e7eb8-1204-4de1-878d-2879f70e8f30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173423484000, "endTime": 10173423501100}, "additional": {"logType": "info", "children": [], "durationId": "46e1d41e-c896-4eaa-a664-283fe2f9d080", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "3a622cc9-dba9-4e16-94d2-a2787e5dbd66", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173423578900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee558bf6-da3a-4a73-bbf9-89f8d5d0e3be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173424876500, "endTime": 10173424899000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "388d99f3-3615-4302-9647-6ded82f0744d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "388d99f3-3615-4302-9647-6ded82f0744d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173424876500, "endTime": 10173424899000}, "additional": {"logType": "info", "children": [], "durationId": "ee558bf6-da3a-4a73-bbf9-89f8d5d0e3be", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "7dcc4457-0dfb-4c23-8f2e-9b523965073a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173425019900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64352e4-a7fa-4e0c-a1c9-065003163501", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173425872000, "endTime": 10173425891600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "f889c538-efbd-4d1e-9cfc-c4de4d82794a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f889c538-efbd-4d1e-9cfc-c4de4d82794a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173425872000, "endTime": 10173425891600}, "additional": {"logType": "info", "children": [], "durationId": "a64352e4-a7fa-4e0c-a1c9-065003163501", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "b0f32be5-f5dc-4c1d-996e-f5c92a73cbfd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173425970600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af9d88f-843a-4abe-ac41-e814a21baa59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173426701800, "endTime": 10173426719000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "41240bde-5a2b-4526-a05a-64ed2d3e1a85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41240bde-5a2b-4526-a05a-64ed2d3e1a85", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173426701800, "endTime": 10173426719000}, "additional": {"logType": "info", "children": [], "durationId": "8af9d88f-843a-4abe-ac41-e814a21baa59", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "9d6404de-831f-4475-ab69-a5584661dda8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173426785100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee24a81-5fc6-4c90-8cee-f85cf8b897d5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173427499000, "endTime": 10173427512700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "73c926c4-08ef-45af-bb1f-44527e48e007"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73c926c4-08ef-45af-bb1f-44527e48e007", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173427499000, "endTime": 10173427512700}, "additional": {"logType": "info", "children": [], "durationId": "aee24a81-5fc6-4c90-8cee-f85cf8b897d5", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "11555620-9ece-48ab-b7a4-772071e2376d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173427575700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fc20a9-187d-46ff-b319-f34b1d3259f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173428286700, "endTime": 10173428301500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "9a98de09-aa38-4066-bb4c-f75b544c34c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a98de09-aa38-4066-bb4c-f75b544c34c2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173428286700, "endTime": 10173428301500}, "additional": {"logType": "info", "children": [], "durationId": "e8fc20a9-187d-46ff-b319-f34b1d3259f8", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e7d23356-921d-40b6-8c83-fc0b0639a111", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173428367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803bf125-aa37-419a-a139-9710dc14b0a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173429074100, "endTime": 10173429089300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "948307e4-a5a7-4267-98cc-fad577cb45f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "948307e4-a5a7-4267-98cc-fad577cb45f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173429074100, "endTime": 10173429089300}, "additional": {"logType": "info", "children": [], "durationId": "803bf125-aa37-419a-a139-9710dc14b0a3", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "52f121ff-3d43-489d-b168-ce9d0388a748", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173429153500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dda4c2b-74ad-4efa-bf7d-3235c6c80e3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173429829500, "endTime": 10173429842800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "50ae402e-85bc-42b3-89f2-2a7a531e9f0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50ae402e-85bc-42b3-89f2-2a7a531e9f0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173429829500, "endTime": 10173429842800}, "additional": {"logType": "info", "children": [], "durationId": "0dda4c2b-74ad-4efa-bf7d-3235c6c80e3a", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "3cfbe8c2-9f36-46b1-aa4a-946e0e67be46", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173429906100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c97534c-3103-4dea-88e1-2c9041d01884", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173430642900, "endTime": 10173430658300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "9d794e05-b911-40be-9fc0-701925fe19bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d794e05-b911-40be-9fc0-701925fe19bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173430642900, "endTime": 10173430658300}, "additional": {"logType": "info", "children": [], "durationId": "3c97534c-3103-4dea-88e1-2c9041d01884", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "ba4c9089-6ad5-4f6a-a78d-9ff1b3429e6c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173430721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b17a67e-f852-4a3c-bcf6-b5c4ac9d32dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173431448800, "endTime": 10173431463600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "b559fb24-0b43-4bf8-9912-f2d3e5039c11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b559fb24-0b43-4bf8-9912-f2d3e5039c11", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173431448800, "endTime": 10173431463600}, "additional": {"logType": "info", "children": [], "durationId": "9b17a67e-f852-4a3c-bcf6-b5c4ac9d32dc", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "d7c3b3f2-b71a-4998-81d6-2c88dd80d155", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173431526900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726bbcd9-17e8-4933-97be-ca3311eced22", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173432260500, "endTime": 10173432276100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "c226730d-eac8-4f44-bd8c-71cc28372868"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c226730d-eac8-4f44-bd8c-71cc28372868", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173432260500, "endTime": 10173432276100}, "additional": {"logType": "info", "children": [], "durationId": "726bbcd9-17e8-4933-97be-ca3311eced22", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "93c4a637-90e6-4ba7-aa3f-9a05deb7bbec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173432339600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d055a6e8-f5e0-46d8-839e-31e2ed7f7890", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173433042500, "endTime": 10173433056700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "9e22648e-f46b-40a7-a98d-425705eb5429"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e22648e-f46b-40a7-a98d-425705eb5429", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173433042500, "endTime": 10173433056700}, "additional": {"logType": "info", "children": [], "durationId": "d055a6e8-f5e0-46d8-839e-31e2ed7f7890", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "559f22a5-064d-44b4-9739-7fec4ca23530", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173433130700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8229452-da4a-4240-bfa4-d80abdc112b4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173434448200, "endTime": 10173434467200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "6e7310af-a899-492e-a70f-9c410452d595"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e7310af-a899-492e-a70f-9c410452d595", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173434448200, "endTime": 10173434467200}, "additional": {"logType": "info", "children": [], "durationId": "d8229452-da4a-4240-bfa4-d80abdc112b4", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "b3c4233d-9f14-4ff9-8076-9353099f6fdd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173434577800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74260243-0920-4528-8e6c-6944e73f230a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173435333900, "endTime": 10173435349900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "2dc803c5-167f-47fe-b386-620b6d903819"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dc803c5-167f-47fe-b386-620b6d903819", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173435333900, "endTime": 10173435349900}, "additional": {"logType": "info", "children": [], "durationId": "74260243-0920-4528-8e6c-6944e73f230a", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "c286a334-c29a-4b7b-a7df-933fa4752b98", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173435421900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b228201c-9266-4596-b66b-477f7eb0ad3d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173436122800, "endTime": 10173436137100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "4b57c80f-76b9-455c-9a76-5b7b93e4e870"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b57c80f-76b9-455c-9a76-5b7b93e4e870", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173436122800, "endTime": 10173436137100}, "additional": {"logType": "info", "children": [], "durationId": "b228201c-9266-4596-b66b-477f7eb0ad3d", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "486180f5-1f0d-4eff-9085-9df23c9601ba", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173436201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7eb115d-ac0d-4f38-9095-2cd99f6555f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173436919500, "endTime": 10173436935700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "e1629f70-1469-4a2b-8d9f-73cb3da5e776"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1629f70-1469-4a2b-8d9f-73cb3da5e776", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173436919500, "endTime": 10173436935700}, "additional": {"logType": "info", "children": [], "durationId": "c7eb115d-ac0d-4f38-9095-2cd99f6555f8", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "79ce9ea5-67cb-4cf5-8083-ce5ff212e4ec", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173437023100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32a7abb2-48ec-4b2d-aa75-8ef8b1172657", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173437739000, "endTime": 10173437756100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "4ed411c0-9d04-4776-8725-a24896e8f63a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ed411c0-9d04-4776-8725-a24896e8f63a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173437739000, "endTime": 10173437756100}, "additional": {"logType": "info", "children": [], "durationId": "32a7abb2-48ec-4b2d-aa75-8ef8b1172657", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "2622b4bb-57e3-4c85-8f5e-075296af0fbd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173437831700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493c0212-88bd-484d-81bc-d98070f87c3a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173438649400, "endTime": 10173438670100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "cd61df9a-1dc3-44a3-827a-8da4298161a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd61df9a-1dc3-44a3-827a-8da4298161a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173438649400, "endTime": 10173438670100}, "additional": {"logType": "info", "children": [], "durationId": "493c0212-88bd-484d-81bc-d98070f87c3a", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "a8ea8dbd-8313-441a-aaec-9127590367b7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173438769900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df246845-7db3-4b2f-9a03-02b52aa7ce30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173439655400, "endTime": 10173439675000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "82360a27-2d79-419b-baa6-bc0587362ffc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82360a27-2d79-419b-baa6-bc0587362ffc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173439655400, "endTime": 10173439675000}, "additional": {"logType": "info", "children": [], "durationId": "df246845-7db3-4b2f-9a03-02b52aa7ce30", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "88be3bd1-9d2c-4892-a08a-151c318f393d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173439762100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7efc9f82-9358-441f-8832-47a0c5765e46", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173440511700, "endTime": 10173440531600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "616daabc-275c-4458-8c48-da075a622e74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "616daabc-275c-4458-8c48-da075a622e74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173440511700, "endTime": 10173440531600}, "additional": {"logType": "info", "children": [], "durationId": "7efc9f82-9358-441f-8832-47a0c5765e46", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "9fdb5bf7-368e-437b-8a0e-2899cbc18f12", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173440615600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2931d74b-03a0-4e11-8b72-5b75012c22ba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173441468100, "endTime": 10173441488500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "12dabcfc-1b85-48c0-960d-eee3c0816600"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12dabcfc-1b85-48c0-960d-eee3c0816600", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173441468100, "endTime": 10173441488500}, "additional": {"logType": "info", "children": [], "durationId": "2931d74b-03a0-4e11-8b72-5b75012c22ba", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "60c610c3-619a-4482-a97a-7c770a87d034", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173441574400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e083db18-1d21-48e4-8699-3f0115d80f9b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173442587300, "endTime": 10173442619100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "dedbd5d5-39b5-464f-89d4-b295602fe659"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dedbd5d5-39b5-464f-89d4-b295602fe659", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173442587300, "endTime": 10173442619100}, "additional": {"logType": "info", "children": [], "durationId": "e083db18-1d21-48e4-8699-3f0115d80f9b", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "52718e7b-4095-4317-9254-505dd40667ca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173442752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fecca482-11e3-44ae-9eda-718a34b819c9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173445016200, "endTime": 10173445066200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "92fe4850-83f2-405f-acb6-25826fb18330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92fe4850-83f2-405f-acb6-25826fb18330", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173445016200, "endTime": 10173445066200}, "additional": {"logType": "info", "children": [], "durationId": "fecca482-11e3-44ae-9eda-718a34b819c9", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "f838b6c8-8627-4477-a2c2-341050e828bf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173445267700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f4c0b8-ded5-4f0d-bf76-87fd46bd906e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173446610300, "endTime": 10173446633300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "d8223d92-66c0-4a0d-944a-6071690ca057"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8223d92-66c0-4a0d-944a-6071690ca057", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173446610300, "endTime": 10173446633300}, "additional": {"logType": "info", "children": [], "durationId": "13f4c0b8-ded5-4f0d-bf76-87fd46bd906e", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "1d8fdd0e-fcff-491a-bd9c-7577c0be2640", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173446745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b347c143-a19a-4210-a3d1-f2489205d995", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173447715800, "endTime": 10173447740200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "4417a029-1a21-4368-8c3e-7e81e903e16e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4417a029-1a21-4368-8c3e-7e81e903e16e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173447715800, "endTime": 10173447740200}, "additional": {"logType": "info", "children": [], "durationId": "b347c143-a19a-4210-a3d1-f2489205d995", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "ea933bb0-07c3-4870-bd1a-b47a10febb13", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173447840700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "419e29fc-70e9-47fc-a6f1-6d490cff399c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173448678700, "endTime": 10173448696100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "9a81151d-65a2-415a-b63b-7d0a0ff4ea1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a81151d-65a2-415a-b63b-7d0a0ff4ea1a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173448678700, "endTime": 10173448696100}, "additional": {"logType": "info", "children": [], "durationId": "419e29fc-70e9-47fc-a6f1-6d490cff399c", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "9e6c733c-ea27-4cb4-841a-938d677a9ad9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173448779600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a994cf-7b16-4214-809b-b7bdd8cf59d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173449566400, "endTime": 10173449585800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "e6a8fb6f-8742-4089-8882-855ad842aa74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6a8fb6f-8742-4089-8882-855ad842aa74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173449566400, "endTime": 10173449585800}, "additional": {"logType": "info", "children": [], "durationId": "31a994cf-7b16-4214-809b-b7bdd8cf59d6", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "e1f25481-631c-416e-ad1e-40efb18f3fff", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173449657100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4514b6-d35d-4276-87aa-8ba8da071366", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173450497400, "endTime": 10173450513400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "a090a8ce-1ce8-4692-994b-eb3898628d44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a090a8ce-1ce8-4692-994b-eb3898628d44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173450497400, "endTime": 10173450513400}, "additional": {"logType": "info", "children": [], "durationId": "ce4514b6-d35d-4276-87aa-8ba8da071366", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "5bd13ba1-19d9-4c7a-a77c-a2aaca5e8000", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173450591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35890463-1706-4b18-9d1b-9cb724b84240", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173451426900, "endTime": 10173451442500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "d5f7e775-b9b1-4ca0-ab29-6b5034f1853a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5f7e775-b9b1-4ca0-ab29-6b5034f1853a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173451426900, "endTime": 10173451442500}, "additional": {"logType": "info", "children": [], "durationId": "35890463-1706-4b18-9d1b-9cb724b84240", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "103a6eb6-5a08-4393-bec4-3a9e0fe7c8e9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173451518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f0e816a-ab64-472d-902b-6410dea336e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173452303600, "endTime": 10173452320200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "776c6bd8-6167-490a-978c-c4adb5b8ac50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "776c6bd8-6167-490a-978c-c4adb5b8ac50", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173452303600, "endTime": 10173452320200}, "additional": {"logType": "info", "children": [], "durationId": "3f0e816a-ab64-472d-902b-6410dea336e8", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "f0f1e54d-8639-432c-b41a-ac27d9e01e94", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173587877400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77528d54-d62a-4ab0-be9d-9288a02640a5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173589190500, "endTime": 10173589212000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "871e7781-18af-4e9a-840e-c1c223645c80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "871e7781-18af-4e9a-840e-c1c223645c80", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173589190500, "endTime": 10173589212000}, "additional": {"logType": "info", "children": [], "durationId": "77528d54-d62a-4ab0-be9d-9288a02640a5", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "7788675b-1dc2-41ac-af2b-28195e93b9bf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173680160200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e85723f-aa62-48fd-aa45-b75e35e07adf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173681946900, "endTime": 10173681979100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "b3765655-f6bd-4338-a5c5-dd044644445e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3765655-f6bd-4338-a5c5-dd044644445e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173681946900, "endTime": 10173681979100}, "additional": {"logType": "info", "children": [], "durationId": "2e85723f-aa62-48fd-aa45-b75e35e07adf", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "63dda6a4-1dc9-473a-9410-4707f655ada2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173761415100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8a5778-9396-4d92-9a6e-217d4aee126c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173762615700, "endTime": 10173762639000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "4a3dbaf1-9e1e-4ce6-97ae-a87e4e0deebc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a3dbaf1-9e1e-4ce6-97ae-a87e4e0deebc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173762615700, "endTime": 10173762639000}, "additional": {"logType": "info", "children": [], "durationId": "dd8a5778-9396-4d92-9a6e-217d4aee126c", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "bd390f5f-8083-4200-9955-5e98041663c2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173789360800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fbaf530-b4e2-4940-bf47-f878884c884f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173791072800, "endTime": 10173791097200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "d7bc0587-da8a-4d5f-ad64-c1c84ae0e9f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7bc0587-da8a-4d5f-ad64-c1c84ae0e9f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173791072800, "endTime": 10173791097200}, "additional": {"logType": "info", "children": [], "durationId": "0fbaf530-b4e2-4940-bf47-f878884c884f", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "3ce6861d-101e-4fe9-9157-93a0fd04d559", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173845704900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3409cb19-3239-471c-b69d-3852c8f3a7ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173846960000, "endTime": 10173846994800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "65199215-3023-4e55-a0fe-8ed9d73e3bfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65199215-3023-4e55-a0fe-8ed9d73e3bfa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173846960000, "endTime": 10173846994800}, "additional": {"logType": "info", "children": [], "durationId": "3409cb19-3239-471c-b69d-3852c8f3a7ce", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "d03c4d20-a828-4a1f-b9b7-afb23fec3fad", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173892932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8985ea59-ae64-457c-91e6-a5f511a96102", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173894012200, "endTime": 10173894031500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "ba5bfe5b-5b01-4b36-861f-3dbf249b411c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba5bfe5b-5b01-4b36-861f-3dbf249b411c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10173894012200, "endTime": 10173894031500}, "additional": {"logType": "info", "children": [], "durationId": "8985ea59-ae64-457c-91e6-a5f511a96102", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "135d60ab-751a-4f94-8d64-15989785339a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176402753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b41d9c-4c8e-47b1-84cb-2ae8f9cacfbf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176403746100, "endTime": 10176403765500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "logId": "e678b908-1f37-4ea6-b5d5-386fd6ddcb44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e678b908-1f37-4ea6-b5d5-386fd6ddcb44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176403746100, "endTime": 10176403765500}, "additional": {"logType": "info", "children": [], "durationId": "87b41d9c-4c8e-47b1-84cb-2ae8f9cacfbf", "parent": "1536e39a-0ff4-4705-a31c-6ca5914a724c"}}, {"head": {"id": "1536e39a-0ff4-4705-a31c-6ca5914a724c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker4", "startTime": 10164316413700, "endTime": 10176403840000}, "additional": {"logType": "error", "children": ["0eb0b1b0-6b79-4c8f-8ff6-9f6bf167be1f", "1aacbfd4-c503-401c-a321-ea9248209f37", "b845884d-1d01-45e7-a04f-3c994ade3de3", "212aed22-b599-4a17-a19f-a28c16fb3f34", "dbd692bb-c19b-4282-a43d-f830aa8218a6", "85cd6d19-b615-4817-9c65-978347489678", "fb9622a2-2354-43aa-a353-9d857be07967", "ef330baf-f918-4e0c-ba09-a67eebe00ab4", "d3f57222-b503-421d-9353-27b077af9220", "aecaaed1-be7c-4b88-9d0c-94ed512e3678", "038e3f72-a1ca-4ff2-8aaf-1f5264336c3a", "9854bb09-24da-4660-8312-b6cd04adfb51", "bee5b4bb-3e75-41d5-a5d5-c154597c1b80", "4d46cc14-cd5d-4aed-9cbe-76098c47fa78", "03e4bdfe-5d10-49ca-9094-5e05a5a6d79e", "5cb9b0f7-e1a6-46ef-9aad-eac86b99a16a", "d2dc6c7b-b040-459d-b346-4e4effbe5742", "0e052da5-1cae-4e3e-b8a5-c065ff8722bf", "c20b504e-4e46-4a95-8021-428f282e3269", "c73d27b4-8068-42f3-b84e-3c3b9609e888", "440427a1-3829-4751-aa6f-bd9636ec831a", "07ed7c4f-ecc1-4fa7-894f-d141861c6d5c", "e6ebc2ec-40eb-43b7-9149-ca45d9eff0d8", "4c85bea2-01b6-430a-a830-00252c12cbaa", "c7f65174-db9a-49e4-9f6b-e053e82ff33a", "fd95d722-b04f-4aa6-944b-a214997a5603", "45a1696f-aced-4e96-952b-357868611ad8", "aff5a139-27e8-4a1a-b418-e2787fa7a80b", "bdb2ae7b-cbc3-4aa2-91e7-ab34324f0092", "0073d8ce-adfd-482a-8eb0-0206a1a42e41", "94afd9bd-2d86-47ee-95c8-761065dc321a", "e25e767f-167a-4ba6-9f2b-8d380c29d915", "68884f82-4ad2-4adc-a69d-8362fc481244", "b572d083-e56a-4279-87bd-d246ed99e013", "79500efb-9157-4aad-b85c-4de80b4e9c53", "ceedc21e-c473-4b4f-8ee2-48a7f4b92da4", "ecdbfefe-a4ae-4b57-9722-135f2328c604", "34fa4d61-84b4-43e6-854b-4979c4929e09", "408740ba-3c00-403b-9550-56cf78fff099", "7ff8ecc8-91b2-4291-b6bc-655da1d0b46b", "858f8cb6-7ff6-4a64-9f3c-5aef994cc1f3", "b533d3a5-7255-432a-bc05-9a04403716f8", "dcaa0aac-49c5-4cf8-bdb7-a3032fc53efc", "7cf1e9ed-2425-409e-bb37-3cbf562ae0e2", "422e747a-1c4d-437c-bb53-4dff080c5ce6", "69bea7f6-d831-4e6f-b9bd-c3048c46d5a4", "ac5e7eb8-1204-4de1-878d-2879f70e8f30", "388d99f3-3615-4302-9647-6ded82f0744d", "f889c538-efbd-4d1e-9cfc-c4de4d82794a", "41240bde-5a2b-4526-a05a-64ed2d3e1a85", "73c926c4-08ef-45af-bb1f-44527e48e007", "9a98de09-aa38-4066-bb4c-f75b544c34c2", "948307e4-a5a7-4267-98cc-fad577cb45f2", "50ae402e-85bc-42b3-89f2-2a7a531e9f0b", "9d794e05-b911-40be-9fc0-701925fe19bd", "b559fb24-0b43-4bf8-9912-f2d3e5039c11", "c226730d-eac8-4f44-bd8c-71cc28372868", "9e22648e-f46b-40a7-a98d-425705eb5429", "6e7310af-a899-492e-a70f-9c410452d595", "2dc803c5-167f-47fe-b386-620b6d903819", "4b57c80f-76b9-455c-9a76-5b7b93e4e870", "e1629f70-1469-4a2b-8d9f-73cb3da5e776", "4ed411c0-9d04-4776-8725-a24896e8f63a", "cd61df9a-1dc3-44a3-827a-8da4298161a0", "82360a27-2d79-419b-baa6-bc0587362ffc", "616daabc-275c-4458-8c48-da075a622e74", "12dabcfc-1b85-48c0-960d-eee3c0816600", "dedbd5d5-39b5-464f-89d4-b295602fe659", "92fe4850-83f2-405f-acb6-25826fb18330", "d8223d92-66c0-4a0d-944a-6071690ca057", "4417a029-1a21-4368-8c3e-7e81e903e16e", "9a81151d-65a2-415a-b63b-7d0a0ff4ea1a", "e6a8fb6f-8742-4089-8882-855ad842aa74", "a090a8ce-1ce8-4692-994b-eb3898628d44", "d5f7e775-b9b1-4ca0-ab29-6b5034f1853a", "776c6bd8-6167-490a-978c-c4adb5b8ac50", "871e7781-18af-4e9a-840e-c1c223645c80", "b3765655-f6bd-4338-a5c5-dd044644445e", "4a3dbaf1-9e1e-4ce6-97ae-a87e4e0deebc", "d7bc0587-da8a-4d5f-ad64-c1c84ae0e9f2", "65199215-3023-4e55-a0fe-8ed9d73e3bfa", "ba5bfe5b-5b01-4b36-861f-3dbf249b411c", "e678b908-1f37-4ea6-b5d5-386fd6ddcb44"], "durationId": "f40f1371-00ee-46a7-b69b-9731aeca0c2c", "parent": "d74f3451-39e9-46d9-a5d6-96bb989af763"}}, {"head": {"id": "03b8d4b8-e159-4d5b-bfc9-9f5caee2d096", "name": "default@PreviewArkTS watch work[4] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176404041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74f3451-39e9-46d9-a5d6-96bb989af763", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10164278439100, "endTime": 10176404158700}, "additional": {"logType": "error", "children": ["1536e39a-0ff4-4705-a31c-6ca5914a724c"], "durationId": "33d82e22-3124-494e-8cd5-19b4f40c4ce4"}}, {"head": {"id": "a9f89936-b371-4e72-859c-5206cc20899a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176404254100}, "additional": {"logType": "debug", "children": [], "durationId": "33d82e22-3124-494e-8cd5-19b4f40c4ce4"}}, {"head": {"id": "6c48132f-70b2-4c84-bb92-93a444928f1a", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:69:16\n Conversion of type 'void & Promise<ValueType>' to type 'number | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:87:16\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:105:16\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:123:16\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:21:10\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:23:9\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:23:82\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:26:9\n \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:40:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:41:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:42:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:42:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:43:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:43:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:44:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:44:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:45:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:45:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:48:17\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:48:32\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:51:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:56:7\n \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:66:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:67:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:68:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:68:45\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:84:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:85:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:86:29\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:86:44\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:102:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:103:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:104:29\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:104:44\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:120:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:121:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:122:32\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:122:47\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:138:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:139:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:140:34\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:140:49\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:141:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:141:45\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:142:29\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:142:44\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:165:32\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:170:28\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:171:27\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:172:27\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:173:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:158:46\n Object literals cannot be used as type declarations (arkts-no-obj-literals-as-types)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:176:16\n Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:196:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:197:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:198:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:198:33\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:199:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:199:33\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:200:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:200:33\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:201:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:201:33\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:202:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:202:33\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:203:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:208:7\n \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:218:13\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:219:11\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:220:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:220:30\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:221:15\n Using \"this\" inside stand-alone functions is not supported (arkts-no-standalone-this)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:226:7\n \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/pages/WalletPage.ets:206:20\n Unknown resource name 'settings'.\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176405047600}, "additional": {"logType": "debug", "children": [], "durationId": "33d82e22-3124-494e-8cd5-19b4f40c4ce4"}}, {"head": {"id": "71af4fda-383b-43da-9ac3-f6acce0ed244", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176414989000, "endTime": 10176415047300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad74d299-e1b5-43f0-bf89-480bf80dc004", "logId": "84ad6f4e-b172-437b-a91f-f1ebe2f5089b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84ad6f4e-b172-437b-a91f-f1ebe2f5089b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176414989000, "endTime": 10176415047300}, "additional": {"logType": "info", "children": [], "durationId": "71af4fda-383b-43da-9ac3-f6acce0ed244"}}, {"head": {"id": "f514067b-0aa6-43ec-9c33-c79643316cd8", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10163344020100, "endTime": 10176415177900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 28}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "f367d7cf-d248-4894-a098-0fb3f905376e", "name": "BUILD FAILED in 13 s 72 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176415206900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "36f1f06a-2415-4ca4-bdbb-d7683161c1fe", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176416023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "956618d6-3640-4b2a-966f-5ae2f5bd7945", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176416330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d17674-5831-4adb-aee3-9307a3452ba7", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176416588100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b81390-ab03-4093-b8de-bbc502c19993", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176416845900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4e21a81-0553-4834-97ad-19a59c8eb09d", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176417091300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9376eef7-3dae-4cfe-8669-2dafc3b456eb", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176417164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a385714f-298d-4e2a-802c-91823105af62", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176417396900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a39031d6-f58c-4de5-a0ae-6d91acbc4769", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176417646600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad62ea42-6c39-4376-a51f-9442ec8edff7", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176417886400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2354595-1a3e-465a-be61-17e637a449d6", "name": "Incremental task entry:default@PreBuild post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418202000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a66e97-2bce-4660-b57f-bab90b219666", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8691997f-6415-41db-a2c8-9d865c643996", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418333900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b334ccff-a134-4946-b555-9ec845183f87", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418376000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d76c698-c180-4ab3-b6e0-b902fdb281ff", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418413500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e184220f-63e2-40dc-af56-d7f4ae339375", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418453200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "965d1951-4a5c-4f28-9c6c-16333d14935b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176418492700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a883e5-16ce-4051-ba9f-e3cf54b7afea", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176419118100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3810e05-d4cc-494b-ab24-e7ce95f86390", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176419188800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97f060ab-e34a-4e58-97c2-a47259360672", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176421648300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2d77da-3e9a-41c8-87de-bd58bbb18d7d", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176422047200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70aad7de-5a96-498e-beb3-c75b855a490d", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176439623600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b84204-62d6-4e53-a4f2-859168646ed1", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:22 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176440274100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c2de054-9117-4976-bf43-ca61b13b7d68", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176440482100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07155088-b497-4e0a-b9f9-52a9690d1117", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176440571800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aed26ac-e7df-44a3-a2c1-56adac65e35e", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176441312800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "157d5772-da11-47a0-b2fc-e9feb0ed15bc", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176441679700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6bcd614-3429-4dc4-9ec1-6520dd7f7bc1", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176441947900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c87882-2989-4809-8a56-235a8cbae873", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176442229200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac9d7d5-48db-4fa0-b603-e1e700a622b2", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176445070700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c1ac56-69b5-43de-b37a-a678520f8148", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176445789300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56cbbba9-dbe5-41c5-89b0-32e5866a1f24", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176446057500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58f6e2e-35cd-4346-8a92-46643b53a311", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176460452000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbed45a-ab44-4b70-a717-54ff1abfa829", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176461398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4d4486b-96dd-430b-9ee7-d0d4982f5145", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176461690200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3523b160-3a6e-4513-9e48-cf56323c64ae", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176461963300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b3da8e-0268-4cea-8284-36a8371e4bc7", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176462667500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b85fbf0-bbf0-468e-ba2a-357c9824ba8a", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176466610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c46f58f-2ff4-4a11-845e-ee0e4440081d", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176466897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb3c9ca-db88-44f7-8067-1bab12610170", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176467170100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4bd7861-2d7e-4a9b-9b47-2b5693fa5f39", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176467469300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eb9ffa6-bfa6-4308-b0b2-5e3d558fb104", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176467758000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}