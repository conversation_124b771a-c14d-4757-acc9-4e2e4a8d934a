import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

interface RouterParams {
  bankName?: string;
  cardNumber?: string;
  fullCardNumber?: string;
  cardType?: string;
}

@Entry
@Component
struct CardDetailPage {
  private params: RouterParams = router.getParams() as RouterParams;

  @State bankName: string = this.params.bankName || '';
  @State fullCardNumber: string = this.params.fullCardNumber || '';
  @State cardType: string = this.params.cardType || '';

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.back'))
          .width(24)
          .height(24)
          .margin({ left: 15, right: 15 })
          .onClick(() => {
            router.back();
          });

        Text('卡号详情')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1);
      }
      .width('100%')
      .height(50)
      .backgroundColor('#ffffff')
      .alignItems(VerticalAlign.Center);

      // 卡片详情
      Column() {
        Stack() {
          Column() {
            // 银行图标和名称
            Row() {
              Stack() {
                Circle()
                  .width(40)
                  .height(40)
                  .fill(Color.White);
                Image(this.getBankIcon())
                  .width(30)
                  .height(30);
              }
              .margin({ right: 15 });

              Text(this.bankName)
                .fontSize(18)
                .fontColor('#ffffff');
            }
            .margin({ top: 20, bottom: 30 });

            // 完整卡号
            Column() {
              Text('完整卡号')
                .fontSize(14)
                .fontColor('#ffffff');
              Text(this.fullCardNumber)
                .fontSize(18)
                .fontColor('#ffffff')
                .margin({ top: 10 });
            }
            .margin({ top: 20 });

            // 卡片类型
            Row() {
              Text('卡片类型:')
                .fontSize(14)
                .fontColor('#ffffff');
              Text(this.cardType === 'debit' ? '借记卡' : '信用卡')
                .fontSize(14)
                .fontColor('#ffffff')
                .margin({ left: 10 });
            }
            .margin({ top: 20 });
          }
          .width('100%')
          .padding(20);
        }
        .width('90%')
        .height(250)
        .backgroundColor('#1274ca')
        .borderRadius(12)
        .margin({ top: 20 });

        // 复制卡号按钮
        Button('复制卡号', { type: ButtonType.Capsule })
          .width('90%')
          .height(50)
          .fontSize(16)
          .margin({ top: 20 })
          .onClick(() => {
            promptAction.showToast({ message: '卡号已复制', duration: 1000 });
          });
      }
      .width('100%');
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5');
  }

  getBankIcon(): Resource {
    switch (this.bankName) {
      case '中国银行': return $r('app.media.boc');
      case '建设银行': return $r('app.media.ccb');
      case '工商银行': return $r('app.media.icbc');
      default: return $r('app.media.bank');
    }
  }
}