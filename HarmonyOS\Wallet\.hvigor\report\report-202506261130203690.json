{"version": "2.0", "ppid": 22720, "events": [{"head": {"id": "6fb3b820-7296-4615-9655-a165683c236b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280023642100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbf7c19f-96e8-4a15-a355-01b8b4e43891", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280025601900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec19b832-5e8b-4bd7-93ad-7c07eed0a45b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280025957900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d81517f-ac13-4d9f-b294-74463015a79f", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280026719100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f17a4ad9-553a-4f09-b623-d79dee78224f", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280027039600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b09f93-d0e7-46f8-b2cf-da3a54c1cd00", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280233597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280240288800, "endTime": 10280381270900}, "additional": {"children": ["b375c3b0-ab7b-4353-b0eb-69c31a2c1742", "9318c5f2-329e-4b45-baea-7d590613bd7f", "08d63cda-bffb-4877-adcc-c6f9cff452f9", "ce881b78-2926-4775-b70d-fa208591cd91", "0b6c3313-29d7-42cf-b842-c097c6c191ee", "157507af-5c16-4543-b7f6-496310668123", "3c2e6e8e-3ac5-4aa2-9f04-b5f8dd7d4524"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "bf766aaa-19fe-4009-9c94-2a06818a0690"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b375c3b0-ab7b-4353-b0eb-69c31a2c1742", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280240290200, "endTime": 10280250973800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "cecce59a-b225-4274-8845-3405763193ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9318c5f2-329e-4b45-baea-7d590613bd7f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280250986800, "endTime": 10280380184500}, "additional": {"children": ["95892bd6-bfc6-4ae7-8799-cfc95d5b1a71", "e142a169-9a88-420d-a061-6a67017de8a1", "e10f4596-ccf7-4824-9ec7-3f7a7b29d9f9", "0a4c00ce-65ae-4fde-8b10-2ff46ef2a425", "8bcf38a8-07a4-469f-9cc7-92000d540242", "623ed527-817f-4b5b-983a-1ec0498296f1", "c796ca32-36f4-41e9-ad84-ed4d8826c7d7", "f635abcd-9c8b-4968-8c6f-d491384c6452", "5f4a75c7-2bfb-42a2-a91b-b6c933ced7ce"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08d63cda-bffb-4877-adcc-c6f9cff452f9", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280380209100, "endTime": 10280381255900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "95147dd2-0f88-4775-a4b9-9b5d15e6812a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce881b78-2926-4775-b70d-fa208591cd91", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280381261200, "endTime": 10280381267100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "72ac46f8-998d-4d56-ad81-3065a30ec404"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b6c3313-29d7-42cf-b842-c097c6c191ee", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280243522200, "endTime": 10280243553700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "a8f47433-fa79-4b89-a0ea-48c7ec07c615"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8f47433-fa79-4b89-a0ea-48c7ec07c615", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280243522200, "endTime": 10280243553700}, "additional": {"logType": "info", "children": [], "durationId": "0b6c3313-29d7-42cf-b842-c097c6c191ee", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "157507af-5c16-4543-b7f6-496310668123", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280248256900, "endTime": 10280248270500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "e7941b1e-8242-4a68-932f-92c7c30f843d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7941b1e-8242-4a68-932f-92c7c30f843d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280248256900, "endTime": 10280248270500}, "additional": {"logType": "info", "children": [], "durationId": "157507af-5c16-4543-b7f6-496310668123", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "0ae13e21-1929-4c64-9c2f-afe84ce0cb26", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280248332000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd3b424-8747-48fc-a804-8fc2b5c237a1", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280250872900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cecce59a-b225-4274-8845-3405763193ab", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280240290200, "endTime": 10280250973800}, "additional": {"logType": "info", "children": [], "durationId": "b375c3b0-ab7b-4353-b0eb-69c31a2c1742", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "95892bd6-bfc6-4ae7-8799-cfc95d5b1a71", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280256043500, "endTime": 10280256055500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "027a82b3-803a-48b2-9a3f-30b08d8f2f0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e142a169-9a88-420d-a061-6a67017de8a1", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280256072500, "endTime": 10280260273000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "f0f90aeb-34f4-45fa-8ee0-4c787284fc80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e10f4596-ccf7-4824-9ec7-3f7a7b29d9f9", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280260286400, "endTime": 10280325547600}, "additional": {"children": ["2c552aab-1d70-430d-8841-99a81c79e8b9", "abf0977a-fdb3-4e19-b3cc-cd3f50a6a9f3", "8e49722c-3f8c-4d7f-8d96-6f0579033b5a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "20e8b0e9-8b9b-448f-a01e-ccbc5279939d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a4c00ce-65ae-4fde-8b10-2ff46ef2a425", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280325562200, "endTime": 10280344819100}, "additional": {"children": ["726d25c2-ecad-4327-aabe-ac799aa8262d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "025832a5-8298-4067-a96a-4571e32f7971"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bcf38a8-07a4-469f-9cc7-92000d540242", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280344826900, "endTime": 10280357749200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "46df91c8-b14d-45ca-8623-042f111f29c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "623ed527-817f-4b5b-983a-1ec0498296f1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280358816700, "endTime": 10280366689700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "00c8ae2c-3ee3-4293-ac02-c6dd66b55a90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c796ca32-36f4-41e9-ad84-ed4d8826c7d7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280366707200, "endTime": 10280380057500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "1cc0aba6-143b-4a23-b681-ee7f24a0391a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f635abcd-9c8b-4968-8c6f-d491384c6452", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280380073900, "endTime": 10280380173800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "87cced7b-c6a1-4a94-8f5b-80b958855785"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "027a82b3-803a-48b2-9a3f-30b08d8f2f0f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280256043500, "endTime": 10280256055500}, "additional": {"logType": "info", "children": [], "durationId": "95892bd6-bfc6-4ae7-8799-cfc95d5b1a71", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "f0f90aeb-34f4-45fa-8ee0-4c787284fc80", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280256072500, "endTime": 10280260273000}, "additional": {"logType": "info", "children": [], "durationId": "e142a169-9a88-420d-a061-6a67017de8a1", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "2c552aab-1d70-430d-8841-99a81c79e8b9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280260892400, "endTime": 10280260911800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e10f4596-ccf7-4824-9ec7-3f7a7b29d9f9", "logId": "94be28e5-6a7e-4d2d-b5c3-08b83c91b25e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94be28e5-6a7e-4d2d-b5c3-08b83c91b25e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280260892400, "endTime": 10280260911800}, "additional": {"logType": "info", "children": [], "durationId": "2c552aab-1d70-430d-8841-99a81c79e8b9", "parent": "20e8b0e9-8b9b-448f-a01e-ccbc5279939d"}}, {"head": {"id": "abf0977a-fdb3-4e19-b3cc-cd3f50a6a9f3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280263080000, "endTime": 10280324762300}, "additional": {"children": ["064caf33-f9a8-4807-a00c-2020b9ff0a5b", "0efac8a0-bc80-4e8c-a8ab-9e77c1240892"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e10f4596-ccf7-4824-9ec7-3f7a7b29d9f9", "logId": "61b2226c-27d6-47ff-bd75-679b5a951dab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "064caf33-f9a8-4807-a00c-2020b9ff0a5b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280263081100, "endTime": 10280267492500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abf0977a-fdb3-4e19-b3cc-cd3f50a6a9f3", "logId": "b70465bb-ea0c-4ea4-9d94-19026fd4dbaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0efac8a0-bc80-4e8c-a8ab-9e77c1240892", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280267505400, "endTime": 10280324751900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abf0977a-fdb3-4e19-b3cc-cd3f50a6a9f3", "logId": "d12f38d2-f3dd-4948-8201-8230e9af5c4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d9dc333-6ed7-4491-9892-d6c693415800", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280263087700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35fbd3cd-91b7-456b-bffc-8922d568af45", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280267392400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b70465bb-ea0c-4ea4-9d94-19026fd4dbaf", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280263081100, "endTime": 10280267492500}, "additional": {"logType": "info", "children": [], "durationId": "064caf33-f9a8-4807-a00c-2020b9ff0a5b", "parent": "61b2226c-27d6-47ff-bd75-679b5a951dab"}}, {"head": {"id": "bf9aa1ca-ddc7-4357-961c-1a8d7282b3e4", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280267519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05634bb8-2aff-4394-bb71-2f1c753c8ab4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280273329800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f47880e-68a4-4173-92cc-4fb2a92004e5", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280273424400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af167ba0-0dc4-4f98-a500-ce99df751143", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280273653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa75843-90b0-406d-8d49-264a792f0ce2", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280273873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70a9e528-847d-44e4-9272-8167b3fcd41c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280275227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18751690-c9df-497f-9e39-032bf801fb73", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280278536400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04994ce3-0bb7-4688-92f6-b54a0ffd5da6", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280286443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea088394-1518-4885-b73f-26755725f911", "name": "Sdk init in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280305916800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f4328de-1462-4490-85cc-62481b62f23d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280306045600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 30}, "markType": "other"}}, {"head": {"id": "1f098036-8c50-4715-acef-17101ff276b5", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280306061000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 30}, "markType": "other"}}, {"head": {"id": "5a1bdd4c-ee44-405f-b5c2-41d609a2e528", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280324471200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c979a3a6-f1ad-4c95-8e53-eb66321bdbf2", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280324584700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29bad605-22c0-4873-8909-b002df29f9fe", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280324650300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa222123-0afb-465c-8e61-e4d1f9a176c7", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280324700300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12f38d2-f3dd-4948-8201-8230e9af5c4b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280267505400, "endTime": 10280324751900}, "additional": {"logType": "info", "children": [], "durationId": "0efac8a0-bc80-4e8c-a8ab-9e77c1240892", "parent": "61b2226c-27d6-47ff-bd75-679b5a951dab"}}, {"head": {"id": "61b2226c-27d6-47ff-bd75-679b5a951dab", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280263080000, "endTime": 10280324762300}, "additional": {"logType": "info", "children": ["b70465bb-ea0c-4ea4-9d94-19026fd4dbaf", "d12f38d2-f3dd-4948-8201-8230e9af5c4b"], "durationId": "abf0977a-fdb3-4e19-b3cc-cd3f50a6a9f3", "parent": "20e8b0e9-8b9b-448f-a01e-ccbc5279939d"}}, {"head": {"id": "8e49722c-3f8c-4d7f-8d96-6f0579033b5a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280325394000, "endTime": 10280325527500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e10f4596-ccf7-4824-9ec7-3f7a7b29d9f9", "logId": "6f5d7dce-f8ce-42cf-855c-f2261cba8c04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f5d7dce-f8ce-42cf-855c-f2261cba8c04", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280325394000, "endTime": 10280325527500}, "additional": {"logType": "info", "children": [], "durationId": "8e49722c-3f8c-4d7f-8d96-6f0579033b5a", "parent": "20e8b0e9-8b9b-448f-a01e-ccbc5279939d"}}, {"head": {"id": "20e8b0e9-8b9b-448f-a01e-ccbc5279939d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280260286400, "endTime": 10280325547600}, "additional": {"logType": "info", "children": ["94be28e5-6a7e-4d2d-b5c3-08b83c91b25e", "61b2226c-27d6-47ff-bd75-679b5a951dab", "6f5d7dce-f8ce-42cf-855c-f2261cba8c04"], "durationId": "e10f4596-ccf7-4824-9ec7-3f7a7b29d9f9", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "726d25c2-ecad-4327-aabe-ac799aa8262d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280326209300, "endTime": 10280344807100}, "additional": {"children": ["bade7fad-380c-4451-8b36-389b7978e77c", "ff1130e4-a029-40da-a092-55e4fa4dea4b", "8d79a30f-5ab2-413c-8c08-13c08a8c6065"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0a4c00ce-65ae-4fde-8b10-2ff46ef2a425", "logId": "c1826281-94b7-4bc4-8470-6cf1d10ac9e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bade7fad-380c-4451-8b36-389b7978e77c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280329023000, "endTime": 10280329038300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "726d25c2-ecad-4327-aabe-ac799aa8262d", "logId": "6017fb6c-8af2-4f49-8066-8c4e584d9dba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6017fb6c-8af2-4f49-8066-8c4e584d9dba", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280329023000, "endTime": 10280329038300}, "additional": {"logType": "info", "children": [], "durationId": "bade7fad-380c-4451-8b36-389b7978e77c", "parent": "c1826281-94b7-4bc4-8470-6cf1d10ac9e8"}}, {"head": {"id": "ff1130e4-a029-40da-a092-55e4fa4dea4b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280330956900, "endTime": 10280343620900}, "additional": {"children": ["9f5ca3d5-6d5a-4618-a821-74e2f50893f5", "b925f5c2-21b1-4647-aae0-daeba4f09585"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "726d25c2-ecad-4327-aabe-ac799aa8262d", "logId": "949a258f-a8fc-45b8-99c9-96ac9ae40fd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f5ca3d5-6d5a-4618-a821-74e2f50893f5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280330958000, "endTime": 10280334517300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff1130e4-a029-40da-a092-55e4fa4dea4b", "logId": "03503d12-3b66-40e9-9782-dfcef39c81ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b925f5c2-21b1-4647-aae0-daeba4f09585", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280334528200, "endTime": 10280343610700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ff1130e4-a029-40da-a092-55e4fa4dea4b", "logId": "76401688-f724-441d-8032-d39e0876b84d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "627a8d7a-1e2f-45d7-bf20-fd1dc7a83d18", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280330988100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5906491e-7a3e-4b63-b154-2c8b9ca28e44", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280334431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03503d12-3b66-40e9-9782-dfcef39c81ac", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280330958000, "endTime": 10280334517300}, "additional": {"logType": "info", "children": [], "durationId": "9f5ca3d5-6d5a-4618-a821-74e2f50893f5", "parent": "949a258f-a8fc-45b8-99c9-96ac9ae40fd8"}}, {"head": {"id": "a1994248-a0a0-4b18-818b-e36191c764a7", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280334541300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fddbf5e7-bf6e-43cc-a2b4-74b63c6c0d03", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340148600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c4fe30-92b0-426b-8279-52288b6e4e65", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1489ae75-45f6-46b7-a529-5ba58c6563ed", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7024f72a-ce1a-4581-bb40-40d196c3eaef", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b94410-60a6-4850-8b10-fd179f0331bc", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b75e95ad-197d-412c-a935-b31f1dc5b77a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340837800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9582d8e-6710-4c13-bfe0-09f454c64922", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280340891300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0320834f-5496-41ba-a59f-3348e1b65d49", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280343350200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb104753-8a42-4db3-b537-821909f25a9f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280343455700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ff6c5c-b648-4df5-8448-74aaf2e5ca61", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280343517900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b09734-945c-41e8-bdc7-3b4ab17a1fe1", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280343567700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76401688-f724-441d-8032-d39e0876b84d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280334528200, "endTime": 10280343610700}, "additional": {"logType": "info", "children": [], "durationId": "b925f5c2-21b1-4647-aae0-daeba4f09585", "parent": "949a258f-a8fc-45b8-99c9-96ac9ae40fd8"}}, {"head": {"id": "949a258f-a8fc-45b8-99c9-96ac9ae40fd8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280330956900, "endTime": 10280343620900}, "additional": {"logType": "info", "children": ["03503d12-3b66-40e9-9782-dfcef39c81ac", "76401688-f724-441d-8032-d39e0876b84d"], "durationId": "ff1130e4-a029-40da-a092-55e4fa4dea4b", "parent": "c1826281-94b7-4bc4-8470-6cf1d10ac9e8"}}, {"head": {"id": "8d79a30f-5ab2-413c-8c08-13c08a8c6065", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280344780200, "endTime": 10280344793900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "726d25c2-ecad-4327-aabe-ac799aa8262d", "logId": "85871b15-a1b2-461c-96a8-51f6007939d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85871b15-a1b2-461c-96a8-51f6007939d4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280344780200, "endTime": 10280344793900}, "additional": {"logType": "info", "children": [], "durationId": "8d79a30f-5ab2-413c-8c08-13c08a8c6065", "parent": "c1826281-94b7-4bc4-8470-6cf1d10ac9e8"}}, {"head": {"id": "c1826281-94b7-4bc4-8470-6cf1d10ac9e8", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280326209300, "endTime": 10280344807100}, "additional": {"logType": "info", "children": ["6017fb6c-8af2-4f49-8066-8c4e584d9dba", "949a258f-a8fc-45b8-99c9-96ac9ae40fd8", "85871b15-a1b2-461c-96a8-51f6007939d4"], "durationId": "726d25c2-ecad-4327-aabe-ac799aa8262d", "parent": "025832a5-8298-4067-a96a-4571e32f7971"}}, {"head": {"id": "025832a5-8298-4067-a96a-4571e32f7971", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280325562200, "endTime": 10280344819100}, "additional": {"logType": "info", "children": ["c1826281-94b7-4bc4-8470-6cf1d10ac9e8"], "durationId": "0a4c00ce-65ae-4fde-8b10-2ff46ef2a425", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "b74c1f93-4267-413f-ba5e-1e0e7ac2acee", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280357322700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a211711c-578f-4cd4-9e7f-97a3c2ed138e", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280357649400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46df91c8-b14d-45ca-8623-042f111f29c6", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280344826900, "endTime": 10280357749200}, "additional": {"logType": "info", "children": [], "durationId": "8bcf38a8-07a4-469f-9cc7-92000d540242", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "5f4a75c7-2bfb-42a2-a91b-b6c933ced7ce", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280358614700, "endTime": 10280358804000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9318c5f2-329e-4b45-baea-7d590613bd7f", "logId": "5a6fd5da-e5bb-4dc3-996e-290478ebfebd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9aa455c-9a6c-4063-913a-fa3cbdc70181", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280358645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6fd5da-e5bb-4dc3-996e-290478ebfebd", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280358614700, "endTime": 10280358804000}, "additional": {"logType": "info", "children": [], "durationId": "5f4a75c7-2bfb-42a2-a91b-b6c933ced7ce", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "4bdc890a-90d3-4dac-a9c0-af3588a80e4c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280360492200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd1811ca-d4f5-4b3d-b1d6-3a709b98553a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280366007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c8ae2c-3ee3-4293-ac02-c6dd66b55a90", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280358816700, "endTime": 10280366689700}, "additional": {"logType": "info", "children": [], "durationId": "623ed527-817f-4b5b-983a-1ec0498296f1", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "22c97e8d-44fb-4020-bdf7-584460f3a5d0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280366725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec3a0dc-deb3-45bd-bdbe-46fee0336c84", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280372093000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742a78ee-fdd2-47b7-a30d-78c1e0031467", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280372215300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cb009c-baa8-4305-92d3-bfe55f60d0f4", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280372452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fababd1-1827-4411-aaab-e8154de30a12", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280377324200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c403d74-0875-4375-ae30-df71360f098f", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280377431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc0aba6-143b-4a23-b681-ee7f24a0391a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280366707200, "endTime": 10280380057500}, "additional": {"logType": "info", "children": [], "durationId": "c796ca32-36f4-41e9-ad84-ed4d8826c7d7", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "3af16e95-3f1e-4977-bb0d-5aee113f0490", "name": "Configuration phase cost:125 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280380101600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87cced7b-c6a1-4a94-8f5b-80b958855785", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280380073900, "endTime": 10280380173800}, "additional": {"logType": "info", "children": [], "durationId": "f635abcd-9c8b-4968-8c6f-d491384c6452", "parent": "3a45cebe-a830-42e9-b6b6-f9fffa475220"}}, {"head": {"id": "3a45cebe-a830-42e9-b6b6-f9fffa475220", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280250986800, "endTime": 10280380184500}, "additional": {"logType": "info", "children": ["027a82b3-803a-48b2-9a3f-30b08d8f2f0f", "f0f90aeb-34f4-45fa-8ee0-4c787284fc80", "20e8b0e9-8b9b-448f-a01e-ccbc5279939d", "025832a5-8298-4067-a96a-4571e32f7971", "46df91c8-b14d-45ca-8623-042f111f29c6", "00c8ae2c-3ee3-4293-ac02-c6dd66b55a90", "1cc0aba6-143b-4a23-b681-ee7f24a0391a", "87cced7b-c6a1-4a94-8f5b-80b958855785", "5a6fd5da-e5bb-4dc3-996e-290478ebfebd"], "durationId": "9318c5f2-329e-4b45-baea-7d590613bd7f", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "3c2e6e8e-3ac5-4aa2-9f04-b5f8dd7d4524", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280381234600, "endTime": 10280381246900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df5e65ce-94fb-4428-a59d-864daef6d5fb", "logId": "83f4f9dc-62c2-45ef-9675-588ca1c701a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83f4f9dc-62c2-45ef-9675-588ca1c701a0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280381234600, "endTime": 10280381246900}, "additional": {"logType": "info", "children": [], "durationId": "3c2e6e8e-3ac5-4aa2-9f04-b5f8dd7d4524", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "95147dd2-0f88-4775-a4b9-9b5d15e6812a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280380209100, "endTime": 10280381255900}, "additional": {"logType": "info", "children": [], "durationId": "08d63cda-bffb-4877-adcc-c6f9cff452f9", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "72ac46f8-998d-4d56-ad81-3065a30ec404", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280381261200, "endTime": 10280381267100}, "additional": {"logType": "info", "children": [], "durationId": "ce881b78-2926-4775-b70d-fa208591cd91", "parent": "bf766aaa-19fe-4009-9c94-2a06818a0690"}}, {"head": {"id": "bf766aaa-19fe-4009-9c94-2a06818a0690", "name": "init", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280240288800, "endTime": 10280381270900}, "additional": {"logType": "info", "children": ["cecce59a-b225-4274-8845-3405763193ab", "3a45cebe-a830-42e9-b6b6-f9fffa475220", "95147dd2-0f88-4775-a4b9-9b5d15e6812a", "72ac46f8-998d-4d56-ad81-3065a30ec404", "a8f47433-fa79-4b89-a0ea-48c7ec07c615", "e7941b1e-8242-4a68-932f-92c7c30f843d", "83f4f9dc-62c2-45ef-9675-588ca1c701a0"], "durationId": "df5e65ce-94fb-4428-a59d-864daef6d5fb"}}, {"head": {"id": "75ceedce-52d0-4f9b-9ea3-de743ff41f19", "name": "Configuration task cost before running: 145 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280381620800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d78a5f6-a1a7-4b31-9bc8-dee2c3b2fb75", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280388292300, "endTime": 10280399475000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0dc9c7b0-375b-4303-9ee5-c57b884120f4", "logId": "9fa0fc42-4906-459d-996a-24cf26d3f5fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dc9c7b0-375b-4303-9ee5-c57b884120f4", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280383281800}, "additional": {"logType": "detail", "children": [], "durationId": "0d78a5f6-a1a7-4b31-9bc8-dee2c3b2fb75"}}, {"head": {"id": "1398238b-4149-4469-be90-b9333526f836", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280384101200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90362b07-2882-4118-a219-331e32d1dbeb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280384194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb2617e2-6b84-46a7-875e-5ca14e509bb8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280384251600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cb36d9-c612-483c-bcea-0459478ed188", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280388304900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8938ec-0994-4cae-bb7f-b372dd440248", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280399218700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "456e851d-4c74-455d-90e8-b113304ea182", "name": "entry : default@PreBuild cost memory 0.29720306396484375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280399359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa0fc42-4906-459d-996a-24cf26d3f5fd", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280388292300, "endTime": 10280399475000}, "additional": {"logType": "info", "children": [], "durationId": "0d78a5f6-a1a7-4b31-9bc8-dee2c3b2fb75"}}, {"head": {"id": "788f291b-44df-49f4-bdcb-a838415d91eb", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280404543400, "endTime": 10280408207500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "33470afb-46a3-4558-82fc-5cb2cf08a2a5", "logId": "425984f5-6baf-4294-9e34-81c9256d57f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33470afb-46a3-4558-82fc-5cb2cf08a2a5", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280403096000}, "additional": {"logType": "detail", "children": [], "durationId": "788f291b-44df-49f4-bdcb-a838415d91eb"}}, {"head": {"id": "8eca9465-bdfa-4cfa-beb3-c62164094c5c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280403658300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "930c8055-699f-4c57-ab23-97ac3f6edcbc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280403757400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95b32d1-3add-428d-93a9-53e6407a90ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280403816800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3385a3d4-f10b-41df-8666-8616d1dcded0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280404555500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bb3b4cb-48e8-4569-86d8-5a847ce0ec8b", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280408041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53bf2570-8df7-441a-bff2-55352bd33668", "name": "entry : default@MergeProfile cost memory 0.202239990234375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280408141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425984f5-6baf-4294-9e34-81c9256d57f0", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280404543400, "endTime": 10280408207500}, "additional": {"logType": "info", "children": [], "durationId": "788f291b-44df-49f4-bdcb-a838415d91eb"}}, {"head": {"id": "edaad18a-2a61-4955-8c02-eb3a9a9f0510", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280411328100, "endTime": 10280414021200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0ef67941-926f-42dd-8eb3-a8b38ec0d4ee", "logId": "09d4eca2-658e-4f53-b04f-9daac65de225"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ef67941-926f-42dd-8eb3-a8b38ec0d4ee", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280409827500}, "additional": {"logType": "detail", "children": [], "durationId": "edaad18a-2a61-4955-8c02-eb3a9a9f0510"}}, {"head": {"id": "c6c13e15-53fd-4939-8447-d920ed40f2ef", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280410309400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce1bb2d-2416-46cc-a395-3096c0b54078", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280410400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb9dac8-2f13-44dc-aec5-fc5eb1060576", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280410456400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58aea9b4-e873-4dcb-b095-753d3578885a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280411341400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c58a4f55-68df-4d70-8f82-ab85d54c2098", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280412542400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c99d139-a696-447b-9b97-b465ddd60f91", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280413841300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc41ad70-ddb7-445a-85e8-494ba4ab840f", "name": "entry : default@CreateBuildProfile cost memory 0.10170745849609375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280413945100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d4eca2-658e-4f53-b04f-9daac65de225", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280411328100, "endTime": 10280414021200}, "additional": {"logType": "info", "children": [], "durationId": "edaad18a-2a61-4955-8c02-eb3a9a9f0510"}}, {"head": {"id": "e4eb0d6f-c3c6-4394-9099-786b6759efa6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417160200, "endTime": 10280417897500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0cb12889-c607-4f0d-826f-b625f3c2f159", "logId": "1d1bde1a-9999-4b28-af33-0bc506f87828"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cb12889-c607-4f0d-826f-b625f3c2f159", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280415775800}, "additional": {"logType": "detail", "children": [], "durationId": "e4eb0d6f-c3c6-4394-9099-786b6759efa6"}}, {"head": {"id": "43ae6a08-a205-4d10-bbf3-b9e0021087f9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280416283600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87fdd6f8-ce6c-4771-8b43-765fec8caf35", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280416384800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5400b9ef-4432-468e-a1db-cdde90708e9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280416443200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf2cdb13-e326-4bb6-9945-2386972acf83", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c086ffe-992f-4f9c-8c19-783d1abd6104", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417414900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbc9ec4-208e-4ffa-b69b-e7c60ef7b23d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e84a592-f791-454f-aaed-10acda686eda", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4cc0f17-4b51-4666-aed9-97d330b39173", "name": "entry : default@PreCheckSyscap cost memory 0.05068206787109375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417736600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715bcf41-5bd6-40e3-a12e-a2a1edbaf838", "name": "runTaskFromQueue task cost before running: 181 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417833500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d1bde1a-9999-4b28-af33-0bc506f87828", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280417160200, "endTime": 10280417897500, "totalTime": 652300}, "additional": {"logType": "info", "children": [], "durationId": "e4eb0d6f-c3c6-4394-9099-786b6759efa6"}}, {"head": {"id": "03fe54fb-0d50-49bb-b13c-d11adfd2b9bf", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280431117100, "endTime": 10280432329400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9c9224c9-91fa-4a5d-930b-049057731f46", "logId": "48ac9201-3aee-4194-ada2-81935803e99c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c9224c9-91fa-4a5d-930b-049057731f46", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280419757500}, "additional": {"logType": "detail", "children": [], "durationId": "03fe54fb-0d50-49bb-b13c-d11adfd2b9bf"}}, {"head": {"id": "2583e0cd-8bb0-47b6-ba47-5b1cd8ac17b5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280420249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef621cb5-c554-4cee-9ccb-bed1bd5d49f9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280420350000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "548fdf35-5f4f-41b5-8ab2-ee48b244f593", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280420405400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a3ed5f-2cfc-47c8-b6a8-ddf91086d2ea", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280431140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d355e2-55b4-4e56-84c4-c5b510f74c15", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280431457900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19cc702e-1728-4966-8a34-84f0fc4c764b", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280432132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9cc48b-42e7-4925-bfd0-5b94a03d51c9", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07024383544921875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280432229500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ac9201-3aee-4194-ada2-81935803e99c", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280431117100, "endTime": 10280432329400}, "additional": {"logType": "info", "children": [], "durationId": "03fe54fb-0d50-49bb-b13c-d11adfd2b9bf"}}, {"head": {"id": "e7956428-692f-4003-b537-e0e7c607ac49", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280436147400, "endTime": 10280437411000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "07ececa7-f7ba-4cd7-a23d-079841351f55", "logId": "8b57ee8c-e639-48f2-af8c-56b1995de948"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07ececa7-f7ba-4cd7-a23d-079841351f55", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280433923400}, "additional": {"logType": "detail", "children": [], "durationId": "e7956428-692f-4003-b537-e0e7c607ac49"}}, {"head": {"id": "a6cb632b-1166-444a-a446-2b8d00156e09", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280434427700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baeefc0e-ef99-458d-a679-053a0326e173", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280434514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545d7c24-14ba-416b-8f4c-ea9c2665ee75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280434569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b0074c-e0bb-4ffa-89c9-07446910dbc4", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280436160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed63c32e-0a2d-436e-a107-6c85ac2aef63", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280437248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e06f21b-c9f7-4450-b810-155288fecc7e", "name": "entry : default@ProcessProfile cost memory 0.0584716796875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280437345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b57ee8c-e639-48f2-af8c-56b1995de948", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280436147400, "endTime": 10280437411000}, "additional": {"logType": "info", "children": [], "durationId": "e7956428-692f-4003-b537-e0e7c607ac49"}}, {"head": {"id": "3e4a5f11-1897-4451-9117-914e14c86335", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280441097100, "endTime": 10280447034400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8abb92fa-a206-47c3-9413-8c0bb750f9e9", "logId": "bde1894c-d315-42fd-b5ec-58a959ddb98b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8abb92fa-a206-47c3-9413-8c0bb750f9e9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280438915100}, "additional": {"logType": "detail", "children": [], "durationId": "3e4a5f11-1897-4451-9117-914e14c86335"}}, {"head": {"id": "8107a946-4a88-4269-90a1-57efcb083c48", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280439396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02625c99-3dba-469d-85c5-e48df7863ff1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280439476700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f4bf57-8fb0-431e-b1bd-c6c7f4e7fe01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280439530900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fdfcc46-d9e9-41d1-8b13-375309baeaf2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280441108500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d9ee4c-6076-4b82-81c9-adefe03f3ecb", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280446859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e8fcef4-07dd-4072-bf97-12755fa6cc59", "name": "entry : default@ProcessRouterMap cost memory 0.21794891357421875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280446953600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde1894c-d315-42fd-b5ec-58a959ddb98b", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280441097100, "endTime": 10280447034400}, "additional": {"logType": "info", "children": [], "durationId": "3e4a5f11-1897-4451-9117-914e14c86335"}}, {"head": {"id": "5b71b461-503a-47ac-995a-da2cf4b2936b", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280456578500, "endTime": 10280460345500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3edba411-7207-4d6c-a4ff-66f05a30373f", "logId": "6c8abdeb-cd3b-4e50-8f8d-1ffd0b867cc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3edba411-7207-4d6c-a4ff-66f05a30373f", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280452012800}, "additional": {"logType": "detail", "children": [], "durationId": "5b71b461-503a-47ac-995a-da2cf4b2936b"}}, {"head": {"id": "bd3bde1f-4ef3-4d84-917e-0523111c26d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280452516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9908e464-d5bf-468a-b262-2c201ddebb51", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280452609300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adba11ff-184a-4e65-aacf-e4eff1a06b38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280452664600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9467b60-f679-4673-93a7-57fc44c7b0e4", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280454055800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47526906-e34b-4a18-8e63-69d7cb0a8ff1", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280458496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb62c58f-497f-4f4a-985b-98d6d2bb5707", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280458665100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0605d8-7607-45d7-93dc-3c86fba33c9c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280458728000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee33da5-b191-4f13-ba2b-18800059cc0b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280458776800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e34def4-6ae0-440d-9253-a4edd3384d7f", "name": "entry : default@PreviewProcessResource cost memory 0.09415435791015625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280458863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0379d18c-8698-4c05-bc0c-0e3c40fc5313", "name": "runTaskFromQueue task cost before running: 224 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280460259000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8abdeb-cd3b-4e50-8f8d-1ffd0b867cc2", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280456578500, "endTime": 10280460345500, "totalTime": 2351400}, "additional": {"logType": "info", "children": [], "durationId": "5b71b461-503a-47ac-995a-da2cf4b2936b"}}, {"head": {"id": "97a5d73f-1f4d-4778-bb4f-094456d96857", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280467956100, "endTime": 10280489576100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "793dca87-cd07-4aa0-85f3-4f55a557a2e0", "logId": "c33c785a-6282-4971-9d89-64eda1eaf755"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "793dca87-cd07-4aa0-85f3-4f55a557a2e0", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280463248900}, "additional": {"logType": "detail", "children": [], "durationId": "97a5d73f-1f4d-4778-bb4f-094456d96857"}}, {"head": {"id": "4b157396-95c9-410c-a3ba-97971186fbc4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280463748400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bfd855e-bc46-4cd9-b536-181589031502", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280463839300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b26311c-758a-497c-a12f-e31b6fcdc123", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280463895200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8f7a58b-6ea4-4317-beb5-aef51a542df7", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280467974300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ccd052-10f3-48bf-b03b-38ec4cf18d7e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280489343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad38ecc-3fc8-4daf-99d1-f8980cdd1c80", "name": "entry : default@GenerateLoaderJson cost memory 0.**********890625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280489490200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c33c785a-6282-4971-9d89-64eda1eaf755", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280467956100, "endTime": 10280489576100}, "additional": {"logType": "info", "children": [], "durationId": "97a5d73f-1f4d-4778-bb4f-094456d96857"}}, {"head": {"id": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280500586400, "endTime": 10281073853400}, "additional": {"children": ["d1f61785-5493-4c56-b18a-79f120e312da", "a922d23a-3e33-4d80-9f86-185b74a8052b", "db335cbf-972d-4e3a-b55a-7bcd69e6dbf4", "abc00d81-10ce-4557-8234-fa364749302d", "4814fc2e-4943-4a17-95e2-5077d936e18a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "7b1854e0-90b9-4e18-bf4e-78fa733716cc", "logId": "14f1799a-c919-4e5b-a8d1-1ba1345544c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b1854e0-90b9-4e18-bf4e-78fa733716cc", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280496602500}, "additional": {"logType": "detail", "children": [], "durationId": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d"}}, {"head": {"id": "28f086a4-b507-4888-ba55-a1ddab6ee9fb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280497109400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776fcc18-cade-46bb-b0b8-288a20a45141", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280497194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d89b558-5597-4ac4-99ea-c1080d052cee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280497250100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bac851fd-9281-463c-bb8d-2e1187fa691f", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280498086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5cfdad-9ed8-415b-927a-72041e3240a7", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280500690700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067b3cd5-81f1-4c80-9998-7d5d2b3222ba", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280547332800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "214f54e3-1dfe-4490-900c-657cb02e493a", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 46 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280547544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f61785-5493-4c56-b18a-79f120e312da", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280548742600, "endTime": 10280573516600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d", "logId": "6161f7c8-f3fb-42a9-9985-f02e26898943"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6161f7c8-f3fb-42a9-9985-f02e26898943", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280548742600, "endTime": 10280573516600}, "additional": {"logType": "info", "children": [], "durationId": "d1f61785-5493-4c56-b18a-79f120e312da", "parent": "14f1799a-c919-4e5b-a8d1-1ba1345544c2"}}, {"head": {"id": "7dd15e4d-5e08-4945-b548-38b72604eb85", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280574109800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a922d23a-3e33-4d80-9f86-185b74a8052b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280574908500, "endTime": 10280687012100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d", "logId": "f4532ab4-1c82-4468-a386-b32885f8634b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c245a5ba-a537-4ffd-acad-9c79f3170718", "name": "current process  memoryUsage: {\n  rss: 206364672,\n  heapTotal: 156045312,\n  heapUsed: 126160400,\n  external: 3201255,\n  arrayBuffers: 190478\n} os memoryUsage :12.073722839355469", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280575955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b02bc3d8-6d99-40e9-9951-fac5a67e0c47", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280685332500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4532ab4-1c82-4468-a386-b32885f8634b", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280574908500, "endTime": 10280687012100}, "additional": {"logType": "info", "children": [], "durationId": "a922d23a-3e33-4d80-9f86-185b74a8052b", "parent": "14f1799a-c919-4e5b-a8d1-1ba1345544c2"}}, {"head": {"id": "89499baf-7639-4639-b086-bb08819e880a", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280687234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db335cbf-972d-4e3a-b55a-7bcd69e6dbf4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280688147800, "endTime": 10280814830900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d", "logId": "7c151f19-bbb5-4662-adae-4104f682ab52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f510886-e96d-41df-a435-af2807bd5262", "name": "current process  memoryUsage: {\n  rss: 207368192,\n  heapTotal: 156045312,\n  heapUsed: 128193320,\n  external: 3248701,\n  arrayBuffers: 237939\n} os memoryUsage :12.070808410644531", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280688934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "291ffb4c-a0f5-4b63-90fc-821135d0a428", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280813108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c151f19-bbb5-4662-adae-4104f682ab52", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280688147800, "endTime": 10280814830900}, "additional": {"logType": "info", "children": [], "durationId": "db335cbf-972d-4e3a-b55a-7bcd69e6dbf4", "parent": "14f1799a-c919-4e5b-a8d1-1ba1345544c2"}}, {"head": {"id": "ffcf5b86-f142-4914-8cd2-b8d4821082d5", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280815255300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abc00d81-10ce-4557-8234-fa364749302d", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280816114300, "endTime": 10280920724600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d", "logId": "9dee70b1-3be3-4c53-9c74-69a3f6dab14f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24ef7c56-0afe-4bb9-a940-a09b9c802709", "name": "current process  memoryUsage: {\n  rss: 207421440,\n  heapTotal: 156045312,\n  heapUsed: 128459520,\n  external: 3257019,\n  arrayBuffers: 246321\n} os memoryUsage :12.07229232788086", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280816909000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "314c778e-f950-49fc-a87a-3563c909014c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280918906200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dee70b1-3be3-4c53-9c74-69a3f6dab14f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280816114300, "endTime": 10280920724600}, "additional": {"logType": "info", "children": [], "durationId": "abc00d81-10ce-4557-8234-fa364749302d", "parent": "14f1799a-c919-4e5b-a8d1-1ba1345544c2"}}, {"head": {"id": "281326df-9201-47c7-bcc8-b568ce765de9", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280921300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4814fc2e-4943-4a17-95e2-5077d936e18a", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280922204100, "endTime": 10281071903000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d", "logId": "7224ea96-3fa5-4982-a025-d036aaf36b8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e88a535-75e2-4415-ab08-b5f7e24ddc5b", "name": "current process  memoryUsage: {\n  rss: 207470592,\n  heapTotal: 156045312,\n  heapUsed: 128759904,\n  external: 3257145,\n  arrayBuffers: 247366\n} os memoryUsage :12.077590942382812", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280923026300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c1cd9e-b0e4-4b47-b061-fd20f7651c9d", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281068960100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7224ea96-3fa5-4982-a025-d036aaf36b8e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280922204100, "endTime": 10281071903000}, "additional": {"logType": "info", "children": [], "durationId": "4814fc2e-4943-4a17-95e2-5077d936e18a", "parent": "14f1799a-c919-4e5b-a8d1-1ba1345544c2"}}, {"head": {"id": "0b3cb31e-be82-42fc-9032-e14d3a99d81b", "name": "entry : default@PreviewCompileResource cost memory 4.7237701416015625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281073613400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71b175d8-55a8-461a-832d-6a329e338938", "name": "runTaskFromQueue task cost before running: 837 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281073784400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f1799a-c919-4e5b-a8d1-1ba1345544c2", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280500586400, "endTime": 10281073853400, "totalTime": 573156900}, "additional": {"logType": "info", "children": ["6161f7c8-f3fb-42a9-9985-f02e26898943", "f4532ab4-1c82-4468-a386-b32885f8634b", "7c151f19-bbb5-4662-adae-4104f682ab52", "9dee70b1-3be3-4c53-9c74-69a3f6dab14f", "7224ea96-3fa5-4982-a025-d036aaf36b8e"], "durationId": "72e0a604-ee3b-4c6f-96ae-c22bc5ae5d4d"}}, {"head": {"id": "f51f7e96-c23c-43ce-8e5a-d23574857eef", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077085800, "endTime": 10281077509000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "801b6a88-70a7-459b-8a83-3319b2f9535a", "logId": "b078d08c-910f-4f43-a10e-680b0e0cc8df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "801b6a88-70a7-459b-8a83-3319b2f9535a", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281076288900}, "additional": {"logType": "detail", "children": [], "durationId": "f51f7e96-c23c-43ce-8e5a-d23574857eef"}}, {"head": {"id": "e4c1e9cc-3848-4910-9443-14d8fc14061d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281076832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43eac4f2-f10a-4431-a1ba-c02da82875c6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281076932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c1986f3-e98a-4cb2-8e29-11b64f205057", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281076988700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b629f606-7cca-45fe-9929-17239a6cf48c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077098000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b888f34-2786-42ff-ab8a-97e42cff425e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077190200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b265a3e-72ad-4417-ad6d-721f5781ec32", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077239400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f95fc9e-2e1b-405e-ab07-20d18bb25276", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077281500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb09c9a-87c3-4c76-8314-fc5ede1e1cf1", "name": "entry : default@PreviewHookCompileResource cost memory 0.05199432373046875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077370200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e6c6973-74e5-4df5-bc70-7d36c243e4fa", "name": "runTaskFromQueue task cost before running: 841 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077453400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b078d08c-910f-4f43-a10e-680b0e0cc8df", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281077085800, "endTime": 10281077509000, "totalTime": 349300}, "additional": {"logType": "info", "children": [], "durationId": "f51f7e96-c23c-43ce-8e5a-d23574857eef"}}, {"head": {"id": "0af5b323-ab53-4e3f-9d84-4177048fdfbb", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281080410200, "endTime": 10281089248100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "788ac039-c8cd-4382-b09a-0e87964f1934", "logId": "354e3746-453c-495c-a075-3fb415f28bdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "788ac039-c8cd-4382-b09a-0e87964f1934", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281079155200}, "additional": {"logType": "detail", "children": [], "durationId": "0af5b323-ab53-4e3f-9d84-4177048fdfbb"}}, {"head": {"id": "7011996a-b2af-47f6-abab-f94102b5ea3e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281079683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95fd1b17-4caa-4c1c-84da-fa0e62537af8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281079768100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d100057-d62c-421e-9618-ff953c55a52a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281079821100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47ab8e6-f955-430a-8643-3c921c2a47d7", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281080423700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92085626-2016-4d2d-90c7-9e79b504e621", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281082147700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc97f36-1092-4aa6-b1d3-2fb914dffc38", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281082258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1cbe5b-fe02-4742-8b2e-37813dc49419", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281082341300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4149780a-698c-4103-977c-0e6d5840b5d3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281082391900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73b585dd-7ca2-4307-a145-34639de1dff2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281082439000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2b6399-b76f-4c12-9bfe-6456b722ddc3", "name": "entry : default@CopyPreviewProfile cost memory 0.2299346923828125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281088856100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4d15d75-b13b-4872-8f9a-003058c389f3", "name": "runTaskFromQueue task cost before running: 852 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281089021700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354e3746-453c-495c-a075-3fb415f28bdd", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281080410200, "endTime": 10281089248100, "totalTime": 8581400}, "additional": {"logType": "info", "children": [], "durationId": "0af5b323-ab53-4e3f-9d84-4177048fdfbb"}}, {"head": {"id": "6b9a1941-3325-499c-8542-2e059b98d7f8", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281092584000, "endTime": 10281093171800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "ae91bcf7-a2a1-458a-bd7f-91bac973ec4a", "logId": "010c7f78-adaa-4fcd-aeef-21411d04ff92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae91bcf7-a2a1-458a-bd7f-91bac973ec4a", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281091183500}, "additional": {"logType": "detail", "children": [], "durationId": "6b9a1941-3325-499c-8542-2e059b98d7f8"}}, {"head": {"id": "8a529b39-2687-41df-85cd-1a7e685a74fd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281091722700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63225285-7cd4-42a1-a058-122c2d81aa7d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281091808400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21515a8e-0781-4cf7-986b-4e63b2e47ea9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281091866900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f955f02-42b2-41af-b903-bb62feb6305f", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281092595200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67601ffa-d051-42de-850f-3fad17dbb0ba", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281092702400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616799c9-a9db-4901-9769-7dbe75f666ed", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281092757800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8e18bf-879d-4a7a-ad85-86bdcab1e7df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281092805800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84c390e6-f97d-44f6-91ac-432b73e506d7", "name": "entry : default@ReplacePreviewerPage cost memory 0.052001953125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281093016400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451c4086-2f7e-4a23-a849-be26a82bf93f", "name": "runTaskFromQueue task cost before running: 856 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281093114800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010c7f78-adaa-4fcd-aeef-21411d04ff92", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281092584000, "endTime": 10281093171800, "totalTime": 511800}, "additional": {"logType": "info", "children": [], "durationId": "6b9a1941-3325-499c-8542-2e059b98d7f8"}}, {"head": {"id": "e0b278e2-6732-411c-ab02-7d2c44abfdf6", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281094749600, "endTime": 10281095058400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "dfac73f0-2c91-4484-bbda-1eefd0973259", "logId": "7c61c8a5-539e-43ca-a920-e02aa74d59f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfac73f0-2c91-4484-bbda-1eefd0973259", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281094697300}, "additional": {"logType": "detail", "children": [], "durationId": "e0b278e2-6732-411c-ab02-7d2c44abfdf6"}}, {"head": {"id": "2ec10378-6abd-4330-abd2-6766c0405ec1", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281094758400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a90f49e-63f1-4d31-ac11-69a672129dec", "name": "entry : buildPreviewerResource cost memory 0.01215362548828125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281094902900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdaed27-e48c-4765-b14a-ec56784a9fd7", "name": "runTaskFromQueue task cost before running: 858 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281095002300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c61c8a5-539e-43ca-a920-e02aa74d59f1", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281094749600, "endTime": 10281095058400, "totalTime": 233900}, "additional": {"logType": "info", "children": [], "durationId": "e0b278e2-6732-411c-ab02-7d2c44abfdf6"}}, {"head": {"id": "88b26893-fd18-400c-9454-c158cbd211f9", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281098199300, "endTime": 10281101817500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "cf56e2e5-0642-4657-a3a0-e71057ab1410", "logId": "a9682674-5e93-48c0-bb2d-12858e527d6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf56e2e5-0642-4657-a3a0-e71057ab1410", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281096739400}, "additional": {"logType": "detail", "children": [], "durationId": "88b26893-fd18-400c-9454-c158cbd211f9"}}, {"head": {"id": "94572d0e-6724-47f5-829f-82134dfc6f1e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281097293800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc69821-81dc-4815-bf98-900b3cb0d415", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281097420900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d27830c-a7eb-41ff-9cdd-38973d5a6344", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281097477600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650c253f-bc6a-4987-89fa-b18606031235", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281098211900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00cb8c0e-6c6f-4750-b554-0323efdc0689", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281100402800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8c7494-b2dc-4548-b797-c6183015e4e7", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281100500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "443b4309-7eff-4cf1-9eff-ec7de45d4821", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281100586400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4c4b67-a4cd-46c2-8a6d-0c30f035664b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281100642200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e53a58-efed-4c1f-a8c2-4c4c98fcb28e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281100687900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0839d025-694a-4e63-b180-63206f58d219", "name": "entry : default@PreviewUpdateAssets cost memory 0.37823486328125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281101652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c660b1-6733-48f1-928b-b0f6a1e16014", "name": "runTaskFromQueue task cost before running: 865 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281101757600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9682674-5e93-48c0-bb2d-12858e527d6a", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281098199300, "endTime": 10281101817500, "totalTime": 3536700}, "additional": {"logType": "info", "children": [], "durationId": "88b26893-fd18-400c-9454-c158cbd211f9"}}, {"head": {"id": "e83ce778-7beb-4ac8-b4bd-8f773500ce2c", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281109674000, "endTime": 10290746187300}, "additional": {"children": ["9b29b98c-05b4-483f-97ac-19dfdb04e562"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "b3c2b640-a95d-47c5-95ee-05e49e0136e2", "logId": "4e6a4ffe-a7b1-4007-acf6-95b627eb80fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3c2b640-a95d-47c5-95ee-05e49e0136e2", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281104159000}, "additional": {"logType": "detail", "children": [], "durationId": "e83ce778-7beb-4ac8-b4bd-8f773500ce2c"}}, {"head": {"id": "dfffb3a8-d0d6-4506-8deb-b3e39c085a38", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281104661200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9660cbf8-474e-4d27-9807-0a8b709564f6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281104741900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47ab2831-ae81-4cac-a611-15dc709ac13c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281104796700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe84d2e-fc7b-4938-8c73-41940f6957de", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281109683500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0ca77f6-8fb4-4e93-bc6e-acdef873c485", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281143616600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa549e6-2f34-4c75-8d52-3cf2f68646cd", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281143780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10281160412300, "endTime": 10290739737300}, "additional": {"children": ["ea4b7425-b0f6-4604-a4d8-3e204d041adb", "3a6cf02c-5dd1-4fdf-9e93-c3548ef15f07", "bc5429e0-c415-49c7-a5f5-a2cfaffdf9b6", "ee6484d9-5c95-4fde-802b-9c0346a8574c", "6a269973-6b2b-45e0-a0f9-eb52c5596986", "493e97cf-e445-4126-a3fa-97d0bcd423ae", "0d81a3f7-460a-4b6b-a86e-2072e5f89956", "94275cab-7398-4d7d-b597-ded4cbf5c14c", "8ae16f95-971b-4904-ac4e-80ee92f237bf", "fc2f62ce-40ea-445a-b7bb-6f136d34cfdb", "2cd5639d-564b-4fc6-9cc8-5acd5dad577f", "3c60969f-6047-4b6a-824f-703008b8a32a", "c408268d-d279-4ccb-aa9f-cbb7d4c93798"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e83ce778-7beb-4ac8-b4bd-8f773500ce2c", "logId": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61caa3ab-12e2-41c1-861c-58d4f893a777", "name": "entry : default@PreviewArkTS cost memory 2.076934814453125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281163186900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85eb7447-6a5a-4b79-8bbb-b838cbb635a2", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10284581623400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4b7425-b0f6-4604-a4d8-3e204d041adb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10284582712200, "endTime": 10284582763000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "23cbbe18-b1fd-4133-aa5e-d514d5ef6ed9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23cbbe18-b1fd-4133-aa5e-d514d5ef6ed9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10284582712200, "endTime": 10284582763000}, "additional": {"logType": "info", "children": [], "durationId": "ea4b7425-b0f6-4604-a4d8-3e204d041adb", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "1056761c-145c-419d-87bf-90222c268e8f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290730589000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6cf02c-5dd1-4fdf-9e93-c3548ef15f07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290731814200, "endTime": 10290731838600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "a80d7f41-b5d1-436e-a99e-dcc06ed2e99a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a80d7f41-b5d1-436e-a99e-dcc06ed2e99a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290731814200, "endTime": 10290731838600}, "additional": {"logType": "info", "children": [], "durationId": "3a6cf02c-5dd1-4fdf-9e93-c3548ef15f07", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "2ce385f2-53a0-4e3e-81b6-7bef480d0ff6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290731917600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5429e0-c415-49c7-a5f5-a2cfaffdf9b6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290732703900, "endTime": 10290732719400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "78b8b6c8-f630-4103-be9b-8c614162c099"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78b8b6c8-f630-4103-be9b-8c614162c099", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290732703900, "endTime": 10290732719400}, "additional": {"logType": "info", "children": [], "durationId": "bc5429e0-c415-49c7-a5f5-a2cfaffdf9b6", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "5fe328a1-909c-4e21-b6e3-9121d91ebfce", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290732783000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee6484d9-5c95-4fde-802b-9c0346a8574c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290733535000, "endTime": 10290733552800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "7f42e532-17a5-4d0f-a195-71360da24b08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f42e532-17a5-4d0f-a195-71360da24b08", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290733535000, "endTime": 10290733552800}, "additional": {"logType": "info", "children": [], "durationId": "ee6484d9-5c95-4fde-802b-9c0346a8574c", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "af6f552d-2501-409f-abf8-01e22a92a258", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290733622500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a269973-6b2b-45e0-a0f9-eb52c5596986", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290734465100, "endTime": 10290734486800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "fb06e91a-8ab8-4dca-a72b-d3c223ddcb0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb06e91a-8ab8-4dca-a72b-d3c223ddcb0c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290734465100, "endTime": 10290734486800}, "additional": {"logType": "info", "children": [], "durationId": "6a269973-6b2b-45e0-a0f9-eb52c5596986", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "14e8381b-10e8-4971-917e-86ba5466d575", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290734573000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493e97cf-e445-4126-a3fa-97d0bcd423ae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290735764800, "endTime": 10290735789000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "032cdc3d-ac5b-4021-b6d1-585a500f437d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "032cdc3d-ac5b-4021-b6d1-585a500f437d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290735764800, "endTime": 10290735789000}, "additional": {"logType": "info", "children": [], "durationId": "493e97cf-e445-4126-a3fa-97d0bcd423ae", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "7074ccc6-2eab-4246-96cf-4878d3995355", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290735885700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d81a3f7-460a-4b6b-a86e-2072e5f89956", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290736979500, "endTime": 10290737002700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "81097028-5e0b-4649-8de7-867a2a8dc8ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81097028-5e0b-4649-8de7-867a2a8dc8ec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290736979500, "endTime": 10290737002700}, "additional": {"logType": "info", "children": [], "durationId": "0d81a3f7-460a-4b6b-a86e-2072e5f89956", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "31eed69a-5877-430c-bb12-05b3163c8b99", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290737139800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94275cab-7398-4d7d-b597-ded4cbf5c14c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290738268000, "endTime": 10290738295700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "2747e14e-70bb-4053-b113-d847b097fd5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2747e14e-70bb-4053-b113-d847b097fd5e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290738268000, "endTime": 10290738295700}, "additional": {"logType": "info", "children": [], "durationId": "94275cab-7398-4d7d-b597-ded4cbf5c14c", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "37e2c450-35f9-4e6f-b55a-5bba2302d77d", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290738435800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae16f95-971b-4904-ac4e-80ee92f237bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10290739431900, "endTime": 10290739455300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "8af0f445-024e-4d34-8b26-fd5f61b5e6ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8af0f445-024e-4d34-8b26-fd5f61b5e6ce", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290739431900, "endTime": 10290739455300}, "additional": {"logType": "info", "children": [], "durationId": "8ae16f95-971b-4904-ac4e-80ee92f237bf", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "ea682ed2-3caa-48a4-9473-7536b34bcc04", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10281160412300, "endTime": 10290739737300}, "additional": {"logType": "info", "children": ["23cbbe18-b1fd-4133-aa5e-d514d5ef6ed9", "a80d7f41-b5d1-436e-a99e-dcc06ed2e99a", "78b8b6c8-f630-4103-be9b-8c614162c099", "7f42e532-17a5-4d0f-a195-71360da24b08", "fb06e91a-8ab8-4dca-a72b-d3c223ddcb0c", "032cdc3d-ac5b-4021-b6d1-585a500f437d", "81097028-5e0b-4649-8de7-867a2a8dc8ec", "2747e14e-70bb-4053-b113-d847b097fd5e", "8af0f445-024e-4d34-8b26-fd5f61b5e6ce", "e64a026f-4eed-4eba-ba44-6fa2ffd45a5f", "5186ed4a-1fe3-41b5-95dc-6f868b8c7887", "3fca6104-19fe-4bc7-86f0-bb337a5faea5", "48603b3e-1eb5-492b-aefe-06da748ee4ba"], "durationId": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "parent": "4e6a4ffe-a7b1-4007-acf6-95b627eb80fc"}}, {"head": {"id": "fc2f62ce-40ea-445a-b7bb-6f136d34cfdb", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10283436343400, "endTime": 10284477878000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "e64a026f-4eed-4eba-ba44-6fa2ffd45a5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e64a026f-4eed-4eba-ba44-6fa2ffd45a5f", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10283436343400, "endTime": 10284477878000}, "additional": {"logType": "info", "children": [], "durationId": "fc2f62ce-40ea-445a-b7bb-6f136d34cfdb", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "2cd5639d-564b-4fc6-9cc8-5acd5dad577f", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10284478073500, "endTime": 10284555118200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "5186ed4a-1fe3-41b5-95dc-6f868b8c7887"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5186ed4a-1fe3-41b5-95dc-6f868b8c7887", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10284478073500, "endTime": 10284555118200}, "additional": {"logType": "info", "children": [], "durationId": "2cd5639d-564b-4fc6-9cc8-5acd5dad577f", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "3c60969f-6047-4b6a-824f-703008b8a32a", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10284555251500, "endTime": 10284555558400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "3fca6104-19fe-4bc7-86f0-bb337a5faea5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fca6104-19fe-4bc7-86f0-bb337a5faea5", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10284555251500, "endTime": 10284555558400}, "additional": {"logType": "info", "children": [], "durationId": "3c60969f-6047-4b6a-824f-703008b8a32a", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "c408268d-d279-4ccb-aa9f-cbb7d4c93798", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker1", "startTime": 10284555636500, "endTime": 10290730630800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "48603b3e-1eb5-492b-aefe-06da748ee4ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48603b3e-1eb5-492b-aefe-06da748ee4ba", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10284555636500, "endTime": 10290730630800}, "additional": {"logType": "info", "children": [], "durationId": "c408268d-d279-4ccb-aa9f-cbb7d4c93798", "parent": "ea682ed2-3caa-48a4-9473-7536b34bcc04"}}, {"head": {"id": "4e6a4ffe-a7b1-4007-acf6-95b627eb80fc", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10281109674000, "endTime": 10290746187300, "totalTime": 9636410400}, "additional": {"logType": "info", "children": ["ea682ed2-3caa-48a4-9473-7536b34bcc04"], "durationId": "e83ce778-7beb-4ac8-b4bd-8f773500ce2c"}}, {"head": {"id": "285f428f-4f8e-4b35-9fe1-d5a0b07e92e8", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290756321900, "endTime": 10290756836700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "247b4d00-0017-402a-a44c-d25c22e466ad", "logId": "bc4e498a-aeee-4e94-9199-ec150ff711cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "247b4d00-0017-402a-a44c-d25c22e466ad", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290756245300}, "additional": {"logType": "detail", "children": [], "durationId": "285f428f-4f8e-4b35-9fe1-d5a0b07e92e8"}}, {"head": {"id": "72461d6e-1f0b-4ef7-a064-4ea7982ecc73", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290756342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "063b84d6-3ae3-472e-a4d3-aac83dd1dd3b", "name": "entry : PreviewBuild cost memory 0.01202392578125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290756562900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7805209d-5a43-42b4-a6d1-4548f70e9d2a", "name": "runTaskFromQueue task cost before running: 10 s 520 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290756722600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4e498a-aeee-4e94-9199-ec150ff711cb", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290756321900, "endTime": 10290756836700, "totalTime": 357600}, "additional": {"logType": "info", "children": [], "durationId": "285f428f-4f8e-4b35-9fe1-d5a0b07e92e8"}}, {"head": {"id": "f828228c-5e6e-46ef-a6ad-adaa004cf515", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290771538800, "endTime": 10290771563300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ba6151c8-a2de-4036-a50e-f169056a405f", "logId": "17289c35-6d01-4c8b-888a-2899af4cb823"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17289c35-6d01-4c8b-888a-2899af4cb823", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290771538800, "endTime": 10290771563300}, "additional": {"logType": "info", "children": [], "durationId": "f828228c-5e6e-46ef-a6ad-adaa004cf515"}}, {"head": {"id": "8eb64467-0684-4a5f-b24a-ca82b1a06548", "name": "BUILD SUCCESSFUL in 10 s 535 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290771666400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "36e5cc41-a4fa-4227-be0c-1aa4ebf71e09", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10280237205900, "endTime": 10290772238100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 30}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "7ea64583-7ab2-4e48-8895-4157b7a65369", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290772430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42becf2-c9f4-4417-91e3-34ee521e73c8", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290772547900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af2e2dd0-39c1-4ba4-b2a0-39aa99043e0b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290772645800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16ecb19-8f00-4a84-aec4-ad2118410fbb", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290772744800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbeafaae-516b-4057-ba57-b174c8b288d0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290772832300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ca481a-6c59-4d11-bd84-7d71bc29d986", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290772915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82786a9-3ca5-472f-bb3c-d0b22a139d54", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290773018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b5fbadf-0876-4ad3-9fef-d41312583ee5", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290774586700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbea6125-aac2-4e27-90ee-672bd6324921", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290791619700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393e9365-a2a0-44b4-9405-abc1377989ab", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290794590200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ce3edb-f597-4046-9e52-87893e46c040", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290795114200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d36d8812-75b4-4a11-b0b9-4c1cc5764af8", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290816910800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f5cd0e6-4292-4d8a-9809-4a867692cc2f", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:46 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290818599100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae34c3b2-1da3-4283-8faf-3adf0630cc86", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290819043700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ed325e-8622-4f7b-8f60-25eecd26e081", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290820396200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9738e722-09ab-4b2a-9cdf-ad6141347b45", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290821853700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cfdab06-c870-49df-9a0b-aad026680cd7", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290822622300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30303f1b-7e64-4057-b34d-6d3ad2b83b67", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290823148800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c000d361-35ec-4a13-a20f-afcc28b26677", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290823723200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70300650-7e27-4d13-a743-c415226b36c8", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290827700700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ffef5b-98a3-4f4d-8c97-e42e3e72f2e2", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290828920900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7a2d5b-a187-4a53-926f-6f6b19b76307", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290829333900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8222c9f-d396-4de9-a4c8-bd470527eda8", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290846224800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621da953-dd77-492f-a5c6-4e77e1e33bae", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290847282600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc1eb92f-55ac-48be-bc75-05c5bf74f7ee", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290847414600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea498feb-2e19-4982-96fc-d8073283c4e1", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290847727200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2385a21-14ef-4590-ba18-44d90bfdbb2f", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290848521600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b94eca55-d57b-407e-a093-8b88b910e65c", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290852075900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ccd7fa-f3a8-47df-86ca-937dd06e4c99", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290852431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57c56524-97b9-4fe8-a9f6-91414b572b5c", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290852772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70df7cac-6edb-4808-8fc5-60969f074d77", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290853316800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542cc65e-ed67-49fd-a8b0-cd97e049ee7b", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:30 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290853753100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}