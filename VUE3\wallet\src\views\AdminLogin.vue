<script setup>
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { User, Lock, Phone, Key, Shield } from '@element-plus/icons-vue';
import axios from "axios";

const router = useRouter();
const loginType = ref('password'); // 登录方式：password-密码登录，code-验证码登录

// 登录表单
const loginForm = reactive({
  username: "",
  password: "",
  code: "",
});

// 验证码相关
const codeCountdown = ref(0);
const codeSending = ref(false);
const currentCode = ref(''); // 当前生成的验证码

// 登录验证规则
const loginRules = reactive({
  username: [
    { required: true, message: "请输入用户名或手机号", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "验证码为6位数字", trigger: "blur" }
  ]
});

const loginFormRef = ref();
const loading = ref(false);

// 处理登录
const handleLogin = () => {
  if (loginType.value === 'password') {
    handlePasswordLogin();
  } else {
    handleCodeLogin();
  }
}

// 管理员密码登录
const handlePasswordLogin = () => {
  console.log('管理员密码登录:', loginForm.username, loginForm.password)
  if (loginForm.username == '' || loginForm.password == '') {
    ElMessage.error('用户名和密码不能为空')
    return
  }

  loading.value = true;

  axios.post('http://localhost:8091/admin/auth/login', null, {
    params: {
      username: loginForm.username,
      password: loginForm.password
    }
  })
  .then(function (response) {
    console.log('管理员密码登录响应:', response.data);
    if (response.data.code == 0) {
      console.log('管理员密码登录成功');
      ElMessage.success('登录成功');

      // 存储管理员登录信息
      const adminData = response.data.data;
      localStorage.setItem('adminToken', adminData.token);
      localStorage.setItem('adminInfo', JSON.stringify(adminData.adminInfo));
      localStorage.setItem('userType', 'admin');

      // 跳转到管理员仪表板
      router.push('/home').catch(err => {
        console.error('路由跳转失败:', err);
      });
    } else {
      console.error('管理员密码登录失败:', response.data);
      ElMessage.error(response.data.msg || '登录失败');
    }
  })
  .catch(function (error) {
    console.error('管理员密码登录请求错误:', error);
    if (error.response) {
      console.error('响应错误:', error.response.data);
      ElMessage.error(`登录失败: ${error.response.data.msg || error.response.status}`);
    } else if (error.request) {
      console.error('请求错误:', error.request);
      ElMessage.error('网络请求失败，请检查网络连接');
    } else {
      console.error('其他错误:', error.message);
      ElMessage.error('登录请求失败');
    }
  })
  .finally(() => {
    loading.value = false;
  });
}

// 验证码登录
const handleCodeLogin = () => {
  console.log('管理员验证码登录:', loginForm.username, loginForm.code)
  if (loginForm.username == '' || loginForm.code == '') {
    ElMessage.error('手机号和验证码不能为空')
    return
  }

  // 验证手机号格式
  if (!/^1[3-9]\d{9}$/.test(loginForm.username)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }

  loading.value = true;

  axios.post('http://localhost:8091/admin/auth/login-with-code', null, {
    params: {
      phone: loginForm.username,
      code: loginForm.code
    }
  })
  .then(function (response) {
    console.log('管理员验证码登录响应:', response.data);
    if (response.data.code == 0) {
      console.log('管理员验证码登录成功');
      ElMessage.success('登录成功');

      // 存储管理员登录信息
      const adminData = response.data.data;
      localStorage.setItem('adminToken', adminData.token);
      localStorage.setItem('adminInfo', JSON.stringify(adminData.adminInfo));
      localStorage.setItem('userType', 'admin');

      // 跳转到管理员仪表板
      router.push('/admin-dashboard').catch(err => {
        console.error('路由跳转失败:', err);
      });
    } else {
      console.error('管理员验证码登录失败:', response.data);
      ElMessage.error(response.data.msg || '验证码登录失败');
    }
  })
  .catch(function (error) {
    console.error('管理员验证码登录请求错误:', error);
    if (error.response) {
      console.error('响应错误:', error.response.data);
      ElMessage.error(`登录失败: ${error.response.data.msg || error.response.status}`);
    } else if (error.request) {
      console.error('请求错误:', error.request);
      ElMessage.error('网络请求失败，请检查网络连接');
    } else {
      console.error('其他错误:', error.message);
      ElMessage.error('验证码登录失败');
    }
  })
  .finally(() => {
    loading.value = false;
  })
}

// 发送验证码
const sendCode = () => {
  if (!loginForm.username) {
    ElMessage.error('请先输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.username)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }

  codeSending.value = true;

  axios.post('http://localhost:8091/admin/auth/send-code', null, {
    params: {
      phone: loginForm.username,
      type: 4 // 4表示管理员登录验证码
    }
  })
  .then(function (response) {
    console.log('验证码发送响应:', response.data);
    if (response.data.code == 0) {
      ElMessage.success('验证码发送成功');
      // 显示验证码在前端页面
      currentCode.value = response.data.data;
      console.log('收到验证码:', response.data.data);
      // 开始倒计时
      codeCountdown.value = 60;
      const timer = setInterval(() => {
        codeCountdown.value--;
        if (codeCountdown.value <= 0) {
          clearInterval(timer);
          currentCode.value = ''; // 倒计时结束后清空验证码显示
        }
      }, 1000);
    } else {
      console.error('验证码发送失败:', response.data);
      ElMessage.error(response.data.msg || '验证码发送失败');
    }
  })
  .catch(function (error) {
    console.error('验证码发送请求错误:', error);
    if (error.response) {
      console.error('响应错误:', error.response.data);
      ElMessage.error(`验证码发送失败: ${error.response.data.msg || error.response.status}`);
    } else if (error.request) {
      console.error('请求错误:', error.request);
      ElMessage.error('网络请求失败，请检查网络连接');
    } else {
      console.error('其他错误:', error.message);
      ElMessage.error('验证码发送失败');
    }
  })
  .finally(() => {
    codeSending.value = false;
  })
}

// 跳转到普通用户登录
// const goToUserLogin = () => {
//   router.push('/login');
// }

</script>

<template>
  <div class="admin-login-container">
    <div class="admin-login-box">
      <!-- Logo和标题区域 -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <el-icon><Shield /></el-icon>
          </div>
          <h2 class="title">管理员登录</h2>
        </div>
        <p class="subtitle">E-Wallet 电子钱包管理系统</p>
      </div>
      
      <!-- 登录方式切换 -->
      <div class="login-type-switch">
        <el-radio-group v-model="loginType" size="default" class="login-type-buttons">
          <el-radio-button label="password" class="login-type-btn">
            <el-icon><Lock /></el-icon>
            <span>密码登录</span>
          </el-radio-button>
          <el-radio-button label="code" class="login-type-btn">
            <el-icon><Key /></el-icon>
            <span>验证码登录</span>
          </el-radio-button>
        </el-radio-group>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="admin-auth-form"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            :placeholder="loginType === 'password' ? '请输入用户名' : '请输入手机号'"
            size="large"
            class="admin-auth-input"
          >
            <template #prefix>
              <el-icon><User v-if="loginType === 'password'" /><Phone v-else /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 密码登录 -->
        <el-form-item v-if="loginType === 'password'" prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            class="admin-auth-input"
            show-password
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 验证码登录 -->
        <el-form-item v-if="loginType === 'code'" prop="code">
          <div class="code-input-group">
            <el-input
              v-model="loginForm.code"
              placeholder="请输入验证码"
              maxlength="6"
              size="large"
              class="admin-auth-input code-input"
            >
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>
            <el-button
              @click="sendCode"
              :disabled="codeCountdown > 0 || codeSending"
              :loading="codeSending"
              size="large"
              class="send-code-btn"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
            </el-button>
          </div>
          <!-- 显示验证码 -->
          <div v-if="currentCode" class="code-display">
            <div class="verification-code-card">
              <div class="code-content">
                <span class="code-label">验证码：</span>
                <span class="code-number">{{ currentCode }}</span>
                <span class="countdown-text">({{ codeCountdown }}秒后过期)</span>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="danger"
            @click="handleLogin"
            class="admin-auth-btn admin-login-btn"
            size="large"
            :loading="loading"
          >
            <el-icon v-if="!loading">
              <Lock v-if="loginType === 'password'" />
              <Key v-else />
            </el-icon>
            <span>{{ loginType === 'password' ? '管理员登录' : '验证码登录' }}</span>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 普通用户登录入口 -->
      <div class="user-login-link">
        <el-button type="text" @click="goToUserLogin" class="user-link-btn">
          切换到普通用户登录
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.admin-login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #e74c3c 100%);
  position: relative;
  overflow: hidden;
}

.admin-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.admin-login-box {
  width: 480px;
  padding: 50px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.admin-login-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Logo和标题区域 */
.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 50%, #a93226 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 50%, #a93226 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

/* 登录方式切换样式 */
.login-type-switch {
  text-align: center;
  margin-bottom: 30px;
}

.login-type-buttons {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.login-type-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

:deep(.el-radio-button__inner) {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  transition: all 0.3s ease;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: none;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 12px 0 0 12px;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0 12px 12px 0;
}

/* 表单样式 */
.admin-auth-form {
  margin-top: 30px;
}

/* 输入框样式 */
.admin-auth-input {
  margin-bottom: 5px;
}

.admin-auth-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.admin-auth-input :deep(.el-input__wrapper):hover {
  border-color: #c0c4cc;
  background: rgba(255, 255, 255, 0.9);
}

.admin-auth-input :deep(.el-input__wrapper.is-focus) {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
  background: white;
}

.admin-auth-input :deep(.el-input__inner) {
  font-size: 16px;
  color: #2c3e50;
}

.admin-auth-input :deep(.el-icon) {
  color: #7f8c8d;
  font-size: 18px;
}

.admin-auth-input :deep(.el-input__wrapper.is-focus .el-icon) {
  color: #e74c3c;
}

/* 按钮样式 */
.admin-auth-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.admin-login-btn {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.admin-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

/* 验证码输入组样式 */
.code-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  width: 130px;
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.send-code-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
}

.send-code-btn:disabled {
  background: #bdc3c7;
  color: #7f8c8d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 验证码显示卡片样式 */
.verification-code-card {
  margin-top: 10px;
  padding: 12px;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 1px solid #e74c3c;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.1);
}

.code-content {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.code-label {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
}

.code-number {
  font-size: 16px;
  font-weight: bold;
  color: #e74c3c;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.countdown-text {
  font-size: 11px;
  color: #dc2626;
}

/* 普通用户登录链接 */
.user-login-link {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.user-link-btn {
  color: #7f8c8d;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
}

.user-link-btn:hover {
  color: #e74c3c;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-login-container {
    padding: 20px;
  }

  .admin-login-box {
    width: 100%;
    max-width: 400px;
    padding: 40px 30px;
    margin: 0 20px;
  }

  .logo-container {
    flex-direction: column;
    gap: 10px;
  }

  .logo-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 13px;
  }

  .code-input-group {
    flex-direction: column;
    gap: 15px;
  }

  .send-code-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .admin-login-box {
    padding: 30px 20px;
    border-radius: 16px;
  }

  .logo-icon {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }

  .title {
    font-size: 20px;
  }

  .admin-auth-btn {
    height: 45px;
    font-size: 15px;
  }
}

/* 页面加载动画 */
.admin-login-box {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单项动画 */
.admin-auth-form .el-form-item {
  animation: fadeInLeft 0.5s ease-out;
  animation-fill-mode: both;
}

.admin-auth-form .el-form-item:nth-child(1) { animation-delay: 0.1s; }
.admin-auth-form .el-form-item:nth-child(2) { animation-delay: 0.2s; }
.admin-auth-form .el-form-item:nth-child(3) { animation-delay: 0.3s; }
.admin-auth-form .el-form-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
