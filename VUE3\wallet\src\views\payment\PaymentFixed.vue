<template>
  <div class="payment-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>支付管理</h2>
      <p>管理系统支付记录，包括查看、修改、删除、添加支付记录等操作</p>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalPayments }}</div>
                <div class="stat-label">总支付数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.successPayments }}</div>
                <div class="stat-label">成功支付</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.failedPayments }}</div>
                <div class="stat-label">失败支付</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon amount">
                <el-icon><Wallet /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ Number(statistics.totalAmount).toFixed(2) }}</div>
                <div class="stat-label">总金额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20" class="search-row">
        <el-col :span="6">
          <el-input
            v-model="searchForm.paymentNo"
            placeholder="请输入支付单号"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><DocumentCopy /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择支付状态"
            clearable
            style="width: 100%"
          >
            <el-option label="待支付" :value="0" />
            <el-option label="支付成功" :value="1" />
            <el-option label="支付失败" :value="2" />
            <el-option label="已取消" :value="3" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAddDialog">
            <el-icon><Plus /></el-icon>
            添加支付记录
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 支付记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="paymentNo" label="支付单号" width="180" />
        <el-table-column prop="orderNo" label="订单号" width="150" />
        <el-table-column prop="amount" label="支付金额" width="120" align="right">
          <template #default="{ row }">
            ¥{{ Number(row.amount).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="merchantName" label="商户名称" width="150" />
        <el-table-column prop="paymentType" label="支付方式" width="100">
          <template #default="{ row }">
            <el-tag :type="row.paymentType === 1 ? 'primary' : 'success'">
              {{ row.paymentType === 1 ? '钱包支付' : '银行卡支付' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="支付状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="支付描述" width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="editPayment(row)"
              >
                <el-icon><Edit /></el-icon>
                修改
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deletePayment(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/修改支付记录对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="editMode === 'add' ? '添加支付记录' : '修改支付记录'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="editForm"
        label-width="120px"
        ref="editFormRef"
        :rules="editRules"
      >
        <el-form-item label="支付单号" prop="paymentNo">
          <el-input
            v-model="editForm.paymentNo"
            placeholder="请输入支付单号"
          />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input-number
            v-model="editForm.userId"
            :min="1"
            style="width: 100%"
            placeholder="请输入用户ID"
          />
        </el-form-item>
        <el-form-item label="支付金额" prop="amount">
          <el-input-number
            v-model="editForm.amount"
            :min="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入支付金额"
          />
        </el-form-item>
        <el-form-item label="商户名称" prop="merchantName">
          <el-input
            v-model="editForm.merchantName"
            placeholder="请输入商户名称"
          />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input
            v-model="editForm.orderNo"
            placeholder="请输入订单号"
          />
        </el-form-item>
        <el-form-item label="支付描述">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入支付描述"
          />
        </el-form-item>
        <el-form-item label="支付状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="0">待支付</el-radio>
            <el-radio :label="1">支付成功</el-radio>
            <el-radio :label="2">支付失败</el-radio>
            <el-radio :label="3">已取消</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Money, Check, Close, Wallet, Plus, Refresh, Search, DocumentCopy, Edit, Delete
} from '@element-plus/icons-vue'

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:8091'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const showEditDialog = ref(false)
const showAddDialog = ref(false)
const editMode = ref('add') // 'add' 或 'edit'
const editFormRef = ref(null)

// 统计数据
const statistics = ref({
  totalPayments: 0,
  successPayments: 0,
  failedPayments: 0,
  totalAmount: 0
})

// 搜索表单
const searchForm = reactive({
  paymentNo: '',
  orderNo: '',
  status: null
})

// 编辑表单
const editForm = reactive({
  paymentId: null,
  paymentNo: '',
  userId: null,
  amount: null,
  merchantName: '',
  orderNo: '',
  description: '',
  status: 0
})

// 表单验证规则
const editRules = {
  paymentNo: [
    { required: true, message: '请输入支付单号', trigger: 'blur' }
  ],
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' }
  ],
  merchantName: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  orderNo: [
    { required: true, message: '请输入订单号', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择支付状态', trigger: 'change' }
  ]
}

// 方法
const loadStatistics = async () => {
  try {
    const response = await axios.get('/payment/admin/statistics')
    if (response.data.code === 0) {
      statistics.value = response.data.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

const fetchPayments = async () => {
  try {
    loading.value = true
    const response = await axios.get('/payment/admin/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        paymentNo: searchForm.paymentNo,
        orderNo: searchForm.orderNo,
        status: searchForm.status
      }
    })

    if (response.data.code === 0) {
      tableData.value = response.data.data.records
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.msg || '获取支付记录列表失败')
    }
  } catch (error) {
    console.error('获取支付记录列表失败:', error)
    ElMessage.error('获取支付记录列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchPayments()
}

// 重置
const handleReset = () => {
  searchForm.paymentNo = ''
  searchForm.orderNo = ''
  searchForm.status = null
  currentPage.value = 1
  fetchPayments()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchPayments()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchPayments()
}

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    0: 'warning', // 待支付
    1: 'success', // 支付成功
    2: 'danger',  // 支付失败
    3: 'info'     // 已取消
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    0: '待支付',
    1: '支付成功',
    2: '支付失败',
    3: '已取消'
  }
  return statusMap[status] || '未知'
}

// 编辑支付记录
const editPayment = (row) => {
  editMode.value = 'edit'
  Object.assign(editForm, {
    paymentId: row.paymentId,
    paymentNo: row.paymentNo,
    userId: row.userId,
    amount: row.amount,
    merchantName: row.merchantName,
    orderNo: row.orderNo,
    description: row.description,
    status: row.status
  })
  showEditDialog.value = true
}

// 删除支付记录
const deletePayment = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该支付记录吗？此操作不可恢复！', '确认删除', {
      type: 'warning'
    })

    await axios.delete(`/payment/admin/${row.paymentId}`)
    ElMessage.success('删除成功')
    fetchPayments()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除支付记录失败:', error)
      ElMessage.error('删除支付记录失败')
    }
  }
}

// 确认编辑
const confirmEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    if (editMode.value === 'add') {
      console.log('添加支付记录:', editForm)
      await axios.post('/payment/admin/add', editForm)
      ElMessage.success('添加支付记录成功')
    } else {
      console.log('修改支付记录 - ID:', editForm.paymentId)
      console.log('修改数据:', editForm)
      const response = await axios.put(`/payment/admin/${editForm.paymentId}`, editForm)
      console.log('修改响应:', response.data)
      ElMessage.success('修改支付记录成功')
    }

    showEditDialog.value = false
    fetchPayments()
    loadStatistics()
    resetEditForm()
  } catch (error) {
    if (error.name !== 'ValidationError') {
      console.error('操作失败:', error)
      console.error('错误详情:', error.response?.data)
      ElMessage.error('操作失败: ' + (error.response?.data?.msg || error.message))
    }
  }
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    paymentId: null,
    paymentNo: '',
    userId: null,
    amount: null,
    merchantName: '',
    orderNo: '',
    description: '',
    status: 0
  })
}

// 监听添加对话框
const handleAddDialog = () => {
  editMode.value = 'add'
  resetEditForm()
  showEditDialog.value = true
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
  fetchPayments()
})
</script>

<style scoped>
.payment-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.amount {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.search-card, .table-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-row {
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-manage {
    padding: 10px;
  }

  .statistics-cards .el-col {
    margin-bottom: 10px;
  }

  .search-row .el-col {
    margin-bottom: 10px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
