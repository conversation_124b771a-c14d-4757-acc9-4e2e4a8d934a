-- 插入测试管理员账号
-- 用于测试管理员验证码登录功能

-- 删除可能存在的测试数据
DELETE FROM admin WHERE username = 'testadmin';

-- 插入测试管理员账号
INSERT INTO admin (
    username, 
    password, 
    phone, 
    real_name, 
    role, 
    status, 
    create_time, 
    update_time
) VALUES (
    'testadmin',                                    -- 用户名
    '202cb962ac59075b964b07152d234b70',            -- 密码：123456 的MD5加密
    '13800138000',                                  -- 手机号
    '测试管理员',                                    -- 真实姓名
    'admin',                                        -- 角色：管理员
    1,                                              -- 状态：正常
    NOW(),                                          -- 创建时间
    NOW()                                           -- 更新时间
);

-- 查询插入结果
SELECT * FROM admin WHERE username = 'testadmin';
