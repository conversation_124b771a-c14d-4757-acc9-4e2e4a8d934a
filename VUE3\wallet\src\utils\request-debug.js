import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建一个调试版本的axios实例
const debugRequest = axios.create({
  baseURL: 'http://localhost:8091',
  timeout: 30000,
  withCredentials: false
})

// 详细的请求拦截器
debugRequest.interceptors.request.use(
  config => {
    console.group('🚀 发送请求')
    console.log('Method:', config.method?.toUpperCase())
    console.log('URL:', config.baseURL + config.url)
    console.log('Full URL:', config.baseURL + config.url + (config.params ? '?' + new URLSearchParams(config.params).toString() : ''))
    console.log('Headers:', config.headers)
    console.log('Params:', config.params)
    console.log('Data:', config.data)
    console.log('Timeout:', config.timeout)
    console.log('WithCredentials:', config.withCredentials)
    console.groupEnd()
    
    // 如果是POST请求且使用params，确保Content-Type正确
    if (config.method === 'post' && config.params && !config.data) {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      console.log('📝 设置Content-Type为application/x-www-form-urlencoded')
    }
    
    return config
  },
  error => {
    console.group('❌ 请求配置错误')
    console.error('Error:', error)
    console.groupEnd()
    return Promise.reject(error)
  }
)

// 详细的响应拦截器
debugRequest.interceptors.response.use(
  response => {
    console.group('✅ 收到响应')
    console.log('Status:', response.status)
    console.log('StatusText:', response.statusText)
    console.log('Headers:', response.headers)
    console.log('Data:', response.data)
    console.log('Config:', response.config)
    console.groupEnd()
    return response
  },
  error => {
    console.group('❌ 响应错误')
    console.log('Error Message:', error.message)
    console.log('Error Code:', error.code)
    console.log('Error Name:', error.name)
    
    if (error.response) {
      console.log('Response Status:', error.response.status)
      console.log('Response StatusText:', error.response.statusText)
      console.log('Response Headers:', error.response.headers)
      console.log('Response Data:', error.response.data)
    } else if (error.request) {
      console.log('Request Object:', error.request)
      console.log('Request ReadyState:', error.request.readyState)
      console.log('Request Status:', error.request.status)
      console.log('Request StatusText:', error.request.statusText)
      console.log('Request ResponseURL:', error.request.responseURL)
      console.log('Request ResponseText:', error.request.responseText)
    } else {
      console.log('Error Config:', error.config)
    }
    
    console.groupEnd()
    
    // 详细的错误处理
    if (error.code === 'ERR_NETWORK') {
      console.error('🔴 网络错误 - 可能的原因:')
      console.error('1. 后端服务未启动 (检查 http://localhost:8091)')
      console.error('2. 端口8091被占用或无法访问')
      console.error('3. 防火墙阻止连接')
      console.error('4. CORS配置问题')
      
      ElMessage({
        message: '网络连接失败！\n请检查后端服务是否启动\n(端口: 8091)',
        type: 'error',
        duration: 5000,
        showClose: true
      })
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else if (error.response) {
      const { status, data } = error.response
      ElMessage.error(data?.msg || `服务器错误 (${status})`)
    } else {
      ElMessage.error('请求失败: ' + error.message)
    }
    
    return Promise.reject(error)
  }
)

// 导出调试版本
export default debugRequest

// 导出一些测试函数
export const testConnection = async () => {
  try {
    console.log('🧪 开始连接测试...')
    
    // 测试1: 简单GET请求
    console.log('测试1: GET /payment/test')
    const response1 = await debugRequest.get('/payment/test')
    console.log('✅ GET测试成功:', response1.data)
    
    // 测试2: POST请求
    console.log('测试2: POST /payment/test')
    const response2 = await debugRequest.post('/payment/test', null, {
      params: { message: 'debug test' }
    })
    console.log('✅ POST测试成功:', response2.data)
    
    return true
  } catch (error) {
    console.error('❌ 连接测试失败:', error)
    return false
  }
}

export const testDirectFetch = async () => {
  try {
    console.log('🧪 开始直接fetch测试...')
    
    const response = await fetch('http://localhost:8091/payment/test', {
      method: 'GET',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 直接fetch测试成功:', data)
      return true
    } else {
      console.error('❌ 直接fetch测试失败:', response.status, response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ 直接fetch测试失败:', error)
    return false
  }
}

export const checkBackendStatus = async () => {
  const results = {
    axios: false,
    fetch: false,
    timestamp: new Date().toISOString()
  }
  
  console.log('🔍 开始后端状态检查...')
  
  // 测试axios
  try {
    await debugRequest.get('/payment/test', { timeout: 5000 })
    results.axios = true
    console.log('✅ Axios连接正常')
  } catch (error) {
    console.log('❌ Axios连接失败:', error.message)
  }
  
  // 测试fetch
  try {
    const response = await fetch('http://localhost:8091/payment/test', {
      method: 'GET',
      mode: 'cors',
      signal: AbortSignal.timeout(5000)
    })
    results.fetch = response.ok
    console.log(response.ok ? '✅ Fetch连接正常' : '❌ Fetch连接失败')
  } catch (error) {
    console.log('❌ Fetch连接失败:', error.message)
  }
  
  console.log('📊 后端状态检查结果:', results)
  return results
}
