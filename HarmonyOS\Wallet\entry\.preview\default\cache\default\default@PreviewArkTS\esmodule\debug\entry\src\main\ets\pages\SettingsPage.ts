if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SettingsPage_Params {
    userInfo?: UserInfo;
    showPasswordDialog?: boolean;
    showLimitDialog?: boolean;
    showLogoutDialog?: boolean;
    preferencesStore?: preferences.Preferences | null;
    userId?: number;
    userName?: string;
    userPhone?: string;
    payLimit?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse } from "@normalized:N&&&@ohos/axios/index&2.2.6";
import preferences from "@ohos:data.preferences";
// 定义API响应类型
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
// 定义通用响应类型（兼容R<T>格式）
interface R<T> {
    code: number;
    msg: string;
    data: T;
}
// 定义用户数据类型
interface UserData {
    userId: number;
    phone: string;
    realName?: string;
    username?: string;
    password?: string;
    payPassword?: string;
    payLimit?: number;
    status?: number;
    createTime?: string;
    updateTime?: string;
    lastLoginTime?: string;
}
// 定义用户更新请求类型
interface UserUpdateRequest {
    userId: number;
    payPassword?: string;
    payLimit?: number;
    updateTime: string;
}
interface UserInfo {
    name: string;
    phone: string;
    avatar: Resource;
}
export class SettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU({
            name: '加载中...',
            phone: '加载中...',
            avatar: { "id": 16777261, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }
        }, this, "userInfo");
        this.__showPasswordDialog = new ObservedPropertySimplePU(false, this, "showPasswordDialog");
        this.__showLimitDialog = new ObservedPropertySimplePU(false, this, "showLimitDialog");
        this.__showLogoutDialog = new ObservedPropertySimplePU(false, this, "showLogoutDialog");
        this.__preferencesStore = new ObservedPropertyObjectPU(null, this, "preferencesStore");
        this.__userId = new ObservedPropertySimplePU(0, this, "userId");
        this.__userName = new ObservedPropertySimplePU('', this, "userName");
        this.__userPhone = new ObservedPropertySimplePU('', this, "userPhone");
        this.__payLimit = new ObservedPropertySimplePU('', this, "payLimit");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SettingsPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.showPasswordDialog !== undefined) {
            this.showPasswordDialog = params.showPasswordDialog;
        }
        if (params.showLimitDialog !== undefined) {
            this.showLimitDialog = params.showLimitDialog;
        }
        if (params.showLogoutDialog !== undefined) {
            this.showLogoutDialog = params.showLogoutDialog;
        }
        if (params.preferencesStore !== undefined) {
            this.preferencesStore = params.preferencesStore;
        }
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
        if (params.userName !== undefined) {
            this.userName = params.userName;
        }
        if (params.userPhone !== undefined) {
            this.userPhone = params.userPhone;
        }
        if (params.payLimit !== undefined) {
            this.payLimit = params.payLimit;
        }
    }
    updateStateVars(params: SettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__showPasswordDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showLimitDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showLogoutDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__preferencesStore.purgeDependencyOnElmtId(rmElmtId);
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
        this.__userName.purgeDependencyOnElmtId(rmElmtId);
        this.__userPhone.purgeDependencyOnElmtId(rmElmtId);
        this.__payLimit.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__showPasswordDialog.aboutToBeDeleted();
        this.__showLimitDialog.aboutToBeDeleted();
        this.__showLogoutDialog.aboutToBeDeleted();
        this.__preferencesStore.aboutToBeDeleted();
        this.__userId.aboutToBeDeleted();
        this.__userName.aboutToBeDeleted();
        this.__userPhone.aboutToBeDeleted();
        this.__payLimit.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<UserInfo>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: UserInfo) {
        this.__userInfo.set(newValue);
    }
    private __showPasswordDialog: ObservedPropertySimplePU<boolean>;
    get showPasswordDialog() {
        return this.__showPasswordDialog.get();
    }
    set showPasswordDialog(newValue: boolean) {
        this.__showPasswordDialog.set(newValue);
    }
    private __showLimitDialog: ObservedPropertySimplePU<boolean>;
    get showLimitDialog() {
        return this.__showLimitDialog.get();
    }
    set showLimitDialog(newValue: boolean) {
        this.__showLimitDialog.set(newValue);
    }
    private __showLogoutDialog: ObservedPropertySimplePU<boolean>;
    get showLogoutDialog() {
        return this.__showLogoutDialog.get();
    }
    set showLogoutDialog(newValue: boolean) {
        this.__showLogoutDialog.set(newValue);
    }
    private __preferencesStore: ObservedPropertyObjectPU<preferences.Preferences | null>;
    get preferencesStore() {
        return this.__preferencesStore.get();
    }
    set preferencesStore(newValue: preferences.Preferences | null) {
        this.__preferencesStore.set(newValue);
    }
    private __userId: ObservedPropertySimplePU<number>;
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    private __userName: ObservedPropertySimplePU<string>;
    get userName() {
        return this.__userName.get();
    }
    set userName(newValue: string) {
        this.__userName.set(newValue);
    }
    private __userPhone: ObservedPropertySimplePU<string>;
    get userPhone() {
        return this.__userPhone.get();
    }
    set userPhone(newValue: string) {
        this.__userPhone.set(newValue);
    }
    private __payLimit: ObservedPropertySimplePU<string>;
    get payLimit() {
        return this.__payLimit.get();
    }
    set payLimit(newValue: string) {
        this.__payLimit.set(newValue);
    }
    aboutToAppear() {
        this.initPreferences();
    }
    // 初始化preferences并加载用户信息
    async initPreferences() {
        try {
            this.preferencesStore = await preferences.getPreferences(getContext(), 'user_prefs');
            await this.loadUserInfo();
        }
        catch (error) {
            console.error('初始化preferences失败:', error);
        }
    }
    // 从本地存储加载用户信息
    async loadUserInfo() {
        if (!this.preferencesStore)
            return;
        try {
            // 从本地存储获取基本信息
            this.userId = await this.preferencesStore.get('userId', 0) as number;
            this.userPhone = await this.preferencesStore.get('userPhone', '') as string;
            this.userName = await this.preferencesStore.get('realName', '') as string ||
                await this.preferencesStore.get('userName', '') as string;
            this.payLimit = await this.preferencesStore.get('payLimit', '') as string;
            console.log('从本地存储加载的用户信息:', {
                userId: this.userId,
                userPhone: this.userPhone,
                userName: this.userName,
                payLimit: this.payLimit
            });
            // 如果有手机号，通过手机号获取最新用户信息
            if (this.userPhone) {
                await this.loadUserInfoByPhone(this.userPhone);
            }
            // 更新显示的用户信息
            this.updateUserInfoDisplay();
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            // 设置默认显示信息
            this.userInfo = {
                name: '未知用户',
                phone: '未绑定手机',
                avatar: { "id": 16777261, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }
            };
        }
    }
    // 通过手机号加载用户信息
    async loadUserInfoByPhone(phone: string) {
        try {
            // 定义手机号查询用户信息的响应类型
            interface PhoneUserData {
                userId: number;
                username: string;
                realName: string;
                phone: string;
                payLimit?: number;
            }
            const response: AxiosResponse<ApiResponse<PhoneUserData>> = await axios({
                url: 'http://localhost:8091/auth/getUserByPhone',
                method: 'get',
                params: {
                    phone: phone
                }
            });
            if (response.data && response.data.code === 0) {
                const userData = response.data.data;
                this.userId = userData.userId;
                this.userName = userData.realName || userData.username || '未知用户';
                this.userPhone = userData.phone || '';
                if (userData.payLimit) {
                    this.payLimit = userData.payLimit.toString();
                }
                // 将用户信息保存到本地存储
                if (this.preferencesStore) {
                    try {
                        await this.preferencesStore.put('userId', userData.userId);
                        await this.preferencesStore.put('realName', userData.realName || '');
                        await this.preferencesStore.put('userName', userData.username || '');
                        await this.preferencesStore.put('userPhone', userData.phone || '');
                        if (userData.payLimit) {
                            await this.preferencesStore.put('payLimit', userData.payLimit.toString());
                        }
                        await this.preferencesStore.flush();
                    }
                    catch (error) {
                        console.error('保存用户信息到本地失败:', error);
                    }
                }
            }
        }
        catch (error) {
            console.error('通过手机号加载用户信息失败:', error);
        }
    }
    // 更新用户信息显示
    updateUserInfoDisplay() {
        // 处理手机号显示（脱敏）
        let displayPhone = this.userPhone;
        if (this.userPhone && this.userPhone.length === 11) {
            displayPhone = this.userPhone.substring(0, 3) + '****' + this.userPhone.substring(7);
        }
        this.userInfo = {
            name: this.userName || '未知用户',
            phone: displayPhone || '未绑定手机',
            avatar: { "id": 16777261, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }
        };
        console.log('更新后的用户信息显示:', this.userInfo);
    }
    // 清除用户信息
    async clearUserInfo() {
        if (!this.preferencesStore)
            return;
        try {
            await this.preferencesStore.clear();
            await this.preferencesStore.flush();
            console.log('用户信息已清除');
        }
        catch (error) {
            console.error('清除用户信息失败:', error);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 根容器
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(199:5)", "entry");
            // 根容器
            Column.width('100%');
            // 根容器
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(201:7)", "entry");
            // 主内容区域
            Column.width('100%');
            // 主内容区域
            Column.height('100%');
            // 主内容区域
            Column.backgroundColor('#f5f5f5');
        }, Column);
        // 用户信息卡片
        this.UserInfoCard.bind(this)();
        // 设置选项列表
        this.SettingsList.bind(this)();
        // 退出登录按钮
        this.LogoutButton.bind(this)();
        // 主内容区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 弹窗区域（覆盖在主内容之上）
            if (this.showPasswordDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.PasswordDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showLimitDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LimitDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showLogoutDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LogoutDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 根容器
        Column.pop();
    }
    UserInfoCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(234:5)", "entry");
            Row.width('90%');
            Row.padding(20);
            Row.backgroundColor('#ffffff');
            Row.borderRadius(12);
            Row.margin({ top: 20 });
            Row.shadow({ radius: 4, color: '#00000010', offsetX: 0, offsetY: 2 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.userInfo.avatar);
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(235:7)", "entry");
            Image.width(60);
            Image.height(60);
            Image.borderRadius(30);
            Image.margin({ right: 15 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(241:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo.name);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(242:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo.phone);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(245:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    SettingsList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(261:5)", "entry");
            Column.width('90%');
            Column.margin({ top: 20 });
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.shadow({ radius: 4, color: '#00000010', offsetX: 0, offsetY: 2 });
        }, Column);
        this.SettingItem.bind(this)('支付密码设置', { "id": 16777265, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            this.showPasswordDialog = true;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/SettingsPage.ets(266:7)", "entry");
            Divider.strokeWidth(0.5);
            Divider.color('#f0f0f0');
        }, Divider);
        this.SettingItem.bind(this)('支付限额设置', { "id": 16777268, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            this.showLimitDialog = true;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/SettingsPage.ets(274:7)", "entry");
            Divider.strokeWidth(0.5);
            Divider.color('#f0f0f0');
        }, Divider);
        this.SettingItem.bind(this)('修改登录密码', { "id": 16777267, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            router.pushUrl({ url: 'pages/ChangePasswordPage' });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/SettingsPage.ets(282:7)", "entry");
            Divider.strokeWidth(0.5);
            Divider.color('#f0f0f0');
        }, Divider);
        this.SettingItem.bind(this)('关于我们', { "id": 16777266, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            router.pushUrl({ url: 'pages/AboutPage' });
        });
        Column.pop();
    }
    SettingItem(title: string, icon: Resource, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(299:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.padding({ left: 15, right: 15 });
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(icon);
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(300:7)", "entry");
            Image.width(20);
            Image.height(20);
            Image.margin({ right: 15 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(305:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777269, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(310:7)", "entry");
            Image.width(16);
            Image.height(16);
        }, Image);
        Row.pop();
    }
    LogoutButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(322:5)", "entry");
            Button.width('90%');
            Button.height(50);
            Button.backgroundColor('#ffffff');
            Button.margin({ top: 30 });
            Button.onClick(() => {
                this.showLogoutDialog = true;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('退出登录');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(323:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF5252');
        }, Text);
        Text.pop();
        Button.pop();
    }
    PasswordDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(338:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '20%' });
            Column.zIndex(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置支付密码');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(339:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请输入6位数字支付密码');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(344:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 10 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '新支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(349:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.margin({ bottom: 10 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '确认支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(356:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.margin({ bottom: 20 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(363:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(364:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.onClick(() => {
                this.showPasswordDialog = false;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('取消');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(365:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(376:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#ee1f98e5');
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.showPasswordDialog = false;
                promptAction.showToast({ message: '支付密码设置成功', duration: 1000 });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(377:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        Button.pop();
        Row.pop();
        Column.pop();
    }
    LimitDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(401:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '15%' });
            Column.zIndex(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付限额设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(402:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('单笔支付限额');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(407:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.margin({ left: '10%', bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入单笔限额' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(413:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Number);
            TextInput.margin({ bottom: 10 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('每日支付限额');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(419:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.margin({ left: '10%', bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入每日限额' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(425:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Number);
            TextInput.margin({ bottom: 20 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(431:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(432:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.onClick(() => {
                this.showLimitDialog = false;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('取消');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(433:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(444:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#ee1f98e5');
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.showLimitDialog = false;
                promptAction.showToast({ message: '限额设置成功', duration: 1000 });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(445:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        Button.pop();
        Row.pop();
        Column.pop();
    }
    LogoutDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(469:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '30%' });
            Column.zIndex(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认退出登录？');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(470:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(475:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.backgroundColor('#FF5252');
            Button.onClick(async () => {
                this.showLogoutDialog = false;
                // 清除本地存储的用户信息
                await this.clearUserInfo();
                router.replaceUrl({ url: 'pages/LoginPage' });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认退出');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(476:9)", "entry");
            Text.fontSize(16);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(490:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.margin({ top: 10 });
            Button.onClick(() => {
                this.showLogoutDialog = false;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('取消');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(491:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SettingsPage";
    }
}
registerNamedRoute(() => new SettingsPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/SettingsPage", pageFullPath: "entry/src/main/ets/pages/SettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
