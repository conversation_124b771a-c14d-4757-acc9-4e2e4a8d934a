{"CommonInfo": {"current.select.target": "default"}, "BuildOptions": {"SELECT_BUILD_TARGET": "default", "BUILD_PATH": {"OUTPUT_METADATA_JSON": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", "OUTPUT_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\outputs\\default", "RES_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\res\\default", "ETS_SUPER_VISUAL_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule", "JS_ASSETS_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\loader_out\\default", "SOURCE_MAP_DIR": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\source_map\\default", "INTERMEDIA_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates", "RES_PROFILE_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "WORKER_LOADER": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "MANIFEST_JSON": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\manifest\\default", "JS_LITE_ASSETS_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\intermediates\\loader_out_lite\\default", "JS_SUPER_VISUAL_PATH": "D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build\\default\\cache\\default\\default@CompileJS\\jsbundle"}}}