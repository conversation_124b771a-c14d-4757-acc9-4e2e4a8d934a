# 管理员登录和注册功能测试指南

## 概述

本文档提供了管理员登录和注册功能的详细测试指南，包括前端页面测试和后端API测试。

## 后端API接口

### 1. 管理员登录API

#### 接口信息
- **URL**: `POST http://localhost:8091/auth1/login1`
- **参数**: 
  - `username`: 管理员用户名
  - `password`: 密码（明文，后端会自动MD5加密比较）

#### 测试用例

**测试用例1：正确的管理员登录**
```bash
curl -X POST "http://localhost:8091/auth1/login1" \
  -d "username=admin&password=admin123"
```

**预期结果**:
```json
{
  "code": 0,
  "msg": "登录成功",
  "data": "eyJhbGciOiJIUzI1NiJ9..."
}
```

**测试用例2：错误的密码**
```bash
curl -X POST "http://localhost:8091/auth1/login1" \
  -d "username=admin&password=wrongpassword"
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "密码错误"
}
```

**测试用例3：不存在的用户名**
```bash
curl -X POST "http://localhost:8091/auth1/login1" \
  -d "username=nonexistent&password=password123"
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "管理员账号不存在"
}
```

### 2. 管理员注册API

#### 接口信息
- **URL**: `POST http://localhost:8091/admin/auth/register`
- **Content-Type**: `application/json`
- **参数**:
  - `username`: 管理员用户名（3-20位，字母数字下划线）
  - `password`: 密码（6-20位）
  - `realName`: 真实姓名（2-10位）
  - `role`: 角色（admin/operator，默认operator）

#### 测试用例

**测试用例1：正确的管理员注册**
```bash
curl -X POST "http://localhost:8091/admin/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testadmin",
    "password": "test123456",
    "realName": "测试管理员",
    "role": "operator"
  }'
```

**预期结果**:
```json
{
  "code": 0,
  "msg": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "adminInfo": {
      "adminId": 6,
      "username": "testadmin",
      "realName": "测试管理员",
      "role": "operator",
      "status": 1
    }
  }
}
```

**测试用例2：用户名已存在**
```bash
curl -X POST "http://localhost:8091/admin/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "test123456",
    "realName": "测试管理员",
    "role": "operator"
  }'
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "用户名已存在"
}
```

### 3. 检查用户名可用性API

#### 接口信息
- **URL**: `GET http://localhost:8091/admin/auth/check-username`
- **参数**: `username` - 要检查的用户名

#### 测试用例

**测试用例1：可用的用户名**
```bash
curl "http://localhost:8091/admin/auth/check-username?username=newadmin"
```

**预期结果**:
```json
{
  "code": 0,
  "msg": "用户名可用"
}
```

**测试用例2：已存在的用户名**
```bash
curl "http://localhost:8091/admin/auth/check-username?username=admin"
```

**预期结果**:
```json
{
  "code": 1,
  "msg": "用户名已存在"
}
```

## 前端页面测试

### 1. 管理员登录页面测试

#### 访问路径
- URL: `http://localhost:5173/admin-login`

#### 测试步骤

**测试1：正常登录流程**
1. 访问管理员登录页面
2. 输入用户名：`admin`
3. 输入密码：`admin123`
4. 点击"管理员登录"按钮
5. 验证是否跳转到管理员仪表板 (`/admin-dashboard`)
6. 验证localStorage中是否存储了正确的token和用户信息

**测试2：错误密码处理**
1. 输入用户名：`admin`
2. 输入错误密码：`wrongpassword`
3. 点击登录按钮
4. 验证是否显示错误提示信息

**测试3：页面跳转功能**
1. 点击"还没有账号？立即注册"链接
2. 验证是否跳转到管理员注册页面
3. 点击"切换到普通用户登录"链接
4. 验证是否跳转到普通用户登录页面

### 2. 管理员注册页面测试

#### 访问路径
- URL: `http://localhost:5173/admin-register`

#### 测试步骤

**测试1：正常注册流程**
1. 访问管理员注册页面
2. 输入用户名：`testadmin2`（确保不重复）
3. 输入真实姓名：`测试管理员2`
4. 选择角色：`操作员`
5. 输入密码：`test123456`
6. 确认密码：`test123456`
7. 点击"立即注册"按钮
8. 验证是否注册成功并跳转到管理员仪表板

**测试2：表单验证测试**
1. 尝试提交空表单，验证必填项提示
2. 输入过短的用户名（少于3位），验证长度提示
3. 输入过短的密码（少于6位），验证长度提示
4. 输入不一致的确认密码，验证一致性提示

**测试3：用户名重复检测**
1. 输入已存在的用户名（如`admin`）
2. 失去焦点后验证是否显示用户名已存在的提示

### 3. 权限控制测试

#### 测试步骤

**测试1：未登录访问保护**
1. 清除localStorage中的所有数据
2. 直接访问 `http://localhost:5173/admin-dashboard`
3. 验证是否自动重定向到管理员登录页面

**测试2：普通用户访问管理员页面**
1. 使用普通用户账号登录
2. 尝试访问 `http://localhost:5173/admin-dashboard`
3. 验证是否被重定向到管理员登录页面

**测试3：管理员访问普通用户页面**
1. 使用管理员账号登录
2. 访问 `http://localhost:5173/home`
3. 验证管理员是否可以正常访问普通用户页面

## 数据库验证

### 检查管理员数据

登录MySQL数据库，执行以下查询验证数据：

```sql
-- 查看所有管理员
SELECT admin_id, username, real_name, role, status, create_time 
FROM admin 
ORDER BY admin_id DESC;

-- 查看最新注册的管理员
SELECT * FROM admin 
WHERE create_time >= CURDATE() 
ORDER BY create_time DESC;

-- 验证密码是否正确加密（MD5）
SELECT username, password, LENGTH(password) as pwd_length 
FROM admin 
WHERE username = 'testadmin2';
```

### 预期结果
- 新注册的管理员应该出现在admin表中
- 密码字段应该是32位的MD5哈希值
- status字段应该为1（启用状态）
- create_time和update_time应该是当前时间

## 常见问题排查

### 1. 登录失败
- 检查后端服务是否正常运行（端口8091）
- 验证数据库连接是否正常
- 确认密码是否正确（注意大小写）

### 2. 注册失败
- 检查用户名是否符合规则（3-20位，字母数字下划线）
- 验证密码长度是否符合要求（6-20位）
- 确认网络请求是否正常发送

### 3. 页面跳转异常
- 检查路由配置是否正确
- 验证认证工具类是否正常工作
- 确认localStorage中的数据是否正确存储

## 安全注意事项

1. **密码安全**：所有密码都使用MD5加密存储
2. **Token验证**：使用JWT Token进行身份验证
3. **权限控制**：严格的路由守卫保护敏感页面
4. **输入验证**：前后端双重验证用户输入

## 测试环境要求

- 前端服务：Vue3 + Vite (端口5173)
- 后端服务：Spring Boot (端口8091)
- 数据库：MySQL 5.5+
- 浏览器：Chrome/Firefox/Safari最新版本
