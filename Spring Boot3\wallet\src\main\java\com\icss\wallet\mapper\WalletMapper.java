package com.icss.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Wallet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
public interface WalletMapper extends BaseMapper<Wallet> {
    
    @Select("SELECT * FROM wallet WHERE user_id = #{userId}")
    Wallet findByUserId(@Param("userId") Long userId);
    
    @Update("UPDATE wallet SET balance = balance + #{amount} WHERE user_id = #{userId} AND status = 1")
    int addBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
    
    @Update("UPDATE wallet SET balance = balance - #{amount} WHERE user_id = #{userId} AND status = 1 AND balance >= #{amount}")
    int subtractBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询钱包信息（包含用户信息）
     */
    @Select("<script>" +
            "SELECT w.*, u.phone, u.real_name " +
            "FROM wallet w " +
            "LEFT JOIN user u ON w.user_id = u.user_id " +
            "WHERE 1=1 " +
            "<if test='phone != null and phone != \"\"'>" +
            "AND u.phone LIKE CONCAT('%', #{phone}, '%') " +
            "</if>" +
            "<if test='realName != null and realName != \"\"'>" +
            "AND u.real_name LIKE CONCAT('%', #{realName}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND w.status = #{status} " +
            "</if>" +
            "ORDER BY w.create_time DESC" +
            "</script>")
    IPage<Wallet> selectWalletsWithUserInfo(Page<Wallet> page,
                                          @Param("phone") String phone,
                                          @Param("realName") String realName,
                                          @Param("status") Integer status);

    /**
     * 管理员获取钱包统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalWallets, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as activeWallets, " +
            "COUNT(CASE WHEN status = 0 THEN 1 END) as frozenWallets, " +
            "COALESCE(SUM(CASE WHEN status = 1 THEN balance ELSE 0 END), 0) as totalBalance " +
            "FROM wallet")
    java.util.Map<String, Object> getWalletStatistics();
}
