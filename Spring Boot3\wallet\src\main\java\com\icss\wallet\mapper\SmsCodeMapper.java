package com.icss.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icss.wallet.entity.SmsCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmsCodeMapper extends BaseMapper<SmsCode> {
    
    @Select("SELECT * FROM sms_code WHERE phone = #{phone} AND type = #{type} AND expire_time > NOW() ORDER BY create_time DESC LIMIT 1")
    SmsCode findValidCode(@Param("phone") String phone, @Param("type") Integer type);
}
