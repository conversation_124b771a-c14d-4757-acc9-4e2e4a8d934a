// import router from '@kit.ArkUI'
import router from '@ohos.router'
import promptAction from '@ohos.promptAction'
import axios, { AxiosResponse, AxiosError } from '@ohos/axios'
import { UserStorage } from '../common/UserStorage'

/**
 * 响应数据结构
 */
interface R<T> {
  code: number
  msg: string
  data: T
}

/**
 * 登录响应数据结构
 */
interface LoginResponse {
  token: string
  userId: number
  phone: string
  realName?: string
}

@Entry
@Component
struct LoginPage {
  @State loginType: string = 'password' // 'password' | 'code'
  @State username: string = ''
  @State password: string = ''
  @State verifyCode: string = ''
  @State countdown: number = 0
  @State isLoading: boolean = false
  @State currentCode: string = ''

  build() {
    Column() {
      // Logo和标题
      Column() {
        Text('💳')
          .fontSize(80)
          .margin({ bottom: 20 })

        Text('E-Wallet')
          .fontSize(28)
          .fontWeight(FontWeight.Bold)
          .fontColor('#4FC3F7')
          .margin({ bottom: 8 })

        Text('电子钱包支付系统')
          .fontSize(16)
          .fontColor('#666')
          .margin({ bottom: 40 })
      }
      .width('100%')
      .margin({ top: 80, bottom: 40 })

      // 登录表单
      Column() {
        Text('用户登录')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 20 })

        // 登录方式切换
        Row() {
          Button('密码登录')
            .type(this.loginType === 'password' ? ButtonType.Capsule : ButtonType.Normal)
            .backgroundColor(this.loginType === 'password' ? '#4FC3F7' : '#f0f0f0')
            .fontColor(this.loginType === 'password' ? Color.White : '#666')
            .onClick(() => {
              this.loginType = 'password'
            })
            .margin({ right: 12 })

          Button('验证码登录')
            .type(this.loginType === 'code' ? ButtonType.Capsule : ButtonType.Normal)
            .backgroundColor(this.loginType === 'code' ? '#4FC3F7' : '#f0f0f0')
            .fontColor(this.loginType === 'code' ? Color.White : '#666')
            .onClick(() => {
              this.loginType = 'code'
            })
        }
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 20 })

        // 手机号输入
        TextInput({ placeholder: '请输入手机号' })
          .type(InputType.PhoneNumber)
          .onChange((value: string) => {
            this.username = value
          })
          .margin({ bottom: 16 })
          .height(48)
          .borderRadius(8)

        // 密码输入（密码登录时显示）
        if (this.loginType === 'password') {
          TextInput({ placeholder: '请输入密码' })
            .type(InputType.Password)
            .onChange((value: string) => {
              this.password = value
            })
            .margin({ bottom: 24 })
            .height(48)
            .borderRadius(8)
        }

        // 验证码输入（验证码登录时显示）
        if (this.loginType === 'code') {
          Row() {
            TextInput({ placeholder: '请输入验证码' })
              .type(InputType.Number)
              .maxLength(6)
              .onChange((value: string) => {
                this.verifyCode = value
              })
              .layoutWeight(1)
              .height(48)
              .borderRadius(8)
              .margin({ right: 12 })

            Button(this.countdown > 0 ? `${this.countdown}s` : '获取验证码')
              .enabled(this.countdown === 0 && this.username.length === 11)
              .onClick(() => {
                this.sendVerifyCode()
              })
              .height(48)
              .borderRadius(8)
          }
          .width('100%')
          .margin({ bottom: 16 })

          // 显示验证码（开发环境）
          if (this.currentCode) {
            Text(`验证码：${this.currentCode}`)
              .fontSize(14)
              .fontColor('#4FC3F7')
              .margin({ bottom: 16 })
          }
        }

        // 登录按钮
        Button(this.isLoading ? '登录中...' : (this.loginType === 'password' ? '密码登录' : '验证码登录'))
          .type(ButtonType.Capsule)
          .backgroundColor('#4FC3F7')
          .width('100%')
          .height(48)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .onClick(() => {
            this.handleLogin()
          })
          .enabled(!this.isLoading)
          .margin({ bottom: 16 })

        // 注册链接
        Row() {
          Text('还没有账号？')
            .fontSize(14)
            .fontColor('#666')

          Text('立即注册')
            .fontSize(14)
            .fontColor('#4FC3F7')
            .onClick(() => {
              router.pushUrl({ url: 'pages/RegisterPage' })
            })
        }
        .justifyContent(FlexAlign.Center)
      }
      .backgroundColor(Color.White)
      .borderRadius(12)
      .padding(24)
      .margin({ left: 24, right: 24, bottom: 30 })

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f7fa')
    .padding({ left: 24, right: 24 })
  }

  // 处理登录
  handleLogin() {
    // 验证输入
    if (!this.username || this.username.length !== 11) {
      promptAction.showToast({
        message: '请输入正确的手机号',
        duration: 2000
      })
      return
    }

    if (this.loginType === 'password') {
      if (!this.password || this.password.length < 6) {
        promptAction.showToast({
          message: '密码长度不能少于6位',
          duration: 2000
        })
        return
      }
      this.passwordLogin()
    } else {
      if (!this.verifyCode || this.verifyCode.length !== 6) {
        promptAction.showToast({
          message: '请输入6位验证码',
          duration: 2000
        })
        return
      }
      this.codeLogin()
    }
  }

  // 密码登录
  passwordLogin() {
    this.isLoading = true

    axios({
      url: 'http://localhost:8091/auth/login',
      method: 'post',
      params: {
        username: this.username,
        password: this.password
      }
    }).then(async (res: AxiosResponse<R<LoginResponse>>) => {
      let message = "request result: " + JSON.stringify(res.data)
      let msg = res.data.msg
      console.log(msg)

      promptAction.showToast({
        message: msg,
        duration: 2000,
        bottom: 50
      })

      if (res.data.code == 0) {
        const loginData = res.data.data;
        console.log('登录成功，用户信息:', loginData)

        try {
          // 保存用户信息到本地存储
          console.log('准备保存用户信息:', loginData);
          await UserStorage.saveUserInfo(
            loginData.userId,
            loginData.token,
            loginData.phone,
            loginData.realName
          );
          console.log('用户信息保存成功，准备跳转页面');

          // 跳转到主页
          router.replaceUrl({ url: 'pages/BarPage' })
        } catch (error) {
          console.error('保存用户信息失败:', error);
          console.error('错误类型:', typeof error);
          console.error('错误消息:', error.message);
          promptAction.showToast({
            message: '登录信息保存失败: ' + error.message,
            duration: 3000,
            bottom: 50
          });
        }
      }
    }).catch((err: AxiosError) => {
      let message = "request error: " + err.message
      console.log(message)

      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000,
        bottom: 50
      })
    }).finally(() => {
      this.isLoading = false
    })
  }

  // 验证码登录
  codeLogin() {
    this.isLoading = true

    axios({
      url: 'http://localhost:8091/auth/loginWithCode',
      method: 'post',
      params: {
        phone: this.username,
        code: this.verifyCode
      }
    }).then(async (res: AxiosResponse<R<LoginResponse>>) => {
      let message = "request result: " + JSON.stringify(res.data)
      let msg = res.data.msg
      console.log(msg)

      promptAction.showToast({
        message: msg,
        duration: 2000,
        bottom: 50
      })

      if (res.data.code == 0) {
        const loginData = res.data.data;
        console.log('验证码登录成功，用户信息:', loginData)

        try {
          // 保存用户信息到本地存储
          console.log('准备保存验证码登录用户信息:', loginData);
          await UserStorage.saveUserInfo(
            loginData.userId,
            loginData.token,
            loginData.phone,
            loginData.realName
          );
          console.log('验证码登录用户信息保存成功，准备跳转页面');

          // 跳转到主页
          router.replaceUrl({ url: 'pages/BarPage' })
        } catch (error) {
          console.error('保存用户信息失败:', error);
          console.error('错误类型:', typeof error);
          console.error('错误消息:', error.message);
          promptAction.showToast({
            message: '登录信息保存失败: ' + error.message,
            duration: 3000,
            bottom: 50
          });
        }
      }
    }).catch((err: AxiosError) => {
      let message = "request error: " + err.message
      console.log(message)

      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000,
        bottom: 50
      })
    }).finally(() => {
      this.isLoading = false
    })
  }

  // 发送验证码
  sendVerifyCode() {
    if (!this.username || this.username.length !== 11) {
      promptAction.showToast({
        message: '请输入正确的手机号',
        duration: 2000
      })
      return
    }

    axios({
      url: 'http://localhost:8091/sms/send',
      method: 'post',
      params: {
        phone: this.username,
        type: 1 // 1表示登录验证码
      }
    }).then((res: AxiosResponse<R<string>>) => {
      let message = "request result: " + JSON.stringify(res.data)
      let msg = res.data.msg
      console.log(msg)

      promptAction.showToast({
        message: msg,
        duration: 2000,
        bottom: 50
      })

      if (res.data.code == 0) {
        // 显示验证码（开发环境）
        this.currentCode = res.data.data
        this.startCountdown()
      }
    }).catch((err: AxiosError) => {
      let message = "request error: " + err.message
      console.log(message)

      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000,
        bottom: 50
      })
    })
  }

  // 开始倒计时
  startCountdown() {
    this.countdown = 60
    const timer = setInterval(() => {
      this.countdown--
      if (this.countdown <= 0) {
        clearInterval(timer)
        this.currentCode = ''
      }
    }, 1000)
  }
}
