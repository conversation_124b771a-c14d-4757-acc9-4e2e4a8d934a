import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';

/**
 * API响应结构
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 分页响应结构
 */
interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 后台返回的交易记录
 */
interface TransactionResponse {
  id: number;
  userId: number;
  type: number; // 1-充值, 2-提现, 3-转账, 4-消费
  amount: number;
  balance: number;
  description: string;
  createTime: string;
  phone?: string;
  accountNumber?: string;
}

/**
 * 前端使用的交易记录
 */
interface Transaction {
  id: number;
  userId: number;
  type: number;
  amount: number;
  balance: number;
  description: string;
  createTime: string;
  phone?: string;
  accountNumber?: string;
}

/**
 * 转账请求
 */
interface TransferRequest {
  fromAccountNumber: string;
  toAccountNumber: string;
  amount: number;
}

@Entry
@Component
export struct TransactionPage {
  @State currentBalance: number = 0;
  @State transactions: Transaction[] = [];
  @State currentTab: number = 0; // 0-全部, 1-充值, 2-提现, 3-转账, 4-消费
  @State isLoading: boolean = false;
  @State isRefreshing: boolean = false;
  @State userId: number = 1;
  @State pageNum: number = 1;
  @State pageSize: number = 10;
  @State hasMore: boolean = true;
  @State showBalance: boolean = false;

  // 统计数据
  @State monthIncome: number = 950.00;
  @State monthExpense: number = 389.39;
  @State totalRecords: number = 2;

  aboutToAppear() {
    this.loadBalance();
    this.loadTransactions();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 余额卡片
      this.buildBalanceCard()

      // 统计信息
      this.buildStatsCard()

      // 标签页切换
      this.buildTabBar()

      // 交易记录列表
      this.buildTransactionList()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f7fa')
  }

  @Builder
  buildHeader() {
    Row() {
      Image($r('app.media.back'))
        .width(24)
        .height(24)
        .onClick(() => {
          router.back();
        })

      Text('交易记录')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      Image($r('app.media.refresh'))
        .width(24)
        .height(24)
        .onClick(() => {
          this.refreshData();
        })
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor('#ffffff')
  }

  @Builder
  buildBalanceCard() {
    Column() {
      Row() {
        Text('当前余额')
          .fontSize(14)
          .fontColor('#666666')

        Image(this.showBalance ? $r('app.media.eye_open') : $r('app.media.eye_close'))
          .width(16)
          .height(16)
          .margin({ left: 8 })
          .onClick(() => {
            this.showBalance = !this.showBalance;
          })
      }
      .margin({ bottom: 12 })

      Text(this.showBalance ? `¥${this.currentBalance.toFixed(2)}` : '******')
        .fontSize(32)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .letterSpacing(this.showBalance ? 0 : 4)
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .padding(20)
    .margin({ left: 16, right: 16, top: 16 })
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  buildStatsCard() {
    Row() {
      Column() {
        Text('本月收入')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ bottom: 4 })

        Text(`+${this.monthIncome.toFixed(2)}`)
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#34a853')
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      Column() {
        Text('本月支出')
          .fontSize(12)
          .fontColor('#666666')
          .margin({ bottom: 4 })

        Text(`-${this.monthExpense.toFixed(2)}`)
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#ea4335')
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.End)
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .padding(20)
    .margin({ left: 16, right: 16, top: 12 })
  }

  @Builder
  buildTabBar() {
    Row() {
      this.buildTabItem('全部', 0)
      this.buildTabItem('充值', 1)
      this.buildTabItem('提现', 2)
      this.buildTabItem('转账', 3)
      this.buildTabItem('消费', 4)
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .padding({ left: 16, right: 16, top: 16, bottom: 16 })
    .margin({ top: 12 })
  }

  @Builder
  buildTabItem(title: string, index: number) {
    Button(title)
      .type(this.currentTab === index ? ButtonType.Capsule : ButtonType.Normal)
      .backgroundColor(this.currentTab === index ? '#4285f4' : 'transparent')
      .fontColor(this.currentTab === index ? '#ffffff' : '#666666')
      .fontSize(14)
      .height(36)
      .layoutWeight(1)
      .onClick(() => {
        this.currentTab = index;
        this.pageNum = 1;
        this.transactions = [];
        this.loadTransactions();
      })
  }

  @Builder
  buildTransactionList() {
    Column() {
      // 记录统计
      Row() {
        Text(`共 ${this.totalRecords} 条记录`)
          .fontSize(12)
          .fontColor('#666666')

        Blank()

        Text('最近30天')
          .fontSize(12)
          .fontColor('#666666')
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 12 })

      // 交易列表
      if (this.isLoading && this.transactions.length === 0) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#4285f4')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 12 })
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
      } else if (this.transactions.length === 0) {
        Column() {
          Image($r('app.media.empty'))
            .width(80)
            .height(80)
            .opacity(0.5)

          Text('暂无交易记录')
            .fontSize(16)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
      } else {
        List() {
          ForEach(this.transactions, (transaction: Transaction) => {
            ListItem() {
              this.buildTransactionItem(transaction)
            }
          }, (transaction: Transaction) => transaction.id?.toString() || Math.random().toString())
        }
        .width('100%')
        .layoutWeight(1)
        .padding({ left: 16, right: 16 })
        .onReachEnd(() => {
          if (this.hasMore && !this.isLoading) {
            this.loadMoreTransactions();
          }
        })
      }
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .layoutWeight(1)
    .margin({ top: 12 })
  }

  @Builder
  buildTransactionItem(transaction: Transaction) {
    Row() {
      // 交易图标
      Stack() {
        Circle()
          .width(40)
          .height(40)
          .fill(this.getTransactionColor(transaction.type))
          .opacity(0.1)

        Image(this.getTransactionIcon(transaction.type))
          .width(20)
          .height(20)
          .fillColor(this.getTransactionColor(transaction.type))
      }
      .margin({ right: 12 })

      // 交易信息
      Column() {
        Text(this.getTransactionTitle(transaction.type))
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor('#1a1a1a')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(this.formatDateTime(transaction.createTime))
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 2 })

        Text(transaction.description || this.getTransactionTitle(transaction.type))
          .fontSize(12)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      // 金额
      Text(this.formatAmount(transaction.type, transaction.amount))
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor(this.getAmountColor(transaction.type))
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#ffffff')
    .borderRadius(8)
    .margin({ bottom: 8 })
  }

  // 加载余额
  loadBalance() {
    // 这里可以调用钱包余额API
    this.currentBalance = 2846.61;
  }

  // 加载交易记录
  loadTransactions() {
    this.isLoading = true;

    // 根据当前标签页确定type参数
    let typeParam: number | undefined = undefined;
    if (this.currentTab > 0) {
      typeParam = this.currentTab; // 1-充值, 2-提现, 3-转账, 4-消费
    }

    axios({
      url: `http://localhost:8091/transactions/user/${this.userId}`,
      method: 'get',
      params: {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: typeParam
      }
    }).then((res: AxiosResponse<ApiResponse<PageResponse<TransactionResponse>>>) => {
      console.log('加载交易记录结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        const pageData = res.data.data;
        const newTransactions = pageData.records?.map((item: TransactionResponse) => this.convertToTransaction(item)) || [];

        if (this.pageNum === 1) {
          this.transactions = newTransactions;
        } else {
          this.transactions = [...this.transactions, ...newTransactions];
        }

        this.totalRecords = pageData.total || 0;
        this.hasMore = pageData.current < pageData.pages;
      } else {
        promptAction.showToast({
          message: res.data.msg || '加载失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('加载交易记录错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isLoading = false;
      this.isRefreshing = false;
    });
  }

  // 加载更多交易记录
  loadMoreTransactions() {
    if (this.hasMore && !this.isLoading) {
      this.pageNum++;
      this.loadTransactions();
    }
  }

  // 刷新数据
  refreshData() {
    this.isRefreshing = true;
    this.pageNum = 1;
    this.transactions = [];
    this.loadBalance();
    this.loadTransactions();
  }

  // 数据转换：后台数据转换为前端数据
  convertToTransaction(response: TransactionResponse): Transaction {
    return {
      id: response.id,
      userId: response.userId,
      type: response.type,
      amount: response.amount,
      balance: response.balance,
      description: response.description,
      createTime: response.createTime,
      phone: response.phone,
      accountNumber: response.accountNumber
    };
  }

  // 获取交易图标
  getTransactionIcon(type: number): Resource {
    switch(type) {
      case 1: return $r('app.media.phone'); // 充值
      case 2: return $r('app.media.withdraw'); // 提现
      case 3: return $r('app.media.transfer'); // 转账
      case 4: return $r('app.media.scan'); // 消费
      default: return $r('app.media.transaction');
    }
  }

  // 获取交易颜色
  getTransactionColor(type: number): string {
    switch(type) {
      case 1: return '#34a853'; // 充值 - 绿色
      case 2: return '#ea4335'; // 提现 - 红色
      case 3: return '#4285f4'; // 转账 - 蓝色
      case 4: return '#ff9800'; // 消费 - 橙色
      default: return '#666666';
    }
  }

  // 获取交易标题
  getTransactionTitle(type: number): string {
    switch(type) {
      case 1: return '充值';
      case 2: return '提现';
      case 3: return '转账';
      case 4: return '消费';
      default: return '交易';
    }
  }

  // 格式化金额
  formatAmount(type: number, amount: number): string {
    const prefix = (type === 1) ? '+' : '-';
    return `${prefix}${Math.abs(amount).toFixed(2)}`;
  }

  // 获取金额颜色
  getAmountColor(type: number): string {
    return (type === 1) ? '#34a853' : '#ea4335';
  }

  // 格式化日期时间
  formatDateTime(dateTimeStr: string): string {
    try {
      const date = new Date(dateTimeStr);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      return dateTimeStr;
    }
  }
}