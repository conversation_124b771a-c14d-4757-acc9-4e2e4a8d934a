{":Wallet:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"82734343919a9219c848a93ce88dfb4d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"c489be8273867a50afbc86c53d938c92\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\"}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"e73d63164b80efdcec307b77e775437d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"b174ea6ff5824844dde5ad92f6b3ef2b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.3.135\",\"_valueType\":\"string\",\"_hash\":\"1a0ee02ad70bbe8a0246b9e84bfd4cbd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":Wallet:entry:default@PreBuild", "_executionId": ":Wallet:entry:default@PreBuild:1750908503643", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "b11611dc7dd5eacadef11e76933d9e4c"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "5db96daa42556fb8e516a9c35cacee47"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5", {"fileSnapShotHashValue": "8a3abc4d80f62a98ee8d349ed78c3254"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build-profile.json5", {"fileSnapShotHashValue": "84c8b4993d3a5326e704ebb436ea2239"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "a96f48aa4cb48acffc27ff23df793601"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "4e59c962689b9f3763f8d0822c16365e"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "488003f3ceff097bc510619f612ced89"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5", {"fileSnapShotHashValue": "2b7fff9411bdad869dd36e9debed421d"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh-package.json5", {"fileSnapShotHashValue": "7d1f9134fbb6743547879e69c9162b23"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":Wallet:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.icss.wallet\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"d344ff8e0574c987ec94d3e6e4261f77\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"bc2129ba20a21b7e5234139ede1b4d7b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\",\"_hash\":\"40d5093f345351dd6d67ce5d6a209345\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\".preview\",\"_valueType\":\"string\",\"_hash\":\"88e9a315669657b02bc470a13f92befe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"44964f8abd318606862a2cee062554fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d35c8440e915c3a94c482ddd6f7af075\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"edbf05a2d2be2c385e75d9565a48d419\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a15b7a3ed818faa99a4a10d67f52cb72\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\"}]}}\",\"_valueType\":\"string\",\"_hash\":\"3e639925ffcfd1cb584a7998531d57d0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"555604752defc243b4e4c55d1549fc06\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"b2877a8a5e4d85b48b0208a17da3ae75\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"086cac69f102cdd9ee25e54982ad7b76\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"3b15da42c5f4b695fbd1d0b43191764a\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":Wallet:entry:default@MergeProfile", "_executionId": ":Wallet:entry:default@MergeProfile:1750908179506", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5", {"fileSnapShotHashValue": "b11611dc7dd5eacadef11e76933d9e4c"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5", {"fileSnapShotHashValue": "8a3abc4d80f62a98ee8d349ed78c3254"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "5db96daa42556fb8e516a9c35cacee47"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\module.json", {"fileSnapShotHashValue": "8cd89930e1deb617d631ba2d5fe7e9b5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "3f0df97cd263751dbcbf28c01b0f1c8a"}]]}}, ":Wallet:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":Wallet:entry:default@CreateBuildProfile", "_executionId": ":Wallet:entry:default@CreateBuildProfile:1750908179526", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5", {"fileSnapShotHashValue": "b11611dc7dd5eacadef11e76933d9e4c"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5", {"fileSnapShotHashValue": "8a3abc4d80f62a98ee8d349ed78c3254"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "88aee184000a3bbe71438ad85867bfea"}]]}}, ":Wallet:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/axios\",\"_value\":\"84de785efb53b8efacdfc7ce12744489\",\"_valueType\":\"string\",\"_hash\":\"6627855ab7d46f50d75b264a995c45ac\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\",\"_hash\":\"52638b5b8d5967d85f7d558e6c0897dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9**************************\",\"_valueType\":\"string\",\"_hash\":\"65eb1ae89d72758b386a93dddd5db61d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\",\"_hash\":\"6fbb0d2287cd1f34051162075393f37a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":Wallet:entry:default@GeneratePkgContextInfo", "_executionId": ":Wallet:entry:default@GeneratePkgContextInfo:1750908179547", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "889604fcc1e74a5258f0f83b30508a7a"}]]}}, ":Wallet:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"750b4bda198545a67903dfb3f6a00a95\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"713e499a13beffe12f1dfb936f957a2a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\",\"_hash\":\"ac54f3d4ced2d4c1d666d40e4f7c454a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\",\"_hash\":\"955f8760c6b7289b81ed107c2c4df075\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":Wallet:entry:default@ProcessProfile", "_executionId": ":Wallet:entry:default@ProcessProfile:1750908179553", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "3f0df97cd263751dbcbf28c01b0f1c8a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "a927ba341509fcb3e8417b1ac624164c"}]]}}, ":Wallet:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":Wallet:entry:default@ProcessRouterMap", "_executionId": ":Wallet:entry:default@ProcessRouterMap:1750908179814", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5", {"fileSnapShotHashValue": "2b7fff9411bdad869dd36e9debed421d"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh-package.json5", {"fileSnapShotHashValue": "7d1f9134fbb6743547879e69c9162b23"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "5db96daa42556fb8e516a9c35cacee47"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "889604fcc1e74a5258f0f83b30508a7a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "bb54c7d065067304a272a842367d689e"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "1b32c8c191c28a05d302a1924c80c8db"}]]}}, ":Wallet:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\modules.ap\",\"_valueType\":\"string\",\"_hash\":\"fcbfc663f16eb3c0a9020b2c48fa0ce7\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"fb2a1d73eb1fbae90a8cf98855837a9d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\",\"_hash\":\"f35d706752ca7c42be23eafc45024d27\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependencyModuleAbility\",\"_valueType\":\"undefined\",\"_hash\":\"3fd1daff9f22581e3cadc0099d1d2fa3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\"}\",\"_valueType\":\"string\",\"_hash\":\"9db73abcfdc15e72276706be3b37b9c1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{\\\"@ohos/axios\\\":\\\"^2.0.9\\\"}\",\"_valueType\":\"string\",\"_hash\":\"27d18aed8d524bc7677d7db57c85473f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\",\"_hash\":\"08882f68a656a45d7c34d2c36857b154\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_valueType\":\"undefined\",\"_hash\":\"4620e35a57f3f6f55564cea6f6128e50\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\",\"_valueType\":\"string\",\"_hash\":\"69573a29fe6e1e98dc6f77e7d3433531\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":Wallet:entry:default@GenerateLoaderJson", "_executionId": ":Wallet:entry:default@GenerateLoaderJson:1750908179854", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "488003f3ceff097bc510619f612ced89"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "889604fcc1e74a5258f0f83b30508a7a"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "bb54c7d065067304a272a842367d689e"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "70dda983c3287ff5c4522f0d01fd4304"}]]}}, ":Wallet:entry:default@PreviewCompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_PAGE\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_SRCPATH\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@PreviewCompileResource", "_key": ":Wallet:entry:default@PreviewCompileResource", "_executionId": ":Wallet:entry:default@PreviewCompileResource:1750909264987", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "2e9c0515dcda6813f55b9877fce44266"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources", {"fileSnapShotHashValue": "72154a63fadd9eb198395bdde53ab120"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "3f0df97cd263751dbcbf28c01b0f1c8a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "66277ee3ffe77db60102c3b5019554f9"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "a63da2766a6b92a7196d13569cb095ff"}]]}}, ":Wallet:entry:default@CopyPreviewProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@CopyPreviewProfile", "_key": ":Wallet:entry:default@CopyPreviewProfile", "_executionId": ":Wallet:entry:default@CopyPreviewProfile:1750909266157", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile", {"fileSnapShotHashValue": "b46868b023a78ad56ef1f1ca55157a3c"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"fileSnapShotHashValue": "069c07e4f3b5389c1f60a9caeda34ead"}]]}}, ":Wallet:entry:default@PreviewUpdateAssets": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"previewBuildConfigJson\",\"_value\":\"{\\\"deviceType\\\":\\\"phone,tablet,2in1\\\",\\\"buildMode\\\":\\\"debug\\\",\\\"note\\\":\\\"false\\\",\\\"logLevel\\\":\\\"3\\\",\\\"isPreview\\\":\\\"true\\\",\\\"checkEntry\\\":\\\"true\\\",\\\"localPropertiesPath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\local.properties\\\",\\\"Path\\\":\\\"D:\\\\\\\\HarmonyOS\\\\\\\\DevEco Studio\\\\\\\\tools\\\\\\\\node\\\\\\\\\\\",\\\"aceProfilePath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\",\\\"hapMode\\\":\\\"false\\\",\\\"img2bin\\\":\\\"true\\\",\\\"projectProfilePath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\build-profile.json5\\\",\\\"watchMode\\\":\\\"true\\\",\\\"appResource\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ResourceTable.txt\\\",\\\"aceBuildJson\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\loader\\\\\\\\default\\\\\\\\loader.json\\\",\\\"aceModuleRoot\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\ets\\\",\\\"aceSoPath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\nativeDependencies.txt\\\",\\\"cachePath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\.default\\\",\\\"aceModuleBuild\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\assets\\\\\\\\default\\\\\\\\ets\\\",\\\"aceModuleJsonPath\\\":\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"stageRouterConfig\\\":{\\\"paths\\\":[\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"D:\\\\\\\\AAAqimo\\\\\\\\wallet\\\\\\\\HarmonyOS\\\\\\\\Wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\\\\\\main_pages.json\\\"],\\\"contents\\\":[\\\"{\\\\\\\"module\\\\\\\":{\\\\\\\"pages\\\\\\\":\\\\\\\"$profile:main_pages\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"entry\\\\\\\"}}\\\",\\\"{\\\\\\\"src\\\\\\\":[\\\\\\\"pages/LoginPage\\\\\\\",\\\\\\\"pages/BarPage\\\\\\\",\\\\\\\"pages/WalletPage\\\\\\\",\\\\\\\"pages/HomePage\\\\\\\",\\\\\\\"pages/TransactionPage\\\\\\\",\\\\\\\"pages/SettingsPage\\\\\\\",\\\\\\\"pages/CardDetailPage\\\\\\\",\\\\\\\"pages/BankCardPage\\\\\\\"]}\\\"]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@PreviewUpdateAssets", "_key": ":Wallet:entry:default@PreviewUpdateAssets", "_executionId": ":Wallet:entry:default@PreviewUpdateAssets:*************", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "e157a13c4a4db57f8e7e3c0c0ddd7eac"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", {"isDirectory": false, "fileSnapShotHashValue": "f2168dff9b7294d5a9d18e6f0b1bb4c7"}]]}}, ":Wallet:entry:default@PreviewArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"@ohos/axios\",\"_value\":\"@ohos/axios: ^2.0.9\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "Wallet", "_moduleName": "entry", "_taskName": "default@PreviewArkTS", "_key": ":Wallet:entry:default@PreviewArkTS", "_executionId": ":Wallet:entry:default@PreviewArkTS:1750909266211", "_inputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "6e33edf824e1eb7b636ae5a49a1889c5"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js", {"fileSnapShotHashValue": "45e2b2a85dee76b87f33cb188f7e9bce"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "e6915bf9f72901f8d6792b50f49e3211"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": "aa05e6f75b89b49fbd3af62037385ec4"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "3db65d933fbc424c0e3c5f44edf6ac70"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "a927ba341509fcb3e8417b1ac624164c"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "069c07e4f3b5389c1f60a9caeda34ead"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "1830e9b4c3f01d71c8a82e96171fb842"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "889604fcc1e74a5258f0f83b30508a7a"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "88aee184000a3bbe71438ad85867bfea"}], ["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "488003f3ceff097bc510619f612ced89"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}