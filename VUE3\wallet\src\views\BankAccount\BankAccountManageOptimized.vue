<template>
  <div class="bank-account-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><CreditCard /></el-icon>
            银行账户管理
          </h2>
          <p>管理用户银行账户信息，包括查看、添加、编辑和删除操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleAdd" size="large">
            <el-icon><Plus /></el-icon>
            添加账户
          </el-button>
        </div>
      </div>
    </div>



    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20" class="search-row">
        <el-col :span="6">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Phone /></el-icon>
            </template>
          </el-input>
        </el-col>

        <el-col :span="6">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择账户状态"
            clearable
            style="width: 100%"
          >
            <el-option label="正常" :value="1" />
            <el-option label="冻结" :value="0" />
            <el-option label="销户" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <!-- 预留空间 -->
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 银行账户列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="accountId" label="账户ID" width="80" />
        <el-table-column prop="accountNumber" label="银行账号" width="180" />
        <el-table-column prop="bankName" label="银行名称" width="120" />
        <el-table-column prop="accountType" label="账户类型" width="100">
          <template #default="scope">
            {{ getAccountTypeName(scope.row.accountType) }}
          </template>
        </el-table-column>
        <el-table-column prop="accountHolder" label="账户持有人" width="120" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="balance" label="账户余额" width="120">
          <template #default="scope">
            {{ scope.row.currency }} {{ scope.row.balance }}
          </template>
        </el-table-column>
        <el-table-column prop="availableBalance" label="可用余额" width="120">
          <template #default="scope">
            {{ scope.row.currency }} {{ scope.row.availableBalance }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认账户" width="90">
          <template #default="scope">
            <el-tag :type="scope.row.isDefault === 1 ? 'success' : 'info'">
              {{ scope.row.isDefault === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="openDate" label="开户日期" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.accountId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input
            v-model="form.userId"
            placeholder="请输入用户ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="银行账号" prop="accountNumber">
          <el-input
            v-model="form.accountNumber"
            placeholder="请输入银行账号"
            clearable
          />
        </el-form-item>
        <el-form-item label="账户类型" prop="accountType">
          <el-select
            v-model="form.accountType"
            placeholder="请选择账户类型"
            style="width: 100%"
          >
            <el-option label="储蓄账户" :value="1" />
            <el-option label="支票账户" :value="2" />
            <el-option label="信用卡账户" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="银行名称" prop="bankName">
          <el-input
            v-model="form.bankName"
            placeholder="请输入银行名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="支行名称" prop="branchName">
          <el-input
            v-model="form.branchName"
            placeholder="请输入支行名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="账户持有人" prop="accountHolder">
          <el-input
            v-model="form.accountHolder"
            placeholder="请输入账户持有人姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="货币类型" prop="currency">
          <el-select
            v-model="form.currency"
            placeholder="请选择货币类型"
            style="width: 100%"
          >
            <el-option label="人民币" value="CNY" />
            <el-option label="美元" value="USD" />
            <el-option label="欧元" value="EUR" />
          </el-select>
        </el-form-item>
        <el-form-item label="账户余额" prop="balance">
          <el-input-number
            v-model="form.balance"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入账户余额"
          />
        </el-form-item>
        <el-form-item label="可用余额" prop="availableBalance">
          <el-input-number
            v-model="form.availableBalance"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入可用余额"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">冻结</el-radio>
            <el-radio :label="2">销户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="默认账户" prop="isDefault">
          <el-radio-group v-model="form.isDefault">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开户日期" prop="openDate">
          <el-date-picker
            v-model="form.openDate"
            type="date"
            placeholder="请选择开户日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import {
  CreditCard, Check, Lock, Money, Plus, Refresh, Search, Phone
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref(null)



// 搜索表单
const searchForm = reactive({
  phone: '',
  status: null
})

// 编辑表单
const form = ref({
  accountId: null,
  userId: '',
  accountNumber: '',
  accountType: 1,
  bankName: '',
  branchName: '',
  accountHolder: '',
  phone: '',
  currency: 'CNY',
  balance: 0,
  availableBalance: 0,
  creditLimit: 0,
  status: 1,
  isDefault: 0,
  openDate: new Date()
})

// 表单验证规则
const formRules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  accountNumber: [
    { required: true, message: '请输入银行账号', trigger: 'blur' },
    { min: 10, max: 25, message: '银行账号长度应为10-25位', trigger: 'blur' }
  ],
  accountType: [
    { required: true, message: '请选择账户类型', trigger: 'change' }
  ],
  bankName: [
    { required: true, message: '请输入银行名称', trigger: 'blur' }
  ],
  accountHolder: [
    { required: true, message: '请输入账户持有人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const response = await axios.get('http://localhost:8091/bankAccounts/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        phone: searchForm.phone,
        status: searchForm.status
      }
    })
    if (response.data && response.data.code === 0) {
      tableData.value = response.data.data?.records || []
      total.value = response.data.data?.total || 0
    } else {
      ElMessage.error('获取银行账户列表失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取银行账户列表失败:', error)
    ElMessage.error('获取银行账户列表失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  dialogTitle.value = '添加银行账户'
  form.value = {
    accountId: null,
    userId: '',
    accountNumber: '',
    accountType: 1,
    bankName: '',
    branchName: '',
    accountHolder: '',
    phone: '',
    currency: 'CNY',
    balance: 0,
    availableBalance: 0,
    creditLimit: 0,
    status: 1,
    isDefault: 0,
    openDate: new Date()
  }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑银行账户'
  form.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}

const submitForm = async () => {
  try {
    await formRef.value.validate()

    if (form.value.accountId) {
      // 更新
      const response = await axios.put('http://localhost:8091/bankAccounts/update', form.value)
      if (response.data && response.data.code === 0) {
        ElMessage.success('更新成功')
        dialogVisible.value = false
        loadData()
      } else {
        ElMessage.error('更新失败: ' + (response.data?.msg || '未知错误'))
      }
    } else {
      // 新增
      const response = await axios.post('http://localhost:8091/bankAccounts/add', form.value)
      if (response.data && response.data.code === 0) {
        ElMessage.success('添加成功')
        dialogVisible.value = false
        loadData()
      } else {
        ElMessage.error('添加失败: ' + (response.data?.msg || '未知错误'))
      }
    }
  } catch (error) {
    if (error.response) {
      console.error('操作失败:', error)
      ElMessage.error('操作失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
    } else if (!error.message.includes('validate')) {
      console.error('网络错误:', error)
      ElMessage.error('网络错误，请稍后重试')
    }
  }
}

const handleDelete = (accountId) => {
  ElMessageBox.confirm('确定要删除该银行账户吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const response = await axios.delete(`http://localhost:8091/bankAccounts/${accountId}`)
        if (response.data && response.data.code === 0) {
          ElMessage.success('删除成功')
          loadData()
        } else {
          ElMessage.error('删除失败: ' + (response.data?.msg || '未知错误'))
        }
      } catch (error) {
        console.error('删除银行账户失败:', error)
        ElMessage.error('删除银行账户失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    phone: '',
    status: null
  })
  currentPage.value = 1
  loadData()
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1 // 重置到第一页
  loadData()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  loadData()
}

// 工具方法
const getAccountTypeName = (type) => {
  const typeMap = {
    1: '储蓄账户',
    2: '支票账户',
    3: '信用卡账户'
  }
  return typeMap[type] || '未知'
}

const getStatusName = (status) => {
  const statusMap = {
    0: '冻结',
    1: '正常',
    2: '销户'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status) => {
  const tagMap = {
    0: 'danger',
    1: 'success',
    2: 'info'
  }
  return tagMap[status] || 'info'
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.bank-account-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}



/* 搜索和表格卡片样式 */
.search-card, .table-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  overflow: hidden;
}

.search-row {
  align-items: center;
}



.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .bank-account-manage {
    padding: 12px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-left h2 {
    font-size: 24px;
  }

  .statistics-cards .el-col {
    margin-bottom: 12px;
  }

  .search-row .el-col {
    margin-bottom: 12px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
    width: 56px;
    height: 56px;
    font-size: 24px;
  }

  .stat-number {
    font-size: 28px;
  }


}
</style>
