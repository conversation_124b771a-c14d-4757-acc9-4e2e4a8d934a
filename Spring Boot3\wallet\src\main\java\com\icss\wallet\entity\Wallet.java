package com.icss.wallet.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("wallet")
public class Wallet {
    @TableId(type = IdType.AUTO)
    private Long walletId;
    
    private Long userId;
    
    private BigDecimal balance;
    
    private Integer status; // 0-冻结,1-正常
    
    private Date createTime;
    
    private Date updateTime;
}
