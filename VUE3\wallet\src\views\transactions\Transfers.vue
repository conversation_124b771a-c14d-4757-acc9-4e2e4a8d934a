<template>
  <div class="transfers-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>转账记录</span>
          <el-button type="primary" @click="showTransferDialog">
            <el-icon><Plus /></el-icon>
            发起转账
          </el-button>
        </div>
      </template>

      <!-- 筛选表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="交易流水号">
          <el-input
            v-model="searchForm.transNo"
            placeholder="请输入交易流水号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="转账方向">
          <el-select
            v-model="searchForm.direction"
            placeholder="请选择转账方向"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="转出" value="out" />
            <el-option label="转入" value="in" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 转账记录表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column prop="transId" label="交易ID" width="80" />
        <el-table-column prop="transNo" label="交易流水号" width="180" />
        <el-table-column label="转账方向" width="100">
          <template #default="{ row }">
            <el-tag :type="getDirectionTagType(row)">
              {{ getDirectionText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="转账金额" width="120" align="right">
          <template #default="{ row }">
            <span class="amount-text">
              ¥{{ Number(row.amount).toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="targetInfo" label="收款人信息" width="150">
          <template #default="{ row }">
            <div class="target-info">
              <div>{{ row.targetInfo }}</div>
              <div class="target-detail">{{ getTargetDetail(row.targetInfo) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="转账时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 转账对话框 -->
    <el-dialog
      v-model="transferDialogVisible"
      title="发起转账"
      width="500px"
      :before-close="handleTransferDialogClose"
    >
      <el-form
        ref="transferFormRef"
        :model="transferForm"
        :rules="transferRules"
        label-width="120px"
      >
        <el-form-item label="转出账户" prop="fromAccountNumber">
          <el-select
            v-model="transferForm.fromAccountNumber"
            placeholder="请选择转出账户"
            style="width: 100%"
            @change="handleFromAccountChange"
          >
            <el-option
              v-for="account in userAccounts"
              :key="account.accountNumber"
              :label="`${account.accountNumber} (余额: ¥${account.balance})`"
              :value="account.accountNumber"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="转入账户" prop="toAccountNumber">
          <el-input
            v-model="transferForm.toAccountNumber"
            placeholder="请输入转入账户号"
            clearable
          />
        </el-form-item>

        <el-form-item label="转账金额" prop="amount">
          <el-input
            v-model="transferForm.amount"
            placeholder="请输入转账金额"
            type="number"
            step="0.01"
            min="0.01"
          >
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="转账备注">
          <el-input
            v-model="transferForm.remark"
            placeholder="请输入转账备注（可选）"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="账户余额" v-if="selectedAccountBalance">
          <span class="balance-info">¥{{ selectedAccountBalance }}</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleTransferDialogClose">取消</el-button>
          <el-button type="primary" @click="handleTransfer" :loading="transferLoading">
            确认转账
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  transNo: '',
  direction: ''
})

// 转账相关数据
const transferDialogVisible = ref(false)
const transferLoading = ref(false)
const transferFormRef = ref()
const userAccounts = ref([])
const selectedAccountBalance = ref('')

// 转账表单
const transferForm = reactive({
  fromAccountNumber: '',
  toAccountNumber: '',
  amount: '',
  remark: ''
})

// 转账表单验证规则
const transferRules = {
  fromAccountNumber: [
    { required: true, message: '请选择转出账户', trigger: 'change' }
  ],
  toAccountNumber: [
    { required: true, message: '请输入转入账户号', trigger: 'blur' },
    { min: 10, max: 20, message: '账户号长度应为10-20位', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入转账金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入转账金额'))
        } else if (isNaN(value) || Number(value) <= 0) {
          callback(new Error('转账金额必须大于0'))
        } else if (Number(value) > 999999.99) {
          callback(new Error('转账金额不能超过999,999.99'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取转账记录列表
const fetchTransfers = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      type: 3 // 只查询转账类型的交易
    }

    const response = await axios.get('http://localhost:8091/transactions', { params })

    if (response.data && response.data.code === 0) {
      let records = response.data.data?.records || []
      // 只显示转账类型的记录
      records = records.filter(record => record.type === 3)

      // 根据交易流水号筛选
      if (searchForm.transNo) {
        records = records.filter(record =>
          record.transNo && record.transNo.toLowerCase().includes(searchForm.transNo.toLowerCase())
        )
      }

      // 根据转账方向筛选
      if (searchForm.direction === 'out') {
        // 可以根据备注或其他字段判断转出记录
        records = records.filter(record => record.remark && record.remark.includes('转账至'))
      } else if (searchForm.direction === 'in') {
        // 可以根据备注或其他字段判断转入记录
        records = records.filter(record => record.remark && !record.remark.includes('转账至'))
      }

      tableData.value = records
      total.value = records.length
    } else {
      ElMessage.error('获取转账记录失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取转账记录失败:', error)
    ElMessage.error('获取转账记录失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTransfers()
}

// 重置
const handleReset = () => {
  searchForm.transNo = ''
  searchForm.direction = ''
  currentPage.value = 1
  fetchTransfers()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchTransfers()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchTransfers()
}

// 获取转账方向文本
const getDirectionText = (row) => {
  // 根据备注判断转账方向
  if (row.remark && row.remark.includes('转账至')) {
    return '转出'
  } else {
    return '转入'
  }
}

// 获取转账方向标签类型
const getDirectionTagType = (row) => {
  // 根据备注判断转账方向
  if (row.remark && row.remark.includes('转账至')) {
    return 'warning' // 转出用橙色
  } else {
    return 'success' // 转入用绿色
  }
}

// 获取目标详情
const getTargetDetail = (targetInfo) => {
  if (!targetInfo) return ''
  // 如果是手机号格式
  if (/^1[3-9]\d{9}$/.test(targetInfo)) {
    return '手机号转账'
  }
  // 如果是银行卡号格式
  if (/^\d{16,19}$/.test(targetInfo)) {
    return '银行卡转账'
  }
  return '其他方式'
}

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    0: '处理中',
    1: '成功',
    2: '失败'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return statusMap[status] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 显示转账对话框
const showTransferDialog = async () => {
  await fetchUserAccounts()
  transferDialogVisible.value = true
}

// 获取用户银行账户列表
const fetchUserAccounts = async () => {
  try {
    // 使用分页API获取所有账户
    const response = await axios.get('http://localhost:8091/bankAccounts/page', {
      params: {
        pageNum: 1,
        pageSize: 100, // 获取足够多的记录
        status: 1 // 只获取正常状态的账户
      }
    })
    if (response.data && response.data.code === 0) {
      const allAccounts = response.data.data?.records || []
      // 显示所有正常状态的账户
      userAccounts.value = allAccounts.filter(account => account.status === 1)
    } else {
      ElMessage.error('获取账户列表失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取账户列表失败:', error)
    ElMessage.error('获取账户列表失败')
  }
}

// 转出账户变化处理
const handleFromAccountChange = (accountNumber) => {
  const account = userAccounts.value.find(acc => acc.accountNumber === accountNumber)
  selectedAccountBalance.value = account ? Number(account.balance).toFixed(2) : ''
}

// 处理转账
const handleTransfer = async () => {
  try {
    await transferFormRef.value.validate()

    // 检查余额
    const selectedAccount = userAccounts.value.find(acc => acc.accountNumber === transferForm.fromAccountNumber)
    if (selectedAccount && Number(transferForm.amount) > Number(selectedAccount.balance)) {
      ElMessage.error('转账金额超过账户余额')
      return
    }

    // 检查是否转给自己
    if (transferForm.fromAccountNumber === transferForm.toAccountNumber) {
      ElMessage.error('不能转账给自己')
      return
    }

    transferLoading.value = true

    const params = new URLSearchParams()
    params.append('fromAccountNumber', transferForm.fromAccountNumber)
    params.append('toAccountNumber', transferForm.toAccountNumber)
    params.append('amount', transferForm.amount)

    const response = await axios.post('http://localhost:8091/transfer', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    if (response.data && response.data.code === 0) {
      ElMessage.success('转账成功')
      handleTransferDialogClose()
      fetchTransfers() // 刷新转账记录
    } else {
      ElMessage.error('转账失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error.message && error.message.includes('validate')) {
      return // 表单验证失败，不显示错误消息
    }
    console.error('转账失败:', error)
    ElMessage.error('转账失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    transferLoading.value = false
  }
}

// 关闭转账对话框
const handleTransferDialogClose = () => {
  transferDialogVisible.value = false
  transferFormRef.value?.resetFields()
  transferForm.fromAccountNumber = ''
  transferForm.toAccountNumber = ''
  transferForm.amount = ''
  transferForm.remark = ''
  selectedAccountBalance.value = ''
}

// 初始化
onMounted(() => {
  fetchTransfers()
})
</script>

<style scoped>
.transfers-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.amount-text {
  color: #409eff;
  font-weight: bold;
}

.target-info {
  line-height: 1.4;
}

.target-detail {
  font-size: 12px;
  color: #999;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

.balance-info {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>