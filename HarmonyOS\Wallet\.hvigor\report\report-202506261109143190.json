{"version": "2.0", "ppid": 4888, "events": [{"head": {"id": "85cae7fe-96d9-42a7-a156-6b410453d523", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891585571500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf15c73b-1e54-4c09-8e6e-3b564ec826a4", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9013303709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb5a9f32-7352-4c01-8257-1e57573b2d44", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9013304013500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0689bf76-0281-4605-bbcb-b881b87c6c0b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014183888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f84b417-829a-46e8-987b-fcef5d2b97da", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014190712000, "endTime": 9014328038300}, "additional": {"children": ["8b3224b6-12b5-4dcf-a91e-87d201124450", "469c0207-a906-4cb1-a2f3-6981a34e98e4", "36db4e5c-b9d9-462e-ae0b-57caa78efb5f", "3e6f9416-fcd6-46ec-b265-d557a219df98", "f78a5c32-3147-44fd-a86d-fe27da556784", "bc6b16aa-3d5b-4ad6-a909-1162167f593a", "98b2ab42-ffe6-4ad5-b226-5244d86f0f8d"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b3224b6-12b5-4dcf-a91e-87d201124450", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014190713400, "endTime": 9014201727800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "e395bd2a-ff72-488c-ad06-d7536ce50bcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014201743200, "endTime": 9014326913000}, "additional": {"children": ["d572c303-0f68-46e0-a880-689b0eee3004", "33ab9fc8-2c69-419d-83b2-2345bf80b0d7", "a3cfdb26-4372-4299-ac77-72d8d6cad7ac", "019e15de-6695-4303-ab50-282529ef225a", "6f734176-4c08-4e2c-b53a-f6824117d982", "1b4b128b-d8b3-4576-80c7-81dc12e6a291", "05f21a8d-1dcb-49f3-9ba1-43cde0e0bd3b", "a7313f8a-a8d5-4d25-bcd0-fce998ecdf9a", "e7d50809-397b-4971-94d0-2418d668ef7c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36db4e5c-b9d9-462e-ae0b-57caa78efb5f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014326943000, "endTime": 9014328030400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "25f48a09-1402-460a-a22f-fdd3159db3e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e6f9416-fcd6-46ec-b265-d557a219df98", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014328034700, "endTime": 9014328035700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "30778472-4c0e-41e1-8d66-feb2cbcad0ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f78a5c32-3147-44fd-a86d-fe27da556784", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014193502500, "endTime": 9014193541900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "4f5809ee-fdd8-437d-897b-f7a102617373"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f5809ee-fdd8-437d-897b-f7a102617373", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014193502500, "endTime": 9014193541900}, "additional": {"logType": "info", "children": [], "durationId": "f78a5c32-3147-44fd-a86d-fe27da556784", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "bc6b16aa-3d5b-4ad6-a909-1162167f593a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014197710800, "endTime": 9014197723200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "674b8cb2-4b84-4b22-8257-23ae413c1fbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "674b8cb2-4b84-4b22-8257-23ae413c1fbe", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014197710800, "endTime": 9014197723200}, "additional": {"logType": "info", "children": [], "durationId": "bc6b16aa-3d5b-4ad6-a909-1162167f593a", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "8db4192b-2e1e-48ea-8c94-f26090910343", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014197770700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f43201-b65e-47ed-a6f5-488b1323f444", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014201634700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e395bd2a-ff72-488c-ad06-d7536ce50bcb", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014190713400, "endTime": 9014201727800}, "additional": {"logType": "info", "children": [], "durationId": "8b3224b6-12b5-4dcf-a91e-87d201124450", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "d572c303-0f68-46e0-a880-689b0eee3004", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014206068000, "endTime": 9014206075000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "2cd6692c-0a10-4ee4-a90e-e81429d78d1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33ab9fc8-2c69-419d-83b2-2345bf80b0d7", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014206084900, "endTime": 9014209938100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "1ebc4f33-9b4d-428c-93e0-12d8abf5c605"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3cfdb26-4372-4299-ac77-72d8d6cad7ac", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014209947600, "endTime": 9014276415400}, "additional": {"children": ["1e1e9e28-01cf-4b1f-8491-32716af617cc", "27c751f7-67c6-48b9-875b-98c0eaeb9fec", "36689a6f-16be-4108-927e-9684609e5dcb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "de32c8ca-7ab1-4252-82b7-1121f30c855b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "019e15de-6695-4303-ab50-282529ef225a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014276426300, "endTime": 9014294294000}, "additional": {"children": ["a7563c6e-59f7-4a08-b6dd-d52da721e62b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "70f667af-0a63-42f8-a73d-28ac0d954c93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f734176-4c08-4e2c-b53a-f6824117d982", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014294299900, "endTime": 9014306816500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "6f41e513-8bcb-4c95-8cf3-6f69c97272a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b4b128b-d8b3-4576-80c7-81dc12e6a291", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014307687500, "endTime": 9014314809500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "07d4c5a1-8fe9-4eea-a43c-f86c48bc0c93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05f21a8d-1dcb-49f3-9ba1-43cde0e0bd3b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014314824800, "endTime": 9014326786500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "d8ec9136-4958-4df0-a92d-940eed658e23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7313f8a-a8d5-4d25-bcd0-fce998ecdf9a", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014326814400, "endTime": 9014326903400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "5995b393-a9ca-4e5e-b426-d46e25044427"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cd6692c-0a10-4ee4-a90e-e81429d78d1b", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014206068000, "endTime": 9014206075000}, "additional": {"logType": "info", "children": [], "durationId": "d572c303-0f68-46e0-a880-689b0eee3004", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "1ebc4f33-9b4d-428c-93e0-12d8abf5c605", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014206084900, "endTime": 9014209938100}, "additional": {"logType": "info", "children": [], "durationId": "33ab9fc8-2c69-419d-83b2-2345bf80b0d7", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "1e1e9e28-01cf-4b1f-8491-32716af617cc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014210486700, "endTime": 9014210499700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3cfdb26-4372-4299-ac77-72d8d6cad7ac", "logId": "b6e90fa5-0d5a-4f3c-8de5-354190776644"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6e90fa5-0d5a-4f3c-8de5-354190776644", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014210486700, "endTime": 9014210499700}, "additional": {"logType": "info", "children": [], "durationId": "1e1e9e28-01cf-4b1f-8491-32716af617cc", "parent": "de32c8ca-7ab1-4252-82b7-1121f30c855b"}}, {"head": {"id": "27c751f7-67c6-48b9-875b-98c0eaeb9fec", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014212204600, "endTime": 9014275843400}, "additional": {"children": ["66b1508e-b91f-4288-88b0-921fc4e5368b", "a82dc3ad-c39c-44e0-b00e-a1ad471e6bea"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3cfdb26-4372-4299-ac77-72d8d6cad7ac", "logId": "cbf4f6eb-e234-42a6-9962-423a11235209"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66b1508e-b91f-4288-88b0-921fc4e5368b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014212205900, "endTime": 9014216112500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27c751f7-67c6-48b9-875b-98c0eaeb9fec", "logId": "42938316-2a90-48e1-8a42-877f7055f080"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a82dc3ad-c39c-44e0-b00e-a1ad471e6bea", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014216126000, "endTime": 9014275831000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27c751f7-67c6-48b9-875b-98c0eaeb9fec", "logId": "75a3b758-60b2-4572-a814-4e5edd0891b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef73f598-75ea-466a-ac9d-1a49ed07f023", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014212211500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49b07fd3-a0f8-426e-826b-f14635de9ffb", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014216011800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42938316-2a90-48e1-8a42-877f7055f080", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014212205900, "endTime": 9014216112500}, "additional": {"logType": "info", "children": [], "durationId": "66b1508e-b91f-4288-88b0-921fc4e5368b", "parent": "cbf4f6eb-e234-42a6-9962-423a11235209"}}, {"head": {"id": "eb940983-da4d-42d3-b92f-e055a0db7d79", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014216137700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13792a49-55c8-44df-8ca3-739147396e85", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014221454000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117961d3-d422-4a3a-a08b-b410e01a7be2", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014221541200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01e3047-1213-48ac-b946-e9845b13f08c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014221647300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ede64e-5009-461e-a7aa-a4636efdb8f8", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014221732900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7152c176-6e7b-4906-9711-0547a3e2722a", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014223085900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd22929-69dc-491b-b01a-1897c8d3ccff", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014226823700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0562b23a-5955-497e-9554-9cd9695208be", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014234245100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e152b7dd-1617-4dbb-adbb-7e5515b4d7ff", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014256609000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03c9c9a-515d-4071-bee9-07f651bbbf7f", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014256738200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 9}, "markType": "other"}}, {"head": {"id": "20abbff9-94e9-4004-89ae-6588f3e752b4", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014256751600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 9}, "markType": "other"}}, {"head": {"id": "3fb78070-9bf0-4ae9-ada9-17a23ccf6022", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014275574300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718ae6ed-8517-4faa-b6ab-911a577e1933", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014275691300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d979d0-2d00-4aa7-84b6-4cb5133758b2", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014275751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf3c875f-668e-4cf5-8e71-844af113ca34", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014275791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75a3b758-60b2-4572-a814-4e5edd0891b4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014216126000, "endTime": 9014275831000}, "additional": {"logType": "info", "children": [], "durationId": "a82dc3ad-c39c-44e0-b00e-a1ad471e6bea", "parent": "cbf4f6eb-e234-42a6-9962-423a11235209"}}, {"head": {"id": "cbf4f6eb-e234-42a6-9962-423a11235209", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014212204600, "endTime": 9014275843400}, "additional": {"logType": "info", "children": ["42938316-2a90-48e1-8a42-877f7055f080", "75a3b758-60b2-4572-a814-4e5edd0891b4"], "durationId": "27c751f7-67c6-48b9-875b-98c0eaeb9fec", "parent": "de32c8ca-7ab1-4252-82b7-1121f30c855b"}}, {"head": {"id": "36689a6f-16be-4108-927e-9684609e5dcb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014276393800, "endTime": 9014276405800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3cfdb26-4372-4299-ac77-72d8d6cad7ac", "logId": "62497c02-43e9-4cf8-83bb-bbcf4122b675"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62497c02-43e9-4cf8-83bb-bbcf4122b675", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014276393800, "endTime": 9014276405800}, "additional": {"logType": "info", "children": [], "durationId": "36689a6f-16be-4108-927e-9684609e5dcb", "parent": "de32c8ca-7ab1-4252-82b7-1121f30c855b"}}, {"head": {"id": "de32c8ca-7ab1-4252-82b7-1121f30c855b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014209947600, "endTime": 9014276415400}, "additional": {"logType": "info", "children": ["b6e90fa5-0d5a-4f3c-8de5-354190776644", "cbf4f6eb-e234-42a6-9962-423a11235209", "62497c02-43e9-4cf8-83bb-bbcf4122b675"], "durationId": "a3cfdb26-4372-4299-ac77-72d8d6cad7ac", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "a7563c6e-59f7-4a08-b6dd-d52da721e62b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014276944600, "endTime": 9014294284100}, "additional": {"children": ["35e3e3b2-ae0d-42e5-9b2f-ecc2de3b7d05", "63019656-d0dc-4d7d-8d37-24b58802484a", "3d9d0148-2da1-46ac-af83-3deb7f8a1c54"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "019e15de-6695-4303-ab50-282529ef225a", "logId": "5a446981-ebef-4327-bf07-297f36995bc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35e3e3b2-ae0d-42e5-9b2f-ecc2de3b7d05", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014279497200, "endTime": 9014279510100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7563c6e-59f7-4a08-b6dd-d52da721e62b", "logId": "c1e49fc0-152b-4258-8e1e-f86e73499642"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1e49fc0-152b-4258-8e1e-f86e73499642", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014279497200, "endTime": 9014279510100}, "additional": {"logType": "info", "children": [], "durationId": "35e3e3b2-ae0d-42e5-9b2f-ecc2de3b7d05", "parent": "5a446981-ebef-4327-bf07-297f36995bc0"}}, {"head": {"id": "63019656-d0dc-4d7d-8d37-24b58802484a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014281106700, "endTime": 9014293208000}, "additional": {"children": ["66ce9b30-fdb5-4ef3-88b2-01b7e9ebbb11", "f1810de4-7ecc-44bf-8c81-29fda1700800"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7563c6e-59f7-4a08-b6dd-d52da721e62b", "logId": "32429f5b-606e-4545-9b0c-b9e54e1debae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66ce9b30-fdb5-4ef3-88b2-01b7e9ebbb11", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014281107900, "endTime": 9014284268400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63019656-d0dc-4d7d-8d37-24b58802484a", "logId": "343822f3-b06c-4bdf-a4f5-6ea95d6ff18d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1810de4-7ecc-44bf-8c81-29fda1700800", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014284279800, "endTime": 9014293198200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63019656-d0dc-4d7d-8d37-24b58802484a", "logId": "f2d9a71c-9148-461f-8400-1da8f083e10d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df932ccc-5e5f-4c99-b9a7-9ae697a2305e", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014281110700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e0e23ce-ef72-4f1f-9667-8f834b3be2ec", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014284172500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "343822f3-b06c-4bdf-a4f5-6ea95d6ff18d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014281107900, "endTime": 9014284268400}, "additional": {"logType": "info", "children": [], "durationId": "66ce9b30-fdb5-4ef3-88b2-01b7e9ebbb11", "parent": "32429f5b-606e-4545-9b0c-b9e54e1debae"}}, {"head": {"id": "2db2a8b3-45d0-40a1-adef-0bc3bfb39724", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014284300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e68ff510-53e2-4e82-a266-7660af199104", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014289639500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f31e4b03-5726-4f19-a8a4-b0f9d254c911", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014289732800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5306dc0-0674-42ea-a785-605bb35fb273", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014290367200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "051d78a4-9ccd-4f62-a365-f0266e896dd7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014290512400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98c0e14-9fec-4e80-a830-e54c6e06b93c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014290569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e030d59-4049-41ac-91b1-868032bb7156", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014290610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ac81e7-95d1-4dc2-aff3-782663b6c24c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014290653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb078dd-df4e-49ae-b0b6-c44acf2bbc57", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014292955900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60187169-2ac2-4cdc-b9d9-8afd3bb9133e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014293069000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886972eb-347e-4638-9ef4-12635f5fafde", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014293119600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd97dffa-4df9-4614-a0f8-3b783cdd8705", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014293156500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d9a71c-9148-461f-8400-1da8f083e10d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014284279800, "endTime": 9014293198200}, "additional": {"logType": "info", "children": [], "durationId": "f1810de4-7ecc-44bf-8c81-29fda1700800", "parent": "32429f5b-606e-4545-9b0c-b9e54e1debae"}}, {"head": {"id": "32429f5b-606e-4545-9b0c-b9e54e1debae", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014281106700, "endTime": 9014293208000}, "additional": {"logType": "info", "children": ["343822f3-b06c-4bdf-a4f5-6ea95d6ff18d", "f2d9a71c-9148-461f-8400-1da8f083e10d"], "durationId": "63019656-d0dc-4d7d-8d37-24b58802484a", "parent": "5a446981-ebef-4327-bf07-297f36995bc0"}}, {"head": {"id": "3d9d0148-2da1-46ac-af83-3deb7f8a1c54", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014294264900, "endTime": 9014294274700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7563c6e-59f7-4a08-b6dd-d52da721e62b", "logId": "6fdb01af-1514-4930-96cc-80ca6b7528a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fdb01af-1514-4930-96cc-80ca6b7528a3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014294264900, "endTime": 9014294274700}, "additional": {"logType": "info", "children": [], "durationId": "3d9d0148-2da1-46ac-af83-3deb7f8a1c54", "parent": "5a446981-ebef-4327-bf07-297f36995bc0"}}, {"head": {"id": "5a446981-ebef-4327-bf07-297f36995bc0", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014276944600, "endTime": 9014294284100}, "additional": {"logType": "info", "children": ["c1e49fc0-152b-4258-8e1e-f86e73499642", "32429f5b-606e-4545-9b0c-b9e54e1debae", "6fdb01af-1514-4930-96cc-80ca6b7528a3"], "durationId": "a7563c6e-59f7-4a08-b6dd-d52da721e62b", "parent": "70f667af-0a63-42f8-a73d-28ac0d954c93"}}, {"head": {"id": "70f667af-0a63-42f8-a73d-28ac0d954c93", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014276426300, "endTime": 9014294294000}, "additional": {"logType": "info", "children": ["5a446981-ebef-4327-bf07-297f36995bc0"], "durationId": "019e15de-6695-4303-ab50-282529ef225a", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "6ceb4377-ecab-450e-a0b3-80b91954c385", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014306433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a5a86ae-2d26-4cb8-a37b-4478911150ed", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014306736100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f41e513-8bcb-4c95-8cf3-6f69c97272a3", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014294299900, "endTime": 9014306816500}, "additional": {"logType": "info", "children": [], "durationId": "6f734176-4c08-4e2c-b53a-f6824117d982", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "e7d50809-397b-4971-94d0-2418d668ef7c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014307523600, "endTime": 9014307677500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "logId": "24735118-6c68-410e-b95c-2fe9d2cbb215"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ced12f3-80e3-4f62-b8a7-d87400c081fa", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014307539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24735118-6c68-410e-b95c-2fe9d2cbb215", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014307523600, "endTime": 9014307677500}, "additional": {"logType": "info", "children": [], "durationId": "e7d50809-397b-4971-94d0-2418d668ef7c", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "80e82fb6-9fcb-47dd-bbcb-92f19a21fb61", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014308890300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6ea86f-ee93-4583-b9df-2825903cf0e5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014314139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d4c5a1-8fe9-4eea-a43c-f86c48bc0c93", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014307687500, "endTime": 9014314809500}, "additional": {"logType": "info", "children": [], "durationId": "1b4b128b-d8b3-4576-80c7-81dc12e6a291", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "974282c4-7cd9-4894-816f-83e5d8d8547c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014314833700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ceabf85-ab99-4e84-8045-5ee6e0d414b6", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014319518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ce9191-b68b-4a74-8ba5-15268b16ad8b", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014319597500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df6f1e8e-e2c5-4c0e-8935-daee419d99e0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014319778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "087e9de8-5ffb-44b1-898f-80ba364af663", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014324139100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cc0be18-a797-432a-af5d-3de169479c35", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014324219800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ec9136-4958-4df0-a92d-940eed658e23", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014314824800, "endTime": 9014326786500}, "additional": {"logType": "info", "children": [], "durationId": "05f21a8d-1dcb-49f3-9ba1-43cde0e0bd3b", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "ae31f2ce-81a2-43eb-b2cd-33f851b874a3", "name": "Configuration phase cost:121 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014326833700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5995b393-a9ca-4e5e-b426-d46e25044427", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014326814400, "endTime": 9014326903400}, "additional": {"logType": "info", "children": [], "durationId": "a7313f8a-a8d5-4d25-bcd0-fce998ecdf9a", "parent": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec"}}, {"head": {"id": "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014201743200, "endTime": 9014326913000}, "additional": {"logType": "info", "children": ["2cd6692c-0a10-4ee4-a90e-e81429d78d1b", "1ebc4f33-9b4d-428c-93e0-12d8abf5c605", "de32c8ca-7ab1-4252-82b7-1121f30c855b", "70f667af-0a63-42f8-a73d-28ac0d954c93", "6f41e513-8bcb-4c95-8cf3-6f69c97272a3", "07d4c5a1-8fe9-4eea-a43c-f86c48bc0c93", "d8ec9136-4958-4df0-a92d-940eed658e23", "5995b393-a9ca-4e5e-b426-d46e25044427", "24735118-6c68-410e-b95c-2fe9d2cbb215"], "durationId": "469c0207-a906-4cb1-a2f3-6981a34e98e4", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "98b2ab42-ffe6-4ad5-b226-5244d86f0f8d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014328011600, "endTime": 9014328022900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f84b417-829a-46e8-987b-fcef5d2b97da", "logId": "42487383-eb6b-41d4-9c44-669219246849"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42487383-eb6b-41d4-9c44-669219246849", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014328011600, "endTime": 9014328022900}, "additional": {"logType": "info", "children": [], "durationId": "98b2ab42-ffe6-4ad5-b226-5244d86f0f8d", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "25f48a09-1402-460a-a22f-fdd3159db3e2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014326943000, "endTime": 9014328030400}, "additional": {"logType": "info", "children": [], "durationId": "36db4e5c-b9d9-462e-ae0b-57caa78efb5f", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "30778472-4c0e-41e1-8d66-feb2cbcad0ef", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014328034700, "endTime": 9014328035700}, "additional": {"logType": "info", "children": [], "durationId": "3e6f9416-fcd6-46ec-b265-d557a219df98", "parent": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4"}}, {"head": {"id": "d3c22ee2-b889-4cc4-8143-04e81f3eb2b4", "name": "init", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014190712000, "endTime": 9014328038300}, "additional": {"logType": "info", "children": ["e395bd2a-ff72-488c-ad06-d7536ce50bcb", "d33e69a3-e0b0-4cb5-a01f-9d6bad0c44ec", "25f48a09-1402-460a-a22f-fdd3159db3e2", "30778472-4c0e-41e1-8d66-feb2cbcad0ef", "4f5809ee-fdd8-437d-897b-f7a102617373", "674b8cb2-4b84-4b22-8257-23ae413c1fbe", "42487383-eb6b-41d4-9c44-669219246849"], "durationId": "8f84b417-829a-46e8-987b-fcef5d2b97da"}}, {"head": {"id": "3e481ff4-7ba3-44b4-9d51-96b5bfa1f449", "name": "Configuration task cost before running: 141 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014328133400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427a3ba9-6c4f-4b87-8198-41eef7290d94", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014332749500, "endTime": 9014340096600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "79275a4f-8708-4423-9e54-195630925cb5", "logId": "bf02f448-3e75-49de-b80d-73db7267fe83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79275a4f-8708-4423-9e54-195630925cb5", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014329402100}, "additional": {"logType": "detail", "children": [], "durationId": "427a3ba9-6c4f-4b87-8198-41eef7290d94"}}, {"head": {"id": "64a8a992-d2d3-4362-9026-0aa15cd52138", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014329856400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da4e307-8dae-40ab-b963-eb5b2d162b50", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014329935100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5702b56d-d9bf-4b32-be68-42dbb43a537e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014329986700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22075171-7e20-4e7a-b019-db13ba44b6bb", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014332761400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f901803-6bc7-4f21-9e7b-8f9297bba0bd", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014339893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d430b8-0e53-4046-bd34-bbb4f7e92527", "name": "entry : default@PreBuild cost memory 0.27161407470703125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014340024800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf02f448-3e75-49de-b80d-73db7267fe83", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014332749500, "endTime": 9014340096600}, "additional": {"logType": "info", "children": [], "durationId": "427a3ba9-6c4f-4b87-8198-41eef7290d94"}}, {"head": {"id": "1d3e5a68-fd9d-4eec-aa5b-326f6adec0c0", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014344337100, "endTime": 9014346899000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "69084954-32b2-4a6f-8609-11cc3c6e71ac", "logId": "910a1ff4-4b30-45f3-a3e9-27ffb2c9c08b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69084954-32b2-4a6f-8609-11cc3c6e71ac", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014343238200}, "additional": {"logType": "detail", "children": [], "durationId": "1d3e5a68-fd9d-4eec-aa5b-326f6adec0c0"}}, {"head": {"id": "0c2f4228-ef13-4263-b132-0ae2a4ebef66", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014343658100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a1b40a-51f9-48e7-a8cc-5356bd10ed19", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014343732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f281c28d-59e4-4fde-bef0-0b2ccf41624b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014343776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b88be59d-46ce-4ae0-a1df-a63826d4d136", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014344345500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489f8d5e-5917-4cba-909b-1125229ee5ad", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014346755100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeeaa4b4-1753-4498-98ff-25844cfe257b", "name": "entry : default@MergeProfile cost memory 0.12755584716796875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014346843200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "910a1ff4-4b30-45f3-a3e9-27ffb2c9c08b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014344337100, "endTime": 9014346899000}, "additional": {"logType": "info", "children": [], "durationId": "1d3e5a68-fd9d-4eec-aa5b-326f6adec0c0"}}, {"head": {"id": "439858b2-c091-4b7f-891d-23f827cd96e6", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014349538300, "endTime": 9014351455200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b7e82071-e1ed-47c5-bf1a-ded642b969d2", "logId": "ec6e75bc-950c-461b-87b6-43bfc86a20de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7e82071-e1ed-47c5-bf1a-ded642b969d2", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014348361500}, "additional": {"logType": "detail", "children": [], "durationId": "439858b2-c091-4b7f-891d-23f827cd96e6"}}, {"head": {"id": "0452d8a2-019c-4354-9cbe-af8c42f978f6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014348771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52485b5-52c6-4dd7-9026-3a3fe5f23d32", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014348844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade70b1a-75a3-464e-a9f4-c169e2f4bdf3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014348887700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d1e8e9-3752-4a5c-8743-f66f03720855", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014349544900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab1a229e-2862-4bb8-b1cd-20bbd525131c", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014350278100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85044b61-e9fe-4ccf-9bb7-55f2056f790e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014351318300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67df51bf-5d9e-434d-a263-72a6e7861faa", "name": "entry : default@CreateBuildProfile cost memory 0.09637451171875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014351401000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec6e75bc-950c-461b-87b6-43bfc86a20de", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014349538300, "endTime": 9014351455200}, "additional": {"logType": "info", "children": [], "durationId": "439858b2-c091-4b7f-891d-23f827cd96e6"}}, {"head": {"id": "87119a07-fa62-41b6-9971-5c15fb01d74e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353794900, "endTime": 9014354151200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "30c4666c-25fc-43a7-b5b9-08944612a7ce", "logId": "c66e7447-975d-40b1-a3e4-fc659e885da2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30c4666c-25fc-43a7-b5b9-08944612a7ce", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014352663400}, "additional": {"logType": "detail", "children": [], "durationId": "87119a07-fa62-41b6-9971-5c15fb01d74e"}}, {"head": {"id": "d35da89c-b579-4ff7-8546-de70903950d1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353082100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfdd27b9-f9cf-4441-8ffe-7b63fc70053a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353148800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b5c4aa-1775-4c43-b581-58a612514617", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353190800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed13f0c0-bb3e-414a-b61e-3b2ec495ebbc", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353800900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "510a5f26-0161-4187-83d1-b3621d3265c6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353895700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfcc8c94-b86a-404a-8e12-db448c1af0f3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6131fc-77ea-47ba-acf3-21f77b0d9cc9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c5c1c6-9cad-41be-8dbf-fb6655f4269c", "name": "entry : default@PreCheckSyscap cost memory 0.05034637451171875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014354041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbbff908-dbd2-439a-aea5-138548c7d97f", "name": "runTaskFromQueue task cost before running: 167 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014354104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66e7447-975d-40b1-a3e4-fc659e885da2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014353794900, "endTime": 9014354151200, "totalTime": 296200}, "additional": {"logType": "info", "children": [], "durationId": "87119a07-fa62-41b6-9971-5c15fb01d74e"}}, {"head": {"id": "4dfb3baa-d40a-467f-aeb9-98ed608a393f", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014363085500, "endTime": 9014363943100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1ea7e817-4945-4bc0-a918-a6c123a93dc1", "logId": "29583e5a-a67c-40f0-8e58-9003bf91880b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ea7e817-4945-4bc0-a918-a6c123a93dc1", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014355372400}, "additional": {"logType": "detail", "children": [], "durationId": "4dfb3baa-d40a-467f-aeb9-98ed608a393f"}}, {"head": {"id": "d4d4152a-f853-470a-89d0-121f92f5b1ff", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014355773300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63681c7e-b4d7-4598-a988-9e14a3b33430", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014355841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f325062-161e-47d4-ba20-edd9a78806a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014355885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2094862d-ed39-4196-a90f-5d4b7353fa32", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014363095900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ffaca0-fb4f-4a2a-8d4d-0d4c15f277b9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014363270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00fcb74-fb18-4e19-9d0e-b00723221cd8", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014363799800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12774bcb-ff77-40bd-a85d-5fbaae815b80", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0686492919921875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014363887000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29583e5a-a67c-40f0-8e58-9003bf91880b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014363085500, "endTime": 9014363943100}, "additional": {"logType": "info", "children": [], "durationId": "4dfb3baa-d40a-467f-aeb9-98ed608a393f"}}, {"head": {"id": "76a79fe1-fb59-49a6-aec2-5b03ccf4aacf", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014366981100, "endTime": 9014368007500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7a03974c-aecc-4181-8fc0-0b1b160843fe", "logId": "bd4914be-9ace-40f8-bfda-908a6384714b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a03974c-aecc-4181-8fc0-0b1b160843fe", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014365349100}, "additional": {"logType": "detail", "children": [], "durationId": "76a79fe1-fb59-49a6-aec2-5b03ccf4aacf"}}, {"head": {"id": "c5758feb-3190-4f01-9d83-503880ca6f6d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014365807700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696e5eff-3a0e-4f5d-bb32-c37b717f591d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014365898500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17995d65-1355-4ab2-9f4a-c72ea81069ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014365947500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1a534de-0676-4ed9-9712-f17072e62408", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014366989600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9810f4ca-1c7f-467b-8649-5c0fd6a9b683", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014367863000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa85c207-6e25-4fbe-b0b6-980080534257", "name": "entry : default@ProcessProfile cost memory 0.0561676025390625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014367945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd4914be-9ace-40f8-bfda-908a6384714b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014366981100, "endTime": 9014368007500}, "additional": {"logType": "info", "children": [], "durationId": "76a79fe1-fb59-49a6-aec2-5b03ccf4aacf"}}, {"head": {"id": "b5aa99bf-f04f-4d5a-9ac5-450a260f6d89", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014371272100, "endTime": 9014376559500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6d647721-7d13-4ed8-a14f-c730ad89d649", "logId": "df8b8629-fc38-46c4-a2cc-3aa4a8555815"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d647721-7d13-4ed8-a14f-c730ad89d649", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014369286200}, "additional": {"logType": "detail", "children": [], "durationId": "b5aa99bf-f04f-4d5a-9ac5-450a260f6d89"}}, {"head": {"id": "98529761-28bf-4b03-8d27-9238af5fffb4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014369703000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89295268-16d7-4e43-b122-d69f09722377", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014369774400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30c00435-31be-4dcf-b72c-3f1d756307b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014369817900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3f067f-0437-4bf9-bc07-15198b06fd0c", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014371279600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebad46a1-5163-4fdd-bc5a-6eac946e2198", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014376410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16f592e8-e1ad-40ec-91da-2e829577fabb", "name": "entry : default@ProcessRouterMap cost memory 0.20660400390625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014376500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8b8629-fc38-46c4-a2cc-3aa4a8555815", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014371272100, "endTime": 9014376559500}, "additional": {"logType": "info", "children": [], "durationId": "b5aa99bf-f04f-4d5a-9ac5-450a260f6d89"}}, {"head": {"id": "ac33cf32-dcdf-4725-888a-c437be9a76e8", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014382563000, "endTime": 9014385090400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "32c9d33f-f90d-48d6-b085-c7151dac1422", "logId": "7a3b7b47-168e-4cc0-9545-ee73bc2e77f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32c9d33f-f90d-48d6-b085-c7151dac1422", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014379024500}, "additional": {"logType": "detail", "children": [], "durationId": "ac33cf32-dcdf-4725-888a-c437be9a76e8"}}, {"head": {"id": "f1a0a984-2cdf-4e13-9622-e01369f84817", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014379450300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2975de4-a9b5-405e-b281-db68f6e1a4f3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014379531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9f718e-9ddc-42e8-b33c-3d4a8e133f0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014379576200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d939ec62-29b4-42ea-82c4-bcc9f4fb6cbd", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014380350500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2de3ff-23d2-41cb-afb7-20680693acfa", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014383623200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "257a0d8c-14dc-4dba-99d2-859013b7c455", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014383733400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef9e7177-e8f3-4424-9136-bea772b91ed2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014383779100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44b6849-18ac-478c-a864-3541764a9ba5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014383829400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea83402-f505-4f59-82d6-95b1a4ce3f07", "name": "entry : default@PreviewProcessResource cost memory 0.0881195068359375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014383893700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ca72ca-0ac1-4092-bfb5-02b225e89c9d", "name": "runTaskFromQueue task cost before running: 198 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014385017400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a3b7b47-168e-4cc0-9545-ee73bc2e77f0", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014382563000, "endTime": 9014385090400, "totalTime": 1379800}, "additional": {"logType": "info", "children": [], "durationId": "ac33cf32-dcdf-4725-888a-c437be9a76e8"}}, {"head": {"id": "76e35a41-5b88-483a-a231-4d34ffdb29ba", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014391616700, "endTime": 9014409751400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d9b08e3d-dcf3-42de-901f-72db71bdaf7d", "logId": "4a33bc3b-e678-497d-a9b1-4a135dfa01a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9b08e3d-dcf3-42de-901f-72db71bdaf7d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014387479300}, "additional": {"logType": "detail", "children": [], "durationId": "76e35a41-5b88-483a-a231-4d34ffdb29ba"}}, {"head": {"id": "cd7ea138-e526-424a-bec7-3bf75a80cef9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014387898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32782fd1-dbb4-4eee-a3c7-c5001735fde5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014387973900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92357f8-80d5-493d-8ef6-034a5e6961de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014388017700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a544cd-7c53-48a7-a10f-04bb79d1e0b6", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014391627000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a848777f-171c-4ff1-ac90-c1c515af7c0e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014409555400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "452e246e-37f1-4749-9837-0ad411d92ac9", "name": "entry : default@GenerateLoaderJson cost memory 0.8169021606445312", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014409686100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a33bc3b-e678-497d-a9b1-4a135dfa01a8", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014391616700, "endTime": 9014409751400}, "additional": {"logType": "info", "children": [], "durationId": "76e35a41-5b88-483a-a231-4d34ffdb29ba"}}, {"head": {"id": "2a2509c0-f298-48a2-95c6-f5c7eda3562b", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014419752000, "endTime": 9015159600600}, "additional": {"children": ["b9a79015-a23f-448d-bc71-2fdea8031c65", "6858e467-c17d-425f-a272-ba0672614773", "4f2f5a9d-de1e-448d-aca7-d86f917e9d3d", "0a7e1340-b338-4169-aa21-80233e96073e", "f0b59151-9d3b-4fb0-bc23-17d526d35d9d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "1ab49656-1f1d-404f-9e21-497cbaef993e", "logId": "e320153c-8607-40d8-9a18-e15fce07746e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ab49656-1f1d-404f-9e21-497cbaef993e", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014416175900}, "additional": {"logType": "detail", "children": [], "durationId": "2a2509c0-f298-48a2-95c6-f5c7eda3562b"}}, {"head": {"id": "5d42e676-a4f9-431f-928f-0df5306ca0f4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014416617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bb7ec07-3fd3-49b9-89fc-132e33d0c4a5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014416698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "330e8488-4dae-4dc4-91b3-360cee7f6822", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014416742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "469f81ec-25ce-4bc7-858f-c7275ee1dac3", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014417498200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9175f8e4-396e-4382-ae36-163132d94f6b", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014419772700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1691b48a-a617-481f-b9c5-e5a728b27a84", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014454843800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "301f4fa6-862e-420c-8688-e4b4d05560d8", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 35 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014455119300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a79015-a23f-448d-bc71-2fdea8031c65", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014456441100, "endTime": 9014486215900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a2509c0-f298-48a2-95c6-f5c7eda3562b", "logId": "2a110ee3-6ffc-4928-b2cc-8d3dc5022244"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a110ee3-6ffc-4928-b2cc-8d3dc5022244", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014456441100, "endTime": 9014486215900}, "additional": {"logType": "info", "children": [], "durationId": "b9a79015-a23f-448d-bc71-2fdea8031c65", "parent": "e320153c-8607-40d8-9a18-e15fce07746e"}}, {"head": {"id": "5db92279-3921-4247-a845-b9889bf148d2", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014486596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6858e467-c17d-425f-a272-ba0672614773", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014487694600, "endTime": 9014619099000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a2509c0-f298-48a2-95c6-f5c7eda3562b", "logId": "cfeb9dbc-e312-4354-b7ca-fd4afaabcb54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0408f7d1-3ec5-4295-b724-5e415ab0d42e", "name": "current process  memoryUsage: {\n  rss: 158928896,\n  heapTotal: 124026880,\n  heapUsed: 105788336,\n  external: 3084149,\n  arrayBuffers: 78018\n} os memoryUsage :11.899871826171875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014488504500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e8145a-7945-4f76-bdc4-2d13d70b9956", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014616888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfeb9dbc-e312-4354-b7ca-fd4afaabcb54", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014487694600, "endTime": 9014619099000}, "additional": {"logType": "info", "children": [], "durationId": "6858e467-c17d-425f-a272-ba0672614773", "parent": "e320153c-8607-40d8-9a18-e15fce07746e"}}, {"head": {"id": "51371351-971d-44d7-bb85-883a1093da51", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014619205800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f2f5a9d-de1e-448d-aca7-d86f917e9d3d", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014620158200, "endTime": 9014833056200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a2509c0-f298-48a2-95c6-f5c7eda3562b", "logId": "70e7468e-5209-4445-9740-54ffcfe6fae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8191694-b68e-4041-a41e-66617a3f9f6e", "name": "current process  memoryUsage: {\n  rss: 158965760,\n  heapTotal: 124026880,\n  heapUsed: 106070888,\n  external: 3084275,\n  arrayBuffers: 78159\n} os memoryUsage :11.897605895996094", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014620938600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8344ef89-e21b-449e-a9e8-b0360ae4832f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014830725000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70e7468e-5209-4445-9740-54ffcfe6fae3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014620158200, "endTime": 9014833056200}, "additional": {"logType": "info", "children": [], "durationId": "4f2f5a9d-de1e-448d-aca7-d86f917e9d3d", "parent": "e320153c-8607-40d8-9a18-e15fce07746e"}}, {"head": {"id": "f0d5aac6-6465-493c-8d42-40607c1bc381", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014833188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7e1340-b338-4169-aa21-80233e96073e", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014834227900, "endTime": 9014968453900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a2509c0-f298-48a2-95c6-f5c7eda3562b", "logId": "2cc583a9-21b3-4de4-95fa-04645e1ac583"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14f42e32-aa46-4f59-b312-b999824867b1", "name": "current process  memoryUsage: {\n  rss: 158969856,\n  heapTotal: 124026880,\n  heapUsed: 106380592,\n  external: 3092593,\n  arrayBuffers: 86541\n} os memoryUsage :11.89206314086914", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014835132900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65f5abd-87af-486e-babc-510816cf1daa", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014966229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cc583a9-21b3-4de4-95fa-04645e1ac583", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014834227900, "endTime": 9014968453900}, "additional": {"logType": "info", "children": [], "durationId": "0a7e1340-b338-4169-aa21-80233e96073e", "parent": "e320153c-8607-40d8-9a18-e15fce07746e"}}, {"head": {"id": "24d399fa-d6d6-4dc3-9056-1419d9b708f9", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014968677900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b59151-9d3b-4fb0-bc23-17d526d35d9d", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014969528100, "endTime": 9015158192500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a2509c0-f298-48a2-95c6-f5c7eda3562b", "logId": "0e012b2b-130d-40da-991c-3eaae315b6db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef86ccee-db95-4695-9af5-0e83ef4cdae0", "name": "current process  memoryUsage: {\n  rss: 158982144,\n  heapTotal: 124026880,\n  heapUsed: 106686432,\n  external: 3092719,\n  arrayBuffers: 87586\n} os memoryUsage :11.89535140991211", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014970416600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ca488b-8302-49c8-b823-dc17302dacab", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015154652700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e012b2b-130d-40da-991c-3eaae315b6db", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014969528100, "endTime": 9015158192500}, "additional": {"logType": "info", "children": [], "durationId": "f0b59151-9d3b-4fb0-bc23-17d526d35d9d", "parent": "e320153c-8607-40d8-9a18-e15fce07746e"}}, {"head": {"id": "3828617a-170d-456d-b3ed-99927936d230", "name": "entry : default@PreviewCompileResource cost memory -10.238822937011719", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015159185200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e655874-c4ca-4711-8fc6-48de7dfbec01", "name": "runTaskFromQueue task cost before running: 973 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015159464700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e320153c-8607-40d8-9a18-e15fce07746e", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014419752000, "endTime": 9015159600600, "totalTime": 739613900}, "additional": {"logType": "info", "children": ["2a110ee3-6ffc-4928-b2cc-8d3dc5022244", "cfeb9dbc-e312-4354-b7ca-fd4afaabcb54", "70e7468e-5209-4445-9740-54ffcfe6fae3", "2cc583a9-21b3-4de4-95fa-04645e1ac583", "0e012b2b-130d-40da-991c-3eaae315b6db"], "durationId": "2a2509c0-f298-48a2-95c6-f5c7eda3562b"}}, {"head": {"id": "57c99445-7bf1-4a71-8db7-453da85ee6f8", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164160400, "endTime": 9015164676900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e6ada5f1-83ea-497d-b98f-4ab7523bb2d1", "logId": "1c95f60e-e0a1-429b-93a2-83b4f7c28b26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6ada5f1-83ea-497d-b98f-4ab7523bb2d1", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015163285800}, "additional": {"logType": "detail", "children": [], "durationId": "57c99445-7bf1-4a71-8db7-453da85ee6f8"}}, {"head": {"id": "24279a1d-c119-4c0d-b174-1cdc6bc763a2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015163881200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d599df89-d52c-48ed-abf9-1386f7087854", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015163998100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bad6133-f10d-4414-a0bf-436cd6a278c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164058800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc4b2f1-125f-410c-b692-03409f7eb2dd", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4458b3e8-2e17-4743-bcfd-0720c1f98442", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164283100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e9b87f8-10d5-43ed-bb78-56b83cf520e0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164342400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9489a88c-490e-4944-8c09-b4ed414def25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164414500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1b62cc-346c-4c0b-8fca-22f2ba89e84d", "name": "entry : default@PreviewHookCompileResource cost memory 0.05146026611328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164510600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f58748c-709e-4333-9452-483facfd3599", "name": "runTaskFromQueue task cost before running: 978 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164612400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c95f60e-e0a1-429b-93a2-83b4f7c28b26", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015164160400, "endTime": 9015164676900, "totalTime": 427500}, "additional": {"logType": "info", "children": [], "durationId": "57c99445-7bf1-4a71-8db7-453da85ee6f8"}}, {"head": {"id": "d3cafba2-8f7c-487c-8b5f-572088c46cf8", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015168054000, "endTime": 9015175315100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "afd65d26-cda7-4600-ad3a-b26dfb7d2386", "logId": "23ba5142-0f2f-4bab-9297-e2a5e7b7352a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afd65d26-cda7-4600-ad3a-b26dfb7d2386", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015166624900}, "additional": {"logType": "detail", "children": [], "durationId": "d3cafba2-8f7c-487c-8b5f-572088c46cf8"}}, {"head": {"id": "7019dd4b-26d7-44a1-8001-1439a24fe4c3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015167185200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c52d9b-9062-48c2-aaaf-b1d68ad51b08", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015167310000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d541434b-b635-4440-9dc2-925c5427d0de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015167394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33e6258e-dde5-4eb5-9d36-b8435d54efa8", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015168063600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf72cdf-2b20-405a-ac88-563a3cfe2d97", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015169394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acdaccb2-8f86-49ea-af6d-6b3c86b1df33", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015169567800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc19beb-55ad-4a4f-b2d2-3c3b9897d803", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015169698300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a70bf0-caf1-469a-9ed6-27bb78741f3c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015169795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb16e12-e514-40f1-9ca2-d8f165ac7d53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015169876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb71195-53f9-40a3-a1b3-51a676ae57e9", "name": "entry : default@CopyPreviewProfile cost memory 0.22784423828125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015175032500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dadd864-f3f7-46f8-a335-4a49748e1d66", "name": "runTaskFromQueue task cost before running: 989 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015175226400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ba5142-0f2f-4bab-9297-e2a5e7b7352a", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015168054000, "endTime": 9015175315100, "totalTime": 7137000}, "additional": {"logType": "info", "children": [], "durationId": "d3cafba2-8f7c-487c-8b5f-572088c46cf8"}}, {"head": {"id": "e8f6de82-6fc0-4586-923b-ba73bb107fa6", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180075300, "endTime": 9015180502900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "69b6ea27-a229-4817-b6e9-246682f364d8", "logId": "93671f44-97f1-429a-b4b7-9346d985f8c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69b6ea27-a229-4817-b6e9-246682f364d8", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015178497000}, "additional": {"logType": "detail", "children": [], "durationId": "e8f6de82-6fc0-4586-923b-ba73bb107fa6"}}, {"head": {"id": "0933f645-c8b0-4d9c-8642-cc36e3a1d1d4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015179079900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c84dfa98-ba68-41cf-986b-88d91f11f7cd", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015179237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a3436f-171d-4c56-b486-e6130bc2908c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015179314100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363be6a1-b738-415a-ad1d-d50f4aa116dd", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180084300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b09bfb-960b-4b45-8195-a11301822f77", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b46dcf2c-4ffa-4885-a345-5520458fd77b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "658577ab-403a-4a6c-850a-c367fcf64c09", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9026cf8-0e30-43f0-8b4c-ef6a894cd689", "name": "entry : default@ReplacePreviewerPage cost memory 0.0516510009765625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180372600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d9410e3-a4d5-460c-ba3e-985806576336", "name": "runTaskFromQueue task cost before running: 994 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180448700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93671f44-97f1-429a-b4b7-9346d985f8c4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015180075300, "endTime": 9015180502900, "totalTime": 354800}, "additional": {"logType": "info", "children": [], "durationId": "e8f6de82-6fc0-4586-923b-ba73bb107fa6"}}, {"head": {"id": "f864467c-899d-473d-9677-df07f3b0aa79", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015182133600, "endTime": 9015182437600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "362fcc38-b806-4543-8053-888a87f7b15c", "logId": "193c23c3-3eb3-4125-a0d6-88f50246c549"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "362fcc38-b806-4543-8053-888a87f7b15c", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015182068100}, "additional": {"logType": "detail", "children": [], "durationId": "f864467c-899d-473d-9677-df07f3b0aa79"}}, {"head": {"id": "be73249d-3faf-491f-8a4b-c14aa3637942", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015182141200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4144cc1-4b2b-4d93-a695-4f2f94fb2025", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015182283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e223ec14-c6ea-418b-b040-c1751112a779", "name": "runTaskFromQueue task cost before running: 996 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015182372700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "193c23c3-3eb3-4125-a0d6-88f50246c549", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015182133600, "endTime": 9015182437600, "totalTime": 217200}, "additional": {"logType": "info", "children": [], "durationId": "f864467c-899d-473d-9677-df07f3b0aa79"}}, {"head": {"id": "87cf82d2-989c-4db4-b1ed-9289c65ebaf1", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015185638200, "endTime": 9015189058100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "d7165beb-381b-4fd9-8db4-c9122e0929f0", "logId": "b6e890ff-ff41-46c9-b0be-7c620a720cb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7165beb-381b-4fd9-8db4-c9122e0929f0", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015184181900}, "additional": {"logType": "detail", "children": [], "durationId": "87cf82d2-989c-4db4-b1ed-9289c65ebaf1"}}, {"head": {"id": "5d0e5f7b-7c5b-4d0f-ae80-dd4c542e5386", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015184718100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d1c3df-3380-4b07-b6ae-93947c58bb99", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015184825000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89d4b720-f05b-4179-b532-d1b14d744692", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015184879100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9fefbf-f78c-4c62-92b1-f9ce239ca617", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015185646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "580214da-f921-4a21-9a5c-316660670b5c", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015187576400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc82ecbd-a66f-4c51-b70c-356b124f36ae", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015187753500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a985344-90c0-4740-b723-2eabc781bfb0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015187868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f6b0099-15ed-40a7-93b4-084d13e89932", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015187925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6c6bc3f-4591-4727-9cde-31f53b439862", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015187980600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e9763f1-b958-41b6-8615-c1968840665c", "name": "entry : default@PreviewUpdateAssets cost memory 0.14310455322265625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015188873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "642ce905-f704-448c-8b44-abd08f469cc4", "name": "runTaskFromQueue task cost before running: 1 s 2 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015188993600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e890ff-ff41-46c9-b0be-7c620a720cb9", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015185638200, "endTime": 9015189058100, "totalTime": 3331800}, "additional": {"logType": "info", "children": [], "durationId": "87cf82d2-989c-4db4-b1ed-9289c65ebaf1"}}, {"head": {"id": "5252cb99-1d71-4df4-bcd8-333067007a1a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015197327800, "endTime": 9024558754300}, "additional": {"children": ["4e4e7a33-c92e-45f6-a914-65ac2b358ee2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "9b87bf1f-34fd-4f5b-aa57-a581bbbaa77f", "logId": "cff0b8fa-3701-4136-bd85-0d6c3ff496f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b87bf1f-34fd-4f5b-aa57-a581bbbaa77f", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015191198600}, "additional": {"logType": "detail", "children": [], "durationId": "5252cb99-1d71-4df4-bcd8-333067007a1a"}}, {"head": {"id": "faa62eda-6b69-455d-9f74-eea43bf035cd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015191683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc7308e-cc39-4bbc-aa7d-3266143be8fb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015191765900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd779942-7c5f-4d03-990f-20e062c8d4b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015191815000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3576a098-5268-4e4a-a7b2-8499b641a3a6", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015197339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a668c4-9cdc-47ab-85d9-0f1248f7f254", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015228323000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47c7e04-8301-4596-92f4-ba760efdf033", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015228517200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9015241760200, "endTime": 9024556347900}, "additional": {"children": ["dedee7b5-0e90-4ed4-b0ce-d00838d82f8e", "2eb8ee3d-583a-4e2a-bb72-8492b210297c", "f0813001-c991-4d10-a4ba-05fe704e1cd2", "8309ff99-e76b-433d-a49b-8566bcf3dde5", "b049e608-9b7a-4946-9586-e4ebea28ecb4", "c484ec76-8048-4166-bb60-8e337918742f", "957b8b01-e9a1-4b28-860b-755a7e0e48b1", "03a77509-2233-424f-994b-d811fc056900", "d4e15a05-98f4-4fad-8bae-1014b79bb378", "2f873efa-c9a9-4576-84d5-c4617b4cb557", "9f8b9c33-c5a1-4fa1-b139-47296a8aae84", "9df67656-ff91-4aae-babc-7805a17a63cc", "cc674838-5b7d-442b-a6ab-e392b8bfb4a2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5252cb99-1d71-4df4-bcd8-333067007a1a", "logId": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ebbe2c2-f796-4009-a598-326922792df2", "name": "entry : default@PreviewArkTS cost memory 0.36638641357421875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015243783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15e925d-2156-4448-8b24-02a80fe94c74", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9018620991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dedee7b5-0e90-4ed4-b0ce-d00838d82f8e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9018622034900, "endTime": 9018622056200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "4bd14720-3478-47e8-a0b9-5fc9ea0f51a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bd14720-3478-47e8-a0b9-5fc9ea0f51a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9018622034900, "endTime": 9018622056200}, "additional": {"logType": "info", "children": [], "durationId": "dedee7b5-0e90-4ed4-b0ce-d00838d82f8e", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "8c7cafaf-4510-45c3-994a-4ac50c79a2d3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024549531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb8ee3d-583a-4e2a-bb72-8492b210297c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024550580800, "endTime": 9024550597900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "6363f232-c979-4399-afd6-c5f8ab2550cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6363f232-c979-4399-afd6-c5f8ab2550cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024550580800, "endTime": 9024550597900}, "additional": {"logType": "info", "children": [], "durationId": "2eb8ee3d-583a-4e2a-bb72-8492b210297c", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "e3836de7-82d1-450c-ba1b-3c6a2207ba7b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024550663200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0813001-c991-4d10-a4ba-05fe704e1cd2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024551377900, "endTime": 9024551390500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "a2b0993d-86fe-4856-b70c-9409f41a7992"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2b0993d-86fe-4856-b70c-9409f41a7992", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024551377900, "endTime": 9024551390500}, "additional": {"logType": "info", "children": [], "durationId": "f0813001-c991-4d10-a4ba-05fe704e1cd2", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "b1bef965-dcfd-43eb-94b5-8609c630e898", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024551441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8309ff99-e76b-433d-a49b-8566bcf3dde5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024552162800, "endTime": 9024552178200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "2f14c147-55a5-4608-95e5-6c31d36015ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f14c147-55a5-4608-95e5-6c31d36015ab", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024552162800, "endTime": 9024552178200}, "additional": {"logType": "info", "children": [], "durationId": "8309ff99-e76b-433d-a49b-8566bcf3dde5", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "8d2fd50e-ad1a-4412-9385-7ab569e75b99", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024552229200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b049e608-9b7a-4946-9586-e4ebea28ecb4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024553002200, "endTime": 9024553024100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "3dd7a688-f942-4c2f-a35f-9e546b5c9bd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3dd7a688-f942-4c2f-a35f-9e546b5c9bd1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024553002200, "endTime": 9024553024100}, "additional": {"logType": "info", "children": [], "durationId": "b049e608-9b7a-4946-9586-e4ebea28ecb4", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "b4c35bc2-f62b-4b41-bbe9-819ad314e5dc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024553099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c484ec76-8048-4166-bb60-8e337918742f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024553990100, "endTime": 9024554004000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "0b480a8b-f4f4-42af-972b-3243fac94f44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b480a8b-f4f4-42af-972b-3243fac94f44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024553990100, "endTime": 9024554004000}, "additional": {"logType": "info", "children": [], "durationId": "c484ec76-8048-4166-bb60-8e337918742f", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "8f99c3dc-32c5-4651-8925-30bd4db8026e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024554055800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "957b8b01-e9a1-4b28-860b-755a7e0e48b1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024554738400, "endTime": 9024554751600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "72311345-f867-46b2-a16e-c4142b027db1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72311345-f867-46b2-a16e-c4142b027db1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024554738400, "endTime": 9024554751600}, "additional": {"logType": "info", "children": [], "durationId": "957b8b01-e9a1-4b28-860b-755a7e0e48b1", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "2e1dc348-8b41-4778-96c0-4d2ecfde86c6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024554799600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03a77509-2233-424f-994b-d811fc056900", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024555478700, "endTime": 9024555493000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "adbf169f-bdb7-40f5-95f4-52aa7e98037d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adbf169f-bdb7-40f5-95f4-52aa7e98037d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024555478700, "endTime": 9024555493000}, "additional": {"logType": "info", "children": [], "durationId": "03a77509-2233-424f-994b-d811fc056900", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "3626e3ec-830f-4410-860b-a5547cc164b0", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024555536400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e15a05-98f4-4fad-8bae-1014b79bb378", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9024556229800, "endTime": 9024556243400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "010be2cc-ba87-4787-9370-7f135532e0a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "010be2cc-ba87-4787-9370-7f135532e0a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024556229800, "endTime": 9024556243400}, "additional": {"logType": "info", "children": [], "durationId": "d4e15a05-98f4-4fad-8bae-1014b79bb378", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "274483ce-68e6-439a-b6b2-9743fdc99bbe", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9015241760200, "endTime": 9024556347900}, "additional": {"logType": "info", "children": ["4bd14720-3478-47e8-a0b9-5fc9ea0f51a2", "6363f232-c979-4399-afd6-c5f8ab2550cc", "a2b0993d-86fe-4856-b70c-9409f41a7992", "2f14c147-55a5-4608-95e5-6c31d36015ab", "3dd7a688-f942-4c2f-a35f-9e546b5c9bd1", "0b480a8b-f4f4-42af-972b-3243fac94f44", "72311345-f867-46b2-a16e-c4142b027db1", "adbf169f-bdb7-40f5-95f4-52aa7e98037d", "010be2cc-ba87-4787-9370-7f135532e0a4", "4d2f5c3d-919b-4ea4-90f3-fcbd0460b70b", "87697c38-035b-4aa3-a21f-658d6f5ed2a6", "c4d537ab-f284-4880-966b-20a7a2d4b6d5", "647a2d4d-6b0c-48c1-b0dd-fdc1d2a04dfb"], "durationId": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "parent": "cff0b8fa-3701-4136-bd85-0d6c3ff496f1"}}, {"head": {"id": "2f873efa-c9a9-4576-84d5-c4617b4cb557", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9017511899400, "endTime": 9018528522900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "4d2f5c3d-919b-4ea4-90f3-fcbd0460b70b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d2f5c3d-919b-4ea4-90f3-fcbd0460b70b", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9017511899400, "endTime": 9018528522900}, "additional": {"logType": "info", "children": [], "durationId": "2f873efa-c9a9-4576-84d5-c4617b4cb557", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "9f8b9c33-c5a1-4fa1-b139-47296a8aae84", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9018528720200, "endTime": 9018593410000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "87697c38-035b-4aa3-a21f-658d6f5ed2a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87697c38-035b-4aa3-a21f-658d6f5ed2a6", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9018528720200, "endTime": 9018593410000}, "additional": {"logType": "info", "children": [], "durationId": "9f8b9c33-c5a1-4fa1-b139-47296a8aae84", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "9df67656-ff91-4aae-babc-7805a17a63cc", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9018593523800, "endTime": 9018593823500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "c4d537ab-f284-4880-966b-20a7a2d4b6d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4d537ab-f284-4880-966b-20a7a2d4b6d5", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9018593523800, "endTime": 9018593823500}, "additional": {"logType": "info", "children": [], "durationId": "9df67656-ff91-4aae-babc-7805a17a63cc", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "cc674838-5b7d-442b-a6ab-e392b8bfb4a2", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker14", "startTime": 9018593901900, "endTime": 9024549689000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e4e7a33-c92e-45f6-a914-65ac2b358ee2", "logId": "647a2d4d-6b0c-48c1-b0dd-fdc1d2a04dfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "647a2d4d-6b0c-48c1-b0dd-fdc1d2a04dfb", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9018593901900, "endTime": 9024549689000}, "additional": {"logType": "info", "children": [], "durationId": "cc674838-5b7d-442b-a6ab-e392b8bfb4a2", "parent": "274483ce-68e6-439a-b6b2-9743fdc99bbe"}}, {"head": {"id": "cff0b8fa-3701-4136-bd85-0d6c3ff496f1", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9015197327800, "endTime": 9024558754300, "totalTime": 9361413900}, "additional": {"logType": "info", "children": ["274483ce-68e6-439a-b6b2-9743fdc99bbe"], "durationId": "5252cb99-1d71-4df4-bcd8-333067007a1a"}}, {"head": {"id": "b4547fc4-5f02-4736-a37f-5d04bed97ade", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024563359500, "endTime": 9024563619700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ce25754d-8211-446f-af7f-1657d2283fee", "logId": "c1b45dde-e6da-4e61-aa06-4b472dbb2576"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce25754d-8211-446f-af7f-1657d2283fee", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024563306700}, "additional": {"logType": "detail", "children": [], "durationId": "b4547fc4-5f02-4736-a37f-5d04bed97ade"}}, {"head": {"id": "70ac1fd2-7d4a-454a-818d-0399e04aaff9", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024563369000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c5b3eea-e6fd-4b32-86e1-bbe688a9677b", "name": "entry : PreviewBuild cost memory 0.0116729736328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024563477200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "187b416f-ffab-426d-8ef5-fd5ae53debd8", "name": "runTaskFromQueue task cost before running: 10 s 377 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024563562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b45dde-e6da-4e61-aa06-4b472dbb2576", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024563359500, "endTime": 9024563619700, "totalTime": 184600}, "additional": {"logType": "info", "children": [], "durationId": "b4547fc4-5f02-4736-a37f-5d04bed97ade"}}, {"head": {"id": "a3ce1309-e4ce-4355-abe9-7969abd764e5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574324000, "endTime": 9024574343500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "01d611a5-ddb7-462d-ae13-a81bc6afb844", "logId": "eb25289a-aa69-4590-9539-2f61fc3ff2a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb25289a-aa69-4590-9539-2f61fc3ff2a1", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574324000, "endTime": 9024574343500}, "additional": {"logType": "info", "children": [], "durationId": "a3ce1309-e4ce-4355-abe9-7969abd764e5"}}, {"head": {"id": "88218b7e-5d85-4c68-9dcc-e0a23b7ebca7", "name": "BUILD SUCCESSFUL in 10 s 388 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574380100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "204c663f-d8ff-49af-889a-8fc21ddf8fcd", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9014187132100, "endTime": 9024574629800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 9}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "6140fa18-823d-430e-a512-dc8330d9f192", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574655900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9625e030-0df1-40ca-ad8f-0892ed47a126", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574729600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66cd7562-9f81-4975-8967-e3ca9532d46d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed133802-2e55-445a-8501-2011b1514449", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574821500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "245ab323-5867-4171-a254-53ef7cffcd0b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574862000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c8192f-fa00-48bc-b337-44bb5f7fa399", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574902400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea500d97-87e1-433d-9615-155bc0e66cde", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024574946500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f46d0b-6db9-459d-8176-fcafc5052301", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024575557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a3a07a3-bff0-4760-967d-6657c32a6218", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024589651000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f27a6de-c3ca-4f6d-89a5-6e7110c0ad6a", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024592299800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a833c4f-32b5-4204-a526-f176ea272ea8", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024592636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04547f80-f25c-41f1-8839-67e5a58ff6d7", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024610231500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "504cac73-1a11-474d-a985-ca595bae81ae", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:36 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024610935200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3af471-10fd-4a68-8e47-afffd1cf9724", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024611185800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7969b70-74e9-4f45-ad6c-d2f4ebc3b5c8", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024611996800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0333f2c0-8b44-4d3d-9c78-183004129121", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024612801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62a2c78-95be-4ee1-993f-ef2c19139af8", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024613173700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77091cc7-7e5e-40f4-873d-bbcdb17c1793", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024613453600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04523504-6969-49bc-9e69-71dea8742ad2", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024613768800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7418f721-a851-4e6a-90c3-2b7a7831ccd9", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024616654000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84998c35-aeed-427f-aba5-44be19dcacae", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024617376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf9ae46-1ced-4f9e-bd87-13ea92699be3", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024617659900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20fac1e-af84-4a3f-992a-dcc44d777f98", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024632669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad9744bd-1815-4ae4-94dc-37f2455353f0", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024633572500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7ad960-1ab2-47d7-9321-07dde26a8832", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024633646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6876f920-466b-4179-a62a-20e24d247cf5", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024633893800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a26c099-e701-4c97-8be4-c438ab056652", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024634570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69da29e3-b705-45f2-8f89-41518c6e5dd5", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024637686800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7804457a-41ec-45e5-aa1a-f80dde9f11b2", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024637939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5278cc5a-560e-4ba1-8af9-ebcc75f2f46b", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024638196800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408c3c84-6a3b-40bb-908e-942760154263", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024638472300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9de1c5e3-96a0-4b53-b0f6-fea2919e325a", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:25 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 9024638770400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}