if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WalletPage_Params {
    balance?: number;
    userId?: number;
    userPhone?: string;
    userRealName?: string;
    isLoading?: boolean;
    recentTransactions?: Transaction[];
    showRecharge?: boolean;
    showTransfer?: boolean;
    showWithdraw?: boolean;
    rechargeAmount?: string;
    transferAmount?: string;
    transferTarget?: string;
    withdrawAmount?: string;
    selectedBankCard?: string;
    remark?: string;
    bankCards?: BankCard[];
    defaultBankCard?: BankCard | null;
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse, AxiosError } from "@normalized:N&&&@ohos/axios/index&2.2.6";
import { UserStorage } from "@normalized:N&&&entry/src/main/ets/common/UserStorage&";
/**
 * API响应结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
/**
 * 分页响应结构
 */
interface PageResponse<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
}
/**
 * 钱包信息
 */
interface Wallet {
    id: number;
    userId: number;
    balance: number;
    status: number; // 0-冻结, 1-正常
    createTime: string;
    updateTime: string;
}
/**
 * 交易记录
 */
interface Transaction {
    id: number;
    userId: number;
    type: number; // 1-充值, 2-提现, 3-转账, 4-消费
    amount: number;
    balance: number;
    description: string;
    createTime: string;
}
/**
 * 银行卡信息
 */
interface BankCard {
    cardId: number;
    userId: number;
    cardNumber: string;
    bankName: string;
    cardType: number;
    cardHolder: string;
    isDefault: number;
    status: number;
}
export class WalletPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__balance = new ObservedPropertySimplePU(0, this, "balance");
        this.__userId = new ObservedPropertySimplePU(0, this, "userId");
        this.__userPhone = new ObservedPropertySimplePU('', this, "userPhone");
        this.__userRealName = new ObservedPropertySimplePU('', this, "userRealName");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__recentTransactions = new ObservedPropertyObjectPU([], this, "recentTransactions");
        this.__showRecharge = new ObservedPropertySimplePU(false, this, "showRecharge");
        this.__showTransfer = new ObservedPropertySimplePU(false, this, "showTransfer");
        this.__showWithdraw = new ObservedPropertySimplePU(false, this, "showWithdraw");
        this.__rechargeAmount = new ObservedPropertySimplePU('', this, "rechargeAmount");
        this.__transferAmount = new ObservedPropertySimplePU('', this, "transferAmount");
        this.__transferTarget = new ObservedPropertySimplePU('', this, "transferTarget");
        this.__withdrawAmount = new ObservedPropertySimplePU('', this, "withdrawAmount");
        this.__selectedBankCard = new ObservedPropertySimplePU('', this, "selectedBankCard");
        this.__remark = new ObservedPropertySimplePU('', this, "remark");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__defaultBankCard = new ObservedPropertyObjectPU(null, this, "defaultBankCard");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WalletPage_Params) {
        if (params.balance !== undefined) {
            this.balance = params.balance;
        }
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
        if (params.userPhone !== undefined) {
            this.userPhone = params.userPhone;
        }
        if (params.userRealName !== undefined) {
            this.userRealName = params.userRealName;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.recentTransactions !== undefined) {
            this.recentTransactions = params.recentTransactions;
        }
        if (params.showRecharge !== undefined) {
            this.showRecharge = params.showRecharge;
        }
        if (params.showTransfer !== undefined) {
            this.showTransfer = params.showTransfer;
        }
        if (params.showWithdraw !== undefined) {
            this.showWithdraw = params.showWithdraw;
        }
        if (params.rechargeAmount !== undefined) {
            this.rechargeAmount = params.rechargeAmount;
        }
        if (params.transferAmount !== undefined) {
            this.transferAmount = params.transferAmount;
        }
        if (params.transferTarget !== undefined) {
            this.transferTarget = params.transferTarget;
        }
        if (params.withdrawAmount !== undefined) {
            this.withdrawAmount = params.withdrawAmount;
        }
        if (params.selectedBankCard !== undefined) {
            this.selectedBankCard = params.selectedBankCard;
        }
        if (params.remark !== undefined) {
            this.remark = params.remark;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.defaultBankCard !== undefined) {
            this.defaultBankCard = params.defaultBankCard;
        }
    }
    updateStateVars(params: WalletPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__balance.purgeDependencyOnElmtId(rmElmtId);
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
        this.__userPhone.purgeDependencyOnElmtId(rmElmtId);
        this.__userRealName.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__recentTransactions.purgeDependencyOnElmtId(rmElmtId);
        this.__showRecharge.purgeDependencyOnElmtId(rmElmtId);
        this.__showTransfer.purgeDependencyOnElmtId(rmElmtId);
        this.__showWithdraw.purgeDependencyOnElmtId(rmElmtId);
        this.__rechargeAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__transferAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__transferTarget.purgeDependencyOnElmtId(rmElmtId);
        this.__withdrawAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedBankCard.purgeDependencyOnElmtId(rmElmtId);
        this.__remark.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__defaultBankCard.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__balance.aboutToBeDeleted();
        this.__userId.aboutToBeDeleted();
        this.__userPhone.aboutToBeDeleted();
        this.__userRealName.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__recentTransactions.aboutToBeDeleted();
        this.__showRecharge.aboutToBeDeleted();
        this.__showTransfer.aboutToBeDeleted();
        this.__showWithdraw.aboutToBeDeleted();
        this.__rechargeAmount.aboutToBeDeleted();
        this.__transferAmount.aboutToBeDeleted();
        this.__transferTarget.aboutToBeDeleted();
        this.__withdrawAmount.aboutToBeDeleted();
        this.__selectedBankCard.aboutToBeDeleted();
        this.__remark.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__defaultBankCard.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __balance: ObservedPropertySimplePU<number>;
    get balance() {
        return this.__balance.get();
    }
    set balance(newValue: number) {
        this.__balance.set(newValue);
    }
    private __userId: ObservedPropertySimplePU<number>; // 初始化为0，将从存储中获取
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    private __userPhone: ObservedPropertySimplePU<string>;
    get userPhone() {
        return this.__userPhone.get();
    }
    set userPhone(newValue: string) {
        this.__userPhone.set(newValue);
    }
    private __userRealName: ObservedPropertySimplePU<string>;
    get userRealName() {
        return this.__userRealName.get();
    }
    set userRealName(newValue: string) {
        this.__userRealName.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __recentTransactions: ObservedPropertyObjectPU<Transaction[]>;
    get recentTransactions() {
        return this.__recentTransactions.get();
    }
    set recentTransactions(newValue: Transaction[]) {
        this.__recentTransactions.set(newValue);
    }
    // 弹窗状态
    private __showRecharge: ObservedPropertySimplePU<boolean>;
    get showRecharge() {
        return this.__showRecharge.get();
    }
    set showRecharge(newValue: boolean) {
        this.__showRecharge.set(newValue);
    }
    private __showTransfer: ObservedPropertySimplePU<boolean>;
    get showTransfer() {
        return this.__showTransfer.get();
    }
    set showTransfer(newValue: boolean) {
        this.__showTransfer.set(newValue);
    }
    private __showWithdraw: ObservedPropertySimplePU<boolean>;
    get showWithdraw() {
        return this.__showWithdraw.get();
    }
    set showWithdraw(newValue: boolean) {
        this.__showWithdraw.set(newValue);
    }
    // 表单数据
    private __rechargeAmount: ObservedPropertySimplePU<string>;
    get rechargeAmount() {
        return this.__rechargeAmount.get();
    }
    set rechargeAmount(newValue: string) {
        this.__rechargeAmount.set(newValue);
    }
    private __transferAmount: ObservedPropertySimplePU<string>;
    get transferAmount() {
        return this.__transferAmount.get();
    }
    set transferAmount(newValue: string) {
        this.__transferAmount.set(newValue);
    }
    private __transferTarget: ObservedPropertySimplePU<string>;
    get transferTarget() {
        return this.__transferTarget.get();
    }
    set transferTarget(newValue: string) {
        this.__transferTarget.set(newValue);
    }
    private __withdrawAmount: ObservedPropertySimplePU<string>;
    get withdrawAmount() {
        return this.__withdrawAmount.get();
    }
    set withdrawAmount(newValue: string) {
        this.__withdrawAmount.set(newValue);
    }
    private __selectedBankCard: ObservedPropertySimplePU<string>;
    get selectedBankCard() {
        return this.__selectedBankCard.get();
    }
    set selectedBankCard(newValue: string) {
        this.__selectedBankCard.set(newValue);
    }
    private __remark: ObservedPropertySimplePU<string>;
    get remark() {
        return this.__remark.get();
    }
    set remark(newValue: string) {
        this.__remark.set(newValue);
    }
    // 银行卡列表
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __defaultBankCard: ObservedPropertyObjectPU<BankCard | null>;
    get defaultBankCard() {
        return this.__defaultBankCard.get();
    }
    set defaultBankCard(newValue: BankCard | null) {
        this.__defaultBankCard.set(newValue);
    }
    async aboutToAppear() {
        await this.loadUserInfo();
        if (this.userId > 0) {
            this.loadWalletBalance();
            this.loadBankCards();
            this.loadRecentTransactions();
        }
        else {
            // 如果没有用户信息，跳转到登录页
            router.replaceUrl({ url: 'pages/LoginPage' });
        }
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            const userInfo = await UserStorage.getCurrentUserInfo();
            if (userInfo) {
                this.userId = userInfo.userId;
                this.userPhone = userInfo.phone;
                this.userRealName = userInfo.realName || '未知用户';
                console.log('加载用户信息成功:', userInfo);
            }
            else {
                console.log('未找到用户信息，需要重新登录');
                this.userId = 0;
                this.userPhone = '';
                this.userRealName = '未知用户';
            }
        }
        catch (error) {
            console.error('加载用户信息失败:', error);
            this.userId = 0;
            this.userPhone = '';
            this.userRealName = '未知用户';
        }
    }
    /**
     * 退出登录
     */
    async logout() {
        try {
            await UserStorage.clearUserInfo();
            console.log('退出登录成功');
            promptAction.showToast({
                message: '已退出登录',
                duration: 2000
            });
            // 跳转到登录页
            router.replaceUrl({ url: 'pages/LoginPage' });
        }
        catch (error) {
            console.error('退出登录失败:', error);
            promptAction.showToast({
                message: '退出登录失败',
                duration: 2000
            });
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(154:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f7fa');
        }, Column);
        // 余额卡片
        this.buildBalanceCard.bind(this)();
        // 银行卡入口
        this.buildBankCardEntry.bind(this)();
        // 操作按钮
        this.buildActionButtons.bind(this)();
        // 最近交易
        this.buildRecentTransactions.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 弹窗
            if (this.showRecharge) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildRechargeDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showTransfer) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildTransferDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showWithdraw) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildWithdrawDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildBalanceCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(187:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#ffffff');
            Column.borderRadius(16);
            Column.padding(24);
            Column.margin({ left: 16, right: 16, top: 20 });
            Column.alignItems(HorizontalAlign.Start);
            Column.shadow({
                radius: 8,
                color: '#1a000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息行
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(189:7)", "entry");
            // 用户信息行
            Row.width('100%');
            // 用户信息行
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(190:9)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userRealName || '未知用户');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(191:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userPhone || '未绑定手机');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(197:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置和退出按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(205:9)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(206:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            Text.borderRadius(4);
            Text.border({ width: 1, color: '#666666' });
            Text.margin({ right: 12 });
            Text.onClick(() => {
                router.pushUrl({ url: 'pages/SettingsPage' });
            });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('退出');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(217:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#f44336');
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            Text.borderRadius(4);
            Text.border({ width: 1, color: '#f44336' });
            Text.onClick(() => {
                this.logout();
            });
        }, Text);
        Text.pop();
        // 设置和退出按钮
        Row.pop();
        // 用户信息行
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('账户余额');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(231:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${this.balance.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(236:7)", "entry");
            Text.fontSize(32);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildBankCardEntry(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(257:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.backgroundColor('#ffffff');
            Row.borderRadius(12);
            Row.padding({ left: 16, right: 16 });
            Row.margin({ left: 16, right: 16, top: 16 });
            Row.onClick(() => {
                router.pushUrl({ url: 'pages/CardsPage' });
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(258:7)", "entry");
            Stack.margin({ right: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/WalletPage.ets(259:9)", "entry");
            Circle.width(32);
            Circle.height(32);
            Circle.fill('#4285f4');
            Circle.opacity(0.1);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777237, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletPage.ets(265:9)", "entry");
            Image.width(16);
            Image.height(16);
        }, Image);
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(271:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(276:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#cccccc');
        }, Text);
        Text.pop();
        Row.pop();
    }
    buildActionButtons(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(293:5)", "entry");
            Row.width('100%');
            Row.backgroundColor('#ffffff');
            Row.borderRadius(12);
            Row.padding(24);
            Row.margin({ left: 16, right: 16, top: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(295:7)", "entry");
            // 充值按钮
            Column.layoutWeight(1);
            // 充值按钮
            Column.onClick(() => {
                this.clearRechargeForm();
                this.showRecharge = true;
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(296:9)", "entry");
            Stack.margin({ bottom: 8 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/WalletPage.ets(297:11)", "entry");
            Circle.width(48);
            Circle.height(48);
            Circle.fill('#e3f2fd');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777248, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletPage.ets(302:11)", "entry");
            Image.width(24);
            Image.height(24);
        }, Image);
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(308:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 充值按钮
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(319:7)", "entry");
            // 转账按钮
            Column.layoutWeight(1);
            // 转账按钮
            Column.onClick(() => {
                this.clearTransferForm();
                this.showTransfer = true;
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(320:9)", "entry");
            Stack.margin({ bottom: 8 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/WalletPage.ets(321:11)", "entry");
            Circle.width(48);
            Circle.height(48);
            Circle.fill('#fff3e0');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletPage.ets(326:11)", "entry");
            Image.width(24);
            Image.height(24);
        }, Image);
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(332:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 转账按钮
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现按钮
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(343:7)", "entry");
            // 提现按钮
            Column.layoutWeight(1);
            // 提现按钮
            Column.onClick(() => {
                this.clearWithdrawForm();
                this.showWithdraw = true;
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(344:9)", "entry");
            Stack.margin({ bottom: 8 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/WalletPage.ets(345:11)", "entry");
            Circle.width(48);
            Circle.height(48);
            Circle.fill('#e8f5e8');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777251, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/WalletPage.ets(350:11)", "entry");
            Image.width(24);
            Image.height(24);
        }, Image);
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(356:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 提现按钮
        Column.pop();
        Row.pop();
    }
    buildRecentTransactions(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(375:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.padding(20);
            Column.margin({ left: 16, right: 16, top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(377:7)", "entry");
            // 标题栏
            Row.width('100%');
            // 标题栏
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近交易');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(378:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/WalletPage.ets(383:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看全部');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(385:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#4285f4');
            Text.onClick(() => {
                router.pushUrl({ url: 'pages/TransactionPage' });
            });
        }, Text);
        Text.pop();
        // 标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易列表
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(397:9)", "entry");
                        Column.width('100%');
                        Column.height(120);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/WalletPage.ets(398:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#4285f4');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(403:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 12 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else if (this.recentTransactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(412:9)", "entry");
                        Column.width('100%');
                        Column.height(120);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📝');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(413:11)", "entry");
                        Text.fontSize(40);
                        Text.opacity(0.5);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(417:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 12 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(426:9)", "entry");
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const transaction = _item;
                            this.buildTransactionItem.bind(this)(transaction);
                        };
                        this.forEachUpdateFunction(elmtId, this.recentTransactions.slice(0, 3), forEachItemGenFunction, (transaction: Transaction) => transaction.id?.toString() || Math.random().toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildTransactionItem(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(443:5)", "entry");
            Row.width('100%');
            Row.padding({ top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易图标
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(445:7)", "entry");
            // 交易图标
            Stack.margin({ right: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/WalletPage.ets(446:9)", "entry");
            Circle.width(40);
            Circle.height(40);
            Circle.fill(this.getTransactionColor(transaction.type));
            Circle.opacity(0.1);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.getTransactionIcon(transaction.type));
            Image.debugLine("entry/src/main/ets/pages/WalletPage.ets(452:9)", "entry");
            Image.width(20);
            Image.height(20);
        }, Image);
        // 交易图标
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(459:7)", "entry");
            // 交易信息
            Column.layoutWeight(1);
            // 交易信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionTitle(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(460:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatDateTime(transaction.createTime));
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(467:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 交易信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 金额
            Text.create(this.formatAmount(transaction.type, transaction.amount));
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(476:7)", "entry");
            // 金额
            Text.fontSize(16);
            // 金额
            Text.fontWeight(FontWeight.Bold);
            // 金额
            Text.fontColor(this.getAmountColor(transaction.type));
        }, Text);
        // 金额
        Text.pop();
        Row.pop();
    }
    // 加载钱包余额
    loadWalletBalance() {
        this.isLoading = true;
        axios({
            url: `http://localhost:8091/wallet/balance/${this.userId}`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<Wallet>>) => {
            console.log('获取钱包余额结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                this.balance = res.data.data.balance;
            }
            else {
                promptAction.showToast({
                    message: res.data.msg || '获取余额失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('获取钱包余额错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 加载银行卡列表
    loadBankCards() {
        axios({
            url: `http://localhost:8091/bankCards/bound/user/${this.userId}`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<BankCard[]>>) => {
            console.log('获取银行卡列表结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                this.bankCards = res.data.data;
                // 找到默认银行卡
                this.defaultBankCard = this.bankCards.find(card => card.isDefault === 1) || null;
                if (this.defaultBankCard) {
                    this.selectedBankCard = this.defaultBankCard.cardNumber;
                }
            }
        }).catch((err: AxiosError) => {
            console.error('获取银行卡列表错误:', err.message);
        });
    }
    // 加载最近交易记录
    loadRecentTransactions() {
        axios({
            url: `http://localhost:8091/transactions/user/${this.userId}`,
            method: 'get',
            params: {
                pageNum: 1,
                pageSize: 3
            }
        }).then((res: AxiosResponse<ApiResponse<PageResponse<Transaction>>>) => {
            console.log('获取最近交易记录结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                this.recentTransactions = res.data.data.records || [];
            }
        }).catch((err: AxiosError) => {
            console.error('获取最近交易记录错误:', err.message);
        });
    }
    // 充值
    recharge() {
        // 首先检查是否有银行卡
        if (this.bankCards.length === 0) {
            promptAction.showDialog({
                title: '提示',
                message: '您还没有绑定银行卡，请先添加银行卡后再进行充值操作。',
                buttons: [
                    {
                        text: '取消',
                        color: '#666666'
                    },
                    {
                        text: '去添加',
                        color: '#4285f4'
                    }
                ]
            }).then((result) => {
                if (result.index === 1) {
                    this.showRecharge = false;
                    router.pushUrl({ url: 'pages/BankCardPage' });
                }
            });
            return;
        }
        if (!this.rechargeAmount || parseFloat(this.rechargeAmount) <= 0) {
            promptAction.showToast({
                message: '请输入有效的充值金额',
                duration: 2000
            });
            return;
        }
        const amount = parseFloat(this.rechargeAmount);
        if (amount > 100000) {
            promptAction.showToast({
                message: '单次充值金额不能超过10万元',
                duration: 2000
            });
            return;
        }
        if (!this.selectedBankCard) {
            promptAction.showToast({
                message: '请选择银行卡',
                duration: 2000
            });
            return;
        }
        axios({
            url: 'http://localhost:8091/wallet/recharge',
            method: 'post',
            params: {
                userId: this.userId,
                amount: amount,
                bankCardNumber: this.selectedBankCard,
                remark: this.remark || '钱包充值'
            }
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
            console.log('充值结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                this.showRecharge = false;
                this.clearRechargeForm();
                this.loadWalletBalance();
                this.loadRecentTransactions();
            }
        }).catch((err: AxiosError) => {
            console.error('充值错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        });
    }
    // 提现
    withdraw() {
        // 首先检查是否有银行卡
        if (this.bankCards.length === 0) {
            promptAction.showDialog({
                title: '提示',
                message: '您还没有绑定银行卡，请先添加银行卡后再进行提现操作。',
                buttons: [
                    {
                        text: '取消',
                        color: '#666666'
                    },
                    {
                        text: '去添加',
                        color: '#4caf50'
                    }
                ]
            }).then((result) => {
                if (result.index === 1) {
                    this.showWithdraw = false;
                    router.pushUrl({ url: 'pages/BankCardPage' });
                }
            });
            return;
        }
        if (!this.withdrawAmount || parseFloat(this.withdrawAmount) <= 0) {
            promptAction.showToast({
                message: '请输入有效的提现金额',
                duration: 2000
            });
            return;
        }
        const amount = parseFloat(this.withdrawAmount);
        if (amount > 50000) {
            promptAction.showToast({
                message: '单次提现金额不能超过5万元',
                duration: 2000
            });
            return;
        }
        if (amount > this.balance) {
            promptAction.showToast({
                message: '余额不足',
                duration: 2000
            });
            return;
        }
        if (!this.selectedBankCard) {
            promptAction.showToast({
                message: '请选择银行卡',
                duration: 2000
            });
            return;
        }
        axios({
            url: 'http://localhost:8091/wallet/withdraw',
            method: 'post',
            params: {
                userId: this.userId,
                amount: amount,
                bankCardNumber: this.selectedBankCard,
                remark: this.remark || '钱包提现'
            }
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
            console.log('提现结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                this.showWithdraw = false;
                this.clearWithdrawForm();
                this.loadWalletBalance();
                this.loadRecentTransactions();
            }
        }).catch((err: AxiosError) => {
            console.error('提现错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        });
    }
    // 转账
    transfer() {
        if (!this.transferTarget || this.transferTarget.length < 10) {
            promptAction.showToast({
                message: '请输入有效的账户号码',
                duration: 2000
            });
            return;
        }
        if (!this.transferAmount || parseFloat(this.transferAmount) <= 0) {
            promptAction.showToast({
                message: '请输入有效的转账金额',
                duration: 2000
            });
            return;
        }
        const amount = parseFloat(this.transferAmount);
        if (amount > this.balance) {
            promptAction.showToast({
                message: '余额不足',
                duration: 2000
            });
            return;
        }
        // 这里使用钱包账户号码作为fromAccountNumber
        const fromAccountNumber = `WALLET_${this.userId}`;
        axios({
            url: 'http://localhost:8091/transfer',
            method: 'post',
            params: {
                fromAccountNumber: fromAccountNumber,
                toAccountNumber: this.transferTarget,
                amount: amount
            }
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
            console.log('转账结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                this.showTransfer = false;
                this.clearTransferForm();
                this.loadWalletBalance();
                this.loadRecentTransactions();
            }
        }).catch((err: AxiosError) => {
            console.error('转账错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        });
    }
    buildRechargeDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(792:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
            Stack.position({ x: 0, y: 0 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景遮罩
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(794:7)", "entry");
            // 背景遮罩
            Column.width('100%');
            // 背景遮罩
            Column.height('100%');
            // 背景遮罩
            Column.backgroundColor('rgba(0,0,0,0.5)');
            // 背景遮罩
            Column.onClick(() => {
                this.showRecharge = false;
                this.clearRechargeForm();
            });
        }, Column);
        // 背景遮罩
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 弹窗内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(804:7)", "entry");
            // 弹窗内容
            Column.width('90%');
            // 弹窗内容
            Column.backgroundColor('#ffffff');
            // 弹窗内容
            Column.borderRadius(16);
            // 弹窗内容
            Column.padding(24);
            // 弹窗内容
            Column.shadow({
                radius: 16,
                color: '#33000000',
                offsetX: 0,
                offsetY: 8
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(806:9)", "entry");
            // 标题
            Row.width('100%');
            // 标题
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包充值');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(807:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('×');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(813:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#999999');
            Text.onClick(() => {
                this.showRecharge = false;
                this.clearRechargeForm();
            });
        }, Text);
        Text.pop();
        // 标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(825:9)", "entry");
            // 银行卡选择
            Column.width('100%');
            // 银行卡选择
            Column.alignItems(HorizontalAlign.Start);
            // 银行卡选择
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(826:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.defaultBankCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(833:13)", "entry");
                        Row.width('100%');
                        Row.height(48);
                        Row.backgroundColor('#f8f9fa');
                        Row.borderRadius(8);
                        Row.padding({ left: 16, right: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.defaultBankCard.bankName} (****${this.defaultBankCard.cardNumber.slice(-4)})`);
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(834:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#1a1a1a');
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(839:15)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#ffffff');
                        Text.backgroundColor('#4285f4');
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.borderRadius(12);
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 没有银行卡时的提示
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(853:13)", "entry");
                        // 没有银行卡时的提示
                        Row.width('100%');
                        // 没有银行卡时的提示
                        Row.height(48);
                        // 没有银行卡时的提示
                        Row.backgroundColor('#fff3cd');
                        // 没有银行卡时的提示
                        Row.borderRadius(8);
                        // 没有银行卡时的提示
                        Row.padding({ left: 16, right: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(854:15)", "entry");
                        Column.layoutWeight(1);
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无绑定银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(855:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 4 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请先添加银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(860:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#666666');
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去添加');
                        Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(867:15)", "entry");
                        Button.type(ButtonType.Capsule);
                        Button.backgroundColor('#4285f4');
                        Button.fontColor('#ffffff');
                        Button.fontSize(12);
                        Button.height(32);
                        Button.onClick(() => {
                            this.showRecharge = false;
                            router.pushUrl({ url: 'pages/BankCardPage' });
                        });
                    }, Button);
                    Button.pop();
                    // 没有银行卡时的提示
                    Row.pop();
                });
            }
        }, If);
        If.pop();
        // 银行卡选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(890:9)", "entry");
            // 充值金额
            Column.width('100%');
            // 充值金额
            Column.alignItems(HorizontalAlign.Start);
            // 充值金额
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值金额');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(891:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入充值金额' });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPage.ets(897:11)", "entry");
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.rechargeAmount = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 充值金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快捷金额
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(911:9)", "entry");
            // 快捷金额
            Row.width('100%');
            // 快捷金额
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('100');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(912:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.rechargeAmount = '100';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('500');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(924:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.margin({ left: 4, right: 4 });
            Button.onClick(() => {
                this.rechargeAmount = '500';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('1000');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(936:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.rechargeAmount = '1000';
            });
        }, Button);
        Button.pop();
        // 快捷金额
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(952:9)", "entry");
            // 操作按钮
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(953:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f8f9fa');
            Button.fontColor('#666666');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.showRecharge = false;
                this.clearRechargeForm();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认充值');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(966:11)", "entry");
            Button.type(ButtonType.Capsule);
            Button.backgroundColor('#4285f4');
            Button.fontColor('#ffffff');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.recharge();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 弹窗内容
        Column.pop();
        Stack.pop();
    }
    buildTransferDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(998:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
            Stack.position({ x: 0, y: 0 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景遮罩
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1000:7)", "entry");
            // 背景遮罩
            Column.width('100%');
            // 背景遮罩
            Column.height('100%');
            // 背景遮罩
            Column.backgroundColor('rgba(0,0,0,0.5)');
            // 背景遮罩
            Column.onClick(() => {
                this.showTransfer = false;
                this.clearTransferForm();
            });
        }, Column);
        // 背景遮罩
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 弹窗内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1010:7)", "entry");
            // 弹窗内容
            Column.width('90%');
            // 弹窗内容
            Column.backgroundColor('#ffffff');
            // 弹窗内容
            Column.borderRadius(16);
            // 弹窗内容
            Column.padding(24);
            // 弹窗内容
            Column.shadow({
                radius: 16,
                color: '#33000000',
                offsetX: 0,
                offsetY: 8
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1012:9)", "entry");
            // 标题
            Row.width('100%');
            // 标题
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包转账');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1013:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('×');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1019:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#999999');
            Text.onClick(() => {
                this.showTransfer = false;
                this.clearTransferForm();
            });
        }, Text);
        Text.pop();
        // 标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 收款账户
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1031:9)", "entry");
            // 收款账户
            Column.width('100%');
            // 收款账户
            Column.alignItems(HorizontalAlign.Start);
            // 收款账户
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('收款账户');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1032:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入收款账户号码' });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPage.ets(1038:11)", "entry");
            TextInput.onChange((value: string) => {
                this.transferTarget = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 收款账户
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1051:9)", "entry");
            // 转账金额
            Column.width('100%');
            // 转账金额
            Column.alignItems(HorizontalAlign.Start);
            // 转账金额
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账金额');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1052:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入转账金额' });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPage.ets(1058:11)", "entry");
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.transferAmount = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 转账金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1072:9)", "entry");
            // 操作按钮
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1073:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f8f9fa');
            Button.fontColor('#666666');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.showTransfer = false;
                this.clearTransferForm();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认转账');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1086:11)", "entry");
            Button.type(ButtonType.Capsule);
            Button.backgroundColor('#ff9800');
            Button.fontColor('#ffffff');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.transfer();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 弹窗内容
        Column.pop();
        Stack.pop();
    }
    buildWithdrawDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/WalletPage.ets(1118:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
            Stack.position({ x: 0, y: 0 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景遮罩
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1120:7)", "entry");
            // 背景遮罩
            Column.width('100%');
            // 背景遮罩
            Column.height('100%');
            // 背景遮罩
            Column.backgroundColor('rgba(0,0,0,0.5)');
            // 背景遮罩
            Column.onClick(() => {
                this.showWithdraw = false;
                this.clearWithdrawForm();
            });
        }, Column);
        // 背景遮罩
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 弹窗内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1130:7)", "entry");
            // 弹窗内容
            Column.width('90%');
            // 弹窗内容
            Column.backgroundColor('#ffffff');
            // 弹窗内容
            Column.borderRadius(16);
            // 弹窗内容
            Column.padding(24);
            // 弹窗内容
            Column.shadow({
                radius: 16,
                color: '#33000000',
                offsetX: 0,
                offsetY: 8
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1132:9)", "entry");
            // 标题
            Row.width('100%');
            // 标题
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包提现');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1133:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('×');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1139:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#999999');
            Text.onClick(() => {
                this.showWithdraw = false;
                this.clearWithdrawForm();
            });
        }, Text);
        Text.pop();
        // 标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1151:9)", "entry");
            // 银行卡选择
            Column.width('100%');
            // 银行卡选择
            Column.alignItems(HorizontalAlign.Start);
            // 银行卡选择
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现银行卡');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1152:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.defaultBankCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1159:13)", "entry");
                        Row.width('100%');
                        Row.height(48);
                        Row.backgroundColor('#f8f9fa');
                        Row.borderRadius(8);
                        Row.padding({ left: 16, right: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.defaultBankCard.bankName} (****${this.defaultBankCard.cardNumber.slice(-4)})`);
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1160:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#1a1a1a');
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1165:15)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#ffffff');
                        Text.backgroundColor('#4caf50');
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.borderRadius(12);
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 没有银行卡时的提示
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1179:13)", "entry");
                        // 没有银行卡时的提示
                        Row.width('100%');
                        // 没有银行卡时的提示
                        Row.height(48);
                        // 没有银行卡时的提示
                        Row.backgroundColor('#fff3cd');
                        // 没有银行卡时的提示
                        Row.borderRadius(8);
                        // 没有银行卡时的提示
                        Row.padding({ left: 16, right: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1180:15)", "entry");
                        Column.layoutWeight(1);
                        Column.alignItems(HorizontalAlign.Start);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无绑定银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1181:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 4 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请先添加银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1186:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#666666');
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去添加');
                        Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1193:15)", "entry");
                        Button.type(ButtonType.Capsule);
                        Button.backgroundColor('#4caf50');
                        Button.fontColor('#ffffff');
                        Button.fontSize(12);
                        Button.height(32);
                        Button.onClick(() => {
                            this.showWithdraw = false;
                            router.pushUrl({ url: 'pages/BankCardPage' });
                        });
                    }, Button);
                    Button.pop();
                    // 没有银行卡时的提示
                    Row.pop();
                });
            }
        }, If);
        If.pop();
        // 银行卡选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WalletPage.ets(1216:9)", "entry");
            // 提现金额
            Column.width('100%');
            // 提现金额
            Column.alignItems(HorizontalAlign.Start);
            // 提现金额
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现金额');
            Text.debugLine("entry/src/main/ets/pages/WalletPage.ets(1217:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入提现金额' });
            TextInput.debugLine("entry/src/main/ets/pages/WalletPage.ets(1223:11)", "entry");
            TextInput.type(InputType.Number);
            TextInput.onChange((value: string) => {
                this.withdrawAmount = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 提现金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快捷金额
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1237:9)", "entry");
            // 快捷金额
            Row.width('100%');
            // 快捷金额
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('200');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1238:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.withdrawAmount = '200';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('500');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1250:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.margin({ left: 4, right: 4 });
            Button.onClick(() => {
                this.withdrawAmount = '500';
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('1000');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1262:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.withdrawAmount = '1000';
            });
        }, Button);
        Button.pop();
        // 快捷金额
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WalletPage.ets(1278:9)", "entry");
            // 操作按钮
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1279:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f8f9fa');
            Button.fontColor('#666666');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.showWithdraw = false;
                this.clearWithdrawForm();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认提现');
            Button.debugLine("entry/src/main/ets/pages/WalletPage.ets(1292:11)", "entry");
            Button.type(ButtonType.Capsule);
            Button.backgroundColor('#4caf50');
            Button.fontColor('#ffffff');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.withdraw();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 弹窗内容
        Column.pop();
        Stack.pop();
    }
    // 清空表单数据
    clearRechargeForm() {
        this.rechargeAmount = '';
        this.remark = '';
    }
    clearTransferForm() {
        this.transferAmount = '';
        this.transferTarget = '';
    }
    clearWithdrawForm() {
        this.withdrawAmount = '';
        this.remark = '';
    }
    // 获取交易图标
    getTransactionIcon(type: number): Resource {
        switch (type) {
            case 1: return { "id": 16777248, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 充值
            case 2: return { "id": 16777251, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 提现
            case 3: return { "id": 16777246, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 转账
            case 4: return { "id": 16777245, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 消费
            default: return { "id": 16777233, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
        }
    }
    // 获取交易颜色
    getTransactionColor(type: number): string {
        switch (type) {
            case 1: return '#4285f4'; // 充值 - 蓝色
            case 2: return '#4caf50'; // 提现 - 绿色
            case 3: return '#ff9800'; // 转账 - 橙色
            case 4: return '#f44336'; // 消费 - 红色
            default: return '#666666';
        }
    }
    // 获取交易标题
    getTransactionTitle(type: number): string {
        switch (type) {
            case 1: return '充值';
            case 2: return '提现';
            case 3: return '转账';
            case 4: return '消费';
            default: return '交易';
        }
    }
    // 格式化金额
    formatAmount(type: number, amount: number): string {
        const prefix = (type === 1) ? '+' : '-';
        return `${prefix}${Math.abs(amount).toFixed(2)}`;
    }
    // 获取金额颜色
    getAmountColor(type: number): string {
        return (type === 1) ? '#4285f4' : '#f44336';
    }
    // 格式化日期时间
    formatDateTime(dateTimeStr: string): string {
        try {
            const date = new Date(dateTimeStr);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${month}-${day} ${hours}:${minutes}`;
        }
        catch (error) {
            return dateTimeStr;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WalletPage";
    }
}
registerNamedRoute(() => new WalletPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/WalletPage", pageFullPath: "entry/src/main/ets/pages/WalletPage", integratedHsp: "false", moduleType: "followWithHap" });
