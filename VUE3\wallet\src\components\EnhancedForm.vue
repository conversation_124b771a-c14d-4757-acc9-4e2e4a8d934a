<template>
  <div class="enhanced-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :class="['wallet-form', { 'form-loading': loading }]"
      @submit.prevent="handleSubmit"
    >
      <slot :form-data="formData" :loading="loading" :errors="errors" />
      
      <!-- 表单操作按钮 -->
      <div class="form-actions" v-if="showActions">
        <el-button
          v-if="showCancel"
          @click="handleCancel"
          :disabled="loading"
          class="cancel-btn"
        >
          {{ cancelText }}
        </el-button>
        
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          :disabled="!isFormValid"
          class="submit-btn"
        >
          <template v-if="!loading">
            <el-icon><Check /></el-icon>
            {{ submitText }}
          </template>
          <template v-else>
            {{ loadingText }}
          </template>
        </el-button>
      </div>
    </el-form>
    
    <!-- 表单验证提示 */
    <transition name="slide-down">
      <div v-if="showValidationSummary && hasErrors" class="validation-summary">
        <div class="validation-header">
          <el-icon class="error-icon"><Warning /></el-icon>
          <span>请修正以下错误：</span>
        </div>
        <ul class="error-list">
          <li v-for="error in errorList" :key="error.field" class="error-item">
            {{ error.message }}
          </li>
        </ul>
      </div>
    </transition>
    
    <!-- 操作反馈 -->
    <ActionFeedback
      :loading="loading"
      :loading-text="loadingText"
      :success="showSuccess"
      :success-text="successText"
      :error="showError"
      :error-text="errorText"
      @hide="handleFeedbackHide"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { Check, Warning } from '@element-plus/icons-vue'
import ActionFeedback from './ActionFeedback.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: String,
    default: '120px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: true
  },
  showCancel: {
    type: Boolean,
    default: true
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  submitText: {
    type: String,
    default: '提交'
  },
  loadingText: {
    type: String,
    default: '提交中...'
  },
  successText: {
    type: String,
    default: '提交成功'
  },
  errorText: {
    type: String,
    default: '提交失败'
  },
  showValidationSummary: {
    type: Boolean,
    default: true
  },
  autoFocus: {
    type: Boolean,
    default: true
  },
  validateOnChange: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'cancel', 'success', 'error'])

const formRef = ref(null)
const formData = ref({ ...props.modelValue })
const errors = ref({})
const showSuccess = ref(false)
const showError = ref(false)
const isFormValid = ref(false)

// 表单规则
const formRules = computed(() => {
  const rules = { ...props.rules }
  
  // 为每个规则添加实时验证
  if (props.validateOnChange) {
    Object.keys(rules).forEach(field => {
      if (rules[field]) {
        rules[field] = rules[field].map(rule => ({
          ...rule,
          trigger: rule.trigger || ['blur', 'change']
        }))
      }
    })
  }
  
  return rules
})

// 错误列表
const errorList = computed(() => {
  return Object.keys(errors.value).map(field => ({
    field,
    message: errors.value[field]
  }))
})

// 是否有错误
const hasErrors = computed(() => {
  return Object.keys(errors.value).length > 0
})

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:modelValue', newVal)
  
  // 实时验证
  if (props.validateOnChange) {
    nextTick(() => {
      validateForm(false)
    })
  }
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  formData.value = { ...newVal }
}, { deep: true })

// 表单验证
const validateForm = async (showMessage = true) => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    errors.value = {}
    isFormValid.value = true
    return true
  } catch (error) {
    if (error.fields) {
      errors.value = {}
      Object.keys(error.fields).forEach(field => {
        const fieldErrors = error.fields[field]
        if (fieldErrors && fieldErrors.length > 0) {
          errors.value[field] = fieldErrors[0].message
        }
      })
    }
    isFormValid.value = false
    
    if (showMessage) {
      // 滚动到第一个错误字段
      scrollToFirstError()
    }
    
    return false
  }
}

// 滚动到第一个错误字段
const scrollToFirstError = () => {
  nextTick(() => {
    const errorField = document.querySelector('.el-form-item.is-error')
    if (errorField) {
      errorField.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
      
      // 聚焦到错误字段的输入框
      const input = errorField.querySelector('input, textarea, select')
      if (input) {
        input.focus()
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  const isValid = await validateForm()
  if (!isValid) return
  
  try {
    emit('submit', formData.value)
  } catch (error) {
    console.error('表单提交错误:', error)
    showError.value = true
    emit('error', error)
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 显示成功反馈
const showSuccessFeedback = () => {
  showSuccess.value = true
  emit('success', formData.value)
}

// 显示错误反馈
const showErrorFeedback = (message) => {
  showError.value = true
  if (message) {
    errorText.value = message
  }
  emit('error', message)
}

// 反馈隐藏处理
const handleFeedbackHide = (type) => {
  if (type === 'success') {
    showSuccess.value = false
  } else if (type === 'error') {
    showError.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  errors.value = {}
  isFormValid.value = false
}

// 清除验证
const clearValidation = () => {
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  errors.value = {}
}

// 自动聚焦
const autoFocusFirstField = () => {
  if (props.autoFocus) {
    nextTick(() => {
      const firstInput = document.querySelector('.enhanced-form input, .enhanced-form textarea')
      if (firstInput) {
        firstInput.focus()
      }
    })
  }
}

// 暴露方法给父组件
defineExpose({
  validate: validateForm,
  resetForm,
  clearValidation,
  showSuccessFeedback,
  showErrorFeedback,
  formRef
})

// 组件挂载后自动聚焦
onMounted(() => {
  autoFocusFirstField()
})
</script>

<style scoped>
.enhanced-form {
  position: relative;
}

.wallet-form {
  transition: all 0.3s ease;
}

.wallet-form.form-loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-light);
}

.cancel-btn,
.submit-btn {
  min-width: 100px;
  height: 40px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-medium);
  color: var(--text-secondary);
}

.cancel-btn:hover {
  background: var(--border-medium);
  transform: translateY(-2px);
}

.submit-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 验证摘要 */
.validation-summary {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(211, 47, 47, 0.05));
  border: 1px solid rgba(244, 67, 54, 0.2);
  border-radius: 8px;
  animation: shake 0.5s ease-in-out;
}

.validation-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--error-color);
}

.error-icon {
  font-size: 18px;
}

.error-list {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.error-item {
  position: relative;
  margin-bottom: 8px;
  color: var(--error-dark);
  font-size: 14px;
}

.error-item::before {
  content: '•';
  position: absolute;
  left: -12px;
  color: var(--error-color);
  font-weight: bold;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* 过渡动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}

/* 动画效果 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .cancel-btn,
  .submit-btn {
    width: 100%;
    margin: 0;
  }
  
  .validation-summary {
    margin: 16px -10px 0;
  }
}

/* 表单字段增强样式 */
:deep(.el-form-item) {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

:deep(.el-form-item.is-error) {
  animation: fieldError 0.3s ease-in-out;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-form-item__error) {
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes fieldError {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-3px);
  }
  75% {
    transform: translateX(3px);
  }
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
