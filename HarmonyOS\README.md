# HarmonyOS E-Wallet 应用开发指南

## 📱 项目概述

这是一个基于HarmonyOS开发的电子钱包支付系统客户端，与Spring Boot后端服务完美集成。

## 🛠️ 开发环境要求

- **DevEco Studio**: 4.0 或以上版本
- **HarmonyOS SDK**: API 9 或以上
- **Node.js**: 16.0 或以上版本
- **ohpm**: 包管理工具

## 📁 项目结构

```
WalletApp/
├── entry/
│   ├── src/main/ets/
│   │   ├── common/           # 公共工具类
│   │   │   ├── HttpUtil.ets     # HTTP请求工具
│   │   │   ├── StorageUtil.ets  # 本地存储工具
│   │   │   ├── AuthService.ets  # 认证服务
│   │   │   └── WalletService.ets # 钱包服务
│   │   ├── pages/            # 页面文件
│   │   │   ├── SplashPage.ets   # 启动页
│   │   │   ├── LoginPage.ets    # 登录页
│   │   │   └── HomePage.ets     # 主页
│   │   └── entryability/     # 应用入口
│   ├── src/main/resources/   # 资源文件
│   └── oh-package.json5      # 依赖配置
└── README.md
```

## 🚀 快速开始

### 1. 创建项目

1. 打开 DevEco Studio
2. 选择 "Create Project"
3. 选择 "Empty Ability (ArkTS)"
4. 项目名称：`WalletApp`
5. Bundle Name: `com.wallet.app`
6. 选择 API 9 或以上版本

### 2. 安装依赖

在项目根目录执行：

```bash
cd entry
ohpm install
```

或在 DevEco Studio 终端中执行：

```bash
ohpm install --all
```

### 3. 配置网络权限

确保 `entry/src/main/module.json5` 包含网络权限：

```json
{
  "requestPermissions": [
    {
      "name": "ohos.permission.INTERNET",
      "reason": "$string:internet_permission_reason",
      "usedScene": {
        "abilities": ["EntryAbility"],
        "when": "inuse"
      }
    }
  ]
}
```

### 4. 修改后端地址

在 `HttpUtil.ets` 中修改服务器地址：

```typescript
private static readonly BASE_URL = 'http://你的IP地址:8091'
```

**重要提示**：
- 如果使用模拟器，IP地址应该是你电脑的局域网IP
- 如果使用真机，确保手机和电脑在同一网络下
- 不要使用 `localhost` 或 `127.0.0.1`

### 5. 运行项目

1. 在 DevEco Studio 中点击 "Run" 按钮
2. 选择模拟器或连接的真机
3. 等待编译和安装完成

## 🔧 配置说明

### 网络配置

项目已配置支持HTTP请求，包括：
- 网络权限声明
- HTTP明文传输支持
- 请求拦截器和响应拦截器
- 错误处理机制

### 本地存储

使用 HarmonyOS 的 `preferences` API 进行本地数据存储：
- 用户token
- 用户ID
- 用户信息

### API接口

与Spring Boot后端完全兼容的API接口：
- 用户认证（密码登录、验证码登录）
- 钱包管理
- 银行卡管理
- 交易记录

## 📱 功能特性

### 已实现功能

1. **用户认证**
   - 密码登录
   - 验证码登录
   - 自动登录状态检查

2. **钱包管理**
   - 余额查询
   - 充值功能
   - 提现功能
   - 转账功能

3. **银行卡管理**
   - 银行卡列表
   - 卡片信息显示

4. **交易记录**
   - 交易历史查询
   - 交易详情显示

### 待开发功能

- 用户注册
- 转账页面
- 充值页面
- 提现页面
- 个人设置
- 安全中心

## 🐛 调试技巧

### 查看日志

在 DevEco Studio 的 Log 窗口查看应用日志：

```typescript
console.log('调试信息')
console.error('错误信息')
```

### 网络调试

1. 检查网络请求是否正常发送
2. 查看请求和响应数据
3. 确认后端服务是否正常运行

### 常见问题

1. **网络请求失败**
   - 检查IP地址是否正确
   - 确认后端服务是否启动
   - 检查网络权限是否配置

2. **编译错误**
   - 检查依赖是否正确安装
   - 确认API版本兼容性
   - 清理并重新构建项目

3. **页面跳转失败**
   - 检查路由配置是否正确
   - 确认页面文件是否存在

## 📞 技术支持

如果在开发过程中遇到问题，可以：

1. 查看 DevEco Studio 的错误提示
2. 检查 HarmonyOS 官方文档
3. 查看项目的日志输出
4. 确认后端API是否正常工作

## 🔄 版本更新

### v1.0.0
- 基础项目结构
- 用户认证功能
- 钱包基础功能
- 网络请求封装
- 本地存储管理

---

**注意**: 这是一个开发版本，部分功能仍在完善中。在生产环境使用前，请确保进行充分的测试。
