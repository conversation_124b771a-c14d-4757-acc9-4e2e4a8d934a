{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "81d7525a-bc86-4532-a3ed-ea84053a10e1", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844448621300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31aca1de-4581-4746-b2e8-5370848649c2", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844460243600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e88365a-f67f-4f0b-bab7-39e96959b904", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844460535400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "442338a0-ffec-4e61-bd75-b0343f1cde1c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872491497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50441fe3-6309-4286-ae63-593c83b08d0c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872499243500, "endTime": 9872676541200}, "additional": {"children": ["e02e76ce-7b92-4e5d-9be5-4263228fd891", "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "2e6fae69-dd34-4c9a-8335-52c6b5fdcf2d", "0e7d33d5-798b-46cb-bbb6-77191f7215cb", "e3db451c-07e0-4332-a334-d1315161f5e6", "b2a84056-666e-40b9-9a0a-8f85c36e1bb5", "9fb8e0ba-f5d3-497e-9d51-2d5a8a41d6d6"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e02e76ce-7b92-4e5d-9be5-4263228fd891", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872499246200, "endTime": 9872514389300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "ad6979a7-2807-4e0c-8f8c-5b7d21358ed0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872514407500, "endTime": 9872675322500}, "additional": {"children": ["bf74552b-d5fd-4141-aa2b-29bcefb5f10e", "748e20ee-4225-468f-9933-c199582b5600", "6f4598fd-46fd-44c6-9869-1073aeeb1d39", "2349517b-4207-4e48-92c6-d876fa68f99f", "381aee62-cea8-4f6b-b8ba-e0189323e533", "b10fd4e1-1ae7-4e13-987d-7af63a96a13a", "b3f777b7-2e18-4f4b-b849-f77f55c7e9ef", "dabdd607-9e7b-4d78-bfeb-4c93a52059a4", "4de4ca72-d183-47e2-ad57-c0a70dbcb80a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "f080ce33-0224-46fd-bf00-313943e02bff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e6fae69-dd34-4c9a-8335-52c6b5fdcf2d", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872675347400, "endTime": 9872676516000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "38e9b7b1-3833-4ef2-aaa7-63895d8b54a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e7d33d5-798b-46cb-bbb6-77191f7215cb", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872676522100, "endTime": 9872676536400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "6f7de53c-a988-49d4-9f5b-36098b26a6aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3db451c-07e0-4332-a334-d1315161f5e6", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872502678500, "endTime": 9872502714600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "19aa5f33-1b88-4f61-9651-28471598c789"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19aa5f33-1b88-4f61-9651-28471598c789", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872502678500, "endTime": 9872502714600}, "additional": {"logType": "info", "children": [], "durationId": "e3db451c-07e0-4332-a334-d1315161f5e6", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "b2a84056-666e-40b9-9a0a-8f85c36e1bb5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872509655000, "endTime": 9872509669400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "be461b4a-0541-4189-8d33-18aa19a0455f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be461b4a-0541-4189-8d33-18aa19a0455f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872509655000, "endTime": 9872509669400}, "additional": {"logType": "info", "children": [], "durationId": "b2a84056-666e-40b9-9a0a-8f85c36e1bb5", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "601bcd11-66e3-4f7d-a4b7-1b0efab465fd", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872509727100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff6c704a-6304-4713-89c0-60a21905b5e1", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872514233900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad6979a7-2807-4e0c-8f8c-5b7d21358ed0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872499246200, "endTime": 9872514389300}, "additional": {"logType": "info", "children": [], "durationId": "e02e76ce-7b92-4e5d-9be5-4263228fd891", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "bf74552b-d5fd-4141-aa2b-29bcefb5f10e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872520294100, "endTime": 9872520304500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "e25eb482-bb36-4fc2-83aa-13f4420034e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "748e20ee-4225-468f-9933-c199582b5600", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872520321300, "endTime": 9872525163300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "6ffd4e34-07db-4f97-a357-709d73c27a2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f4598fd-46fd-44c6-9869-1073aeeb1d39", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872525177400, "endTime": 9872612147600}, "additional": {"children": ["0f65a39b-f93b-482b-83e8-41d1f128aa58", "f3e0fac5-719d-452b-8646-714cbd34084b", "199e6987-6a43-4e9e-a602-a6d2f4d346f8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "a71768e4-623a-449e-b0a4-ce8ab85d755d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2349517b-4207-4e48-92c6-d876fa68f99f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872612164400, "endTime": 9872635532200}, "additional": {"children": ["a8be5fd9-1fc4-4f28-adc0-761290ca6f3a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "13c470ae-0880-4729-a037-e31c440324ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "381aee62-cea8-4f6b-b8ba-e0189323e533", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872635541600, "endTime": 9872650351200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "11a91ebc-0fc8-4123-8d5b-0e3e6a6bb21a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b10fd4e1-1ae7-4e13-987d-7af63a96a13a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872651327800, "endTime": 9872659151700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "5415f2a9-5567-4a78-9bc6-a9f3d053ac2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3f777b7-2e18-4f4b-b849-f77f55c7e9ef", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872659188100, "endTime": 9872675138000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "1050a798-4efa-4f0a-be59-fad9bb9ceafb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dabdd607-9e7b-4d78-bfeb-4c93a52059a4", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872675179800, "endTime": 9872675309000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "88249390-1d06-4cf1-9aae-3adb56f10a05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e25eb482-bb36-4fc2-83aa-13f4420034e3", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872520294100, "endTime": 9872520304500}, "additional": {"logType": "info", "children": [], "durationId": "bf74552b-d5fd-4141-aa2b-29bcefb5f10e", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "6ffd4e34-07db-4f97-a357-709d73c27a2c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872520321300, "endTime": 9872525163300}, "additional": {"logType": "info", "children": [], "durationId": "748e20ee-4225-468f-9933-c199582b5600", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "0f65a39b-f93b-482b-83e8-41d1f128aa58", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872525839100, "endTime": 9872525852900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f4598fd-46fd-44c6-9869-1073aeeb1d39", "logId": "5a5a458e-af3d-47f2-b3a4-0c1e181e0505"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a5a458e-af3d-47f2-b3a4-0c1e181e0505", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872525839100, "endTime": 9872525852900}, "additional": {"logType": "info", "children": [], "durationId": "0f65a39b-f93b-482b-83e8-41d1f128aa58", "parent": "a71768e4-623a-449e-b0a4-ce8ab85d755d"}}, {"head": {"id": "f3e0fac5-719d-452b-8646-714cbd34084b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872527809700, "endTime": 9872611478100}, "additional": {"children": ["5e10c802-f097-4fc3-83c4-7d529716b670", "5dcd59c8-661d-4947-8828-7f9e3500516e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f4598fd-46fd-44c6-9869-1073aeeb1d39", "logId": "88b26ce9-e103-4364-a4c2-2d9705b52085"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e10c802-f097-4fc3-83c4-7d529716b670", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872527810900, "endTime": 9872535368100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3e0fac5-719d-452b-8646-714cbd34084b", "logId": "8d0f218c-9dab-4649-87f7-a89254f7646f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dcd59c8-661d-4947-8828-7f9e3500516e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872535384100, "endTime": 9872611466100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3e0fac5-719d-452b-8646-714cbd34084b", "logId": "c1ebd667-2322-41f3-815c-ea99ad44e2b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab6741b9-4d54-41e1-af36-3aa103bcbd80", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872527816400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f56d18-5bd2-4af9-9165-fd5cb082841d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872535233500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0f218c-9dab-4649-87f7-a89254f7646f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872527810900, "endTime": 9872535368100}, "additional": {"logType": "info", "children": [], "durationId": "5e10c802-f097-4fc3-83c4-7d529716b670", "parent": "88b26ce9-e103-4364-a4c2-2d9705b52085"}}, {"head": {"id": "abd9b538-a3c4-4609-a10d-00b7c938190c", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872535397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f7e4c0-d52c-4fb0-8310-b14e85e2b347", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872542196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68418934-27a3-4e25-9fb3-93489c9719e1", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872542328500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5633936-dfdd-498f-91b6-d6bc9bace423", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872542485500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbc4cac6-9494-4e71-b41f-f6750ca83b2b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872542737900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8da41c-71cf-4276-8ab6-558020c49655", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872544221800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c42023-9952-417b-a50d-a07c22d44161", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872549795600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab13da9-c47c-414b-8a5c-29eb85bce990", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872560133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d636ae7a-d9bf-4eb1-8afb-256329e3159a", "name": "Sdk init in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872590167400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e754ec-4abe-4e90-bc4c-2c40f839bf92", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872590321000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 23}, "markType": "other"}}, {"head": {"id": "6ffe94c1-d774-48ef-9933-485d768fe6d8", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872590373900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 23}, "markType": "other"}}, {"head": {"id": "b3722eff-19a0-47c9-b991-fe6f82848bd7", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872611169400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b547d47-935c-497a-988d-f871781971a5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872611298900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ece424-24fa-4104-a041-22ee2245d079", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872611367500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8186792f-4cd7-49e5-88fc-ade8d3b5a1d1", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872611419600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ebd667-2322-41f3-815c-ea99ad44e2b3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872535384100, "endTime": 9872611466100}, "additional": {"logType": "info", "children": [], "durationId": "5dcd59c8-661d-4947-8828-7f9e3500516e", "parent": "88b26ce9-e103-4364-a4c2-2d9705b52085"}}, {"head": {"id": "88b26ce9-e103-4364-a4c2-2d9705b52085", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872527809700, "endTime": 9872611478100}, "additional": {"logType": "info", "children": ["8d0f218c-9dab-4649-87f7-a89254f7646f", "c1ebd667-2322-41f3-815c-ea99ad44e2b3"], "durationId": "f3e0fac5-719d-452b-8646-714cbd34084b", "parent": "a71768e4-623a-449e-b0a4-ce8ab85d755d"}}, {"head": {"id": "199e6987-6a43-4e9e-a602-a6d2f4d346f8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872612116500, "endTime": 9872612131200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f4598fd-46fd-44c6-9869-1073aeeb1d39", "logId": "a1b31240-9f74-4020-a09f-248c21a558e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1b31240-9f74-4020-a09f-248c21a558e5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872612116500, "endTime": 9872612131200}, "additional": {"logType": "info", "children": [], "durationId": "199e6987-6a43-4e9e-a602-a6d2f4d346f8", "parent": "a71768e4-623a-449e-b0a4-ce8ab85d755d"}}, {"head": {"id": "a71768e4-623a-449e-b0a4-ce8ab85d755d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872525177400, "endTime": 9872612147600}, "additional": {"logType": "info", "children": ["5a5a458e-af3d-47f2-b3a4-0c1e181e0505", "88b26ce9-e103-4364-a4c2-2d9705b52085", "a1b31240-9f74-4020-a09f-248c21a558e5"], "durationId": "6f4598fd-46fd-44c6-9869-1073aeeb1d39", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "a8be5fd9-1fc4-4f28-adc0-761290ca6f3a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872612807600, "endTime": 9872635518700}, "additional": {"children": ["0b31da2c-8e00-4110-bbb0-19076559b6fd", "113003a1-b06a-408b-814b-5ac860c2ce57", "a3c1df3e-ac97-47f0-a9b6-f1fa9ba0a674"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2349517b-4207-4e48-92c6-d876fa68f99f", "logId": "9892a6c9-0f0e-48a1-89ea-cf892e4c08d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b31da2c-8e00-4110-bbb0-19076559b6fd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872615960000, "endTime": 9872615975600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8be5fd9-1fc4-4f28-adc0-761290ca6f3a", "logId": "599638fc-d3a4-4456-9f2a-e3a3cac33393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "599638fc-d3a4-4456-9f2a-e3a3cac33393", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872615960000, "endTime": 9872615975600}, "additional": {"logType": "info", "children": [], "durationId": "0b31da2c-8e00-4110-bbb0-19076559b6fd", "parent": "9892a6c9-0f0e-48a1-89ea-cf892e4c08d6"}}, {"head": {"id": "113003a1-b06a-408b-814b-5ac860c2ce57", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872618600000, "endTime": 9872634225400}, "additional": {"children": ["5bd7a847-c060-4ae3-9c76-ceccd4514e34", "770e90ef-579c-4f47-a56a-1bddf2e563f1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8be5fd9-1fc4-4f28-adc0-761290ca6f3a", "logId": "f9d802a2-afb1-49ec-a2e7-e7026d1b1078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bd7a847-c060-4ae3-9c76-ceccd4514e34", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872618601100, "endTime": 9872622547300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "113003a1-b06a-408b-814b-5ac860c2ce57", "logId": "1e68757d-db7a-42e0-8f83-edc3a876689b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "770e90ef-579c-4f47-a56a-1bddf2e563f1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872622575200, "endTime": 9872634211700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "113003a1-b06a-408b-814b-5ac860c2ce57", "logId": "fea1bba2-5883-4e84-8cd8-190283fdfb5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fbf11ef-4542-4765-b5aa-f7910d31d0e2", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872618606400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b42397f-0b4c-4247-8fc5-69b69042cb59", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872622418100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e68757d-db7a-42e0-8f83-edc3a876689b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872618601100, "endTime": 9872622547300}, "additional": {"logType": "info", "children": [], "durationId": "5bd7a847-c060-4ae3-9c76-ceccd4514e34", "parent": "f9d802a2-afb1-49ec-a2e7-e7026d1b1078"}}, {"head": {"id": "f9c87d8e-53cd-49b2-a561-35db884e84c6", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872622590200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a6d7b7-4449-43b9-9d51-2664d471f323", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872629391800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ca2aba-1ffe-431b-b0d5-035b8aec30df", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872629618300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb31c23-0c56-4f80-8a5a-9cca4e587e3a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872630197100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "523dc3e6-ea0a-461a-8935-a2e411f0c33c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872630468300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd49c6f1-f6e2-4dda-b52a-03367e625231", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872630555100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361cdb17-fb9a-4138-9cb9-a197c9c5298c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872630615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "289d7384-7f30-44bb-af6f-ddc6c13dc3a7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872630682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5225d9a9-927b-4538-8987-0aaa453499fb", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872633905200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe77c97-354a-47da-b8ac-c2fadd047003", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872634037800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc37e8b-f64b-4a39-b81f-ca9b5e74163a", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872634100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4088670a-45fb-4e44-8893-5eaf9dd27a78", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872634155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea1bba2-5883-4e84-8cd8-190283fdfb5c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872622575200, "endTime": 9872634211700}, "additional": {"logType": "info", "children": [], "durationId": "770e90ef-579c-4f47-a56a-1bddf2e563f1", "parent": "f9d802a2-afb1-49ec-a2e7-e7026d1b1078"}}, {"head": {"id": "f9d802a2-afb1-49ec-a2e7-e7026d1b1078", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872618600000, "endTime": 9872634225400}, "additional": {"logType": "info", "children": ["1e68757d-db7a-42e0-8f83-edc3a876689b", "fea1bba2-5883-4e84-8cd8-190283fdfb5c"], "durationId": "113003a1-b06a-408b-814b-5ac860c2ce57", "parent": "9892a6c9-0f0e-48a1-89ea-cf892e4c08d6"}}, {"head": {"id": "a3c1df3e-ac97-47f0-a9b6-f1fa9ba0a674", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872635489800, "endTime": 9872635502400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8be5fd9-1fc4-4f28-adc0-761290ca6f3a", "logId": "ad0c10cc-eadb-4c41-830a-5453b9886a1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad0c10cc-eadb-4c41-830a-5453b9886a1b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872635489800, "endTime": 9872635502400}, "additional": {"logType": "info", "children": [], "durationId": "a3c1df3e-ac97-47f0-a9b6-f1fa9ba0a674", "parent": "9892a6c9-0f0e-48a1-89ea-cf892e4c08d6"}}, {"head": {"id": "9892a6c9-0f0e-48a1-89ea-cf892e4c08d6", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872612807600, "endTime": 9872635518700}, "additional": {"logType": "info", "children": ["599638fc-d3a4-4456-9f2a-e3a3cac33393", "f9d802a2-afb1-49ec-a2e7-e7026d1b1078", "ad0c10cc-eadb-4c41-830a-5453b9886a1b"], "durationId": "a8be5fd9-1fc4-4f28-adc0-761290ca6f3a", "parent": "13c470ae-0880-4729-a037-e31c440324ec"}}, {"head": {"id": "13c470ae-0880-4729-a037-e31c440324ec", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872612164400, "endTime": 9872635532200}, "additional": {"logType": "info", "children": ["9892a6c9-0f0e-48a1-89ea-cf892e4c08d6"], "durationId": "2349517b-4207-4e48-92c6-d876fa68f99f", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "316807bd-5690-4ced-b540-11ee7ec30603", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872649631300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba207851-7488-48ca-b7f4-33bd2dfdf485", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872650272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a91ebc-0fc8-4123-8d5b-0e3e6a6bb21a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872635541600, "endTime": 9872650351200}, "additional": {"logType": "info", "children": [], "durationId": "381aee62-cea8-4f6b-b8ba-e0189323e533", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "4de4ca72-d183-47e2-ad57-c0a70dbcb80a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872651130800, "endTime": 9872651316000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "logId": "ba348e7f-1bf6-40c2-bb00-3b5aeccb1062"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "043a1e2d-57d9-4161-9e11-b8dd661bafb2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872651154400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba348e7f-1bf6-40c2-bb00-3b5aeccb1062", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872651130800, "endTime": 9872651316000}, "additional": {"logType": "info", "children": [], "durationId": "4de4ca72-d183-47e2-ad57-c0a70dbcb80a", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "50c9cb01-4128-4397-9326-4115c1ec381a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872652576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c101c99d-14a7-4725-872c-db71b4f05ae4", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872658392500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5415f2a9-5567-4a78-9bc6-a9f3d053ac2b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872651327800, "endTime": 9872659151700}, "additional": {"logType": "info", "children": [], "durationId": "b10fd4e1-1ae7-4e13-987d-7af63a96a13a", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "7d77b272-f6e5-470c-9508-dabdf33b099a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872659209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "409cfc1f-b2b2-4d68-9055-d3b3ff92e66d", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872665053200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef27ec48-2143-4f6d-85a8-448029de3a1a", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872665214200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "095a25e7-7793-4e15-afb6-4d6d303f1f05", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872665506400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d251d6c0-50d2-43b1-870e-8d787730e770", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872671183700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4615608-9813-48df-91d6-b1f1c34829d9", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872671634000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1050a798-4efa-4f0a-be59-fad9bb9ceafb", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872659188100, "endTime": 9872675138000}, "additional": {"logType": "info", "children": [], "durationId": "b3f777b7-2e18-4f4b-b849-f77f55c7e9ef", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "b86e8b71-3720-4889-bfbc-6e86dabd4226", "name": "Configuration phase cost:155 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872675209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88249390-1d06-4cf1-9aae-3adb56f10a05", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872675179800, "endTime": 9872675309000}, "additional": {"logType": "info", "children": [], "durationId": "dabdd607-9e7b-4d78-bfeb-4c93a52059a4", "parent": "f080ce33-0224-46fd-bf00-313943e02bff"}}, {"head": {"id": "f080ce33-0224-46fd-bf00-313943e02bff", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872514407500, "endTime": 9872675322500}, "additional": {"logType": "info", "children": ["e25eb482-bb36-4fc2-83aa-13f4420034e3", "6ffd4e34-07db-4f97-a357-709d73c27a2c", "a71768e4-623a-449e-b0a4-ce8ab85d755d", "13c470ae-0880-4729-a037-e31c440324ec", "11a91ebc-0fc8-4123-8d5b-0e3e6a6bb21a", "5415f2a9-5567-4a78-9bc6-a9f3d053ac2b", "1050a798-4efa-4f0a-be59-fad9bb9ceafb", "88249390-1d06-4cf1-9aae-3adb56f10a05", "ba348e7f-1bf6-40c2-bb00-3b5aeccb1062"], "durationId": "a3fd0596-cd19-4f68-8b24-4d5f1e3b1521", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "9fb8e0ba-f5d3-497e-9d51-2d5a8a41d6d6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872676492700, "endTime": 9872676505800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50441fe3-6309-4286-ae63-593c83b08d0c", "logId": "7bdc711c-3587-415f-a784-e4786841aa60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bdc711c-3587-415f-a784-e4786841aa60", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872676492700, "endTime": 9872676505800}, "additional": {"logType": "info", "children": [], "durationId": "9fb8e0ba-f5d3-497e-9d51-2d5a8a41d6d6", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "38e9b7b1-3833-4ef2-aaa7-63895d8b54a5", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872675347400, "endTime": 9872676516000}, "additional": {"logType": "info", "children": [], "durationId": "2e6fae69-dd34-4c9a-8335-52c6b5fdcf2d", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "6f7de53c-a988-49d4-9f5b-36098b26a6aa", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872676522100, "endTime": 9872676536400}, "additional": {"logType": "info", "children": [], "durationId": "0e7d33d5-798b-46cb-bbb6-77191f7215cb", "parent": "80981efc-7943-4cd7-a30e-05d88a4c13f6"}}, {"head": {"id": "80981efc-7943-4cd7-a30e-05d88a4c13f6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872499243500, "endTime": 9872676541200}, "additional": {"logType": "info", "children": ["ad6979a7-2807-4e0c-8f8c-5b7d21358ed0", "f080ce33-0224-46fd-bf00-313943e02bff", "38e9b7b1-3833-4ef2-aaa7-63895d8b54a5", "6f7de53c-a988-49d4-9f5b-36098b26a6aa", "19aa5f33-1b88-4f61-9651-28471598c789", "be461b4a-0541-4189-8d33-18aa19a0455f", "7bdc711c-3587-415f-a784-e4786841aa60"], "durationId": "50441fe3-6309-4286-ae63-593c83b08d0c"}}, {"head": {"id": "a2067460-00df-4ac8-8e30-72ca6f7145e7", "name": "Configuration task cost before running: 182 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872676679500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d54452c8-6c86-4150-aad7-9eb564f575a6", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872682252600, "endTime": 9872691721400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c3015dd8-dbf4-49b9-9e9a-f6d8a63f13c6", "logId": "a9ceba5f-95e0-4993-ad91-7fc896196820"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3015dd8-dbf4-49b9-9e9a-f6d8a63f13c6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872678353100}, "additional": {"logType": "detail", "children": [], "durationId": "d54452c8-6c86-4150-aad7-9eb564f575a6"}}, {"head": {"id": "dae5f65b-b286-404e-b94d-7650e992d9d4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872678831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103308ea-b179-45ca-a902-b4fa4e6af34e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872678959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5d7a98-0720-40f6-a284-678ff1e34149", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872679072300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18a93c43-2380-4b1f-977e-76299de86232", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872682269500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88eca140-7f95-4c8a-8867-6a22d74b89d6", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872691420600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f05720b-2c64-4aa4-bdac-655e86dd2d59", "name": "entry : default@PreBuild cost memory 0.54998779296875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872691592900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ceba5f-95e0-4993-ad91-7fc896196820", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872682252600, "endTime": 9872691721400}, "additional": {"logType": "info", "children": [], "durationId": "d54452c8-6c86-4150-aad7-9eb564f575a6"}}, {"head": {"id": "b16059a3-718c-4b8c-a6b1-05741505435d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872696935500, "endTime": 9872700209500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9f2838f6-97b3-4acb-86d2-ff59ac52f3a1", "logId": "7ae4f8b6-840d-437d-82bf-f8ff0ee114dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f2838f6-97b3-4acb-86d2-ff59ac52f3a1", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872695419700}, "additional": {"logType": "detail", "children": [], "durationId": "b16059a3-718c-4b8c-a6b1-05741505435d"}}, {"head": {"id": "16e1e809-cdb0-49ed-a35d-061c2defa1fd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872695929400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e2223f-3175-4647-a626-c591600670c0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872696052900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab9f53b-e9d1-4096-bcb8-8b43dd419688", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872696130600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34e97e7b-452c-4f7e-9e66-14b7a08cb190", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872696949000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ac1b9cc-bb81-49b7-9cc2-9bf82a16c5d7", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872700024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44331859-5f8d-4cf2-8dfa-242409912020", "name": "entry : default@MergeProfile cost memory 0.13852691650390625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872700128800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae4f8b6-840d-437d-82bf-f8ff0ee114dc", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872696935500, "endTime": 9872700209500}, "additional": {"logType": "info", "children": [], "durationId": "b16059a3-718c-4b8c-a6b1-05741505435d"}}, {"head": {"id": "b8a6f7d8-469f-4b6b-908d-b7e1d34e12a1", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872703423700, "endTime": 9872705928800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bfaa4cf5-02dc-4dec-bec2-6410f38c5122", "logId": "c3f90893-ecd6-4116-a673-e7d7a66e44d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfaa4cf5-02dc-4dec-bec2-6410f38c5122", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872702003700}, "additional": {"logType": "detail", "children": [], "durationId": "b8a6f7d8-469f-4b6b-908d-b7e1d34e12a1"}}, {"head": {"id": "0d2e5431-521b-43e6-ae6d-481f5c1f97c1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872702501600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdcd6200-69ef-4d3b-99fc-d85c663a9871", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872702588300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c183dec4-a5a6-4975-8cc4-d87be45751e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872702673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db8c7b00-a79d-4207-b034-cf318989f465", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872703433700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef902ad7-47a9-4376-927c-0cbe34a8b45f", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872704322400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44636d28-8692-40b2-95cf-30b890e93dbc", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872705737500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d5cd0f-da3f-4b82-8f71-4cf7146022d9", "name": "entry : default@CreateBuildProfile cost memory 0.105316162109375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872705854900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f90893-ecd6-4116-a673-e7d7a66e44d5", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872703423700, "endTime": 9872705928800}, "additional": {"logType": "info", "children": [], "durationId": "b8a6f7d8-469f-4b6b-908d-b7e1d34e12a1"}}, {"head": {"id": "7b110268-7320-4007-b815-e932cd17c033", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709497600, "endTime": 9872709996600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f08212da-062b-40ac-ab2e-6ca2109c09dc", "logId": "1d03ab40-8171-4bf0-b45f-fb586af41573"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f08212da-062b-40ac-ab2e-6ca2109c09dc", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872708012500}, "additional": {"logType": "detail", "children": [], "durationId": "7b110268-7320-4007-b815-e932cd17c033"}}, {"head": {"id": "ed56f4ef-9272-4a47-b845-ab4db568b0db", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872708508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8409fe55-cd53-4e27-ab45-4c91d783a900", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872708601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2fed3a-006e-43db-8489-93abee904f7f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872708657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed31e51a-4671-4f1b-a43e-4010a58010ea", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d4e3687-891d-458d-864b-8620c2a50dac", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709626900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8da46c-aa67-4e0f-9cff-71c0f79f61b3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709683400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4529d448-c17f-4a17-936d-5f3575c2e3f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709729300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "978158ef-b01a-4ad5-ab20-c7ca0ed4b9f6", "name": "entry : default@PreCheckSyscap cost memory 0.05107879638671875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709811400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb6ee103-b315-4669-b74e-93a80fcd0d4b", "name": "runTaskFromQueue task cost before running: 215 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d03ab40-8171-4bf0-b45f-fb586af41573", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872709497600, "endTime": 9872709996600, "totalTime": 413700}, "additional": {"logType": "info", "children": [], "durationId": "7b110268-7320-4007-b815-e932cd17c033"}}, {"head": {"id": "c652d8d3-f3c0-4580-b9d8-cf01cf26d38a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872720630300, "endTime": 9872722172300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "31f70f3a-2ded-401f-afde-f8c030096975", "logId": "c513b584-52a7-46ac-9dd4-90426ccf36f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31f70f3a-2ded-401f-afde-f8c030096975", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872711499100}, "additional": {"logType": "detail", "children": [], "durationId": "c652d8d3-f3c0-4580-b9d8-cf01cf26d38a"}}, {"head": {"id": "2ceb2107-b70b-46ca-9116-5296d6c6103b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872711961200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05aa73b1-7e9c-4139-97cb-fc5d73036164", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872712049700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103a9ed5-024d-4d6c-9b3c-e4de5ce5334f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872712107800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e1b9e4d-451e-4e5e-8d2a-bbdea746a51f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872720653800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58cc1de1-c15b-4431-a65e-7beddcb37574", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872721146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b57dc7-7830-479e-94b7-4f2f94360983", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872721967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec80118c-56c3-4dba-98b5-4301d15607b6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07117462158203125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872722092700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c513b584-52a7-46ac-9dd4-90426ccf36f7", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872720630300, "endTime": 9872722172300}, "additional": {"logType": "info", "children": [], "durationId": "c652d8d3-f3c0-4580-b9d8-cf01cf26d38a"}}, {"head": {"id": "401e1e4b-dd12-4b40-82a9-0c1da2672803", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872725619300, "endTime": 9872726792900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b31d1595-fd12-4083-96e3-d923a9b6c951", "logId": "b2c714aa-ee49-4ad6-9a5e-6c9af2f8e39b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b31d1595-fd12-4083-96e3-d923a9b6c951", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872723822500}, "additional": {"logType": "detail", "children": [], "durationId": "401e1e4b-dd12-4b40-82a9-0c1da2672803"}}, {"head": {"id": "9fb5263e-d122-412b-bc1c-09f64cb2d43f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872724327500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc88683-5580-4f2e-9db4-35c40eab024d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872724428600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5effaf13-af66-4d6e-a17f-0cf46f80eafc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872724487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eb09c52-53a9-45c2-9680-a6caafdeec31", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872725629400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec85281-e482-4931-8261-89a2605727aa", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872726628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23943c95-5e67-453a-9d96-3fd0719df7df", "name": "entry : default@ProcessProfile cost memory 0.06002044677734375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872726726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c714aa-ee49-4ad6-9a5e-6c9af2f8e39b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872725619300, "endTime": 9872726792900}, "additional": {"logType": "info", "children": [], "durationId": "401e1e4b-dd12-4b40-82a9-0c1da2672803"}}, {"head": {"id": "7ecfb850-fc4c-48d0-98f1-2736efb64a12", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872730694500, "endTime": 9872737336100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d10ce1fa-85ab-4aaf-a8e4-67bb81749998", "logId": "e8dfc784-aefd-46a9-bef4-788d4105f714"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d10ce1fa-85ab-4aaf-a8e4-67bb81749998", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872728255400}, "additional": {"logType": "detail", "children": [], "durationId": "7ecfb850-fc4c-48d0-98f1-2736efb64a12"}}, {"head": {"id": "ff93e2f6-0cea-4f4b-b22d-8aa36d8af5ca", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872728717300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b463054a-9b92-4c0e-b3d2-c426f09969bf", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872728801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53151b5-f3cd-466b-89e9-2a82a6804ab7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872728857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22f1a4e-0866-48b7-a15d-d715d95094f0", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872730710500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f1d711-2531-48ca-99af-b261c79375d3", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872736976700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86012e43-5a01-4b3a-abaf-d3394cf4d693", "name": "entry : default@ProcessRouterMap cost memory -1.377349853515625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872737233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8dfc784-aefd-46a9-bef4-788d4105f714", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872730694500, "endTime": 9872737336100}, "additional": {"logType": "info", "children": [], "durationId": "7ecfb850-fc4c-48d0-98f1-2736efb64a12"}}, {"head": {"id": "92c95043-5193-445f-a976-f22ae74ad726", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872744543700, "endTime": 9872747796000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bb04e355-de04-437d-9324-29955c91c0b1", "logId": "671447c2-1e0c-4cb5-bf35-018e0cfa7161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb04e355-de04-437d-9324-29955c91c0b1", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872740601200}, "additional": {"logType": "detail", "children": [], "durationId": "92c95043-5193-445f-a976-f22ae74ad726"}}, {"head": {"id": "f8cf7100-3b99-4739-890f-5c937a779e19", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872741088400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7ca74c7-2ebe-4d2d-92cd-18adf447192b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872741182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172c7e6b-a130-4683-afa4-1869d80c9b07", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872741238900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b345b802-ec95-4c54-9973-adfcd1fa9dc1", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872742203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0651e197-fe98-409c-a9b9-3bea9eed78f4", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872745753100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e425cb92-66bf-4463-89df-25108f8d1a01", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872745952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedf05af-fc75-4bf0-a23a-22463adc86f0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872746032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "665ef276-6ced-4554-962c-9a36abb1ee5b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872746096000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad497e1-8971-4546-861c-daa89bec5ac8", "name": "entry : default@PreviewProcessResource cost memory 0.09453582763671875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872746208600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "993f9fd4-9cf1-450a-9cc6-49a3611a4846", "name": "runTaskFromQueue task cost before running: 253 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872747679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671447c2-1e0c-4cb5-bf35-018e0cfa7161", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872744543700, "endTime": 9872747796000, "totalTime": 1736800}, "additional": {"logType": "info", "children": [], "durationId": "92c95043-5193-445f-a976-f22ae74ad726"}}, {"head": {"id": "5cf59034-6d6f-4c57-8145-4b6486917bc4", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872754700100, "endTime": 9872776731100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9e87fc2a-6ec8-420f-a517-c51f8759e535", "logId": "aa1bb263-b5a8-407f-98ce-273385e63d47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e87fc2a-6ec8-420f-a517-c51f8759e535", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872750561400}, "additional": {"logType": "detail", "children": [], "durationId": "5cf59034-6d6f-4c57-8145-4b6486917bc4"}}, {"head": {"id": "11d91c21-7ded-4270-9b47-9258587f9be1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872751043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05a8d98e-38a3-43ee-a375-5b0b02bfd3f5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872751129800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae3517d0-86fd-42f8-aa86-b24becd5e223", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872751183100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0fa8b64-892e-46f8-ad08-1dcc3c0065aa", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872754725300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d47847-ed78-4130-8a4e-e5cda012435b", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872776493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f39aceb-902f-4356-a0cd-ce72dc684a10", "name": "entry : default@GenerateLoaderJson cost memory -0.829864501953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872776648800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1bb263-b5a8-407f-98ce-273385e63d47", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872754700100, "endTime": 9872776731100}, "additional": {"logType": "info", "children": [], "durationId": "5cf59034-6d6f-4c57-8145-4b6486917bc4"}}, {"head": {"id": "4755bf01-516b-453c-a2f0-7a4dd76ecae5", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872790680500, "endTime": 9873555684300}, "additional": {"children": ["7aeaa9a6-5756-4a00-b0e6-25f6e2d58980", "14646cb6-0cc5-45eb-995f-8b5601ed249d", "e903bc2b-7d1f-4ea1-a17e-b291756ff71a", "1092f623-59ed-497d-ad3a-75373c5e8357", "03f5edb8-369f-4e18-9377-d57f164792d0"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The task snapshots are different."], "detailId": "bdeed30e-21c9-4a5c-8f63-25cb673ebd54", "logId": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdeed30e-21c9-4a5c-8f63-25cb673ebd54", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872785284100}, "additional": {"logType": "detail", "children": [], "durationId": "4755bf01-516b-453c-a2f0-7a4dd76ecae5"}}, {"head": {"id": "68014737-43db-48ad-93b9-398f05d9fcab", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872785951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8562dc2c-a247-46fd-8805-3291a3a91cf8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872786101800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7edcbc69-ce86-45db-ab97-031e89f0b7cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872786179100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e71e952-e2a6-4bef-9ba7-2c28892e355a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872787484900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b64881cf-b9e0-454a-a7d5-02382d3dd0d8", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872790731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f6a8d8-8b07-49de-b1e1-92e2e8f27918", "name": "entry:default@PreviewCompileResource is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872833118900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0de43f9-74dd-4044-b2e2-0247b4681e0b", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872833449900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aeaa9a6-5756-4a00-b0e6-25f6e2d58980", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872834658700, "endTime": 9872860638500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4755bf01-516b-453c-a2f0-7a4dd76ecae5", "logId": "6d335305-b283-401e-abfa-e4b601371aeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d335305-b283-401e-abfa-e4b601371aeb", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872834658700, "endTime": 9872860638500}, "additional": {"logType": "info", "children": [], "durationId": "7aeaa9a6-5756-4a00-b0e6-25f6e2d58980", "parent": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8"}}, {"head": {"id": "192c2826-7cd9-4e8f-938a-93c24e1020cb", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872860913000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14646cb6-0cc5-45eb-995f-8b5601ed249d", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872861823000, "endTime": 9872998416900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4755bf01-516b-453c-a2f0-7a4dd76ecae5", "logId": "9a23a61a-04ba-4b1e-891c-4e8dc51ba86d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4ea5f4f-f05b-4d01-903d-6876170b740d", "name": "current process  memoryUsage: {\n  rss: 166989824,\n  heapTotal: 119140352,\n  heapUsed: 111233192,\n  external: 3100990,\n  arrayBuffers: 94887\n} os memoryUsage :12.57025146484375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872862912100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe25b548-b474-42d9-a257-30e6b2c3526b", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872995160100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a23a61a-04ba-4b1e-891c-4e8dc51ba86d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872861823000, "endTime": 9872998416900}, "additional": {"logType": "info", "children": [], "durationId": "14646cb6-0cc5-45eb-995f-8b5601ed249d", "parent": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8"}}, {"head": {"id": "a4f5d480-bd95-4a18-97c3-bf8c3c4d63a9", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872998610300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e903bc2b-7d1f-4ea1-a17e-b291756ff71a", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872999988400, "endTime": 9873176374200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4755bf01-516b-453c-a2f0-7a4dd76ecae5", "logId": "53f5b008-a0d0-413d-af4c-4f8d7c73285e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d077f8bc-19a7-4bc4-9805-6dd1e512ee19", "name": "current process  memoryUsage: {\n  rss: 167063552,\n  heapTotal: 119140352,\n  heapUsed: 111512104,\n  external: 3109308,\n  arrayBuffers: 103220\n} os memoryUsage :12.57516860961914", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873001211700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f7d07e-bc43-4477-82bc-6358849549f7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873172908100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f5b008-a0d0-413d-af4c-4f8d7c73285e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872999988400, "endTime": 9873176374200}, "additional": {"logType": "info", "children": [], "durationId": "e903bc2b-7d1f-4ea1-a17e-b291756ff71a", "parent": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8"}}, {"head": {"id": "24ac6286-9b48-400a-8bcc-35d7da578e41", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873176537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1092f623-59ed-497d-ad3a-75373c5e8357", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873177872200, "endTime": 9873341967800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4755bf01-516b-453c-a2f0-7a4dd76ecae5", "logId": "33c0d4f2-7d81-49af-a181-d8d1a2e8dfc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d2d1f17-f023-429e-990d-8bd98e8cf721", "name": "current process  memoryUsage: {\n  rss: 167088128,\n  heapTotal: 119140352,\n  heapUsed: 111906568,\n  external: 3109434,\n  arrayBuffers: 103410\n} os memoryUsage :12.592754364013672", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873178899400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13a2968c-ed43-4820-9a5a-b5b56870e81d", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873338319100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c0d4f2-7d81-49af-a181-d8d1a2e8dfc2", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873177872200, "endTime": 9873341967800}, "additional": {"logType": "info", "children": [], "durationId": "1092f623-59ed-497d-ad3a-75373c5e8357", "parent": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8"}}, {"head": {"id": "68ce88ad-d4db-43bc-b245-67a09cc738e2", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873342277100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f5edb8-369f-4e18-9377-d57f164792d0", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873344082700, "endTime": 9873553730500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4755bf01-516b-453c-a2f0-7a4dd76ecae5", "logId": "38fbe236-0f85-4072-97f9-607afcd1219e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03e77ba5-88e6-4cd2-a144-5010a5236a4a", "name": "current process  memoryUsage: {\n  rss: 167157760,\n  heapTotal: 119664640,\n  heapUsed: 110329592,\n  external: 3109560,\n  arrayBuffers: 94547\n} os memoryUsage :12.541175842285156", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873346558000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8bb654b-6a3e-4b31-bd8b-9503023b17d4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873549962500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38fbe236-0f85-4072-97f9-607afcd1219e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873344082700, "endTime": 9873553730500}, "additional": {"logType": "info", "children": [], "durationId": "03f5edb8-369f-4e18-9377-d57f164792d0", "parent": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8"}}, {"head": {"id": "d59f51bf-52f4-45c2-888c-c5ad9dcf231f", "name": "entry : default@PreviewCompileResource cost memory -0.29709625244140625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873555182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf30660-7fa1-4aef-9758-4821224d773c", "name": "runTaskFromQueue task cost before running: 1 s 61 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873555522500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd6e4aa-bd71-48fb-b02a-34d998c23bc8", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872790680500, "endTime": 9873555684300, "totalTime": 764740000}, "additional": {"logType": "info", "children": ["6d335305-b283-401e-abfa-e4b601371aeb", "9a23a61a-04ba-4b1e-891c-4e8dc51ba86d", "53f5b008-a0d0-413d-af4c-4f8d7c73285e", "33c0d4f2-7d81-49af-a181-d8d1a2e8dfc2", "38fbe236-0f85-4072-97f9-607afcd1219e"], "durationId": "4755bf01-516b-453c-a2f0-7a4dd76ecae5"}}, {"head": {"id": "e8c2e501-033b-4d9d-b515-418559c34036", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559477200, "endTime": 9873559938200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "3eadd5bb-9cf6-4b54-b676-44dd4c04a96a", "logId": "da05d765-7a70-4ce6-b3b1-0704f5651e98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3eadd5bb-9cf6-4b54-b676-44dd4c04a96a", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873558681200}, "additional": {"logType": "detail", "children": [], "durationId": "e8c2e501-033b-4d9d-b515-418559c34036"}}, {"head": {"id": "2c35f6cb-437d-4aa2-8608-99d4be9302e6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559220300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5fb57d-2535-44fd-a856-1aac62f02503", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559327200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f09cbd7-1cdf-4dab-b96d-2be1421b9347", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa695b6-f395-404c-bb9b-1e7707e4a808", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559485900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e62f0c-8ee1-4aba-84ab-71c40bc1311d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559597100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f3eea0-9b5f-459c-bcd8-6d97f0da4694", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559674100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef15aff-05e6-4fca-8b7d-0d8ff01376e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559724300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793b51ab-df7c-426a-8bba-1528610ed965", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559797600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfc06aa-1fcf-4d9a-af00-41e0935cd77d", "name": "runTaskFromQueue task cost before running: 1 s 65 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559882400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da05d765-7a70-4ce6-b3b1-0704f5651e98", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873559477200, "endTime": 9873559938200, "totalTime": 380500}, "additional": {"logType": "info", "children": [], "durationId": "e8c2e501-033b-4d9d-b515-418559c34036"}}, {"head": {"id": "633d8a4b-69dd-45c5-9e9f-5d520f39b4e5", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873563416400, "endTime": 9873572966400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "56474c0b-8cb4-4831-8867-6721c6e063e6", "logId": "b08a1c88-3b45-45cc-87be-c30347aaa043"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56474c0b-8cb4-4831-8867-6721c6e063e6", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873561516400}, "additional": {"logType": "detail", "children": [], "durationId": "633d8a4b-69dd-45c5-9e9f-5d520f39b4e5"}}, {"head": {"id": "c2fdaf1f-d73d-4407-8dbe-8a435ec98877", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873562050500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88a4358e-e839-42d1-8fe1-1068af57b136", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873562159000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "730d87ca-2f41-4e02-ae9c-db79980d7e53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873562263100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae6a0c03-d7e0-490c-950c-01eeeab92535", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873563433600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84bd3b46-3b46-47f3-8de2-97f679f9f3be", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873565134700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90030731-ce6a-450b-9c72-190931afcd33", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873565283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af26ca62-d40a-49ca-a183-450d2f264264", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873565387400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f696ac-7dd2-4f72-9a9d-dae6cbb20141", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873565445700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbeced3f-b041-4cb3-84d9-ce4f0cd30d96", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873565502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d7d367f-ed29-4e65-922e-91bbedc0fafc", "name": "entry : default@CopyPreviewProfile cost memory 0.25092315673828125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873572632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b64b874b-7d69-4d06-ba4a-5ddae9a72d99", "name": "runTaskFromQueue task cost before running: 1 s 78 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873572866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08a1c88-3b45-45cc-87be-c30347aaa043", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873563416400, "endTime": 9873572966400, "totalTime": 9403300}, "additional": {"logType": "info", "children": [], "durationId": "633d8a4b-69dd-45c5-9e9f-5d520f39b4e5"}}, {"head": {"id": "51d64e75-8c9f-43a5-b980-b186333fdfce", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577208800, "endTime": 9873578212800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "62907477-50ff-4055-ac08-86a54f329ba4", "logId": "6488fe0d-e1ac-4072-b3a2-cd3a60153dd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62907477-50ff-4055-ac08-86a54f329ba4", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873575495800}, "additional": {"logType": "detail", "children": [], "durationId": "51d64e75-8c9f-43a5-b980-b186333fdfce"}}, {"head": {"id": "965ee3a2-9456-4e46-8293-0af28ca82473", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873576123500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe5dff4-a3c3-4e91-9517-04581cb4ccbb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873576240900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38036843-1289-4e32-9a53-a0934ec5c097", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873576305800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6088e46b-9628-4d1e-867d-83b5265d7518", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac50e5e2-ee07-4d0d-a569-c520614facbc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577379500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933c13e0-e78d-4f65-9e76-6038a0813761", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577486500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6799aec4-afd8-4f33-a562-0260a9febd13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be916a51-9467-43e2-a98e-d4b2d59cc795", "name": "entry : default@ReplacePreviewerPage cost memory 0.0514678955078125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577755500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d3a6f8-b128-4fc0-b1fb-2c9e4370f3db", "name": "runTaskFromQueue task cost before running: 1 s 83 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577907300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6488fe0d-e1ac-4072-b3a2-cd3a60153dd0", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873577208800, "endTime": 9873578212800, "totalTime": 664100}, "additional": {"logType": "info", "children": [], "durationId": "51d64e75-8c9f-43a5-b980-b186333fdfce"}}, {"head": {"id": "8d04103d-2056-4230-9163-5b8554aa9353", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873581597800, "endTime": 9873582037700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "58c936f6-03b4-4a5d-8368-8f0273d20ed0", "logId": "341ecdf1-da86-41f9-ae4a-46c912af4059"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58c936f6-03b4-4a5d-8368-8f0273d20ed0", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873581505000}, "additional": {"logType": "detail", "children": [], "durationId": "8d04103d-2056-4230-9163-5b8554aa9353"}}, {"head": {"id": "8b765d3f-6867-4012-bad0-7a6bcb12dba4", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873581611100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c7e893-bdb3-4b2f-b694-57a70b755f7c", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873581835400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4777b18-4159-4fa5-9bbf-958d420c0f9f", "name": "runTaskFromQueue task cost before running: 1 s 87 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873581961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341ecdf1-da86-41f9-ae4a-46c912af4059", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873581597800, "endTime": 9873582037700, "totalTime": 328600}, "additional": {"logType": "info", "children": [], "durationId": "8d04103d-2056-4230-9163-5b8554aa9353"}}, {"head": {"id": "d5ad8cc6-0252-4ace-9a1f-d86a1132ab18", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873586424200, "endTime": 9873591606300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "c22fd141-2421-4a07-945b-6ecbb6670843", "logId": "7e619a5d-052a-4983-929a-b714d57c3765"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c22fd141-2421-4a07-945b-6ecbb6670843", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873584548000}, "additional": {"logType": "detail", "children": [], "durationId": "d5ad8cc6-0252-4ace-9a1f-d86a1132ab18"}}, {"head": {"id": "fd5118a9-0353-4515-a242-c92a35da43b1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873585314200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a4a8dd1-da55-4889-9531-a02d6eafaf2a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873585446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6758a86b-031c-4548-b84e-b85e6449b86b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873585512400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656ca8d7-50b8-45dd-8ad2-9aa4104d7619", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873586437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b606dca4-6d51-4256-ad40-ff676cd9a030", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873590024900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f32697-e9f5-4c13-a48e-7b4c3f712fb9", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873590192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fb6274-b6a1-4659-a5a6-bc6e88e0d20e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873590304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239ce217-fdad-4b7c-98cb-79d1e0379817", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873590370600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d646266d-8357-440d-bbfc-7f6bcb368893", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873590426600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2cf5d2-0416-4fdd-8193-ad2e6bdf0b78", "name": "entry : default@PreviewUpdateAssets cost memory -1.5111007690429688", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873591410700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3317fc7d-04da-4084-bf13-c3430a378c81", "name": "runTaskFromQueue task cost before running: 1 s 97 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873591538500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e619a5d-052a-4983-929a-b714d57c3765", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873586424200, "endTime": 9873591606300, "totalTime": 5087300}, "additional": {"logType": "info", "children": [], "durationId": "d5ad8cc6-0252-4ace-9a1f-d86a1132ab18"}}, {"head": {"id": "e08439b5-2cfd-458b-8e4d-10eb15195247", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873602013800, "endTime": 9877118043500}, "additional": {"children": ["75e2f41b-aac5-4640-bb09-66c69424446b"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "49ea1c1f-c95c-4d2e-ba64-cba5e21ec824", "logId": "414e4567-d8b3-4ce7-935c-6bd97a98f033"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49ea1c1f-c95c-4d2e-ba64-cba5e21ec824", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873594366900}, "additional": {"logType": "detail", "children": [], "durationId": "e08439b5-2cfd-458b-8e4d-10eb15195247"}}, {"head": {"id": "cbc13510-fb7e-4f4d-8d26-e604aaf8cd43", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873595012600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8a51c5-7b5f-43e8-9733-4bda3f0f141f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873595147800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69fe83c5-ee60-42d8-add7-ff41aef11f94", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873595224200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c197b53-5b58-4437-8a3c-ef1c5747267e", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873602035300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e2f41b-aac5-4640-bb09-66c69424446b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker2", "startTime": 9873632969900, "endTime": 9877117772900}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e08439b5-2cfd-458b-8e4d-10eb15195247", "logId": "a130c83b-9fe8-4ab2-8e0d-fd4ca8019fdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d63985d-eb92-4b5b-83db-d76e47e6250d", "name": "entry : default@PreviewArkTS cost memory -0.47564697265625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873636798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a130c83b-9fe8-4ab2-8e0d-fd4ca8019fdc", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker2", "startTime": 9873632969900, "endTime": 9877117772900}, "additional": {"logType": "error", "children": [], "durationId": "75e2f41b-aac5-4640-bb09-66c69424446b", "parent": "414e4567-d8b3-4ce7-935c-6bd97a98f033"}}, {"head": {"id": "9a156027-6dce-46c1-895d-108098e87a41", "name": "default@PreviewArkTS watch work[2] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877117835600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "414e4567-d8b3-4ce7-935c-6bd97a98f033", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9873602013800, "endTime": 9877118043500}, "additional": {"logType": "error", "children": ["a130c83b-9fe8-4ab2-8e0d-fd4ca8019fdc"], "durationId": "e08439b5-2cfd-458b-8e4d-10eb15195247"}}, {"head": {"id": "a2e2b9b4-4f79-4ad0-bd7e-fd2fd69ae776", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877118173000}, "additional": {"logType": "debug", "children": [], "durationId": "e08439b5-2cfd-458b-8e4d-10eb15195247"}}, {"head": {"id": "c4596d9d-7ee1-4a37-87b2-c57f2e26d639", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/pages/Index.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877118844300}, "additional": {"logType": "debug", "children": [], "durationId": "e08439b5-2cfd-458b-8e4d-10eb15195247"}}, {"head": {"id": "2e9409cc-ebfd-4012-bc41-36c3e99a8329", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132397100, "endTime": 9877132447400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "663cf8ff-8c10-4c9f-8679-ceea5a2deb3f", "logId": "06e78f15-dddc-4089-bd61-5c3a605f519d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06e78f15-dddc-4089-bd61-5c3a605f519d", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132397100, "endTime": 9877132447400}, "additional": {"logType": "info", "children": [], "durationId": "2e9409cc-ebfd-4012-bc41-36c3e99a8329"}}, {"head": {"id": "f81f8307-6b1a-4775-86f5-a12570fd52f5", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9872495175300, "endTime": 9877132581600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 23}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "b8754f5a-0ccd-4761-8bd4-b034ca767fdd", "name": "BUILD FAILED in 4 s 638 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132612200}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "54a24c2b-cec5-445c-91ac-c7b9fa0541d1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f920438-3d49-4a9a-8c09-9ceb3fba7fdd", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132851000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd017b7-aaf5-4a92-9563-bc9dc3d5dc7a", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb25332-d2b2-45f1-8fc0-5cea919f6724", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132946400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3289c9c-4023-4a96-9df5-47d8a3d91c6e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877132992600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972dc1ec-991d-4d46-904d-b0a216b0b362", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877133032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851e8627-d842-4a9c-8ce3-6361310c2387", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877133069100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "298fa43b-a1dd-4e4b-ac73-075ec0ddcd30", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877133830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "969c9d8c-ece0-4e20-9c01-0f5a1159e176", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877147663000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001fa1a2-48b3-489f-aedf-e5deae7afe8b", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877150230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e6a819-19c5-4bcf-806b-f80e98f0973c", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877150572000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53d70056-8de7-4d8e-95e2-28b980172c03", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877167137700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd22daa-94da-4bff-8c56-7e05b4f63e1a", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:35 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877167828000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c5af79-9a0b-4fb7-8f17-8bdbd123dc43", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877168063800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd33ae50-6b84-4dbf-86aa-aaaab569ea94", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877168806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f630825f-11b3-492d-bc8a-1b24ae3f347a", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877169576700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e1c92fa-7622-47d5-bf44-00765227a3ba", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877169937600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46d34d5-0dc2-407e-9ec9-3aa2404afaba", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877170228100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c4236bd-e917-4008-ad66-bc48e04a46e9", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877170588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27d2e23-72fc-476c-bb6c-b00954cdde0c", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877173811900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d76c2ba-200a-4fb1-9d06-34992fa4baec", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877174714600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7064afdb-b597-4f0e-9819-f696ec2ad953", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877175023300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f186b5-20d4-4ce9-bf86-cfbd592e6d86", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877189723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d043a198-745a-45af-a874-fb31429a6670", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877190689100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f9cb70-a40b-4fc5-847e-28ade93649be", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877190986300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4960150-c134-4de5-98c2-f02d935fec8a", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877191266800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf7baf76-3557-4248-b223-3da91f168d89", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877192002600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d2d2c8-f2c3-4cf0-995a-ed5071d29b58", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877195630700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee40e80-d9b4-4f0b-a1cc-61d2759b0918", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877195958100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873fb068-de7e-4c02-b32c-49d9e4699bd1", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877196243800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caedaae9-8dff-4bf5-8359-f8aa7567631c", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877196553400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed399133-34cc-4a56-8827-2f547f4a5b43", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:27 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877196874800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}