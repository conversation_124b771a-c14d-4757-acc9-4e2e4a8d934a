[2025-06-26T11:30:21.209] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.05199432373046875
[2025-06-26T11:30:21.209] [DEBUG] debug-file - runTaskFromQueue task cost before running: 841 ms 
[2025-06-26T11:30:21.210] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:30:21.212] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.212] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.212] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.212] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:30:21.214] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T11:30:21.214] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:30:21.214] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.214] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.214] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.221] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.2299346923828125
[2025-06-26T11:30:21.221] [DEBUG] debug-file - runTaskFromQueue task cost before running: 852 ms 
[2025-06-26T11:30:21.222] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 9 ms 
[2025-06-26T11:30:21.224] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.224] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.224] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.225] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:30:21.225] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.225] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.225] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.225] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.052001953125
[2025-06-26T11:30:21.225] [DEBUG] debug-file - runTaskFromQueue task cost before running: 856 ms 
[2025-06-26T11:30:21.225] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:30:21.227] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:30:21.227] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01215362548828125
[2025-06-26T11:30:21.227] [DEBUG] debug-file - runTaskFromQueue task cost before running: 858 ms 
[2025-06-26T11:30:21.227] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:30:21.229] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.229] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.230] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.230] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:30:21.232] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T11:30:21.233] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T11:30:21.233] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.233] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.233] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.234] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.37823486328125
[2025-06-26T11:30:21.234] [DEBUG] debug-file - runTaskFromQueue task cost before running: 865 ms 
[2025-06-26T11:30:21.235] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 4 ms 
[2025-06-26T11:30:21.237] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:30:21.237] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:30:21.237] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:30:21.242] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:30:21.276] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt' has been changed.
[2025-06-26T11:30:21.276] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 25 ms .
[2025-06-26T11:30:21.293] [DEBUG] debug-file - session manager: binding session. socketId=xip2GOPlvzIiMsU2AAAD, threadId=1@1.
[2025-06-26T11:30:21.295] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 2.076934814453125
[2025-06-26T11:30:24.714] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:30:24.715] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:30:30.863] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.864] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.864] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:30:30.864] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.865] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.865] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.867] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.866] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.868] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.867] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.869] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.868] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.871] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.869] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:30:30.872] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:30:30.871] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:30:30.874] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:30:30.885] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 9 s 637 ms 
[2025-06-26T11:30:30.888] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:30:30.889] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.01202392578125
[2025-06-26T11:30:30.889] [DEBUG] debug-file - runTaskFromQueue task cost before running: 10 s 520 ms 
[2025-06-26T11:30:30.889] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:30:30.904] [DEBUG] debug-file - BUILD SUCCESSFUL in 10 s 535 ms 
[2025-06-26T11:30:30.904] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:30:30.905] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:30:30.905] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:30:30.905] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:30:30.905] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:30:30.905] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:30:30.905] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:30:30.907] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources cache by regenerate.
[2025-06-26T11:30:30.924] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\resources cache by regenerate.
[2025-06-26T11:30:30.927] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:30:30.927] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:30:30.949] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:30:30.951] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:46 ms .
[2025-06-26T11:30:30.951] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:30:30.952] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:30:30.954] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .
[2025-06-26T11:30:30.955] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:30:30.955] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:30:30.956] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-26T11:30:30.960] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:30:30.961] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T11:30:30.961] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T11:30:30.978] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T11:30:30.979] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache from map.
[2025-06-26T11:30:30.979] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:30:30.980] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:30:30.981] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache by regenerate.
[2025-06-26T11:30:30.984] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:30:30.984] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:30:30.985] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:30:30.985] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:30:30.986] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:30 ms .
[2025-06-26T11:30:31.007] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:30:31.008] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:47:26.753] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:47:26.759] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:47:27.709] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:47:27.710] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:47:28.533] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:28.535] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:28.536] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:47:28.535] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:28.538] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:28.538] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-26T11:47:28.590] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-26T11:47:28.762] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:47:28.762] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:47:28.954] [DEBUG] debug-file - session manager: set active socket. socketId=rwJkqeuCAfGaub8BAAAF
[2025-06-26T11:47:28.960] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:47:28.978] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:47:28.985] [DEBUG] debug-file - Cache service initialization finished in 7 ms 
[2025-06-26T11:47:29.001] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:47:29.016] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:47:29.016] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:47:29.027] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:47:29.028] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:47:29.028] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:47:29.029] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:47:29.033] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:47:29.039] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:47:29.051] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:47:29.081] [DEBUG] debug-file - Sdk init in 41 ms 
[2025-06-26T11:47:29.106] [DEBUG] debug-file - Project task initialization takes 22 ms 
[2025-06-26T11:47:29.106] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:47:29.106] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:47:29.107] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:47:29.114] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:47:29.119] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:47:29.119] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:47:29.127] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:47:29.128] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:47:29.128] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:47:29.128] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:47:29.129] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:47:29.129] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:47:29.129] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:47:29.133] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2025-06-26T11:47:29.133] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:47:29.133] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:47:29.133] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:47:29.155] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 20 ms 
[2025-06-26T11:47:29.156] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:47:29.157] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:47:29.165] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:47:29.166] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:47:29.173] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T11:47:29.173] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T11:47:29.174] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:47:29.179] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T11:47:29.179] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T11:47:29.183] [DEBUG] debug-file - Configuration phase cost:192 ms 
[2025-06-26T11:47:29.185] [DEBUG] debug-file - Configuration task cost before running: 221 ms 
[2025-06-26T11:47:29.187] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.187] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.187] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.190] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:47:29.200] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 7 ms .
[2025-06-26T11:47:29.200] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.29140472412109375
[2025-06-26T11:47:29.203] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:47:29.205] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.205] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.205] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.206] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:47:29.209] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:47:29.209] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.13375091552734375
[2025-06-26T11:47:29.210] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:47:29.212] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.212] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.212] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.213] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:47:29.214] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:47:29.216] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:47:29.216] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.1011199951171875
[2025-06-26T11:47:29.216] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:47:29.220] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.220] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.220] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.221] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:47:29.221] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.221] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.221] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.222] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.05078887939453125
[2025-06-26T11:47:29.222] [DEBUG] debug-file - runTaskFromQueue task cost before running: 258 ms 
[2025-06-26T11:47:29.222] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:47:29.224] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.224] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.224] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.234] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:47:29.234] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:47:29.235] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:47:29.235] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0704803466796875
[2025-06-26T11:47:29.235] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:47:29.238] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.238] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.238] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.239] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:47:29.240] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:47:29.240] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.0591888427734375
[2025-06-26T11:47:29.241] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:47:29.243] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.243] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.243] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.245] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:47:29.252] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .
[2025-06-26T11:47:29.252] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.21848297119140625
[2025-06-26T11:47:29.254] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:47:29.258] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.258] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.258] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.260] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:47:29.264] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:47:29.264] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.264] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.264] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.264] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.08972930908203125
[2025-06-26T11:47:29.266] [DEBUG] debug-file - runTaskFromQueue task cost before running: 302 ms 
[2025-06-26T11:47:29.268] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:47:29.270] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.270] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.270] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.275] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:47:29.301] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .
[2025-06-26T11:47:29.301] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.847015380859375
[2025-06-26T11:47:29.312] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:47:29.315] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.315] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.315] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.317] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:47:29.321] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:47:29.368] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 46 ms .
[2025-06-26T11:47:29.368] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.217254638671875
[2025-06-26T11:47:29.369] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-26T11:47:29.371] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.371] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.371] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.371] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:47:29.371] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.372] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.372] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.372] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.05159759521484375
[2025-06-26T11:47:29.372] [DEBUG] debug-file - runTaskFromQueue task cost before running: 408 ms 
[2025-06-26T11:47:29.372] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:47:29.374] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.374] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.374] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.374] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:47:29.377] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:47:29.377] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.09710693359375
[2025-06-26T11:47:29.377] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-26T11:47:29.379] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.379] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.379] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.380] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:47:29.380] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.380] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.380] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.380] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.05173492431640625
[2025-06-26T11:47:29.380] [DEBUG] debug-file - runTaskFromQueue task cost before running: 417 ms 
[2025-06-26T11:47:29.381] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:47:29.382] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:47:29.382] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01181793212890625
[2025-06-26T11:47:29.382] [DEBUG] debug-file - runTaskFromQueue task cost before running: 419 ms 
[2025-06-26T11:47:29.383] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:47:29.385] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.385] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.385] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.386] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:47:29.388] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T11:47:29.388] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.10407257080078125
[2025-06-26T11:47:29.389] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-26T11:47:29.392] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:47:29.392] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:47:29.392] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:47:29.397] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:47:29.435] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\src\main\ets' has been changed.
[2025-06-26T11:47:29.435] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 31 ms .
[2025-06-26T11:47:29.454] [DEBUG] debug-file - session manager: binding session. socketId=rwJkqeuCAfGaub8BAAAF, threadId=1@2.
[2025-06-26T11:47:29.456] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.41294097900390625
[2025-06-26T11:47:33.523] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:47:33.525] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:47:38.160] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:38.162] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:38.161] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:38.163] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:38.480] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:38.482] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:38.757] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:38.759] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:38.872] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:38.874] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:38.948] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:38.950] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:39.088] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:39.090] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:39.219] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:39.221] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:39.268] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:47:39.270] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:47:41.327] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:47:41.328] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:47:41.328] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:47:41.336] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 11 s 935 ms 
[2025-06-26T11:47:41.338] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:47:41.338] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.011688232421875
[2025-06-26T11:47:41.338] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 375 ms 
[2025-06-26T11:47:41.338] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:47:41.357] [DEBUG] debug-file - BUILD SUCCESSFUL in 12 s 393 ms 
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:47:41.357] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-26T11:47:41.358] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-26T11:47:41.358] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-26T11:47:41.362] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:47:41.363] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T11:47:41.364] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T11:47:41.384] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T11:47:41.385] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:47:41.386] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:47:41.386] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:47:41.388] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache from map.
[2025-06-26T11:47:41.388] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:47:41.388] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:47:41.388] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:47:41.389] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:47:41.389] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:32 ms .
[2025-06-26T11:47:41.416] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:47:41.417] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:50:18.394] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-06-26T11:50:18.504] [DEBUG] debug-file - env: daemon=true
[2025-06-26T11:50:18.397] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.3',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-06-26T11:50:19.358] [DEBUG] debug-file - java daemon tryConnect failed Error: connect ECONNREFUSED 127.0.0.1:45050
[2025-06-26T11:50:19.410] [DEBUG] debug-file - java daemon started at port 45050 pid 31380
[2025-06-26T11:50:19.439] [DEBUG] debug-file - session manager: set active socket. socketId=n65__0biSMmBVSkKAAAB
[2025-06-26T11:50:20.447] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:50:20.469] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:50:20.475] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2025-06-26T11:50:20.489] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:50:22.814] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:50:22.814] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:50:23.004] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:50:23.005] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:50:23.005] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:50:23.005] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:50:23.009] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:50:23.018] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:50:23.022] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer
[2025-06-26T11:50:23.027] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2025-06-26T11:50:23.033] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:50:23.061] [DEBUG] debug-file - Sdk init in 42 ms 
[2025-06-26T11:50:23.083] [DEBUG] debug-file - Project task initialization takes 21 ms 
[2025-06-26T11:50:23.083] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:50:23.083] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:50:23.083] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:50:23.090] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:50:23.096] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:50:23.097] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:50:23.111] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:50:23.111] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:50:23.112] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:50:23.112] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:50:23.112] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:50:23.112] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:50:23.112] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:50:23.120] [DEBUG] debug-file - Module entry task initialization takes 5 ms 
[2025-06-26T11:50:23.120] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:50:23.120] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:50:23.120] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:50:23.137] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 16 ms 
[2025-06-26T11:50:23.138] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:50:23.140] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:50:23.150] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:50:23.151] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:50:23.163] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T11:50:23.163] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T11:50:23.165] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:50:23.177] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T11:50:23.177] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T11:50:23.182] [DEBUG] debug-file - Configuration phase cost:2 s 701 ms 
[2025-06-26T11:50:23.185] [DEBUG] debug-file - Configuration task cost before running: 2 s 733 ms 
[2025-06-26T11:50:23.185] [DEBUG] debug-file - Executing task :entry:init
[2025-06-26T11:50:23.186] [DEBUG] debug-file - entry : init cost memory 0.01548004150390625
[2025-06-26T11:50:23.186] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 734 ms 
[2025-06-26T11:50:23.186] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2025-06-26T11:50:23.187] [DEBUG] debug-file - Executing task ::init
[2025-06-26T11:50:23.187] [DEBUG] debug-file - Wallet : init cost memory 0.01280975341796875
[2025-06-26T11:50:23.187] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 735 ms 
[2025-06-26T11:50:23.187] [INFO] debug-file - Finished ::init... after 1 ms 
[2025-06-26T11:50:23.204] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:50:23.210] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
} in this build.
[2025-06-26T11:50:23.218] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-06-26T11:50:23.218] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-06-26T11:50:23.219] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-06-26T11:50:23.221] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:50:23.221] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:50:23.222] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-06-26T11:50:23.225] [DEBUG] debug-file - Cleanup worker 0.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Worker 0 has been cleaned up.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Cleanup worker 1.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Worker 1 has been cleaned up.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Current idle worker size: 0.
[2025-06-26T11:50:23.226] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-26T11:50:23.227] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:50:23.230] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:50:23.230] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:50:23.231] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2025-06-26T11:50:23.231] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2025-06-26T11:51:29.996] [DEBUG] debug-file - session manager: set active socket. socketId=C8kOvgjnkCo70w2lAAAD
[2025-06-26T11:51:30.003] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:51:30.024] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:51:30.029] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2025-06-26T11:51:30.048] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:51:30.055] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:51:30.055] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:51:30.064] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:51:30.064] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:51:30.064] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:51:30.065] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:51:30.067] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:51:30.072] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:51:30.084] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:51:30.109] [DEBUG] debug-file - Sdk init in 37 ms 
[2025-06-26T11:51:30.133] [DEBUG] debug-file - Project task initialization takes 22 ms 
[2025-06-26T11:51:30.133] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:51:30.133] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:51:30.133] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:51:30.141] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:51:30.145] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:51:30.145] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:51:30.153] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:51:30.153] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:51:30.154] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:51:30.154] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:51:30.154] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:51:30.154] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:51:30.154] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:51:30.157] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:51:30.157] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:51:30.157] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:51:30.157] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:51:30.174] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 16 ms 
[2025-06-26T11:51:30.176] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:51:30.178] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:51:30.185] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:51:30.186] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:51:30.193] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T11:51:30.193] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T11:51:30.194] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:51:30.200] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T11:51:30.200] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T11:51:30.203] [DEBUG] debug-file - Configuration phase cost:165 ms 
[2025-06-26T11:51:30.205] [DEBUG] debug-file - Configuration task cost before running: 199 ms 
[2025-06-26T11:51:30.208] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.208] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.208] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.213] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:51:30.227] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 9 ms .
[2025-06-26T11:51:30.227] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.4188079833984375
[2025-06-26T11:51:30.229] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:51:30.232] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.233] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.233] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.234] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:51:30.238] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:51:30.238] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1342315673828125
[2025-06-26T11:51:30.239] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:51:30.241] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.241] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.241] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.242] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:51:30.243] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-26T11:51:30.245] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:51:30.245] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.10507965087890625
[2025-06-26T11:51:30.245] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:51:30.247] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.247] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.247] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.248] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:51:30.248] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.249] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.249] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.249] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.05062103271484375
[2025-06-26T11:51:30.249] [DEBUG] debug-file - runTaskFromQueue task cost before running: 243 ms 
[2025-06-26T11:51:30.249] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:51:30.251] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.251] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.251] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.264] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:51:30.264] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:51:30.265] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:51:30.266] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0708160400390625
[2025-06-26T11:51:30.266] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:51:30.269] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.270] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.270] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.271] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:51:30.273] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-26T11:51:30.273] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.0594482421875
[2025-06-26T11:51:30.273] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:51:30.275] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.276] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.276] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.278] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:51:30.285] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .
[2025-06-26T11:51:30.285] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.21765899658203125
[2025-06-26T11:51:30.287] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:51:30.289] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.289] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.289] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.291] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:51:30.296] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:51:30.296] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.296] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.296] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.296] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.09613037109375
[2025-06-26T11:51:30.298] [DEBUG] debug-file - runTaskFromQueue task cost before running: 291 ms 
[2025-06-26T11:51:30.299] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 3 ms 
[2025-06-26T11:51:30.302] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.302] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.302] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.307] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:51:30.333] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .
[2025-06-26T11:51:30.333] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.7922134399414062
[2025-06-26T11:51:30.340] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:51:30.343] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:30.343] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:30.343] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:30.344] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:51:30.347] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:51:30.409] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T11:51:30.409] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 61 ms .
[2025-06-26T11:51:30.442] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\AppScope\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T11:51:30.445] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 161021952,
  heapTotal: 114888704,
  heapUsed: 107118288,
  external: 3099655,
  arrayBuffers: 93522
} os memoryUsage :11.735076904296875
[2025-06-26T11:51:30.582] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:51:30.586] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T11:51:30.588] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 161271808,
  heapTotal: 115412992,
  heapUsed: 107997968,
  external: 3147101,
  arrayBuffers: 85258
} os memoryUsage :11.64046859741211
[2025-06-26T11:51:30.750] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:51:30.754] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled'
]
[2025-06-26T11:51:30.756] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 161320960,
  heapTotal: 115412992,
  heapUsed: 108494048,
  external: 3099694,
  arrayBuffers: 93640
} os memoryUsage :11.590560913085938
[2025-06-26T11:51:30.888] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:51:30.892] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***t',
  '-r',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T11:51:30.894] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 161378304,
  heapTotal: 115412992,
  heapUsed: 108806760,
  external: 3099820,
  arrayBuffers: 94685
} os memoryUsage :11.58780288696289
[2025-06-26T11:51:31.077] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:51:31.082] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 0.7925262451171875
[2025-06-26T11:51:31.082] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 76 ms 
[2025-06-26T11:51:31.084] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 735 ms 
[2025-06-26T11:51:31.087] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.087] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.087] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.087] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:51:31.087] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.087] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.087] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.087] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.05193328857421875
[2025-06-26T11:51:31.087] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 81 ms 
[2025-06-26T11:51:31.087] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:51:31.091] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.092] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.092] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.093] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:51:31.094] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T11:51:31.094] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:51:31.095] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.095] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.095] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.102] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.2299652099609375
[2025-06-26T11:51:31.102] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 95 ms 
[2025-06-26T11:51:31.102] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 10 ms 
[2025-06-26T11:51:31.105] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.105] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.105] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.106] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:51:31.106] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.106] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.106] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.107] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.05194854736328125
[2025-06-26T11:51:31.107] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 100 ms 
[2025-06-26T11:51:31.107] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:51:31.110] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:51:31.110] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01213836669921875
[2025-06-26T11:51:31.110] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 104 ms 
[2025-06-26T11:51:31.110] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:51:31.113] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.113] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.113] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.114] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:51:31.117] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T11:51:31.117] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T11:51:31.118] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.118] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.118] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.119] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.14666748046875
[2025-06-26T11:51:31.119] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 113 ms 
[2025-06-26T11:51:31.120] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 5 ms 
[2025-06-26T11:51:31.122] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:51:31.123] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:51:31.123] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:51:31.128] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:51:31.164] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt' has been changed.
[2025-06-26T11:51:31.164] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 26 ms .
[2025-06-26T11:51:31.186] [DEBUG] debug-file - session manager: binding session. socketId=C8kOvgjnkCo70w2lAAAD, threadId=1@1.
[2025-06-26T11:51:31.188] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -1.1902313232421875
[2025-06-26T11:51:34.887] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:51:34.889] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:51:39.861] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:39.862] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:40.088] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:40.090] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:40.197] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:40.199] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:40.358] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:40.360] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:40.494] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:40.496] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:40.561] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:40.564] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:40.646] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:51:40.648] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:51:42.713] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:51:42.715] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:51:42.714] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:51:42.723] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 11 s 591 ms 
[2025-06-26T11:51:42.725] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:51:42.725] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.01201629638671875
[2025-06-26T11:51:42.725] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 719 ms 
[2025-06-26T11:51:42.726] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:51:42.736] [DEBUG] debug-file - BUILD SUCCESSFUL in 12 s 730 ms 
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:51:42.737] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:51:42.738] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources cache by regenerate.
[2025-06-26T11:51:42.753] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\resources cache by regenerate.
[2025-06-26T11:51:42.756] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:51:42.757] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:51:42.776] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:51:42.777] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:40 ms .
[2025-06-26T11:51:42.777] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:51:42.778] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:51:42.779] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-26T11:51:42.779] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:51:42.779] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:51:42.780] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-26T11:51:42.783] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:51:42.784] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T11:51:42.784] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T11:51:42.800] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T11:51:42.801] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache from map.
[2025-06-26T11:51:42.802] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:51:42.802] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:51:42.803] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache by regenerate.
[2025-06-26T11:51:42.806] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:51:42.807] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:51:42.807] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:51:42.807] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:51:42.808] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:28 ms .
[2025-06-26T11:51:42.829] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:51:42.830] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:53:23.219] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:53:23.220] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:53:24.552] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:24.554] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:24.554] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:24.556] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.026] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.027] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.027] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.029] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.028] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.030] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.029] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.031] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.030] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.032] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.031] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.033] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.032] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:25.034] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:25.033] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:53:25.035] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:53:40.335] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:53:40.336] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:53:40.389] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-26T11:53:40.409] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:53:40.409] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:53:42.080] [DEBUG] debug-file - session manager: set active socket. socketId=njtPbntBHajOdmumAAAF
[2025-06-26T11:53:42.086] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:53:42.101] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:53:42.104] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-26T11:53:42.116] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:53:42.121] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:53:42.121] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:53:42.127] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:53:42.127] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:53:42.127] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:53:42.127] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:53:42.129] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:53:42.133] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:53:42.141] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:53:42.163] [DEBUG] debug-file - Sdk init in 30 ms 
[2025-06-26T11:53:42.182] [DEBUG] debug-file - Project task initialization takes 19 ms 
[2025-06-26T11:53:42.182] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:53:42.182] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:53:42.182] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:53:42.189] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:53:42.192] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:53:42.192] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:53:42.198] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:53:42.199] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:53:42.199] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:53:42.199] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:53:42.199] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:53:42.199] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:53:42.199] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:53:42.201] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:53:42.202] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:53:42.202] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:53:42.202] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:53:42.217] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 14 ms 
[2025-06-26T11:53:42.218] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:53:42.220] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:53:42.226] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:53:42.226] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:53:42.232] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T11:53:42.232] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T11:53:42.232] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:53:42.237] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T11:53:42.237] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T11:53:42.240] [DEBUG] debug-file - Configuration phase cost:131 ms 
[2025-06-26T11:53:42.241] [DEBUG] debug-file - Configuration task cost before running: 151 ms 
[2025-06-26T11:53:42.243] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.243] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.243] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.245] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:53:42.253] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 5 ms .
[2025-06-26T11:53:42.253] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.2841644287109375
[2025-06-26T11:53:42.255] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:53:42.257] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.257] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.257] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.257] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:53:42.260] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T11:53:42.260] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.13440704345703125
[2025-06-26T11:53:42.261] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:53:42.263] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.263] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.263] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.263] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:53:42.264] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:53:42.266] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T11:53:42.266] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.1013031005859375
[2025-06-26T11:53:42.266] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:53:42.268] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.268] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.268] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.269] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:53:42.269] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.269] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.269] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.269] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.05072784423828125
[2025-06-26T11:53:42.269] [DEBUG] debug-file - runTaskFromQueue task cost before running: 179 ms 
[2025-06-26T11:53:42.269] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:53:42.271] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.271] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.271] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.279] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:53:42.279] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:53:42.280] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:53:42.280] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0708160400390625
[2025-06-26T11:53:42.280] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:53:42.282] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.282] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.282] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.283] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:53:42.284] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-26T11:53:42.284] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.0587615966796875
[2025-06-26T11:53:42.285] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:53:42.286] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.286] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.286] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.288] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:53:42.294] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T11:53:42.294] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.21776580810546875
[2025-06-26T11:53:42.295] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:53:42.297] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.298] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.298] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.299] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:53:42.302] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:53:42.302] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.302] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.302] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.302] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.0896759033203125
[2025-06-26T11:53:42.304] [DEBUG] debug-file - runTaskFromQueue task cost before running: 214 ms 
[2025-06-26T11:53:42.305] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:53:42.307] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.307] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.307] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.311] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:53:42.330] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-06-26T11:53:42.330] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.8418807983398438
[2025-06-26T11:53:42.336] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:53:42.338] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.338] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.338] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.339] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:53:42.342] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:53:42.379] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T11:53:42.379] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 37 ms .
[2025-06-26T11:53:42.412] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\AppScope\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T11:53:42.413] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 173809664,
  heapTotal: 120393728,
  heapUsed: 101772360,
  external: 3084149,
  arrayBuffers: 78016
} os memoryUsage :11.153438568115234
[2025-06-26T11:53:42.535] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:53:42.538] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T11:53:42.540] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 173826048,
  heapTotal: 120393728,
  heapUsed: 102077384,
  external: 3084275,
  arrayBuffers: 78157
} os memoryUsage :11.15850830078125
[2025-06-26T11:53:42.678] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:53:42.680] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled'
]
[2025-06-26T11:53:42.682] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 173838336,
  heapTotal: 120393728,
  heapUsed: 102364632,
  external: 3092593,
  arrayBuffers: 86539
} os memoryUsage :11.164730072021484
[2025-06-26T11:53:42.797] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:53:42.800] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***t',
  '-r',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T11:53:42.802] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 173842432,
  heapTotal: 120393728,
  heapUsed: 102683616,
  external: 3092719,
  arrayBuffers: 87584
} os memoryUsage :11.172325134277344
[2025-06-26T11:53:42.957] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T11:53:42.961] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -9.411323547363281
[2025-06-26T11:53:42.961] [DEBUG] debug-file - runTaskFromQueue task cost before running: 871 ms 
[2025-06-26T11:53:42.962] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 619 ms 
[2025-06-26T11:53:42.964] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.964] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.964] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.964] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:53:42.964] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.964] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.964] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.964] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.052398681640625
[2025-06-26T11:53:42.965] [DEBUG] debug-file - runTaskFromQueue task cost before running: 875 ms 
[2025-06-26T11:53:42.965] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:53:42.967] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.967] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.967] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.968] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:53:42.969] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T11:53:42.970] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:53:42.970] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.970] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.970] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.975] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory -1.4832916259765625
[2025-06-26T11:53:42.975] [DEBUG] debug-file - runTaskFromQueue task cost before running: 886 ms 
[2025-06-26T11:53:42.976] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 8 ms 
[2025-06-26T11:53:42.978] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.978] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.978] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.979] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:53:42.979] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.979] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.979] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.979] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.05181121826171875
[2025-06-26T11:53:42.979] [DEBUG] debug-file - runTaskFromQueue task cost before running: 890 ms 
[2025-06-26T11:53:42.980] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:53:42.981] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:53:42.981] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01180267333984375
[2025-06-26T11:53:42.981] [DEBUG] debug-file - runTaskFromQueue task cost before running: 891 ms 
[2025-06-26T11:53:42.981] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:53:42.983] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.983] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.983] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.984] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:53:42.986] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T11:53:42.986] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T11:53:42.986] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.986] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.986] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.987] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.15358734130859375
[2025-06-26T11:53:42.987] [DEBUG] debug-file - runTaskFromQueue task cost before running: 897 ms 
[2025-06-26T11:53:42.988] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 4 ms 
[2025-06-26T11:53:42.990] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:53:42.990] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:53:42.990] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:53:42.995] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:53:43.029] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt' has been changed.
[2025-06-26T11:53:43.030] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 25 ms .
[2025-06-26T11:53:43.045] [DEBUG] debug-file - session manager: binding session. socketId=njtPbntBHajOdmumAAAF, threadId=1@2.
[2025-06-26T11:53:43.047] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.6046295166015625
[2025-06-26T11:53:46.669] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:53:46.671] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:53:51.138] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:51.140] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:51.140] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:51.141] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.910] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.912] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.911] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.912] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:53:52.913] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.912] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.914] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.913] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.915] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.914] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.917] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.915] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.919] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.916] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:53:52.920] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:53:52.917] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:53:52.922] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:53:52.926] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 9 s 927 ms 
[2025-06-26T11:53:52.928] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:53:52.928] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.01168060302734375
[2025-06-26T11:53:52.928] [DEBUG] debug-file - runTaskFromQueue task cost before running: 10 s 838 ms 
[2025-06-26T11:53:52.928] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:53:52.942] [DEBUG] debug-file - BUILD SUCCESSFUL in 10 s 852 ms 
[2025-06-26T11:53:52.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:53:52.942] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:53:52.943] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:53:52.943] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:53:52.943] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:53:52.943] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:53:52.943] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:53:52.944] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources cache by regenerate.
[2025-06-26T11:53:52.962] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\resources cache by regenerate.
[2025-06-26T11:53:52.965] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T11:53:52.966] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T11:53:52.987] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\r\default cache.
[2025-06-26T11:53:52.988] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:45 ms .
[2025-06-26T11:53:52.988] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T11:53:52.989] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T11:53:52.990] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-26T11:53:52.990] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T11:53:52.990] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T11:53:52.991] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .
[2025-06-26T11:53:52.994] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:53:52.995] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T11:53:52.995] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T11:53:53.011] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T11:53:53.012] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache from map.
[2025-06-26T11:53:53.012] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:53:53.013] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:53:53.013] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache by regenerate.
[2025-06-26T11:53:53.017] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:53:53.018] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:53:53.018] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:53:53.018] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:53:53.019] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:28 ms .
[2025-06-26T11:53:53.048] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:53:53.049] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:56:03.157] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:56:03.160] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:56:06.304] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:56:06.305] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T11:56:06.409] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-26T11:56:06.456] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T11:56:06.456] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T11:56:13.709] [DEBUG] debug-file - session manager: set active socket. socketId=Y4tO1wqr6Ez2T1r_AAAH
[2025-06-26T11:56:13.714] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T11:56:13.727] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T11:56:13.731] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-26T11:56:13.743] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:56:13.748] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:56:13.748] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:56:13.755] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T11:56:13.755] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T11:56:13.755] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T11:56:13.755] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T11:56:13.757] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T11:56:13.761] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T11:56:13.770] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T11:56:13.792] [DEBUG] debug-file - Sdk init in 30 ms 
[2025-06-26T11:56:13.812] [DEBUG] debug-file - Project task initialization takes 19 ms 
[2025-06-26T11:56:13.812] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:56:13.812] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:56:13.812] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T11:56:13.819] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:56:13.823] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T11:56:13.823] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T11:56:13.830] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T11:56:13.830] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T11:56:13.831] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T11:56:13.831] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T11:56:13.831] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T11:56:13.831] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T11:56:13.831] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T11:56:13.834] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T11:56:13.834] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T11:56:13.834] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:56:13.834] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T11:56:13.849] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 14 ms 
[2025-06-26T11:56:13.850] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T11:56:13.852] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T11:56:13.857] [DEBUG] debug-file - load to the disk finished
[2025-06-26T11:56:13.858] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:56:13.863] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T11:56:13.863] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T11:56:13.864] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T11:56:13.868] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T11:56:13.868] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T11:56:13.871] [DEBUG] debug-file - Configuration phase cost:135 ms 
[2025-06-26T11:56:13.873] [DEBUG] debug-file - Configuration task cost before running: 156 ms 
[2025-06-26T11:56:13.874] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.874] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.874] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.877] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T11:56:13.885] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 6 ms .
[2025-06-26T11:56:13.885] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.294586181640625
[2025-06-26T11:56:13.887] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T11:56:13.889] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.889] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.889] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.890] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T11:56:13.892] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-26T11:56:13.892] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1310272216796875
[2025-06-26T11:56:13.893] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T11:56:13.895] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.895] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.895] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.896] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T11:56:13.897] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:56:13.898] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-26T11:56:13.898] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.09677886962890625
[2025-06-26T11:56:13.898] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T11:56:13.900] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.900] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.900] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.901] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T11:56:13.901] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.901] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.901] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.901] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.05088043212890625
[2025-06-26T11:56:13.901] [DEBUG] debug-file - runTaskFromQueue task cost before running: 185 ms 
[2025-06-26T11:56:13.901] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T11:56:13.903] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.903] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.903] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.911] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T11:56:13.912] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T11:56:13.912] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T11:56:13.912] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.07645416259765625
[2025-06-26T11:56:13.912] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T11:56:13.914] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.914] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.915] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.916] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T11:56:13.916] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-26T11:56:13.917] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.058502197265625
[2025-06-26T11:56:13.917] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T11:56:13.918] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.919] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.919] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.920] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T11:56:13.927] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T11:56:13.927] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory -1.3883743286132812
[2025-06-26T11:56:13.928] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T11:56:13.930] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.930] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.930] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.931] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T11:56:13.934] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T11:56:13.935] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.935] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.935] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.935] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.08844757080078125
[2025-06-26T11:56:13.936] [DEBUG] debug-file - runTaskFromQueue task cost before running: 219 ms 
[2025-06-26T11:56:13.938] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T11:56:13.939] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.940] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.940] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.943] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T11:56:13.963] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-06-26T11:56:13.963] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.8736114501953125
[2025-06-26T11:56:13.969] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T11:56:13.971] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:13.971] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:13.971] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:13.972] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T11:56:13.974] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T11:56:14.012] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 38 ms .
[2025-06-26T11:56:14.012] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.30266571044921875
[2025-06-26T11:56:14.013] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewCompileResource...  
[2025-06-26T11:56:14.015] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.015] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.015] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.015] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T11:56:14.015] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.016] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.016] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.016] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.05146026611328125
[2025-06-26T11:56:14.016] [DEBUG] debug-file - runTaskFromQueue task cost before running: 299 ms 
[2025-06-26T11:56:14.016] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T11:56:14.018] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.018] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.018] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.018] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T11:56:14.020] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T11:56:14.020] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.0970306396484375
[2025-06-26T11:56:14.021] [INFO] debug-file - UP-TO-DATE :entry:default@CopyPreviewProfile...  
[2025-06-26T11:56:14.022] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.022] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.023] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.023] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T11:56:14.023] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.023] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.023] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.023] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.05141448974609375
[2025-06-26T11:56:14.024] [DEBUG] debug-file - runTaskFromQueue task cost before running: 307 ms 
[2025-06-26T11:56:14.024] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T11:56:14.025] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T11:56:14.025] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01184844970703125
[2025-06-26T11:56:14.025] [DEBUG] debug-file - runTaskFromQueue task cost before running: 309 ms 
[2025-06-26T11:56:14.025] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T11:56:14.027] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.027] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.027] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.028] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T11:56:14.030] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T11:56:14.030] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.10992431640625
[2025-06-26T11:56:14.031] [INFO] debug-file - UP-TO-DATE :entry:default@PreviewUpdateAssets...  
[2025-06-26T11:56:14.033] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T11:56:14.033] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T11:56:14.033] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T11:56:14.038] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T11:56:14.072] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\src\main\ets' has been changed.
[2025-06-26T11:56:14.072] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 27 ms .
[2025-06-26T11:56:14.086] [DEBUG] debug-file - session manager: binding session. socketId=Y4tO1wqr6Ez2T1r_AAAH, threadId=1@3.
[2025-06-26T11:56:14.087] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory -1.2884521484375
[2025-06-26T11:56:17.917] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:56:17.920] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:56:22.437] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:22.439] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:22.741] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:22.743] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:22.864] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:22.866] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:22.998] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:23.000] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:23.137] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:23.139] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:23.259] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:23.261] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:23.338] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:56:23.341] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:56:25.502] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:56:25.504] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T11:56:25.504] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T11:56:25.509] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 11 s 469 ms 
[2025-06-26T11:56:25.510] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T11:56:25.510] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.01175689697265625
[2025-06-26T11:56:25.510] [DEBUG] debug-file - runTaskFromQueue task cost before running: 11 s 794 ms 
[2025-06-26T11:56:25.510] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T11:56:25.520] [DEBUG] debug-file - BUILD SUCCESSFUL in 11 s 803 ms 
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.
[2025-06-26T11:56:25.520] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.
[2025-06-26T11:56:25.523] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T11:56:25.524] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T11:56:25.524] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T11:56:25.538] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T11:56:25.539] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-26T11:56:25.540] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T11:56:25.540] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T11:56:25.541] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache from map.
[2025-06-26T11:56:25.541] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T11:56:25.541] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T11:56:25.541] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T11:56:25.542] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T11:56:25.542] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:22 ms .
[2025-06-26T11:56:25.563] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T11:56:25.564] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T11:58:50.491] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T11:58:50.493] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T11:58:52.460] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.462] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.462] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.464] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.463] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.465] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.464] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.466] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.465] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.467] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.466] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.468] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.467] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T11:58:52.470] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T11:58:52.468] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T11:58:52.471] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T12:03:09.940] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T12:03:09.943] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T12:03:11.414] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T12:03:11.415] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T12:03:11.552] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-26T12:03:11.571] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T12:03:11.572] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T12:03:13.144] [DEBUG] debug-file - session manager: set active socket. socketId=nC0yjrMbBJ_e_jRvAAAJ
[2025-06-26T12:03:13.149] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T12:03:13.164] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T12:03:13.169] [DEBUG] debug-file - Cache service initialization finished in 5 ms 
[2025-06-26T12:03:13.180] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T12:03:13.188] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T12:03:13.189] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T12:03:13.195] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T12:03:13.195] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T12:03:13.195] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T12:03:13.195] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T12:03:13.197] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T12:03:13.201] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T12:03:13.209] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T12:03:13.233] [DEBUG] debug-file - Sdk init in 32 ms 
[2025-06-26T12:03:13.254] [DEBUG] debug-file - Project task initialization takes 20 ms 
[2025-06-26T12:03:13.254] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T12:03:13.254] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T12:03:13.254] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T12:03:13.260] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T12:03:13.265] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T12:03:13.265] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T12:03:13.272] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T12:03:13.272] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T12:03:13.273] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T12:03:13.273] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T12:03:13.273] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T12:03:13.273] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T12:03:13.274] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T12:03:13.277] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T12:03:13.277] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T12:03:13.277] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T12:03:13.277] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T12:03:13.293] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 15 ms 
[2025-06-26T12:03:13.294] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T12:03:13.296] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T12:03:13.302] [DEBUG] debug-file - load to the disk finished
[2025-06-26T12:03:13.302] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T12:03:13.307] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T12:03:13.308] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T12:03:13.308] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T12:03:13.313] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T12:03:13.313] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T12:03:13.316] [DEBUG] debug-file - Configuration phase cost:143 ms 
[2025-06-26T12:03:13.317] [DEBUG] debug-file - Configuration task cost before running: 165 ms 
[2025-06-26T12:03:13.319] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.319] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.319] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.322] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T12:03:13.330] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 6 ms .
[2025-06-26T12:03:13.330] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.27420806884765625
[2025-06-26T12:03:13.332] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T12:03:13.334] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.335] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.335] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.335] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T12:03:13.338] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T12:03:13.338] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.15102386474609375
[2025-06-26T12:03:13.338] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T12:03:13.340] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.340] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.340] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.341] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T12:03:13.342] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T12:03:13.343] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T12:03:13.343] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.09771728515625
[2025-06-26T12:03:13.344] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T12:03:13.345] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.345] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.345] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.346] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T12:03:13.346] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.346] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.346] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.346] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.05034637451171875
[2025-06-26T12:03:13.346] [DEBUG] debug-file - runTaskFromQueue task cost before running: 194 ms 
[2025-06-26T12:03:13.347] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T12:03:13.348] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.348] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.348] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.357] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T12:03:13.357] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T12:03:13.358] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T12:03:13.358] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06931304931640625
[2025-06-26T12:03:13.358] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T12:03:13.360] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.360] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.360] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.361] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T12:03:13.362] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-26T12:03:13.362] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.056610107421875
[2025-06-26T12:03:13.363] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T12:03:13.364] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.364] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.364] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.366] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T12:03:13.371] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T12:03:13.372] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.2078857421875
[2025-06-26T12:03:13.373] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T12:03:13.375] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.375] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.375] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.376] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T12:03:13.379] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T12:03:13.379] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.379] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.379] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.379] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.08831787109375
[2025-06-26T12:03:13.381] [DEBUG] debug-file - runTaskFromQueue task cost before running: 229 ms 
[2025-06-26T12:03:13.382] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T12:03:13.385] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.386] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.386] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.390] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T12:03:13.411] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-06-26T12:03:13.411] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.8242416381835938
[2025-06-26T12:03:13.417] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T12:03:13.419] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:13.420] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:13.420] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:13.421] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T12:03:13.424] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T12:03:13.463] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T12:03:13.463] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 39 ms .
[2025-06-26T12:03:13.489] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\AppScope\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T12:03:13.491] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 113725440,
  heapTotal: 121667584,
  heapUsed: 112342208,
  external: 3168477,
  arrayBuffers: 162344
} os memoryUsage :12.283000946044922
[2025-06-26T12:03:13.613] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:03:13.615] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T12:03:13.618] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 113946624,
  heapTotal: 121929728,
  heapUsed: 110980968,
  external: 3168603,
  arrayBuffers: 144500
} os memoryUsage :12.259239196777344
[2025-06-26T12:03:13.756] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:03:13.759] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled'
]
[2025-06-26T12:03:13.762] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 113963008,
  heapTotal: 121929728,
  heapUsed: 111253112,
  external: 3150744,
  arrayBuffers: 144690
} os memoryUsage :12.271045684814453
[2025-06-26T12:03:13.885] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:03:13.887] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***t',
  '-r',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T12:03:13.889] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 113979392,
  heapTotal: 121929728,
  heapUsed: 111559864,
  external: 3159062,
  arrayBuffers: 153927
} os memoryUsage :12.272205352783203
[2025-06-26T12:03:14.054] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:03:14.059] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.44203948974609375
[2025-06-26T12:03:14.059] [DEBUG] debug-file - runTaskFromQueue task cost before running: 907 ms 
[2025-06-26T12:03:14.060] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 636 ms 
[2025-06-26T12:03:14.062] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.062] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.062] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.062] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T12:03:14.062] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.062] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.062] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.063] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.05146026611328125
[2025-06-26T12:03:14.063] [DEBUG] debug-file - runTaskFromQueue task cost before running: 911 ms 
[2025-06-26T12:03:14.063] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T12:03:14.065] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.065] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.065] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.065] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T12:03:14.067] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T12:03:14.067] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T12:03:14.067] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.067] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.067] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.072] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.22016143798828125
[2025-06-26T12:03:14.072] [DEBUG] debug-file - runTaskFromQueue task cost before running: 920 ms 
[2025-06-26T12:03:14.072] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 7 ms 
[2025-06-26T12:03:14.075] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.075] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.075] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.076] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T12:03:14.076] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.076] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.076] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.076] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.0541534423828125
[2025-06-26T12:03:14.076] [DEBUG] debug-file - runTaskFromQueue task cost before running: 924 ms 
[2025-06-26T12:03:14.076] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T12:03:14.079] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T12:03:14.079] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01180267333984375
[2025-06-26T12:03:14.079] [DEBUG] debug-file - runTaskFromQueue task cost before running: 927 ms 
[2025-06-26T12:03:14.079] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T12:03:14.081] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.081] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.081] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.082] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T12:03:14.084] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T12:03:14.084] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T12:03:14.084] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.084] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.084] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.085] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.142303466796875
[2025-06-26T12:03:14.086] [DEBUG] debug-file - runTaskFromQueue task cost before running: 933 ms 
[2025-06-26T12:03:14.087] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 4 ms 
[2025-06-26T12:03:14.090] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:03:14.090] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:03:14.090] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:03:14.097] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T12:03:14.134] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt' has been changed.
[2025-06-26T12:03:14.135] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 27 ms .
[2025-06-26T12:03:14.149] [DEBUG] debug-file - session manager: binding session. socketId=nC0yjrMbBJ_e_jRvAAAJ, threadId=1@4.
[2025-06-26T12:03:14.151] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.28255462646484375
[2025-06-26T12:03:17.777] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T12:03:17.778] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T12:03:24.341] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.342] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.342] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T12:03:24.342] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.343] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.342] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.344] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.343] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.345] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.344] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.346] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.345] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.347] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.345] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:03:24.348] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:03:24.346] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T12:03:24.349] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T12:03:24.353] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 10 s 253 ms 
[2025-06-26T12:03:24.354] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T12:03:24.355] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.01168060302734375
[2025-06-26T12:03:24.355] [DEBUG] debug-file - runTaskFromQueue task cost before running: 11 s 203 ms 
[2025-06-26T12:03:24.355] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T12:03:24.365] [DEBUG] debug-file - BUILD SUCCESSFUL in 11 s 212 ms 
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T12:03:24.365] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T12:03:24.367] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources cache by regenerate.
[2025-06-26T12:03:24.381] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\resources cache by regenerate.
[2025-06-26T12:03:24.383] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T12:03:24.384] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T12:03:24.401] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\r\default cache.
[2025-06-26T12:03:24.402] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:37 ms .
[2025-06-26T12:03:24.402] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T12:03:24.403] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T12:03:24.404] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-26T12:03:24.404] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T12:03:24.404] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T12:03:24.405] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .
[2025-06-26T12:03:24.407] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T12:03:24.408] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T12:03:24.408] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T12:03:24.423] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T12:03:24.424] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache from map.
[2025-06-26T12:03:24.424] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T12:03:24.425] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T12:03:24.425] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache by regenerate.
[2025-06-26T12:03:24.429] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T12:03:24.429] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T12:03:24.430] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T12:03:24.430] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T12:03:24.430] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .
[2025-06-26T12:03:24.451] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T12:03:24.451] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T12:05:27.538] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T12:05:27.540] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T12:05:28.665] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:28.667] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:28.667] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:28.670] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:28.668] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:28.671] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.178] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.180] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.180] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.183] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.182] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.185] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.184] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.187] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.185] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.189] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.187] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.192] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.189] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:05:29.194] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:05:29.191] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T12:05:29.197] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T12:06:09.687] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T12:06:09.688] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-26T12:06:09.770] [DEBUG] debug-file - bad codeSnippet [object Object]
[2025-06-26T12:06:09.806] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-26T12:06:09.807] [DEBUG] debug-file - Server currently has 0 watch-worker
[2025-06-26T12:06:10.754] [DEBUG] debug-file - session manager: set active socket. socketId=jx26Cjtypwl3tOieAAAL
[2025-06-26T12:06:10.760] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-26T12:06:10.775] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-26T12:06:10.779] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-26T12:06:10.795] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T12:06:10.800] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T12:06:10.800] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T12:06:10.806] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-26T12:06:10.806] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-26T12:06:10.808] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-26T12:06:10.808] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-26T12:06:10.809] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-26T12:06:10.814] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-26T12:06:10.823] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-26T12:06:10.847] [DEBUG] debug-file - Sdk init in 32 ms 
[2025-06-26T12:06:10.867] [DEBUG] debug-file - Project task initialization takes 20 ms 
[2025-06-26T12:06:10.867] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T12:06:10.867] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T12:06:10.867] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\hvigorfile.ts
[2025-06-26T12:06:10.878] [DEBUG] debug-file - hvigorfile, resolving D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T12:06:10.885] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-26T12:06:10.886] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-26T12:06:10.899] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-26T12:06:10.899] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-26T12:06:10.899] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-26T12:06:10.900] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-26T12:06:10.900] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-26T12:06:10.900] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-26T12:06:10.900] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-26T12:06:10.903] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-26T12:06:10.904] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-26T12:06:10.904] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T12:06:10.904] [DEBUG] debug-file - hvigorfile, resolve finished D:\HarmonyOSProject\Wallet\entry\hvigorfile.ts
[2025-06-26T12:06:10.926] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 19 ms 
[2025-06-26T12:06:10.927] [DEBUG] debug-file - project has submodules:entry
[2025-06-26T12:06:10.929] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-26T12:06:10.935] [DEBUG] debug-file - load to the disk finished
[2025-06-26T12:06:10.935] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T12:06:10.941] [DEBUG] debug-file - Module Wallet Collected Dependency: 
[2025-06-26T12:06:10.941] [DEBUG] debug-file - Module Wallet's total dependency: 0
[2025-06-26T12:06:10.941] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-26T12:06:10.948] [DEBUG] debug-file - Module entry Collected Dependency: D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios
[2025-06-26T12:06:10.948] [DEBUG] debug-file - Module entry's total dependency: 1
[2025-06-26T12:06:10.951] [DEBUG] debug-file - Configuration phase cost:165 ms 
[2025-06-26T12:06:10.953] [DEBUG] debug-file - Configuration task cost before running: 190 ms 
[2025-06-26T12:06:10.955] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.955] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.955] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:10.958] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-26T12:06:10.966] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 6 ms .
[2025-06-26T12:06:10.966] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.30648040771484375
[2025-06-26T12:06:10.969] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-26T12:06:10.971] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.971] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.971] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:10.972] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-26T12:06:10.974] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-26T12:06:10.975] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1282501220703125
[2025-06-26T12:06:10.975] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-26T12:06:10.977] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.977] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.977] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:10.978] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-26T12:06:10.979] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T12:06:10.980] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-26T12:06:10.980] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.10066986083984375
[2025-06-26T12:06:10.980] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-26T12:06:10.984] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.984] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.984] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:10.985] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-26T12:06:10.985] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.985] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.985] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:10.985] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.05034637451171875
[2025-06-26T12:06:10.985] [DEBUG] debug-file - runTaskFromQueue task cost before running: 222 ms 
[2025-06-26T12:06:10.985] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-26T12:06:10.987] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.987] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.987] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:10.995] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-26T12:06:10.996] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-26T12:06:10.997] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-06-26T12:06:10.997] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.06966400146484375
[2025-06-26T12:06:10.997] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
[2025-06-26T12:06:10.999] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:10.999] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:10.999] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.000] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-26T12:06:11.001] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-26T12:06:11.001] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05651092529296875
[2025-06-26T12:06:11.002] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-26T12:06:11.004] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.004] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.004] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.007] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-26T12:06:11.013] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-26T12:06:11.013] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.20728302001953125
[2025-06-26T12:06:11.016] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-26T12:06:11.018] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.018] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.018] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.019] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-26T12:06:11.024] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-26T12:06:11.024] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.024] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.024] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.024] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.08931732177734375
[2025-06-26T12:06:11.025] [DEBUG] debug-file - runTaskFromQueue task cost before running: 263 ms 
[2025-06-26T12:06:11.027] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-26T12:06:11.029] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.029] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.029] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.033] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-26T12:06:11.056] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .
[2025-06-26T12:06:11.056] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.8340606689453125
[2025-06-26T12:06:11.063] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-26T12:06:11.065] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.066] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.066] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.067] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-26T12:06:11.069] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-26T12:06:11.110] [DEBUG] debug-file - entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default' has been changed.
[2025-06-26T12:06:11.110] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource pre-execution cost: 41 ms .
[2025-06-26T12:06:11.138] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\AppScope\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-26T12:06:11.140] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118202368,
  heapTotal: 122454016,
  heapUsed: 104821864,
  external: 3092851,
  arrayBuffers: 86718
} os memoryUsage :12.444690704345703
[2025-06-26T12:06:11.268] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:06:11.272] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-26T12:06:11.274] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118243328,
  heapTotal: 122454016,
  heapUsed: 105114232,
  external: 3092977,
  arrayBuffers: 86859
} os memoryUsage :12.44921875
[2025-06-26T12:06:11.442] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:06:11.445] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled'
]
[2025-06-26T12:06:11.448] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118247424,
  heapTotal: 122454016,
  heapUsed: 105394400,
  external: 3101295,
  arrayBuffers: 95241
} os memoryUsage :12.459827423095703
[2025-06-26T12:06:11.616] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:06:11.620] [DEBUG] debug-file - Use tool [D:\HarmonyOS\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***t',
  '-r',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-i',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\har_compiled',
  '-o',
  'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-26T12:06:11.624] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 118374400,
  heapTotal: 122454016,
  heapUsed: 105719840,
  external: 3101421,
  arrayBuffers: 96286
} os memoryUsage :12.460819244384766
[2025-06-26T12:06:11.796] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-26T12:06:11.801] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 1.11553955078125
[2025-06-26T12:06:11.802] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 39 ms 
[2025-06-26T12:06:11.803] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 733 ms 
[2025-06-26T12:06:11.805] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.805] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.805] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.805] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-26T12:06:11.805] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.806] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.806] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.806] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.05344390869140625
[2025-06-26T12:06:11.806] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 43 ms 
[2025-06-26T12:06:11.807] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-26T12:06:11.810] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.810] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.810] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.810] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-26T12:06:11.812] [DEBUG] debug-file - entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile' does not exist.
[2025-06-26T12:06:11.812] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .
[2025-06-26T12:06:11.812] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.812] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.812] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.817] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.22603607177734375
[2025-06-26T12:06:11.817] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 54 ms 
[2025-06-26T12:06:11.817] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 7 ms 
[2025-06-26T12:06:11.819] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.819] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.819] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.820] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-26T12:06:11.820] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.820] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.820] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.820] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.05141448974609375
[2025-06-26T12:06:11.820] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 58 ms 
[2025-06-26T12:06:11.821] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-26T12:06:11.822] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-26T12:06:11.822] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01180267333984375
[2025-06-26T12:06:11.822] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 59 ms 
[2025-06-26T12:06:11.822] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-26T12:06:11.825] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.825] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.825] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.825] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-26T12:06:11.827] [DEBUG] debug-file - entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json' has been changed.
[2025-06-26T12:06:11.827] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .
[2025-06-26T12:06:11.827] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.827] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.827] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.828] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.14532470703125
[2025-06-26T12:06:11.828] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 66 ms 
[2025-06-26T12:06:11.829] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 3 ms 
[2025-06-26T12:06:11.831] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/axios":"^2.0.9"} at undefined
[2025-06-26T12:06:11.831] [DEBUG] debug-file - jsonObjWithoutParam {"@ohos/hypium":"1.0.6"} at undefined
[2025-06-26T12:06:11.831] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{"@ohos/axios":"^2.0.9"},"devDependencies":{"@ohos/hypium":"1.0.6"}} at undefined
[2025-06-26T12:06:11.837] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-26T12:06:11.868] [DEBUG] debug-file - entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt' has been changed.
[2025-06-26T12:06:11.868] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS pre-execution cost: 23 ms .
[2025-06-26T12:06:11.884] [DEBUG] debug-file - session manager: binding session. socketId=jx26Cjtypwl3tOieAAAL, threadId=1@5.
[2025-06-26T12:06:11.886] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 0.30844879150390625
[2025-06-26T12:06:15.699] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T12:06:15.700] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T12:06:20.294] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:20.295] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:20.295] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:20.297] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:20.296] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:20.298] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:20.574] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:20.575] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:20.828] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:20.829] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:20.945] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:20.946] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:21.036] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:21.037] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:21.197] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:21.198] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:21.354] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:21.356] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:21.468] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:06:21.471] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:06:23.704] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T12:06:23.705] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-26T12:06:23.705] [DEBUG] debug-file - Ark compile finished.
[2025-06-26T12:06:23.711] [INFO] debug-file - Finished :entry:default@PreviewArkTS... after 11 s 871 ms 
[2025-06-26T12:06:23.712] [DEBUG] debug-file - Executing task :entry:PreviewBuild
[2025-06-26T12:06:23.712] [DEBUG] debug-file - entry : PreviewBuild cost memory 0.01168060302734375
[2025-06-26T12:06:23.712] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 949 ms 
[2025-06-26T12:06:23.712] [INFO] debug-file - Finished :entry:PreviewBuild... after 1 ms 
[2025-06-26T12:06:23.723] [DEBUG] debug-file - BUILD SUCCESSFUL in 12 s 960 ms 
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-26T12:06:23.723] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-26T12:06:23.724] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources cache by regenerate.
[2025-06-26T12:06:23.738] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\resources cache by regenerate.
[2025-06-26T12:06:23.742] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-26T12:06:23.742] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default cache.
[2025-06-26T12:06:23.763] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\r\default cache.
[2025-06-26T12:06:23.764] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:41 ms .
[2025-06-26T12:06:23.764] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-26T12:06:23.765] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-26T12:06:23.766] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-26T12:06:23.766] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-26T12:06:23.766] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-26T12:06:23.767] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-26T12:06:23.770] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-26T12:06:23.771] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\index.js cache by regenerate.
[2025-06-26T12:06:23.771] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\oh_modules\.ohpm\@ohos+axios@2.2.6\oh_modules\@ohos\axios\src\main\ets cache by regenerate.
[2025-06-26T12:06:23.786] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-26T12:06:23.787] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache from map.
[2025-06-26T12:06:23.787] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-26T12:06:23.787] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-26T12:06:23.788] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\main\ets cache by regenerate.
[2025-06-26T12:06:23.792] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-26T12:06:23.792] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-26T12:06:23.792] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\HarmonyOSProject\Wallet\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-26T12:06:23.793] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\HarmonyOSProject\Wallet\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-26T12:06:23.793] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .
[2025-06-26T12:06:23.812] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-26T12:06:23.812] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-26T12:07:41.992] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-26T12:07:41.993] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-26T12:07:46.199] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.200] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:46.200] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.201] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:46.201] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.204] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:46.342] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.344] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:46.570] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.573] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:46.644] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.647] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:46.722] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:46.724] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:47.584] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-26T12:07:47.585] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-26T12:07:47.585] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-26T12:07:47.587] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
