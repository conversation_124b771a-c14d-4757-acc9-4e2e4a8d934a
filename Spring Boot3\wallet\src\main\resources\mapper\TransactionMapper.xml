<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.icss.wallet.mapper.TransactionMapper">

    <select id="searchTransactions" resultType="com.icss.wallet.entity.Transaction">
        SELECT t.* FROM transaction t
        JOIN user u ON t.user_id = u.user_id
        <where>
            <if test="phone != null and phone != ''">
                AND u.phone = #{phone}
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

    <!-- Vue前端专用：分页查询交易记录（支持更多筛选条件） -->
    <select id="searchTransactionsPage" resultType="com.icss.wallet.entity.Transaction">
        SELECT t.*, u.phone, u.real_name
        FROM transaction t
        LEFT JOIN user u ON t.user_id = u.user_id
        <where>
            <if test="phone != null and phone != ''">
                AND u.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="type != null">
                AND t.type = #{type}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="transNo != null and transNo != ''">
                AND t.trans_no LIKE CONCAT('%', #{transNo}, '%')
            </if>
            <if test="startDate != null and startDate != ''">
                AND t.create_time &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND t.create_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

    <!-- Vue前端专用：获取总交易金额（只计算成功的交易） -->
    <select id="getTotalAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM transaction WHERE status = 1
    </select>

</mapper>