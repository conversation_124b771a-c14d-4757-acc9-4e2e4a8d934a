import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';
import preferences from '@ohos.data.preferences';

// 定义API响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 定义通用响应类型（兼容R<T>格式）
interface R<T> {
  code: number;
  msg: string;
  data: T;
}

// 定义用户数据类型
interface UserData {
  userId: number;
  phone: string;
  realName?: string;
  username?: string;
  password?: string;
  payPassword?: string;
  payLimit?: number;
  status?: number;
  createTime?: string;
  updateTime?: string;
  lastLoginTime?: string;
}

// 定义用户更新请求类型
interface UserUpdateRequest {
  userId: number;
  payPassword?: string;
  payLimit?: number;
  updateTime: string;
}

interface UserInfo {
  name: string;
  phone: string;
  avatar: Resource;
}

@Entry
@Component
export struct SettingsPage {
  @State userInfo: UserInfo = {
    name: '加载中...',
    phone: '加载中...',
    avatar: $r('app.media.icon_avatar')
  };
  @State showPasswordDialog: boolean = false;
  @State showLimitDialog: boolean = false;
  @State showLogoutDialog: boolean = false;
  @State preferencesStore: preferences.Preferences | null = null;
  @State userId: number = 0;
  @State userName: string = '';
  @State userPhone: string = '';
  @State payLimit: string = '';

  aboutToAppear() {
    this.initPreferences();
  }

  // 初始化preferences并加载用户信息
  async initPreferences() {
    try {
      this.preferencesStore = await preferences.getPreferences(getContext(), 'user_prefs');
      await this.loadUserInfo();
    } catch (error) {
      console.error('初始化preferences失败:', error);
    }
  }

  // 从本地存储加载用户信息
  async loadUserInfo() {
    if (!this.preferencesStore) return;

    try {
      // 从本地存储获取基本信息
      this.userId = await this.preferencesStore.get('userId', 0) as number;
      this.userPhone = await this.preferencesStore.get('userPhone', '') as string;
      this.userName = await this.preferencesStore.get('realName', '') as string ||
        await this.preferencesStore.get('userName', '') as string;
      this.payLimit = await this.preferencesStore.get('payLimit', '') as string;

      console.log('从本地存储加载的用户信息:', {
        userId: this.userId,
        userPhone: this.userPhone,
        userName: this.userName,
        payLimit: this.payLimit
      });

      // 如果有手机号，通过手机号获取最新用户信息
      if (this.userPhone) {
        await this.loadUserInfoByPhone(this.userPhone);
      }

      // 更新显示的用户信息
      this.updateUserInfoDisplay();
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 设置默认显示信息
      this.userInfo = {
        name: '未知用户',
        phone: '未绑定手机',
        avatar: $r('app.media.icon_avatar')
      };
    }
  }

  // 通过手机号加载用户信息
  async loadUserInfoByPhone(phone: string) {
    try {
      // 定义手机号查询用户信息的响应类型
      interface PhoneUserData {
        userId: number;
        username: string;
        realName: string;
        phone: string;
        payLimit?: number;
      }

      const response: AxiosResponse<ApiResponse<PhoneUserData>> = await axios({
        url: 'http://localhost:8091/auth/getUserByPhone',
        method: 'get',
        params: {
          phone: phone
        }
      });

      if (response.data && response.data.code === 0) {
        const userData = response.data.data;
        this.userId = userData.userId;
        this.userName = userData.realName || userData.username || '未知用户';
        this.userPhone = userData.phone || '';
        if (userData.payLimit) {
          this.payLimit = userData.payLimit.toString();
        }

        // 将用户信息保存到本地存储
        if (this.preferencesStore) {
          try {
            await this.preferencesStore.put('userId', userData.userId);
            await this.preferencesStore.put('realName', userData.realName || '');
            await this.preferencesStore.put('userName', userData.username || '');
            await this.preferencesStore.put('userPhone', userData.phone || '');
            if (userData.payLimit) {
              await this.preferencesStore.put('payLimit', userData.payLimit.toString());
            }
            await this.preferencesStore.flush();
          } catch (error) {
            console.error('保存用户信息到本地失败:', error);
          }
        }
      }
    } catch (error) {
      console.error('通过手机号加载用户信息失败:', error);
    }
  }

  // 更新用户信息显示
  updateUserInfoDisplay() {
    // 处理手机号显示（脱敏）
    let displayPhone = this.userPhone;
    if (this.userPhone && this.userPhone.length === 11) {
      displayPhone = this.userPhone.substring(0, 3) + '****' + this.userPhone.substring(7);
    }

    this.userInfo = {
      name: this.userName || '未知用户',
      phone: displayPhone || '未绑定手机',
      avatar: $r('app.media.icon_avatar')
    };

    console.log('更新后的用户信息显示:', this.userInfo);
  }

  // 清除用户信息
  async clearUserInfo() {
    if (!this.preferencesStore) return;

    try {
      await this.preferencesStore.clear();
      await this.preferencesStore.flush();
      console.log('用户信息已清除');
    } catch (error) {
      console.error('清除用户信息失败:', error);
    }
  }

  build() {
    // 根容器
    Column() {
      // 主内容区域
      Column() {
        // 用户信息卡片
        this.UserInfoCard()

        // 设置选项列表
        this.SettingsList()

        // 退出登录按钮
        this.LogoutButton()
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#f5f5f5')

      // 弹窗区域（覆盖在主内容之上）
      if (this.showPasswordDialog) {
        this.PasswordDialog()
      }

      if (this.showLimitDialog) {
        this.LimitDialog()
      }

      if (this.showLogoutDialog) {
        this.LogoutDialog()
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  UserInfoCard() {
    Row() {
      Image(this.userInfo.avatar)
        .width(60)
        .height(60)
        .borderRadius(30)
        .margin({right: 15})

      Column() {
        Text(this.userInfo.name)
          .fontSize(18)
          .fontColor('#333333')
        Text(this.userInfo.phone)
          .fontSize(14)
          .fontColor('#666666')
          .margin({top: 4})
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .margin({top: 20})
    .shadow({radius: 4, color: '#00000010', offsetX: 0, offsetY: 2})
  }

  @Builder
  SettingsList() {
    Column() {
      this.SettingItem('支付密码设置', $r('app.media.password'), () => {
        this.showPasswordDialog = true;
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('支付限额设置', $r('app.media.setting'), () => {
        this.showLimitDialog = true;
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('修改登录密码', $r('app.media.edit'), () => {
        router.pushUrl({url: 'pages/ChangePasswordPage'});
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('关于我们', $r('app.media.about'), () => {
        router.pushUrl({url: 'pages/AboutPage'});
      })
    }
    .width('90%')
    .margin({top: 20})
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .shadow({radius: 4, color: '#00000010', offsetX: 0, offsetY: 2})
  }

  @Builder
  SettingItem(title: string, icon: Resource, onClick: () => void) {
    Row() {
      Image(icon)
        .width(20)
        .height(20)
        .margin({right: 15})

      Text(title)
        .fontSize(16)
        .fontColor('#333333')
        .layoutWeight(1)

      Image($r('app.media.arrow'))
        .width(16)
        .height(16)
    }
    .width('100%')
    .height(60)
    .padding({left: 15, right: 15})
    .onClick(onClick)
  }

  @Builder
  LogoutButton() {
    Button() {
      Text('退出登录')
        .fontSize(16)
        .fontColor('#FF5252')
    }
    .width('90%')
    .height(50)
    .backgroundColor('#ffffff')
    .margin({top: 30})
    .onClick(() => {
      this.showLogoutDialog = true;
    })
  }

  @Builder
  PasswordDialog() {
    Column() {
      Text('设置支付密码')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Text('请输入6位数字支付密码')
        .fontSize(14)
        .fontColor('#666666')
        .margin({bottom: 10})

      TextInput({placeholder: '新支付密码'})
        .width('80%')
        .height(50)
        .type(InputType.Password)
        .maxLength(6)
        .margin({bottom: 10})

      TextInput({placeholder: '确认支付密码'})
        .width('80%')
        .height(50)
        .type(InputType.Password)
        .maxLength(6)
        .margin({bottom: 20})

      Row() {
        Button() {
          Text('取消')
            .fontSize(16)
            .fontColor('#333333')
        }
        .width('40%')
        .height(50)
        .backgroundColor('#f0f0f0')
        .onClick(() => {
          this.showPasswordDialog = false;
        })

        Button() {
          Text('确认')
            .fontSize(16)
            .fontColor(Color.White)
        }
        .width('40%')
        .height(50)
        .backgroundColor('#ee1f98e5')
        .margin({left: 10})
        .onClick(() => {
          this.showPasswordDialog = false;
          promptAction.showToast({message: '支付密码设置成功', duration: 1000});
        })
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '20%'})
    .zIndex(1)
  }

  @Builder
  LimitDialog() {
    Column() {
      Text('支付限额设置')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Text('单笔支付限额')
        .fontSize(16)
        .fontColor('#333333')
          // .alignSelf(HorizontalAlign.Start)
        .margin({left: '10%', bottom: 5})

      TextInput({placeholder: '请输入单笔限额'})
        .width('80%')
        .height(50)
        .type(InputType.Number)
        .margin({bottom: 10})

      Text('每日支付限额')
        .fontSize(16)
        .fontColor('#333333')
          // .alignSelf(HorizontalAlign.Start)
        .margin({left: '10%', bottom: 5})

      TextInput({placeholder: '请输入每日限额'})
        .width('80%')
        .height(50)
        .type(InputType.Number)
        .margin({bottom: 20})

      Row() {
        Button() {
          Text('取消')
            .fontSize(16)
            .fontColor('#333333')
        }
        .width('40%')
        .height(50)
        .backgroundColor('#f0f0f0')
        .onClick(() => {
          this.showLimitDialog = false;
        })

        Button() {
          Text('确认')
            .fontSize(16)
            .fontColor(Color.White)
        }
        .width('40%')
        .height(50)
        .backgroundColor('#ee1f98e5')
        .margin({left: 10})
        .onClick(() => {
          this.showLimitDialog = false;
          promptAction.showToast({message: '限额设置成功', duration: 1000});
        })
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '15%'})
    .zIndex(1)
  }

  @Builder
  LogoutDialog() {
    Column() {
      Text('确认退出登录？')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Button() {
        Text('确认退出')
          .fontSize(16)
          .fontColor(Color.White)
      }
      .width('80%')
      .height(50)
      .backgroundColor('#FF5252')
      .onClick(async () => {
        this.showLogoutDialog = false;
        // 清除本地存储的用户信息
        await this.clearUserInfo();
        router.replaceUrl({url: 'pages/LoginPage'});
      })

      Button() {
        Text('取消')
          .fontSize(16)
          .fontColor('#333333')
      }
      .width('80%')
      .height(50)
      .backgroundColor('#f0f0f0')
      .margin({top: 10})
      .onClick(() => {
        this.showLogoutDialog = false;
      })
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '30%'})
    .zIndex(1)
  }
}