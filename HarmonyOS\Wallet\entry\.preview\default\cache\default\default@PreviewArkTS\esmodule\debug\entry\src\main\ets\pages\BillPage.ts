if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BillPage_Params {
    params?: RouterParams;
    bankName?: string;
    cardNumber?: string;
    cardType?: string;
    billData?: Array<BillItem>;
}
import router from "@ohos:router";
interface RouterParams {
    bankName?: string;
    cardNumber?: string;
    fullCardNumber?: string;
    cardType?: string;
}
interface BillItem {
    date: string;
    amount: number;
    type: 'income' | 'expense';
    description: string;
}
class BillPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.params = router.getParams() as RouterParams;
        this.__bankName = new ObservedPropertySimplePU(this.params.bankName || '', this, "bankName");
        this.__cardNumber = new ObservedPropertySimplePU(this.params.cardNumber || '', this, "cardNumber");
        this.__cardType = new ObservedPropertySimplePU(this.params.cardType || '', this, "cardType");
        this.__billData = new ObservedPropertyObjectPU([], this, "billData");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BillPage_Params) {
        if (params.params !== undefined) {
            this.params = params.params;
        }
        if (params.bankName !== undefined) {
            this.bankName = params.bankName;
        }
        if (params.cardNumber !== undefined) {
            this.cardNumber = params.cardNumber;
        }
        if (params.cardType !== undefined) {
            this.cardType = params.cardType;
        }
        if (params.billData !== undefined) {
            this.billData = params.billData;
        }
    }
    updateStateVars(params: BillPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__bankName.purgeDependencyOnElmtId(rmElmtId);
        this.__cardNumber.purgeDependencyOnElmtId(rmElmtId);
        this.__cardType.purgeDependencyOnElmtId(rmElmtId);
        this.__billData.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__bankName.aboutToBeDeleted();
        this.__cardNumber.aboutToBeDeleted();
        this.__cardType.aboutToBeDeleted();
        this.__billData.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private params: RouterParams;
    private __bankName: ObservedPropertySimplePU<string>;
    get bankName() {
        return this.__bankName.get();
    }
    set bankName(newValue: string) {
        this.__bankName.set(newValue);
    }
    private __cardNumber: ObservedPropertySimplePU<string>;
    get cardNumber() {
        return this.__cardNumber.get();
    }
    set cardNumber(newValue: string) {
        this.__cardNumber.set(newValue);
    }
    private __cardType: ObservedPropertySimplePU<string>;
    get cardType() {
        return this.__cardType.get();
    }
    set cardType(newValue: string) {
        this.__cardType.set(newValue);
    }
    private __billData: ObservedPropertyObjectPU<Array<BillItem>>;
    get billData() {
        return this.__billData.get();
    }
    set billData(newValue: Array<BillItem>) {
        this.__billData.set(newValue);
    }
    aboutToAppear() {
        // 模拟账单数据
        this.billData = [
            { date: '2023-05-15', amount: 128.50, type: 'expense', description: '超市购物' },
            { date: '2023-05-14', amount: 2500.00, type: 'income', description: '工资收入' },
            { date: '2023-05-10', amount: 39.90, type: 'expense', description: '餐饮消费' },
            { date: '2023-05-08', amount: 199.00, type: 'expense', description: '网购消费' },
            { date: '2023-05-05', amount: 500.00, type: 'income', description: '转账收入' }
        ];
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BillPage.ets(41:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BillPage.ets(43:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(50);
            // 顶部导航栏
            Row.backgroundColor('#ffffff');
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BillPage.ets(44:9)", "entry");
            Image.width(24);
            Image.height(24);
            Image.margin({ left: 15, right: 15 });
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.bankName}账单`);
            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(52:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BillPage.ets(63:7)", "entry");
            // 卡片信息
            Column.width('90%');
            // 卡片信息
            Column.padding(15);
            // 卡片信息
            Column.backgroundColor('#f5f5f5');
            // 卡片信息
            Column.borderRadius(8);
            // 卡片信息
            Column.margin({ top: 15 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.bankName);
            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(64:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardNumber);
            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(67:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardType === 'debit' ? '借记卡' : '信用卡');
            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(71:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.margin({ top: 5 });
        }, Text);
        Text.pop();
        // 卡片信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 账单列表
            List.create({ space: 10 });
            List.debugLine("entry/src/main/ets/pages/BillPage.ets(83:7)", "entry");
            // 账单列表
            List.width('100%');
            // 账单列表
            List.layoutWeight(1);
            // 账单列表
            List.margin({ top: 15 });
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.debugLine("entry/src/main/ets/pages/BillPage.ets(85:11)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Row.create();
                            Row.debugLine("entry/src/main/ets/pages/BillPage.ets(86:13)", "entry");
                            Row.width('100%');
                            Row.padding(15);
                            Row.backgroundColor('#ffffff');
                            Row.borderRadius(8);
                        }, Row);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create();
                            Column.debugLine("entry/src/main/ets/pages/BillPage.ets(87:15)", "entry");
                            Column.layoutWeight(1);
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(item.date);
                            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(88:17)", "entry");
                            Text.fontSize(12);
                            Text.fontColor('#999999');
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(item.description);
                            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(91:17)", "entry");
                            Text.fontSize(14);
                            Text.fontColor('#333333');
                            Text.margin({ top: 5 });
                        }, Text);
                        Text.pop();
                        Column.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(`${item.type === 'income' ? '+' : '-'}${item.amount.toFixed(2)}`);
                            Text.debugLine("entry/src/main/ets/pages/BillPage.ets(98:15)", "entry");
                            Text.fontSize(16);
                            Text.fontColor(item.type === 'income' ? '#4CAF50' : '#F44336');
                        }, Text);
                        Text.pop();
                        Row.pop();
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.billData, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        // 账单列表
        List.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BillPage";
    }
}
registerNamedRoute(() => new BillPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/BillPage", pageFullPath: "entry/src/main/ets/pages/BillPage", integratedHsp: "false", moduleType: "followWithHap" });
