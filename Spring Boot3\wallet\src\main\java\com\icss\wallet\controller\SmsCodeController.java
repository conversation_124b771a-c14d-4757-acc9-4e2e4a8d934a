package com.icss.wallet.controller;

import com.icss.wallet.result.R;
import com.icss.wallet.service.SmsCodeService;
import com.icss.wallet.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@CrossOrigin
@RestController
@RequestMapping("/sms")
public class SmsCodeController {
    
    @Autowired
    private SmsCodeService smsCodeService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 发送验证码
     */
    @PostMapping("/send")
    public R sendCode(@RequestParam String phone, @RequestParam Integer type) {
        try {
            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return R.failure("手机号格式不正确");
            }
            
            // 如果是登录验证码，检查用户是否存在
            if (type == 1) {
                if (userService.findByPhone(phone) == null) {
                    return R.failure("用户不存在");
                }
            }
            
            String code = smsCodeService.sendCode(phone, type);
            return R.success("验证码发送成功", code); // 实际项目中不应该返回验证码
        } catch (Exception e) {
            return R.failure("验证码发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证验证码
     */
    @PostMapping("/verify")
    public R verifyCode(@RequestParam String phone, @RequestParam String code, @RequestParam Integer type) {
        try {
            boolean isValid = smsCodeService.verifyCode(phone, code, type);
            if (isValid) {
                return R.success("验证码验证成功");
            } else {
                return R.failure("验证码错误或已过期");
            }
        } catch (Exception e) {
            return R.failure("验证码验证失败: " + e.getMessage());
        }
    }
}
