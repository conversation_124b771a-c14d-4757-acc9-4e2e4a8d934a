package com.icss.wallet.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.wallet.entity.BankAccount;
import com.icss.wallet.entity.Transaction;
import com.icss.wallet.mapper.BankAccountMapper;
import com.icss.wallet.mapper.TransactionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Service
public class TransferService {
    @Autowired
    private BankAccountMapper bankAccountMapper;

    @Autowired
    private TransactionMapper transactionMapper;

    @Transactional
    public void transfer(String fromAccountNumber, String toAccountNumber, BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("转账金额必须大于零");
        }

        BankAccount fromAccount = bankAccountMapper.selectOne(
                new QueryWrapper<BankAccount>()
                        .eq("account_number", fromAccountNumber.trim()));
        if (fromAccount == null) {
            throw new RuntimeException("转出账户不存在");
        }

        if (fromAccount.getStatus() != 1) {
            throw new RuntimeException("转出账户状态异常，无法操作");
        }

        if (fromAccount.getBalance().compareTo(amount) < 0) {
            throw new RuntimeException("转出账户余额不足");
        }

        // 查询方式
        BankAccount toAccount = bankAccountMapper.selectOne(
                new LambdaQueryWrapper<BankAccount>()
                        .eq(BankAccount::getAccountNumber, toAccountNumber.trim()));
        if (toAccount == null) {
            throw new RuntimeException("转入账户不存在");
        }

        if (toAccount.getStatus() != 1) {
            throw new RuntimeException("转入账户状态异常，无法操作");
        }

        if (!fromAccount.getCurrency().equals(toAccount.getCurrency())) {
            throw new RuntimeException("账户币种不一致，无法转账");
        }

        // Update balances
        int rows = bankAccountMapper.decreaseBalance(fromAccountNumber, amount);
        if (rows != 1) {
            throw new RuntimeException("扣款失败");
        }

        rows = bankAccountMapper.increaseBalance(toAccountNumber, amount);
        if (rows != 1) {
            throw new RuntimeException("存款失败");
        }

        // Create transaction record
        Transaction transaction = new Transaction();
        transaction.setTransNo("T" + UUID.randomUUID().toString().replace("-", "").substring(0, 15));
        transaction.setUserId(fromAccount.getUserId());
        transaction.setAmount(amount);
        transaction.setType(3); // 3 represents transfer
        transaction.setStatus(1); // success
        transaction.setTargetInfo(toAccountNumber);
        transaction.setRemark("转账至账户 " + toAccountNumber);
        transaction.setCreateTime(new Date());
        transaction.setUpdateTime(new Date());
        transactionMapper.insert(transaction);
    }
}