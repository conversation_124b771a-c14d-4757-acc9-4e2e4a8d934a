# TransactionsManageOptimized.vue 所需的API接口

## 新添加的API接口

### 1. 分页查询交易记录（支持更多筛选条件）
```
GET /transactions/page
```
**参数：**
- phone (可选): 用户手机号
- type (可选): 交易类型 (1-充值, 2-提现, 3-转账, 4-消费)
- status (可选): 交易状态 (0-处理中, 1-成功, 2-失败)
- transNo (可选): 交易流水号
- startDate (可选): 开始日期
- endDate (可选): 结束日期
- pageNum (默认1): 页码
- pageSize (默认10): 每页大小

**返回示例：**
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1
  }
}
```

### 2. 获取交易统计数据
```
GET /transactions/statistics
```
**返回示例：**
```json
{
  "code": 0,
  "msg": "获取统计数据成功",
  "data": {
    "totalTransactions": 1256,
    "rechargeCount": 314,
    "withdrawCount": 289,
    "transferCount": 356,
    "consumeCount": 297,
    "totalAmount": 1234567.89
  }
}
```

### 3. 获取总交易金额
```
GET /transactions/totalAmount
```
**返回示例：**
```json
{
  "code": 0,
  "msg": "获取总交易金额成功",
  "data": 1234567.89
}
```

### 4. 按类型统计交易数量
```
GET /transactions/count?type=1
```
**参数：**
- type (可选): 交易类型，不传则返回总数量

**返回示例：**
```json
{
  "code": 0,
  "msg": "获取交易数量成功",
  "data": 314
}
```

## 现有API接口（保持不变）

### 1. 基础查询交易记录
```
GET /transactions
```

### 2. 根据用户ID查询交易记录
```
GET /transactions/user/{userId}
```

### 3. HarmonyOS专用接口
```
GET /transactions/user/{userId}/withType
GET /transactions/stats/monthly/{userId}
```

## 测试命令

使用Postman或curl测试：

```bash
# 测试分页查询
GET http://localhost:8091/transactions/page?pageNum=1&pageSize=10

# 测试统计数据
GET http://localhost:8091/transactions/statistics

# 测试总交易金额
GET http://localhost:8091/transactions/totalAmount

# 测试按类型统计
GET http://localhost:8091/transactions/count?type=1
```
