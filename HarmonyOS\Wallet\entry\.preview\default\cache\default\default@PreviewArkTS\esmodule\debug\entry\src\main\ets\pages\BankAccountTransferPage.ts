if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankAccountTransferPage_Params {
    userId?: number;
    userAccounts?: BankAccount[];
    selectedFromAccount?: BankAccount | null;
    toAccountNumber?: string;
    transferAmount?: string;
    transferRemark?: string;
    isLoading?: boolean;
    isLoadingAccounts?: boolean;
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse, AxiosError } from "@normalized:N&&&@ohos/axios/index&2.2.6";
/**
 * API响应结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
/**
 * 分页响应结构
 */
interface PageResponse<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
}
/**
 * 银行账户信息
 */
interface BankAccount {
    accountId: number;
    userId: number;
    accountNumber: string;
    accountType: number; // 1-储蓄账户, 2-支票账户, 3-信用卡账户
    bankName: string;
    branchName?: string;
    accountHolder: string;
    phone: string;
    currency: string;
    balance: number;
    availableBalance: number;
    creditLimit: number;
    status: number; // 0-冻结, 1-正常, 2-注销
    isDefault: number; // 0-非默认, 1-默认
    openDate?: string;
    lastTransactionTime?: string;
    createTime?: string;
    updateTime?: string;
}
/**
 * 转账请求
 */
interface TransferRequest {
    fromAccountNumber: string;
    toAccountNumber: string;
    amount: number;
}
export class BankAccountTransferPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userId = new ObservedPropertySimplePU(1, this, "userId");
        this.__userAccounts = new ObservedPropertyObjectPU([], this, "userAccounts");
        this.__selectedFromAccount = new ObservedPropertyObjectPU(null, this, "selectedFromAccount");
        this.__toAccountNumber = new ObservedPropertySimplePU('', this, "toAccountNumber");
        this.__transferAmount = new ObservedPropertySimplePU('', this, "transferAmount");
        this.__transferRemark = new ObservedPropertySimplePU('', this, "transferRemark");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isLoadingAccounts = new ObservedPropertySimplePU(false, this, "isLoadingAccounts");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankAccountTransferPage_Params) {
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
        if (params.userAccounts !== undefined) {
            this.userAccounts = params.userAccounts;
        }
        if (params.selectedFromAccount !== undefined) {
            this.selectedFromAccount = params.selectedFromAccount;
        }
        if (params.toAccountNumber !== undefined) {
            this.toAccountNumber = params.toAccountNumber;
        }
        if (params.transferAmount !== undefined) {
            this.transferAmount = params.transferAmount;
        }
        if (params.transferRemark !== undefined) {
            this.transferRemark = params.transferRemark;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isLoadingAccounts !== undefined) {
            this.isLoadingAccounts = params.isLoadingAccounts;
        }
    }
    updateStateVars(params: BankAccountTransferPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
        this.__userAccounts.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedFromAccount.purgeDependencyOnElmtId(rmElmtId);
        this.__toAccountNumber.purgeDependencyOnElmtId(rmElmtId);
        this.__transferAmount.purgeDependencyOnElmtId(rmElmtId);
        this.__transferRemark.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingAccounts.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userId.aboutToBeDeleted();
        this.__userAccounts.aboutToBeDeleted();
        this.__selectedFromAccount.aboutToBeDeleted();
        this.__toAccountNumber.aboutToBeDeleted();
        this.__transferAmount.aboutToBeDeleted();
        this.__transferRemark.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isLoadingAccounts.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userId: ObservedPropertySimplePU<number>; // 当前用户ID
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    private __userAccounts: ObservedPropertyObjectPU<BankAccount[]>; // 用户银行账户列表
    get userAccounts() {
        return this.__userAccounts.get();
    }
    set userAccounts(newValue: BankAccount[]) {
        this.__userAccounts.set(newValue);
    }
    private __selectedFromAccount: ObservedPropertyObjectPU<BankAccount | null>; // 选中的转出账户
    get selectedFromAccount() {
        return this.__selectedFromAccount.get();
    }
    set selectedFromAccount(newValue: BankAccount | null) {
        this.__selectedFromAccount.set(newValue);
    }
    private __toAccountNumber: ObservedPropertySimplePU<string>; // 转入账户号
    get toAccountNumber() {
        return this.__toAccountNumber.get();
    }
    set toAccountNumber(newValue: string) {
        this.__toAccountNumber.set(newValue);
    }
    private __transferAmount: ObservedPropertySimplePU<string>; // 转账金额
    get transferAmount() {
        return this.__transferAmount.get();
    }
    set transferAmount(newValue: string) {
        this.__transferAmount.set(newValue);
    }
    private __transferRemark: ObservedPropertySimplePU<string>; // 转账备注
    get transferRemark() {
        return this.__transferRemark.get();
    }
    set transferRemark(newValue: string) {
        this.__transferRemark.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isLoadingAccounts: ObservedPropertySimplePU<boolean>;
    get isLoadingAccounts() {
        return this.__isLoadingAccounts.get();
    }
    set isLoadingAccounts(newValue: boolean) {
        this.__isLoadingAccounts.set(newValue);
    }
    aboutToAppear() {
        // 获取路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params && params['userId']) {
            this.userId = params['userId'] as number;
        }
        this.loadUserBankAccounts();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(80:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f7fa');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        // 转账表单
        this.buildTransferForm.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(87:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(96:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 16, right: 16 });
            Row.backgroundColor('#ffffff');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(97:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行账户转账');
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(104:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位符保持居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(112:7)", "entry");
            // 占位符保持居中
            Row.width(24);
            // 占位符保持居中
            Row.height(24);
        }, Row);
        // 占位符保持居中
        Row.pop();
        Row.pop();
    }
    buildTransferForm(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(124:5)", "entry");
            Column.backgroundColor(Color.White);
            Column.borderRadius(12);
            Column.padding(24);
            Column.margin(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转出账户选择
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(126:7)", "entry");
            // 转出账户选择
            Column.width('100%');
            // 转出账户选择
            Column.alignItems(HorizontalAlign.Start);
            // 转出账户选择
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转出账户');
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(127:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoadingAccounts) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(134:11)", "entry");
                        Row.height(48);
                        Row.width('100%');
                        Row.justifyContent(FlexAlign.Start);
                        Row.alignItems(VerticalAlign.Center);
                        Row.backgroundColor('#f8f9fa');
                        Row.borderRadius(8);
                        Row.padding({ left: 12, right: 12 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(135:13)", "entry");
                        LoadingProgress.width(20);
                        LoadingProgress.height(20);
                        LoadingProgress.color('#4285f4');
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载账户中...');
                        Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(140:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999');
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildAccountSelector.bind(this)();
                });
            }
        }, If);
        If.pop();
        // 转出账户选择
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转入账户号
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(160:7)", "entry");
            // 转入账户号
            Column.width('100%');
            // 转入账户号
            Column.alignItems(HorizontalAlign.Start);
            // 转入账户号
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转入账户号');
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(161:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入转入账户号' });
            TextInput.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(167:9)", "entry");
            TextInput.onChange((value: string) => {
                this.toAccountNumber = value;
            });
            TextInput.height(48);
            TextInput.borderRadius(8);
        }, TextInput);
        // 转入账户号
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账金额
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(179:7)", "entry");
            // 转账金额
            Column.width('100%');
            // 转账金额
            Column.alignItems(HorizontalAlign.Start);
            // 转账金额
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(180:9)", "entry");
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账金额');
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(181:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedFromAccount) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`余额: ¥${this.selectedFromAccount.balance.toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(186:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#999');
                        Text.margin({ left: 8 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(196:9)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.backgroundColor('#f8f9fa');
            Row.borderRadius(8);
            Row.padding({ left: 12, right: 12 });
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('¥');
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(197:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666');
            Text.margin({ right: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入转账金额' });
            TextInput.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(202:11)", "entry");
            TextInput.onChange((value: string) => {
                this.transferAmount = value;
            });
            TextInput.type(InputType.Number);
            TextInput.layoutWeight(1);
            TextInput.borderWidth(0);
            TextInput.backgroundColor('transparent');
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedFromAccount) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('全部');
                        Button.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(212:13)", "entry");
                        Button.type(ButtonType.Normal);
                        Button.backgroundColor('transparent');
                        Button.fontColor('#4285f4');
                        Button.fontSize(12);
                        Button.height(32);
                        Button.onClick(() => {
                            this.transferAmount = this.selectedFromAccount!.balance.toString();
                        });
                    }, Button);
                    Button.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
        // 转账金额
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账备注
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(235:7)", "entry");
            // 转账备注
            Column.width('100%');
            // 转账备注
            Column.alignItems(HorizontalAlign.Start);
            // 转账备注
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账备注');
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(236:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入转账备注（可选）' });
            TextInput.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(242:9)", "entry");
            TextInput.onChange((value: string) => {
                this.transferRemark = value;
            });
            TextInput.height(48);
            TextInput.borderRadius(8);
        }, TextInput);
        // 转账备注
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账按钮
            Button.createWithLabel(this.isLoading ? '转账中...' : '确认转账');
            Button.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(254:7)", "entry");
            // 转账按钮
            Button.type(ButtonType.Capsule);
            // 转账按钮
            Button.backgroundColor('#4285f4');
            // 转账按钮
            Button.width('100%');
            // 转账按钮
            Button.height(48);
            // 转账按钮
            Button.fontSize(16);
            // 转账按钮
            Button.fontWeight(FontWeight.Medium);
            // 转账按钮
            Button.onClick(() => {
                this.handleTransfer();
            });
            // 转账按钮
            Button.enabled(!this.isLoading && this.canTransfer());
        }, Button);
        // 转账按钮
        Button.pop();
        Column.pop();
    }
    buildAccountSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(274:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.userAccounts.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行账户');
                        Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(276:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999');
                        Text.height(48);
                        Text.textAlign(TextAlign.Center);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const account = _item;
                            this.buildAccountItem.bind(this)(account);
                        };
                        this.forEachUpdateFunction(elmtId, this.userAccounts, forEachItemGenFunction, (account: BankAccount) => account.accountId.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildAccountItem(account: BankAccount, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(292:5)", "entry");
            Row.width('100%');
            Row.padding(12);
            Row.backgroundColor(this.selectedFromAccount?.accountId === account.accountId ? '#f0f7ff' : '#f8f9fa');
            Row.borderRadius(8);
            Row.margin({ bottom: 8 });
            Row.onClick(() => {
                this.selectedFromAccount = account;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 选择圆圈
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(294:7)", "entry");
            // 选择圆圈
            Stack.margin({ right: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(295:9)", "entry");
            Circle.width(20);
            Circle.height(20);
            Circle.fill(this.selectedFromAccount?.accountId === account.accountId ? '#4285f4' : 'transparent');
            Circle.border({
                width: 2,
                color: this.selectedFromAccount?.accountId === account.accountId ? '#4285f4' : '#ddd'
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedFromAccount?.accountId === account.accountId) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Circle.create();
                        Circle.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(305:11)", "entry");
                        Circle.width(8);
                        Circle.height(8);
                        Circle.fill('#ffffff');
                    }, Circle);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 选择圆圈
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 账户信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(314:7)", "entry");
            // 账户信息
            Column.layoutWeight(1);
            // 账户信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${account.bankName} (${this.getAccountTypeText(account.accountType)})`);
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(315:9)", "entry");
            Text.fontSize(14);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.maskAccountNumber(account.accountNumber));
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(322:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`余额: ¥${account.balance.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/BankAccountTransferPage.ets(328:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#34a853');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 账户信息
        Column.pop();
        Row.pop();
    }
    // 加载用户银行账户
    loadUserBankAccounts() {
        this.isLoadingAccounts = true;
        axios({
            url: `http://localhost:8091/bankAccounts/user/${this.userId}/active`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<BankAccount[]>>) => {
            console.log('加载用户银行账户结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                // 获取当前用户的正常状态银行账户
                this.userAccounts = res.data.data || [];
                // 如果有默认账户，自动选中
                const defaultAccount = this.userAccounts.find(account => account.isDefault === 1);
                if (defaultAccount) {
                    this.selectedFromAccount = defaultAccount;
                }
                if (this.userAccounts.length === 0) {
                    promptAction.showToast({
                        message: '您还没有可用的银行账户',
                        duration: 2000
                    });
                }
            }
            else {
                promptAction.showToast({
                    message: res.data.msg || '加载账户失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('加载银行账户错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isLoadingAccounts = false;
        });
    }
    // 处理转账
    handleTransfer() {
        // 验证输入
        if (!this.selectedFromAccount) {
            promptAction.showToast({
                message: '请选择转出账户',
                duration: 2000
            });
            return;
        }
        if (!this.toAccountNumber || this.toAccountNumber.trim().length === 0) {
            promptAction.showToast({
                message: '请输入转入账户号',
                duration: 2000
            });
            return;
        }
        if (!this.transferAmount || parseFloat(this.transferAmount) <= 0) {
            promptAction.showToast({
                message: '请输入正确的转账金额',
                duration: 2000
            });
            return;
        }
        const amount = parseFloat(this.transferAmount);
        if (amount > this.selectedFromAccount.balance) {
            promptAction.showToast({
                message: '转账金额超过账户余额',
                duration: 2000
            });
            return;
        }
        if (this.selectedFromAccount.accountNumber === this.toAccountNumber.trim()) {
            promptAction.showToast({
                message: '不能向同一账户转账',
                duration: 2000
            });
            return;
        }
        this.isLoading = true;
        // 发送转账请求
        axios({
            url: 'http://localhost:8091/transfer',
            method: 'post',
            params: {
                fromAccountNumber: this.selectedFromAccount.accountNumber,
                toAccountNumber: this.toAccountNumber.trim(),
                amount: this.transferAmount
            }
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
            console.log('转账结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                // 转账成功，清空表单并返回
                this.clearForm();
                setTimeout(() => {
                    router.back();
                }, 1500);
            }
        }).catch((err: AxiosError) => {
            console.error('转账错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 检查是否可以转账
    canTransfer(): boolean {
        return !!(this.selectedFromAccount &&
            this.toAccountNumber.trim() &&
            this.transferAmount &&
            parseFloat(this.transferAmount) > 0);
    }
    // 清空表单
    clearForm() {
        this.toAccountNumber = '';
        this.transferAmount = '';
        this.transferRemark = '';
        this.selectedFromAccount = null;
    }
    // 获取账户类型文本
    getAccountTypeText(accountType: number): string {
        switch (accountType) {
            case 1: return '储蓄账户';
            case 2: return '支票账户';
            case 3: return '信用卡账户';
            default: return '未知类型';
        }
    }
    // 掩码显示账户号
    maskAccountNumber(accountNumber: string): string {
        if (accountNumber.length <= 8) {
            return accountNumber;
        }
        const start = accountNumber.substring(0, 4);
        const end = accountNumber.substring(accountNumber.length - 4);
        const middle = '*'.repeat(accountNumber.length - 8);
        return `${start}${middle}${end}`;
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankAccountTransferPage";
    }
}
registerNamedRoute(() => new BankAccountTransferPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/BankAccountTransferPage", pageFullPath: "entry/src/main/ets/pages/BankAccountTransferPage", integratedHsp: "false", moduleType: "followWithHap" });
