{"version": "2.0", "ppid": 13468, "events": [{"head": {"id": "e22e83e0-50ac-470c-a34b-90ba757a72a4", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443680437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59102e20-7394-456e-b7f8-e757540fbabb", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12784672974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8b5f4f-f278-4cca-9caa-840f2f7cd8d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12784674629500, "endTime": 12784674652800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "6b9cbefe-e3b0-40a9-9a82-04b9530ce21a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b9cbefe-e3b0-40a9-9a82-04b9530ce21a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12784674629500, "endTime": 12784674652800}, "additional": {"logType": "info", "children": [], "durationId": "cc8b5f4f-f278-4cca-9caa-840f2f7cd8d1"}}, {"head": {"id": "591e5674-4fc6-4904-9b2b-2961605cfa61", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786004476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ab8552-b34c-447f-96af-6cac49ddb0ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786005500700, "endTime": 12786005518800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "5699f167-7773-4915-9a29-4da00fdec754"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5699f167-7773-4915-9a29-4da00fdec754", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786005500700, "endTime": 12786005518800}, "additional": {"logType": "info", "children": [], "durationId": "14ab8552-b34c-447f-96af-6cac49ddb0ac"}}, {"head": {"id": "f36f351c-a37a-428f-8875-5f2e10cd0b70", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786005598700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b47c8b-e863-4547-8e87-9d54751a40d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786006406300, "endTime": 12786006420200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "3f8a8710-d05b-492f-b912-2b080868d0ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f8a8710-d05b-492f-b912-2b080868d0ba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786006406300, "endTime": 12786006420200}, "additional": {"logType": "info", "children": [], "durationId": "b7b47c8b-e863-4547-8e87-9d54751a40d6"}}, {"head": {"id": "38e50da5-d681-4e6c-b033-73bf836aa955", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786006492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96eaf012-fa21-4cbb-be46-ed3782d8faef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786007281700, "endTime": 12786007297000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "380ca7a2-b0e6-48c0-8aca-9e9038bf73f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "380ca7a2-b0e6-48c0-8aca-9e9038bf73f3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786007281700, "endTime": 12786007297000}, "additional": {"logType": "info", "children": [], "durationId": "96eaf012-fa21-4cbb-be46-ed3782d8faef"}}, {"head": {"id": "48435460-afa0-449e-9a05-9be354a2bc7f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786068146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d85f57-9bab-4074-8722-d26c2e953196", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786069432500, "endTime": 12786069457900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "3785ea2b-3049-4f56-96e4-221d7443c8bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3785ea2b-3049-4f56-96e4-221d7443c8bc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786069432500, "endTime": 12786069457900}, "additional": {"logType": "info", "children": [], "durationId": "c5d85f57-9bab-4074-8722-d26c2e953196"}}, {"head": {"id": "749f41aa-e636-4178-a3cb-ec3aab6a921a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786508681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe4626d-ec04-4cd3-8ee9-b32cbf0960d0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786509772800, "endTime": 12786509789100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "b8f11e4c-faa5-4ecb-94a8-21d27e0e5307"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8f11e4c-faa5-4ecb-94a8-21d27e0e5307", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786509772800, "endTime": 12786509789100}, "additional": {"logType": "info", "children": [], "durationId": "fbe4626d-ec04-4cd3-8ee9-b32cbf0960d0"}}, {"head": {"id": "91f29ba3-6211-4e14-8811-bec20c76a076", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786509866000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b8b8fcd-0350-4370-a672-382cabfc6a3e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786510603700, "endTime": 12786510616600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "3612a8c6-ce25-404c-a168-9350d47293ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3612a8c6-ce25-404c-a168-9350d47293ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786510603700, "endTime": 12786510616600}, "additional": {"logType": "info", "children": [], "durationId": "1b8b8fcd-0350-4370-a672-382cabfc6a3e"}}, {"head": {"id": "a2a4dd20-ade7-4479-bfd5-8464699af483", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786510682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c68972d-1875-4fd5-a68c-968b3d58a342", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786511399500, "endTime": 12786511413400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "84702032-3bc3-48d5-97cf-9cec6dbd99c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84702032-3bc3-48d5-97cf-9cec6dbd99c4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786511399500, "endTime": 12786511413400}, "additional": {"logType": "info", "children": [], "durationId": "6c68972d-1875-4fd5-a68c-968b3d58a342"}}, {"head": {"id": "4c4bf3af-0935-4fe5-992e-3fe13abbf6b0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786511494900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d9f88e-011a-4ad5-b2c7-623fcdf9b651", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786512268400, "endTime": 12786512285100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "70836952-3c50-4f16-b5a6-af6797cd0774"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70836952-3c50-4f16-b5a6-af6797cd0774", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786512268400, "endTime": 12786512285100}, "additional": {"logType": "info", "children": [], "durationId": "96d9f88e-011a-4ad5-b2c7-623fcdf9b651"}}, {"head": {"id": "a4cddf44-dfc7-476a-b12a-360055fa1d1f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786512360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429c0b39-419a-41e4-8bb9-a80ae4b9ece2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786513184600, "endTime": 12786513199200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "77bd148f-601c-46e0-b402-6dc34685ca1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77bd148f-601c-46e0-b402-6dc34685ca1e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786513184600, "endTime": 12786513199200}, "additional": {"logType": "info", "children": [], "durationId": "429c0b39-419a-41e4-8bb9-a80ae4b9ece2"}}, {"head": {"id": "b3aa7236-70a3-47ee-bc2d-7b6a05f0bbd4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786513268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0efc3682-f025-4f06-a959-329d798e2e2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786513979800, "endTime": 12786513990200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "59747c35-f8e7-4ea2-a474-5f83083627c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59747c35-f8e7-4ea2-a474-5f83083627c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786513979800, "endTime": 12786513990200}, "additional": {"logType": "info", "children": [], "durationId": "0efc3682-f025-4f06-a959-329d798e2e2c"}}, {"head": {"id": "fa3a51d5-2da5-444c-9781-8d99bb6edb3d", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786514043000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ea4795-7bd4-4ce1-b89c-1cfd4fbf0c2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786514719900, "endTime": 12786514730300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "c963204f-0e78-4604-9c54-fdd6810ed6bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c963204f-0e78-4604-9c54-fdd6810ed6bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12786514719900, "endTime": 12786514730300}, "additional": {"logType": "info", "children": [], "durationId": "51ea4795-7bd4-4ce1-b89c-1cfd4fbf0c2c"}}, {"head": {"id": "eaf9b8ee-0405-4390-8913-b4025c309a39", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12805130853400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "269b91bc-eae6-4aff-a73c-3e048a3bc016", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12805131822500, "endTime": 12805131839400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "4f0f84d4-ce1e-4d53-8831-7df8eab76a50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f0f84d4-ce1e-4d53-8831-7df8eab76a50", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12805131822500, "endTime": 12805131839400}, "additional": {"logType": "info", "children": [], "durationId": "269b91bc-eae6-4aff-a73c-3e048a3bc016"}}, {"head": {"id": "1d846d90-854f-4b27-b5fe-0183474148d6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806403399500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bbac4cd-49a2-4feb-9487-f48619a07b90", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806404637500, "endTime": 12806404659100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "307ba8fd-3306-44a4-9731-9de2d03d1cb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "307ba8fd-3306-44a4-9731-9de2d03d1cb6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806404637500, "endTime": 12806404659100}, "additional": {"logType": "info", "children": [], "durationId": "8bbac4cd-49a2-4feb-9487-f48619a07b90"}}, {"head": {"id": "887a018b-7c6d-4738-829c-00dc9f061565", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806404753400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be4bd0b-88cd-42b6-aa14-3d791157b644", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806405556000, "endTime": 12806405571500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "d3ce5417-c1e3-4904-a85a-6d4027cbe6c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3ce5417-c1e3-4904-a85a-6d4027cbe6c9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806405556000, "endTime": 12806405571500}, "additional": {"logType": "info", "children": [], "durationId": "0be4bd0b-88cd-42b6-aa14-3d791157b644"}}, {"head": {"id": "a66327a7-49d0-449b-8c83-cfc8f9d2e5ff", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806405643400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27243b4e-4467-4e5f-8de6-ecf3dfd3697a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806406426200, "endTime": 12806406441900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "e2394fa9-e7e3-465f-9a52-80f4f632177b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2394fa9-e7e3-465f-9a52-80f4f632177b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806406426200, "endTime": 12806406441900}, "additional": {"logType": "info", "children": [], "durationId": "27243b4e-4467-4e5f-8de6-ecf3dfd3697a"}}, {"head": {"id": "da9c07f1-cd99-4385-a3b9-627a25b44ab0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806454578800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f3105a-0d85-4f60-b7cd-7c7d007a6307", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806455867300, "endTime": 12806455890400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "2b580ff8-4ef7-40d5-8e64-b1eb73d525bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b580ff8-4ef7-40d5-8e64-b1eb73d525bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806455867300, "endTime": 12806455890400}, "additional": {"logType": "info", "children": [], "durationId": "e1f3105a-0d85-4f60-b7cd-7c7d007a6307"}}, {"head": {"id": "2278dccb-5768-481f-87a4-432c15b22169", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806965778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e91f102f-6bea-4885-b3ca-33ef228d5bb9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806967032700, "endTime": 12806967060000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "16e83211-02c8-440c-8d3e-fa8fc926c4e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16e83211-02c8-440c-8d3e-fa8fc926c4e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806967032700, "endTime": 12806967060000}, "additional": {"logType": "info", "children": [], "durationId": "e91f102f-6bea-4885-b3ca-33ef228d5bb9"}}, {"head": {"id": "12910000-f780-4c66-bdb2-17ed596bd97c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806967193900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00f28dd8-2da0-460a-b4d7-33ac2472e762", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806968430800, "endTime": 12806968451800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "29641ea9-1c1d-4725-8f1f-55025b62dba0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29641ea9-1c1d-4725-8f1f-55025b62dba0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806968430800, "endTime": 12806968451800}, "additional": {"logType": "info", "children": [], "durationId": "00f28dd8-2da0-460a-b4d7-33ac2472e762"}}, {"head": {"id": "6a0e2f3f-35b4-4ba7-86aa-891d5dfb6264", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806968548200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9141c721-02d2-407f-bf17-27edf950d925", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806969504600, "endTime": 12806969520500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "bb106804-5e98-4a36-9516-8963f4195179"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb106804-5e98-4a36-9516-8963f4195179", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806969504600, "endTime": 12806969520500}, "additional": {"logType": "info", "children": [], "durationId": "9141c721-02d2-407f-bf17-27edf950d925"}}, {"head": {"id": "4878d015-67bb-459b-85f0-59d0cb643f59", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806969599600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79025d0c-c9fa-4c60-ac35-26879fd52575", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806970438400, "endTime": 12806970452800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "38908e2f-9049-42ea-8388-3898323f1832"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38908e2f-9049-42ea-8388-3898323f1832", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806970438400, "endTime": 12806970452800}, "additional": {"logType": "info", "children": [], "durationId": "79025d0c-c9fa-4c60-ac35-26879fd52575"}}, {"head": {"id": "a57582ca-ff6d-42f9-8138-92d0a9eb27ea", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806970528900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "171adce7-d23b-4797-92f3-e1b0aa9928a6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806972039200, "endTime": 12806972059100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "04da9c3e-8e56-445e-be24-535a7bb99935"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04da9c3e-8e56-445e-be24-535a7bb99935", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806972039200, "endTime": 12806972059100}, "additional": {"logType": "info", "children": [], "durationId": "171adce7-d23b-4797-92f3-e1b0aa9928a6"}}, {"head": {"id": "2068b359-e8b1-47b7-a8fc-1b927d3cda42", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806972152100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf815964-1cd7-4a03-b6de-73e5a1d961c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806972998200, "endTime": 12806973011600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "6dabe183-628e-4ea6-bf73-03308fc34f87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dabe183-628e-4ea6-bf73-03308fc34f87", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806972998200, "endTime": 12806973011600}, "additional": {"logType": "info", "children": [], "durationId": "cf815964-1cd7-4a03-b6de-73e5a1d961c3"}}, {"head": {"id": "227c9462-dd25-4857-91bf-830cad7d01f9", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806973076700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0f088e-a400-4713-b285-77bb33647198", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806973833000, "endTime": 12806973845400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "fede64a5-3b2b-4dc4-92ea-6c828b7111e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fede64a5-3b2b-4dc4-92ea-6c828b7111e4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12806973833000, "endTime": 12806973845400}, "additional": {"logType": "info", "children": [], "durationId": "ad0f088e-a400-4713-b285-77bb33647198"}}, {"head": {"id": "411b3e48-475f-4655-be85-78be447bb42d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12906386351900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a540fe-3505-4e3f-8a9a-064b7bd78304", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12906386578500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49c11ca-dda6-4c53-9fac-a72b07ec5b86", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907395377600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907402653900, "endTime": 12907574110400}, "additional": {"children": ["94dc84a3-9467-4fe2-aeb9-36df9b2bf603", "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "fdf18bc5-30f3-4310-90b0-c75c8dd0d801", "6b9d64b7-7e12-4272-9191-84d07a85af64", "aecddd95-ca89-43ff-ab75-e3fc3f7dd680", "1b240818-38d0-48e9-abb4-ab319f6e9623", "03012c22-11e2-4ba6-891e-df0d1951c25b"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a3fd4158-bd0e-4637-8981-86e31017acad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94dc84a3-9467-4fe2-aeb9-36df9b2bf603", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907402655300, "endTime": 12907414890500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "97b00b34-83a6-4d82-9381-b9cd7dab0044"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907414916700, "endTime": 12907572648900}, "additional": {"children": ["0be83ed7-63d9-49fc-adee-5cfeba4ddf06", "bf0b1b6c-cb11-41b1-8304-b58f51879039", "8d8d4b70-cd10-427a-a810-4e0734f72234", "9b53667d-7a74-4f0c-855d-e9ec03376a08", "345c273a-f3c6-4750-88d1-66a10b025804", "2f477c3e-f92c-4fec-a561-fb9bd8a7998e", "84cdc881-b538-4334-8024-a3cce81afcef", "639d8207-812e-49bf-a68a-87f752bbfddc", "e810272e-a665-44c4-8cb6-5003738cc92f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdf18bc5-30f3-4310-90b0-c75c8dd0d801", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907572678500, "endTime": 12907574070200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "f82946b1-a1e5-4b53-a710-1c2f25c213de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b9d64b7-7e12-4272-9191-84d07a85af64", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907574076800, "endTime": 12907574105000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "5cabf36e-491e-4cd1-9897-7c2a8efe273c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aecddd95-ca89-43ff-ab75-e3fc3f7dd680", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907405452300, "endTime": 12907405492500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "b0dd2911-a633-442c-b395-77700dbdb641"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0dd2911-a633-442c-b395-77700dbdb641", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907405452300, "endTime": 12907405492500}, "additional": {"logType": "info", "children": [], "durationId": "aecddd95-ca89-43ff-ab75-e3fc3f7dd680", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "1b240818-38d0-48e9-abb4-ab319f6e9623", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907410176700, "endTime": 12907410193400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "6dcdff5b-7074-4917-9b8c-ce8bfb35c3be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dcdff5b-7074-4917-9b8c-ce8bfb35c3be", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907410176700, "endTime": 12907410193400}, "additional": {"logType": "info", "children": [], "durationId": "1b240818-38d0-48e9-abb4-ab319f6e9623", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "23610e8f-015d-4c19-bb55-f1afb7b34085", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907410247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb73c43-fc1b-4101-abe4-0e3fce88fc35", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907414725900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b00b34-83a6-4d82-9381-b9cd7dab0044", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907402655300, "endTime": 12907414890500}, "additional": {"logType": "info", "children": [], "durationId": "94dc84a3-9467-4fe2-aeb9-36df9b2bf603", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "0be83ed7-63d9-49fc-adee-5cfeba4ddf06", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907420076500, "endTime": 12907420088600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "e2537100-7412-4997-8d4e-827070d28e6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf0b1b6c-cb11-41b1-8304-b58f51879039", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907420116600, "endTime": 12907424860500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "92ecb7a0-339a-42a4-81a8-54f479476742"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d8d4b70-cd10-427a-a810-4e0734f72234", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907424876800, "endTime": 12907512974300}, "additional": {"children": ["f3a1c83d-9a90-41be-94d6-a457bded3e64", "e41eb24e-033f-4327-b1d8-fe65bdf28e7b", "c1c1da13-f158-4f3d-82cd-ffb31e7bfa02"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "2994407c-0b7a-405a-84b7-48e8f099a69b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b53667d-7a74-4f0c-855d-e9ec03376a08", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512990100, "endTime": 12907537398900}, "additional": {"children": ["3c7965cf-9991-46d0-a69e-2b352461d796"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "b4f1088e-bc4f-4698-90da-cc10630bc367"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "345c273a-f3c6-4750-88d1-66a10b025804", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907537408400, "endTime": 12907551416400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "15d96f87-9c70-406e-a722-6b085407f4b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f477c3e-f92c-4fec-a561-fb9bd8a7998e", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907552318500, "endTime": 12907559949000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "3ee6037e-0d22-42e1-b288-5d1c9397724c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84cdc881-b538-4334-8024-a3cce81afcef", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907559966600, "endTime": 12907572498000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "1867fc23-da8c-4170-967d-d37e13273935"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "639d8207-812e-49bf-a68a-87f752bbfddc", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907572519300, "endTime": 12907572635600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "018def63-001d-4cc6-839c-8f94216bcc62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2537100-7412-4997-8d4e-827070d28e6a", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907420076500, "endTime": 12907420088600}, "additional": {"logType": "info", "children": [], "durationId": "0be83ed7-63d9-49fc-adee-5cfeba4ddf06", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "92ecb7a0-339a-42a4-81a8-54f479476742", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907420116600, "endTime": 12907424860500}, "additional": {"logType": "info", "children": [], "durationId": "bf0b1b6c-cb11-41b1-8304-b58f51879039", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "f3a1c83d-9a90-41be-94d6-a457bded3e64", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907425506200, "endTime": 12907425524300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d8d4b70-cd10-427a-a810-4e0734f72234", "logId": "0bea32ab-fdc9-4aff-a42b-846b2cadf283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0bea32ab-fdc9-4aff-a42b-846b2cadf283", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907425506200, "endTime": 12907425524300}, "additional": {"logType": "info", "children": [], "durationId": "f3a1c83d-9a90-41be-94d6-a457bded3e64", "parent": "2994407c-0b7a-405a-84b7-48e8f099a69b"}}, {"head": {"id": "e41eb24e-033f-4327-b1d8-fe65bdf28e7b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907427401200, "endTime": 12907512350700}, "additional": {"children": ["6bb965fa-89ca-4b55-8a41-bd4d687f4017", "1e4e00f5-a22a-4a8d-a0dd-339f76d08c07"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d8d4b70-cd10-427a-a810-4e0734f72234", "logId": "4d20ffd2-109a-4248-a8ec-70ebbea4fe63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bb965fa-89ca-4b55-8a41-bd4d687f4017", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907427403200, "endTime": 12907435048600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e41eb24e-033f-4327-b1d8-fe65bdf28e7b", "logId": "1fb7c9dc-8726-4eca-9e41-cb4f75c89bea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e4e00f5-a22a-4a8d-a0dd-339f76d08c07", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907435076300, "endTime": 12907512336800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e41eb24e-033f-4327-b1d8-fe65bdf28e7b", "logId": "f4b6ddad-1776-49cb-aa38-00e8bab6b6e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03b21463-7a56-4778-91ad-f74e8c41037f", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907427408100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb6c6f59-d3f9-401d-ad79-4088ebba4633", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907434873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb7c9dc-8726-4eca-9e41-cb4f75c89bea", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907427403200, "endTime": 12907435048600}, "additional": {"logType": "info", "children": [], "durationId": "6bb965fa-89ca-4b55-8a41-bd4d687f4017", "parent": "4d20ffd2-109a-4248-a8ec-70ebbea4fe63"}}, {"head": {"id": "784e4ba4-3901-4116-899c-511957bc0fa7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907435097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0ac0f7-6d80-4e0d-ae98-2034f5f392c0", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907444100500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77d2c45e-73fa-4a17-babc-7c2b3e407478", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907444264200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4880d6a-0bc3-418d-8ac9-b94475e1d69c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907444471800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb5d344-51d9-4316-8906-750ecc9d9d67", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907444600200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea7f519-1c0b-4288-b48e-84ef8d4b8ed1", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907446891200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea98565f-51b9-4d43-a4ad-fff134797af0", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907452032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c41793a-2222-4750-b915-9970024d75e0", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907461623300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71fbadb0-2b0f-4d79-a20b-e6be9595a6cd", "name": "Sdk init in 37 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907489593600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a278d0e-8c75-448a-abd3-0586ebca2cb6", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907489799700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 14}, "markType": "other"}}, {"head": {"id": "80674dda-72ec-49fb-8b23-b7f9170a0ec3", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907489866700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 14}, "markType": "other"}}, {"head": {"id": "e2746957-7a73-4ce7-9902-2427c2e114c2", "name": "Project task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9ac17b-754a-49a3-9da9-77d54c885e08", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512148500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad7be6a-4327-4caf-b49d-d81c2e1135dd", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512215500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78a00eb-a2bb-4437-832c-de1a0c33f949", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b6ddad-1776-49cb-aa38-00e8bab6b6e6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907435076300, "endTime": 12907512336800}, "additional": {"logType": "info", "children": [], "durationId": "1e4e00f5-a22a-4a8d-a0dd-339f76d08c07", "parent": "4d20ffd2-109a-4248-a8ec-70ebbea4fe63"}}, {"head": {"id": "4d20ffd2-109a-4248-a8ec-70ebbea4fe63", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907427401200, "endTime": 12907512350700}, "additional": {"logType": "info", "children": ["1fb7c9dc-8726-4eca-9e41-cb4f75c89bea", "f4b6ddad-1776-49cb-aa38-00e8bab6b6e6"], "durationId": "e41eb24e-033f-4327-b1d8-fe65bdf28e7b", "parent": "2994407c-0b7a-405a-84b7-48e8f099a69b"}}, {"head": {"id": "c1c1da13-f158-4f3d-82cd-ffb31e7bfa02", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512944700, "endTime": 12907512958600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d8d4b70-cd10-427a-a810-4e0734f72234", "logId": "429da8b4-d7bb-44da-9c3e-0b6641f584dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "429da8b4-d7bb-44da-9c3e-0b6641f584dd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512944700, "endTime": 12907512958600}, "additional": {"logType": "info", "children": [], "durationId": "c1c1da13-f158-4f3d-82cd-ffb31e7bfa02", "parent": "2994407c-0b7a-405a-84b7-48e8f099a69b"}}, {"head": {"id": "2994407c-0b7a-405a-84b7-48e8f099a69b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907424876800, "endTime": 12907512974300}, "additional": {"logType": "info", "children": ["0bea32ab-fdc9-4aff-a42b-846b2cadf283", "4d20ffd2-109a-4248-a8ec-70ebbea4fe63", "429da8b4-d7bb-44da-9c3e-0b6641f584dd"], "durationId": "8d8d4b70-cd10-427a-a810-4e0734f72234", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "3c7965cf-9991-46d0-a69e-2b352461d796", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907513546100, "endTime": 12907537377000}, "additional": {"children": ["b01bcae3-6891-4798-a52f-c285eb943075", "4d7740fc-59b8-41c5-9e39-1053ef733c63", "79713ec8-7dbd-4bb1-9eb0-8b570211260c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b53667d-7a74-4f0c-855d-e9ec03376a08", "logId": "c0dcdc8c-9ab6-47bc-a9d2-18dbdda35dd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b01bcae3-6891-4798-a52f-c285eb943075", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907516566700, "endTime": 12907516584900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c7965cf-9991-46d0-a69e-2b352461d796", "logId": "e3fde77e-c3ec-46ec-b436-38822cc6881a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3fde77e-c3ec-46ec-b436-38822cc6881a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907516566700, "endTime": 12907516584900}, "additional": {"logType": "info", "children": [], "durationId": "b01bcae3-6891-4798-a52f-c285eb943075", "parent": "c0dcdc8c-9ab6-47bc-a9d2-18dbdda35dd6"}}, {"head": {"id": "4d7740fc-59b8-41c5-9e39-1053ef733c63", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907519042600, "endTime": 12907535368200}, "additional": {"children": ["f8748cbf-3739-4076-ab9a-d9d1aed22af7", "fd32cc40-f465-4fad-bcf3-9943103798fb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c7965cf-9991-46d0-a69e-2b352461d796", "logId": "b58fa750-da33-433c-b51a-20ade232e645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8748cbf-3739-4076-ab9a-d9d1aed22af7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907519044100, "endTime": 12907522857500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4d7740fc-59b8-41c5-9e39-1053ef733c63", "logId": "be8a8857-0e2f-4030-8d56-edde7f3a9369"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd32cc40-f465-4fad-bcf3-9943103798fb", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907522883900, "endTime": 12907535352600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4d7740fc-59b8-41c5-9e39-1053ef733c63", "logId": "34ce1d7a-291a-4774-822b-c0b2d4282106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc1d3013-a1f5-4122-8002-eac460fc7907", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907519049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61135a15-8330-4f67-8a8a-3d7c19df6044", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907522697500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be8a8857-0e2f-4030-8d56-edde7f3a9369", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907519044100, "endTime": 12907522857500}, "additional": {"logType": "info", "children": [], "durationId": "f8748cbf-3739-4076-ab9a-d9d1aed22af7", "parent": "b58fa750-da33-433c-b51a-20ade232e645"}}, {"head": {"id": "b838b9e6-b54b-4e35-8dcf-ee5b31c71e00", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907522903100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a120fd6c-04c8-4601-9102-3ce4b0a60d74", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907530796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b914dd3-da5e-4607-83ff-e1932593726f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907530971700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8fb7650-41ca-4836-ba49-709279fac9f4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907531204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9f8bd1-21c4-4a4f-874d-ebd89622b049", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907531402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b92f0a-1b5e-412c-935a-06e4118231e0", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907531478200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c020a5e6-3a88-4f79-a90c-29c564a6cf81", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907531533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ceb827-f600-461f-bf87-18711ca69158", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907531603800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c7ae19-9f95-4351-9dfb-5174f6e7108f", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907534969300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc440f2a-7b18-4b0d-8a03-68c4a35d23fe", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907535124700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6b17450-fb6f-485b-b3d3-cbcecce596bf", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907535194400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec295faf-7c0f-4834-8edc-f6d4b4932268", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907535241300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34ce1d7a-291a-4774-822b-c0b2d4282106", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907522883900, "endTime": 12907535352600}, "additional": {"logType": "info", "children": [], "durationId": "fd32cc40-f465-4fad-bcf3-9943103798fb", "parent": "b58fa750-da33-433c-b51a-20ade232e645"}}, {"head": {"id": "b58fa750-da33-433c-b51a-20ade232e645", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907519042600, "endTime": 12907535368200}, "additional": {"logType": "info", "children": ["be8a8857-0e2f-4030-8d56-edde7f3a9369", "34ce1d7a-291a-4774-822b-c0b2d4282106"], "durationId": "4d7740fc-59b8-41c5-9e39-1053ef733c63", "parent": "c0dcdc8c-9ab6-47bc-a9d2-18dbdda35dd6"}}, {"head": {"id": "79713ec8-7dbd-4bb1-9eb0-8b570211260c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907537216800, "endTime": 12907537229100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c7965cf-9991-46d0-a69e-2b352461d796", "logId": "bf1492ac-9229-4438-bd50-625dced6823f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf1492ac-9229-4438-bd50-625dced6823f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907537216800, "endTime": 12907537229100}, "additional": {"logType": "info", "children": [], "durationId": "79713ec8-7dbd-4bb1-9eb0-8b570211260c", "parent": "c0dcdc8c-9ab6-47bc-a9d2-18dbdda35dd6"}}, {"head": {"id": "c0dcdc8c-9ab6-47bc-a9d2-18dbdda35dd6", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907513546100, "endTime": 12907537377000}, "additional": {"logType": "info", "children": ["e3fde77e-c3ec-46ec-b436-38822cc6881a", "b58fa750-da33-433c-b51a-20ade232e645", "bf1492ac-9229-4438-bd50-625dced6823f"], "durationId": "3c7965cf-9991-46d0-a69e-2b352461d796", "parent": "b4f1088e-bc4f-4698-90da-cc10630bc367"}}, {"head": {"id": "b4f1088e-bc4f-4698-90da-cc10630bc367", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907512990100, "endTime": 12907537398900}, "additional": {"logType": "info", "children": ["c0dcdc8c-9ab6-47bc-a9d2-18dbdda35dd6"], "durationId": "9b53667d-7a74-4f0c-855d-e9ec03376a08", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "3b4d1f18-4d80-4c11-9b27-5b7dee1402cd", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907550884800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0c1b0d-8cc7-4e81-a438-b5ba9926ab5f", "name": "hvigorfile, resolve hvigorfile dependencies in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907551341400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d96f87-9c70-406e-a722-6b085407f4b1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907537408400, "endTime": 12907551416400}, "additional": {"logType": "info", "children": [], "durationId": "345c273a-f3c6-4750-88d1-66a10b025804", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "e810272e-a665-44c4-8cb6-5003738cc92f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907552146600, "endTime": 12907552309000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "logId": "96b83c32-9b6b-4ef2-9f16-c9568564eebb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7c2aed5-760d-4018-b239-b631124aafd2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907552167800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b83c32-9b6b-4ef2-9f16-c9568564eebb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907552146600, "endTime": 12907552309000}, "additional": {"logType": "info", "children": [], "durationId": "e810272e-a665-44c4-8cb6-5003738cc92f", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "21f5ae07-1877-4b80-88df-e27336306f34", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907553464600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4406515d-8449-4315-96ac-b4f2f8816ff5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907559203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee6037e-0d22-42e1-b288-5d1c9397724c", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907552318500, "endTime": 12907559949000}, "additional": {"logType": "info", "children": [], "durationId": "2f477c3e-f92c-4fec-a561-fb9bd8a7998e", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "85341cfb-5c9c-4cb0-8d0a-d14744bcf10e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907559981400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e35017cc-d48d-4757-be5f-b13800a6b7ae", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907564884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d057190-53da-492c-80f1-39d1ed7b52e3", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907565009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e23b09-2ee0-4cfd-aa3d-91b9fa6e33ca", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907565246900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd53fe8d-49d7-42eb-9ca1-9ea1e8920b25", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907569885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0917c0-d0e2-4e25-aeda-19658eccee9e", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907569985100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1867fc23-da8c-4170-967d-d37e13273935", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907559966600, "endTime": 12907572498000}, "additional": {"logType": "info", "children": [], "durationId": "84cdc881-b538-4334-8024-a3cce81afcef", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "7470c48b-4c93-4cac-b3c1-3e46fcadecbf", "name": "Configuration phase cost:153 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907572545200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "018def63-001d-4cc6-839c-8f94216bcc62", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907572519300, "endTime": 12907572635600}, "additional": {"logType": "info", "children": [], "durationId": "639d8207-812e-49bf-a68a-87f752bbfddc", "parent": "55b05ec2-d3d4-49f9-8b72-e22413e835f3"}}, {"head": {"id": "55b05ec2-d3d4-49f9-8b72-e22413e835f3", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907414916700, "endTime": 12907572648900}, "additional": {"logType": "info", "children": ["e2537100-7412-4997-8d4e-827070d28e6a", "92ecb7a0-339a-42a4-81a8-54f479476742", "2994407c-0b7a-405a-84b7-48e8f099a69b", "b4f1088e-bc4f-4698-90da-cc10630bc367", "15d96f87-9c70-406e-a722-6b085407f4b1", "3ee6037e-0d22-42e1-b288-5d1c9397724c", "1867fc23-da8c-4170-967d-d37e13273935", "018def63-001d-4cc6-839c-8f94216bcc62", "96b83c32-9b6b-4ef2-9f16-c9568564eebb"], "durationId": "b49b3c8a-ee59-48cc-bdfb-77b9c4ada4d7", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "03012c22-11e2-4ba6-891e-df0d1951c25b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907574037200, "endTime": 12907574055000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3", "logId": "b25986f9-243d-4936-8bb6-7adf79514368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b25986f9-243d-4936-8bb6-7adf79514368", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907574037200, "endTime": 12907574055000}, "additional": {"logType": "info", "children": [], "durationId": "03012c22-11e2-4ba6-891e-df0d1951c25b", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "f82946b1-a1e5-4b53-a710-1c2f25c213de", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907572678500, "endTime": 12907574070200}, "additional": {"logType": "info", "children": [], "durationId": "fdf18bc5-30f3-4310-90b0-c75c8dd0d801", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "5cabf36e-491e-4cd1-9897-7c2a8efe273c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907574076800, "endTime": 12907574105000}, "additional": {"logType": "info", "children": [], "durationId": "6b9d64b7-7e12-4272-9191-84d07a85af64", "parent": "a3fd4158-bd0e-4637-8981-86e31017acad"}}, {"head": {"id": "a3fd4158-bd0e-4637-8981-86e31017acad", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907402653900, "endTime": 12907574110400}, "additional": {"logType": "info", "children": ["97b00b34-83a6-4d82-9381-b9cd7dab0044", "55b05ec2-d3d4-49f9-8b72-e22413e835f3", "f82946b1-a1e5-4b53-a710-1c2f25c213de", "5cabf36e-491e-4cd1-9897-7c2a8efe273c", "b0dd2911-a633-442c-b395-77700dbdb641", "6dcdff5b-7074-4917-9b8c-ce8bfb35c3be", "b25986f9-243d-4936-8bb6-7adf79514368"], "durationId": "d807a8f1-2f3c-4bf5-93ad-cd47b4b273c3"}}, {"head": {"id": "5258a234-6051-481b-b05e-8e9ccfb81b5e", "name": "Configuration task cost before running: 176 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907574274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af3f085-bd92-4632-a5c5-de9fbd6df5ff", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907578662200, "endTime": 12907586547300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f94555d7-1ee9-472d-aadc-db9e97ac82de", "logId": "114fbaf0-59d6-4f46-9d0d-a0d58fee3e36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f94555d7-1ee9-472d-aadc-db9e97ac82de", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907575593600}, "additional": {"logType": "detail", "children": [], "durationId": "8af3f085-bd92-4632-a5c5-de9fbd6df5ff"}}, {"head": {"id": "a55d8e1f-a8fc-425a-a177-95692d1a17ee", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907576054400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e5774b-2a4f-4479-821b-d8d6a24eb188", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907576138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e8fcaae-c2b3-4b80-9f0e-a193ee818f10", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907576188500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6146bf2a-1fb4-43df-816b-e8a0f3b40d9a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907578673200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c79fc7-5c9c-4948-8391-05c1637b9413", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907586336100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68c18e1d-a059-4433-beef-67686b3a73e3", "name": "entry : default@PreBuild cost memory 0.28911590576171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907586471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114fbaf0-59d6-4f46-9d0d-a0d58fee3e36", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907578662200, "endTime": 12907586547300}, "additional": {"logType": "info", "children": [], "durationId": "8af3f085-bd92-4632-a5c5-de9fbd6df5ff"}}, {"head": {"id": "a42a0c9d-f9db-4be1-ae07-3987a4bb7eed", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907591879200, "endTime": 12907594640300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e905f493-d1d9-40b1-8fa3-bdc2e285ffcc", "logId": "d5dbb837-7fa1-4740-975f-aa3bb529477d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e905f493-d1d9-40b1-8fa3-bdc2e285ffcc", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907590484500}, "additional": {"logType": "detail", "children": [], "durationId": "a42a0c9d-f9db-4be1-ae07-3987a4bb7eed"}}, {"head": {"id": "bc3c666b-cb1c-4293-aebe-d1a78eca9c0e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907591063600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0c0d1a-8bdb-42a0-b80e-d7fe45694642", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907591169600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9151574-4d2a-400c-9c11-bd12bdead572", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907591230300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade9625c-9eb9-47b9-96e2-1ef60f1f1445", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907591889000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "685c9a58-3278-43c4-a661-3acc0e4f62c5", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907594469200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21aade4d-9e6e-4868-85c1-a9ed50a13fcb", "name": "entry : default@MergeProfile cost memory 0.12831878662109375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907594571300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5dbb837-7fa1-4740-975f-aa3bb529477d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907591879200, "endTime": 12907594640300}, "additional": {"logType": "info", "children": [], "durationId": "a42a0c9d-f9db-4be1-ae07-3987a4bb7eed"}}, {"head": {"id": "bbd4e954-27b5-4a8f-bf15-03412d1e1748", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907597724300, "endTime": 12907599975900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6007355e-f54b-4296-a1a5-27adead7d142", "logId": "c7db0ee0-25d9-4fe4-a33d-f95febb00b20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6007355e-f54b-4296-a1a5-27adead7d142", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907596293400}, "additional": {"logType": "detail", "children": [], "durationId": "bbd4e954-27b5-4a8f-bf15-03412d1e1748"}}, {"head": {"id": "dc6d73d5-1c1a-4598-a425-b4d7389745ce", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907596760200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace20bc0-a8b7-4d9e-a5e2-0b0314a4aeb9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907596847400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eae7b90-3b1c-4d2d-ad92-d62b9b12047e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907596900800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0ccf7a3-f340-47f5-8bd0-5861789e4cf6", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907597734400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb36c61-837b-4d34-a2d3-d36eae6528a8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907598627700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4582faa9-1a02-4a6d-9615-ece580ec9489", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907599810000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa14c4f1-ea77-4e00-8293-9d9cd4819aa5", "name": "entry : default@CreateBuildProfile cost memory 0.09810638427734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907599908600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7db0ee0-25d9-4fe4-a33d-f95febb00b20", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907597724300, "endTime": 12907599975900}, "additional": {"logType": "info", "children": [], "durationId": "bbd4e954-27b5-4a8f-bf15-03412d1e1748"}}, {"head": {"id": "7f030614-e673-4691-b199-40787a1a3e20", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602748200, "endTime": 12907603176400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6a2ab592-3478-4849-8b50-1b20555496b4", "logId": "e98a49ee-d7aa-495d-83aa-00e1826d471f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a2ab592-3478-4849-8b50-1b20555496b4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907601419400}, "additional": {"logType": "detail", "children": [], "durationId": "7f030614-e673-4691-b199-40787a1a3e20"}}, {"head": {"id": "622abc7c-e986-43a4-aebd-0f6200c8f8d0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907601903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29385897-b8f1-404e-b33f-9b98bd2b86b0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907601991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374c965a-2436-40ea-8ba2-ce638781674e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602044700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f200f3-503d-453f-86f8-b98ca8a74dae", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602757700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45046364-c0f4-4e6f-a39e-118a0884c1cd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602861600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04631979-0cce-450a-b930-518810cd166b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac5cf9c-7d49-4ea2-b46a-bbb9b5a8f59a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602958900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb9c43f-1a0d-46c4-a3bc-9e2fe4139e41", "name": "entry : default@PreCheckSyscap cost memory 0.05034637451171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907603035500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1cc5ae-0698-411e-a68a-9507a9487962", "name": "runTaskFromQueue task cost before running: 205 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907603114800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e98a49ee-d7aa-495d-83aa-00e1826d471f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907602748200, "endTime": 12907603176400, "totalTime": 345500}, "additional": {"logType": "info", "children": [], "durationId": "7f030614-e673-4691-b199-40787a1a3e20"}}, {"head": {"id": "876dc551-eef1-4481-9417-d8e82c9fe3bc", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907613279600, "endTime": 12907614360500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fc8d9923-cb35-4bf9-aa4d-f67284330721", "logId": "7fe7d040-36dc-4fd2-9b2c-8a99a6393ccc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc8d9923-cb35-4bf9-aa4d-f67284330721", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907604741600}, "additional": {"logType": "detail", "children": [], "durationId": "876dc551-eef1-4481-9417-d8e82c9fe3bc"}}, {"head": {"id": "8cf258e7-ffee-4b20-b84b-b475d95931c3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907605228900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d393bab-106e-4cff-8590-32658f867a09", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907605317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62899c0f-9442-4128-b980-18ccac9681a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907605369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1f8537-7e80-426b-bcad-d27933b70d02", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907613296400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1661fbcb-0f19-4cc6-b2f7-19ea52c380f7", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907613518100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5210ae77-c1e5-4d9a-9735-11df1f70c3b0", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907614166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c03fd01-4679-4f6e-b6da-02583933b79d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.069610595703125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907614285900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe7d040-36dc-4fd2-9b2c-8a99a6393ccc", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907613279600, "endTime": 12907614360500}, "additional": {"logType": "info", "children": [], "durationId": "876dc551-eef1-4481-9417-d8e82c9fe3bc"}}, {"head": {"id": "bdb7cbdb-951e-4329-9d17-3266b0ad072d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907617992400, "endTime": 12907619841200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "30a8841d-e15a-4461-9736-9e32cff9451c", "logId": "531c9bcd-8580-4911-9ec1-dfc4a3fe90a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30a8841d-e15a-4461-9736-9e32cff9451c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907616172700}, "additional": {"logType": "detail", "children": [], "durationId": "bdb7cbdb-951e-4329-9d17-3266b0ad072d"}}, {"head": {"id": "e0cd3526-6504-41b5-bfd3-a6e1551e1588", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907616688900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30063d9b-0ca7-45d0-8405-f1e0147c8aea", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907616797400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea21c1f4-2319-41ef-b587-2a240064b9b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907616857300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516f603d-83d6-4341-94df-85ab571e51b9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907618002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3db53f-e788-4cd3-a700-43b92f390328", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907619668200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776c6ce3-d36f-4668-b13c-1516cf28240e", "name": "entry : default@ProcessProfile cost memory -1.5738067626953125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907619773200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "531c9bcd-8580-4911-9ec1-dfc4a3fe90a6", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907617992400, "endTime": 12907619841200}, "additional": {"logType": "info", "children": [], "durationId": "bdb7cbdb-951e-4329-9d17-3266b0ad072d"}}, {"head": {"id": "4c50b2a7-0bf4-488e-b9ee-afda899f29c6", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907623624800, "endTime": 12907629508500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c03e5f15-6d96-4a9f-80f4-cf56252118e6", "logId": "603d32b2-412d-4fbb-93c0-36d04ea3578c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c03e5f15-6d96-4a9f-80f4-cf56252118e6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907621343500}, "additional": {"logType": "detail", "children": [], "durationId": "4c50b2a7-0bf4-488e-b9ee-afda899f29c6"}}, {"head": {"id": "5be9a2b3-d5f0-4084-ad29-914ad5fadedb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907621824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ee04f0-cfad-40d5-a34d-07549fe26c28", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907621910300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f29c31-1599-482e-bb1c-d0b31c425da1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907621963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f02791cc-e9b5-4cc1-95a3-24c51019c234", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907623637500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "321328bf-e586-4109-bc4b-25f4b8ab9316", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907629311200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfc6cde-54c4-48b5-8e96-338e347198a4", "name": "entry : default@ProcessRouterMap cost memory 0.33446502685546875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907629436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "603d32b2-412d-4fbb-93c0-36d04ea3578c", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907623624800, "endTime": 12907629508500}, "additional": {"logType": "info", "children": [], "durationId": "4c50b2a7-0bf4-488e-b9ee-afda899f29c6"}}, {"head": {"id": "c3dad672-1d5b-4e89-99a6-e54d946ac348", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907636808900, "endTime": 12907639735500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ac54c12b-eaef-4f54-8141-88c2cf3d1727", "logId": "d0904d6a-a963-4ac2-848a-40a5265987ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac54c12b-eaef-4f54-8141-88c2cf3d1727", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907632404400}, "additional": {"logType": "detail", "children": [], "durationId": "c3dad672-1d5b-4e89-99a6-e54d946ac348"}}, {"head": {"id": "fc57f584-43de-4efa-b48c-33b67b075250", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907632934900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef528426-4942-4122-9344-45839cc60d82", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907633045200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd3f54d8-b4e5-4290-a599-6b2c6f8913d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907633105600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72ca12c-cb08-42d6-943b-79a9f0eefa7e", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907634331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf78d70-62dc-43e0-b708-72d0936b8a9b", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907637966000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e616867-40fa-4aea-ad4c-4fe76c151b1f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907638122000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd7c9fb4-211d-4a2d-9227-5031269441cd", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907638192200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc565c3c-74e7-4b51-8323-05b17421a27c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907638243900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df02e4ce-8bf0-48cd-92f8-e2a933d46447", "name": "entry : default@PreviewProcessResource cost memory 0.08855438232421875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907638326800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc4b186-de66-4652-869b-638e53faec31", "name": "runTaskFromQueue task cost before running: 241 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907639619300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0904d6a-a963-4ac2-848a-40a5265987ac", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907636808900, "endTime": 12907639735500, "totalTime": 1579200}, "additional": {"logType": "info", "children": [], "durationId": "c3dad672-1d5b-4e89-99a6-e54d946ac348"}}, {"head": {"id": "b552ed1c-c995-4fa6-b22f-f30a1f0c68fe", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907646929700, "endTime": 12907668338400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c8e3dbc9-38f4-4a80-b469-946599833ede", "logId": "e7539cd4-0178-4d64-8e9e-39bba393667a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8e3dbc9-38f4-4a80-b469-946599833ede", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907642602700}, "additional": {"logType": "detail", "children": [], "durationId": "b552ed1c-c995-4fa6-b22f-f30a1f0c68fe"}}, {"head": {"id": "13ad7a20-6174-4772-bd04-99aa74bd17dd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907643107400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c672302d-fabf-4dc1-88ba-eaa54b743882", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907643202700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b56ad64-b239-442b-bc99-35311a0dbfd7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907643257400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3bb838-6338-4f7e-ab38-7b5a9f1b9499", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907646949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1387a794-a605-4575-adbd-575b555d12fe", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907668090200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e194074a-db4e-4a1f-8812-23ae7b4d2131", "name": "entry : default@GenerateLoaderJson cost memory -0.86407470703125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907668255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7539cd4-0178-4d64-8e9e-39bba393667a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907646929700, "endTime": 12907668338400}, "additional": {"logType": "info", "children": [], "durationId": "b552ed1c-c995-4fa6-b22f-f30a1f0c68fe"}}, {"head": {"id": "62079542-a4ad-4195-b736-c43ab996c911", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907679129500, "endTime": 12908320427900}, "additional": {"children": ["96d55d2c-87f3-46d7-aa70-1011aca40327", "03a2271c-4f06-4634-9b6d-97ce6070a593", "60b7ccba-201a-49df-9c47-f8a6f5627115", "bcdde4a5-d5aa-41b8-a34c-05f9d9d12952", "4a6c0e62-3bfe-461f-abe9-df253c41b397"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "df3642e6-1be4-41f5-b339-7fa79e542252", "logId": "7b232bc4-41e9-4f55-b458-aeaaef846928"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df3642e6-1be4-41f5-b339-7fa79e542252", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907675253000}, "additional": {"logType": "detail", "children": [], "durationId": "62079542-a4ad-4195-b736-c43ab996c911"}}, {"head": {"id": "b998f0d5-19a0-4b85-9edf-cb10d217489b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907675724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b299f670-20d7-4fd8-a1d1-7ea5dada86ef", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907675812600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d6e273-e0a9-47d0-bde1-e94a63838d12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907675870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e28f843-9b47-4014-8b65-2eaeb3a55c20", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907676679600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d917e5f-2486-4e68-b4a3-c198113f8a35", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907679154000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0237d444-a645-42c2-89fb-f0e532d75b01", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907715182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4606bf1f-a4fd-4fa0-b0c1-d97717cdd376", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907715361200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d55d2c-87f3-46d7-aa70-1011aca40327", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907716485600, "endTime": 12907742865200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62079542-a4ad-4195-b736-c43ab996c911", "logId": "e59bf925-6010-4250-a84a-c9d7debb0eb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e59bf925-6010-4250-a84a-c9d7debb0eb6", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907716485600, "endTime": 12907742865200}, "additional": {"logType": "info", "children": [], "durationId": "96d55d2c-87f3-46d7-aa70-1011aca40327", "parent": "7b232bc4-41e9-4f55-b458-aeaaef846928"}}, {"head": {"id": "2536e5a8-bf69-4c54-a467-30a8b8518c68", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907743133300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03a2271c-4f06-4634-9b6d-97ce6070a593", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907744033900, "endTime": 12907869981600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62079542-a4ad-4195-b736-c43ab996c911", "logId": "ca021600-49b0-46ec-8778-e2040e2e424a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afc313bb-93b3-4e49-8c3c-c37aac78d86b", "name": "current process  memoryUsage: {\n  rss: 110694400,\n  heapTotal: 114589696,\n  heapUsed: 107592648,\n  external: 3109235,\n  arrayBuffers: 103102\n} os memoryUsage :10.673534393310547", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907744881600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271d99aa-0fc7-4efa-9112-2eb05d3ccf99", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907867373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca021600-49b0-46ec-8778-e2040e2e424a", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907744033900, "endTime": 12907869981600}, "additional": {"logType": "info", "children": [], "durationId": "03a2271c-4f06-4634-9b6d-97ce6070a593", "parent": "7b232bc4-41e9-4f55-b458-aeaaef846928"}}, {"head": {"id": "a5402b46-82aa-49c1-ab51-86507d30db32", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907870141400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b7ccba-201a-49df-9c47-f8a6f5627115", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907871190600, "endTime": 12908019377400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62079542-a4ad-4195-b736-c43ab996c911", "logId": "e675fde4-4b3f-4867-afbf-d23fc32868af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9ce920b-9b6e-4c64-bdba-422fb8020602", "name": "current process  memoryUsage: {\n  rss: 110755840,\n  heapTotal: 114589696,\n  heapUsed: 107878408,\n  external: 3109361,\n  arrayBuffers: 103243\n} os memoryUsage :10.673572540283203", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907872073200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c4a5ce5-d4c7-4660-aea1-620a7376e2f6", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908016840600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e675fde4-4b3f-4867-afbf-d23fc32868af", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907871190600, "endTime": 12908019377400}, "additional": {"logType": "info", "children": [], "durationId": "60b7ccba-201a-49df-9c47-f8a6f5627115", "parent": "7b232bc4-41e9-4f55-b458-aeaaef846928"}}, {"head": {"id": "4c817252-e898-4dc7-b93a-2d86bff0c8ab", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908019573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdde4a5-d5aa-41b8-a34c-05f9d9d12952", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908020835600, "endTime": 12908151660200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62079542-a4ad-4195-b736-c43ab996c911", "logId": "ef59e8bc-dbe7-47da-81bd-07503e5847a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ab7c1ae-3b8b-4541-8c37-bfaaf9a5d0de", "name": "current process  memoryUsage: {\n  rss: 110772224,\n  heapTotal: 114589696,\n  heapUsed: 108159112,\n  external: 3117679,\n  arrayBuffers: 111625\n} os memoryUsage :10.686866760253906", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908022078600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46557dce-61cf-48a6-aba0-d62b78a15223", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908148541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef59e8bc-dbe7-47da-81bd-07503e5847a4", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908020835600, "endTime": 12908151660200}, "additional": {"logType": "info", "children": [], "durationId": "bcdde4a5-d5aa-41b8-a34c-05f9d9d12952", "parent": "7b232bc4-41e9-4f55-b458-aeaaef846928"}}, {"head": {"id": "d8fb84e4-66fc-45cc-a694-c3a71608b748", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908151933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6c0e62-3bfe-461f-abe9-df253c41b397", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908153036000, "endTime": 12908319001500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "62079542-a4ad-4195-b736-c43ab996c911", "logId": "4bf105b3-4de5-47b7-ab6e-375c62f306b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "501f2528-7469-4a68-967f-779e65fc9a04", "name": "current process  memoryUsage: {\n  rss: 110821376,\n  heapTotal: 114589696,\n  heapUsed: 106721192,\n  external: 3091502,\n  arrayBuffers: 86367\n} os memoryUsage :10.689697265625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908153882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d3bb2c-f72c-47ed-bd36-73340103b363", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908314509700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf105b3-4de5-47b7-ab6e-375c62f306b2", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908153036000, "endTime": 12908319001500}, "additional": {"logType": "info", "children": [], "durationId": "4a6c0e62-3bfe-461f-abe9-df253c41b397", "parent": "7b232bc4-41e9-4f55-b458-aeaaef846928"}}, {"head": {"id": "6cd44c07-b877-4092-8c72-6e51112a5950", "name": "entry : default@PreviewCompileResource cost memory -0.6129608154296875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908320118400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3ff3b5-40d7-4f64-8f38-33441dba02da", "name": "runTaskFromQueue task cost before running: 922 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908320334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b232bc4-41e9-4f55-b458-aeaaef846928", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907679129500, "endTime": 12908320427900, "totalTime": 641142800}, "additional": {"logType": "info", "children": ["e59bf925-6010-4250-a84a-c9d7debb0eb6", "ca021600-49b0-46ec-8778-e2040e2e424a", "e675fde4-4b3f-4867-afbf-d23fc32868af", "ef59e8bc-dbe7-47da-81bd-07503e5847a4", "4bf105b3-4de5-47b7-ab6e-375c62f306b2"], "durationId": "62079542-a4ad-4195-b736-c43ab996c911"}}, {"head": {"id": "59f3be02-513c-4799-a569-c3f78b64b16d", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323941800, "endTime": 12908324420600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c9e585c9-2451-4e66-aea8-fd62e8ba5396", "logId": "575550f3-d9ff-4e64-ac21-c90a1c86a9b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9e585c9-2451-4e66-aea8-fd62e8ba5396", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323067000}, "additional": {"logType": "detail", "children": [], "durationId": "59f3be02-513c-4799-a569-c3f78b64b16d"}}, {"head": {"id": "67635ad2-81ef-4747-b7c3-5abed09a3b89", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323660700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20e18d1-6543-4f0e-91f6-2d31d9f1b65a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323779600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7b667b-2eaf-4102-817c-881860071729", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323841700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a82131b-4518-4597-8d9e-721213e57801", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323960100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304f4a26-1152-4683-995e-e8092189e4d0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908324079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9a1ef3-6522-4f7c-bead-09209c6e734b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908324133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99cac339-5f8f-4f8d-a04a-e026fd8d5069", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908324186900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca3a4112-d54f-45fe-a402-c0890b47b6ec", "name": "entry : default@PreviewHookCompileResource cost memory 0.052825927734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908324272400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be46f83-8cb6-4e40-954b-00aeb60a2e70", "name": "runTaskFromQueue task cost before running: 926 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908324363600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "575550f3-d9ff-4e64-ac21-c90a1c86a9b8", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908323941800, "endTime": 12908324420600, "totalTime": 395600}, "additional": {"logType": "info", "children": [], "durationId": "59f3be02-513c-4799-a569-c3f78b64b16d"}}, {"head": {"id": "cc3288b6-05df-4c9e-9833-0a8ba8448314", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908327230200, "endTime": 12908334285700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "781de212-fc05-4fe6-b484-b53e0e6d31bd", "logId": "da6c9db5-ee74-439c-a97a-f2a37aeb6ca4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "781de212-fc05-4fe6-b484-b53e0e6d31bd", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908326042900}, "additional": {"logType": "detail", "children": [], "durationId": "cc3288b6-05df-4c9e-9833-0a8ba8448314"}}, {"head": {"id": "6ccfe3da-899c-4946-a760-4830ed136e5a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908326516400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b64ed0-fc13-4cb3-8537-fb9272c67c23", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908326606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff48ca51-2b22-42ee-b026-641278a1c215", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908326658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479d5619-6955-4d11-beab-984b3de58718", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908327239000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b68d78e-e239-4e9c-a1d5-1cc7750b857c", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908328454400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87a2a0c8-0cf0-4181-acba-cd1ec2fff3cf", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908328562700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c784b18-4047-4a14-8443-f8890449e549", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908328639900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5769b67b-5f3b-4960-9ca8-298dc72a59a0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908328691800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41c9da26-4892-486b-aa96-3f5a85aa59e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908328735000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "787d1c85-de63-4569-8eec-5a56640ec0d1", "name": "entry : default@CopyPreviewProfile cost memory 0.2237396240234375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908334019400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7332d982-31d7-4884-8a24-99528adb7142", "name": "runTaskFromQueue task cost before running: 936 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908334203400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6c9db5-ee74-439c-a97a-f2a37aeb6ca4", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908327230200, "endTime": 12908334285700, "totalTime": 6936700}, "additional": {"logType": "info", "children": [], "durationId": "cc3288b6-05df-4c9e-9833-0a8ba8448314"}}, {"head": {"id": "158bf036-5333-4f87-bfd8-489fa40ddbe1", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337430600, "endTime": 12908337886100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "393976d9-3f71-437b-b2eb-61d3a3f6ce41", "logId": "54e44333-843c-4ff6-b19b-fe41080dbaaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "393976d9-3f71-437b-b2eb-61d3a3f6ce41", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908336072700}, "additional": {"logType": "detail", "children": [], "durationId": "158bf036-5333-4f87-bfd8-489fa40ddbe1"}}, {"head": {"id": "28058c42-7cfc-4b22-941f-1cba4016454b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908336570000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d170873-2c8f-4ca3-b04d-2690992841fe", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908336667700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f999a9-0569-41a5-a8c2-ac0677f1c53c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908336723700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99937f7b-efba-4dc8-9a10-a3a3181ed575", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337441200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b8f6ea-84f1-4839-bcee-debe06f9eb9a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337560900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8050c2a-39cd-43eb-ac73-90c43099c6ac", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337615200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34ff48a-fd0d-4ba6-8947-0439a91dfea6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337658500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf19f70-43cb-4e32-a558-4885db3d2d46", "name": "entry : default@ReplacePreviewerPage cost memory 0.05147552490234375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a89094e7-1f7c-4a44-ab59-c676ddeb121d", "name": "runTaskFromQueue task cost before running: 940 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e44333-843c-4ff6-b19b-fe41080dbaaa", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908337430600, "endTime": 12908337886100, "totalTime": 381600}, "additional": {"logType": "info", "children": [], "durationId": "158bf036-5333-4f87-bfd8-489fa40ddbe1"}}, {"head": {"id": "6cd8066e-57a1-4965-9fb4-33a01d86f812", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908339470800, "endTime": 12908339890300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7ff33f91-6487-4559-b51f-b5f434ea8425", "logId": "b0f4715b-f936-4fc7-b530-2b1e54805f22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ff33f91-6487-4559-b51f-b5f434ea8425", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908339405600}, "additional": {"logType": "detail", "children": [], "durationId": "6cd8066e-57a1-4965-9fb4-33a01d86f812"}}, {"head": {"id": "85a9a075-bf24-4dab-ab91-610cfbaa1d9e", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908339480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c894166f-e79c-4e02-9240-c0e4b42645bd", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908339640500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bff7ff8e-e298-4fec-9a62-e2dbe616d603", "name": "runTaskFromQueue task cost before running: 942 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908339779500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f4715b-f936-4fc7-b530-2b1e54805f22", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908339470800, "endTime": 12908339890300, "totalTime": 276500}, "additional": {"logType": "info", "children": [], "durationId": "6cd8066e-57a1-4965-9fb4-33a01d86f812"}}, {"head": {"id": "8ea7e3ca-5818-4629-b775-421d56cf7602", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908343505400, "endTime": 12908346547200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "26d6e0f3-bf61-4d7f-93cc-08c581b539f9", "logId": "11ddfcd5-8f29-4f86-bdfc-2c03e2d52367"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26d6e0f3-bf61-4d7f-93cc-08c581b539f9", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908341713800}, "additional": {"logType": "detail", "children": [], "durationId": "8ea7e3ca-5818-4629-b775-421d56cf7602"}}, {"head": {"id": "14763466-ebba-4a05-9ce1-57aa6f4fb94f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908342182600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29289370-9f1f-4890-877b-8c20fff74f7e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908342268100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "688383a1-c557-437d-b8c4-6ecf9d79a9a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908342323700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe3a9f87-9392-4813-95e5-e25ff4560b5e", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908343514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f1b2f1-a9e6-4075-b0d9-28e67dfac68b", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908345202300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa6ea64-469b-459d-8cee-8b75871bdd11", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908345314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ba517f-11e5-4077-94eb-decc3b9ac8d4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908345396600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4aa805-ab08-4587-8cdb-5180672d5c41", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908345449200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545b7723-f610-4eff-9e00-f05160bc27f4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908345493800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "967dba6f-5a24-4e37-892f-7f19ed188178", "name": "entry : default@PreviewUpdateAssets cost memory 0.14948272705078125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908346384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d2d501-bd5c-4dbe-97d0-df8e702fab28", "name": "runTaskFromQueue task cost before running: 948 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908346488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ddfcd5-8f29-4f86-bdfc-2c03e2d52367", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908343505400, "endTime": 12908346547200, "totalTime": 2961600}, "additional": {"logType": "info", "children": [], "durationId": "8ea7e3ca-5818-4629-b775-421d56cf7602"}}, {"head": {"id": "86806f67-e33c-4821-92fa-cb7efd140bee", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908354402500, "endTime": 12920937520400}, "additional": {"children": ["d8cd3285-2fb4-4a7e-84a6-a3e944412174"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "94e65edc-e79d-4646-b181-23c3d4d0cc53", "logId": "5fb9f4c5-b97d-42ed-bea8-fae8c3d82727"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94e65edc-e79d-4646-b181-23c3d4d0cc53", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908349004500}, "additional": {"logType": "detail", "children": [], "durationId": "86806f67-e33c-4821-92fa-cb7efd140bee"}}, {"head": {"id": "a2a69b8e-c636-4ac9-a8cd-fc2dbc88d0d0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908349503100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edbbc875-1823-4e4f-8656-5080d372e072", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908349610500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "978d66b3-d8bc-45a0-bf8c-0293e9927e98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908349665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6bb2f29-6889-4de8-b104-70da104bf5d7", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908354417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "967a2182-ca97-4d47-a004-e7d7e03d07a9", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908386761500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b841fd04-21c5-452b-8d57-45e698b4d0f2", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908386916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12908401334600, "endTime": 12920934808800}, "additional": {"children": ["c0c8095d-6c3c-4508-afd8-f79768334ab7", "c7539d10-a85a-4884-bf25-f853d2b56745", "a66f03be-69c8-4caa-a868-aefa8e8fb948", "c4ca195f-a60e-418e-b40e-11c0ef4bbf4d", "f7447d7d-9375-4104-a6a6-a33cdf76d052", "bfd41426-8710-4697-9f8b-9b880ab6ec7e", "e1f5a849-c6a1-4c95-8794-a5aa218937e0", "2c7885d6-8496-43c4-a412-c94c4b3639e1", "1c7a4483-0e72-4ff5-af18-df4a4a889261", "356dce68-90c1-4688-b92e-5b4124516a7d", "43e77a95-77f7-4663-96dc-86e58bc7b429", "58f71403-4def-4d7a-97c8-db997ece1776", "575a5dc7-afa2-4e32-8dc9-78965050c8f6", "137c5f3f-00c8-4b9d-aaaa-c75a043e1eb0", "d7f99c3f-5cad-448c-8eee-327651e30559", "cfb1d3fe-edcc-4817-b296-ba9286cf7ceb"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "86806f67-e33c-4821-92fa-cb7efd140bee", "logId": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63fe6f1d-3e7e-4bf6-90b9-a0895873f5fa", "name": "entry : default@PreviewArkTS cost memory 0.29204559326171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908403328800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52310d82-6e0c-41b8-a769-0b8a905d687b", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12912160920200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0c8095d-6c3c-4508-afd8-f79768334ab7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12912162673900, "endTime": 12912162710300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "302ea049-3ff2-4243-b439-c8d2d90ef4d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "302ea049-3ff2-4243-b439-c8d2d90ef4d8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12912162673900, "endTime": 12912162710300}, "additional": {"logType": "info", "children": [], "durationId": "c0c8095d-6c3c-4508-afd8-f79768334ab7", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "bc130ffc-d5be-4023-849f-14d862b0b9e1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12919124279600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7539d10-a85a-4884-bf25-f853d2b56745", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12919125472300, "endTime": 12919125492900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "07eaa189-c0e0-4c16-bd0a-26e437e27941"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07eaa189-c0e0-4c16-bd0a-26e437e27941", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12919125472300, "endTime": 12919125492900}, "additional": {"logType": "info", "children": [], "durationId": "c7539d10-a85a-4884-bf25-f853d2b56745", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "5241b19c-bf03-4b87-bd6d-6ca3a8967392", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12919125566100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a66f03be-69c8-4caa-a868-aefa8e8fb948", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12919126330300, "endTime": 12919126344100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "85e91a49-eb9e-4318-90da-e75fcb2197db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85e91a49-eb9e-4318-90da-e75fcb2197db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12919126330300, "endTime": 12919126344100}, "additional": {"logType": "info", "children": [], "durationId": "a66f03be-69c8-4caa-a868-aefa8e8fb948", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "e1a9c88e-4905-4c47-9e83-548df6b1f9d2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12919126411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ca195f-a60e-418e-b40e-11c0ef4bbf4d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12919127092200, "endTime": 12919127104800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "34ab1ed0-cbd1-4f44-844a-49754b6b735a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34ab1ed0-cbd1-4f44-844a-49754b6b735a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12919127092200, "endTime": 12919127104800}, "additional": {"logType": "info", "children": [], "durationId": "c4ca195f-a60e-418e-b40e-11c0ef4bbf4d", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "529bd4fa-1df7-4d84-abb7-b43a44906418", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920927898900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7447d7d-9375-4104-a6a6-a33cdf76d052", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920928899900, "endTime": 12920928916800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "802bb5c2-3b5a-441e-92e0-1deea443e594"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "802bb5c2-3b5a-441e-92e0-1deea443e594", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920928899900, "endTime": 12920928916800}, "additional": {"logType": "info", "children": [], "durationId": "f7447d7d-9375-4104-a6a6-a33cdf76d052", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "43e3d1cc-072b-45a9-a3ef-c8ba8d976a4b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920928973700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd41426-8710-4697-9f8b-9b880ab6ec7e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920929681700, "endTime": 12920929697900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "233221ac-eae3-4966-80a7-e42650f512ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "233221ac-eae3-4966-80a7-e42650f512ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920929681700, "endTime": 12920929697900}, "additional": {"logType": "info", "children": [], "durationId": "bfd41426-8710-4697-9f8b-9b880ab6ec7e", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "945239aa-40f1-4403-9c0c-095c726725b2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920929760300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f5a849-c6a1-4c95-8794-a5aa218937e0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920930476200, "endTime": 12920930489800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "9d1ca79a-5641-4314-9bea-4915cf6226ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d1ca79a-5641-4314-9bea-4915cf6226ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920930476200, "endTime": 12920930489800}, "additional": {"logType": "info", "children": [], "durationId": "e1f5a849-c6a1-4c95-8794-a5aa218937e0", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "b0a686ae-3ec0-4840-8936-2e0fc5564bb5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920930548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7885d6-8496-43c4-a412-c94c4b3639e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920931208200, "endTime": 12920931218900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "9920ab5d-6423-4f5b-b188-f03217068c03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9920ab5d-6423-4f5b-b188-f03217068c03", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920931208200, "endTime": 12920931218900}, "additional": {"logType": "info", "children": [], "durationId": "2c7885d6-8496-43c4-a412-c94c4b3639e1", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "28a8bf6b-be90-4d43-9079-3408e3373229", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920931268900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c7a4483-0e72-4ff5-af18-df4a4a889261", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920931978200, "endTime": 12920931992400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "a0cd763c-1958-4c6b-bb5e-d8e27b38f960"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0cd763c-1958-4c6b-bb5e-d8e27b38f960", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920931978200, "endTime": 12920931992400}, "additional": {"logType": "info", "children": [], "durationId": "1c7a4483-0e72-4ff5-af18-df4a4a889261", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "202bfaf5-f282-4c0d-ad5e-0f1467d31712", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920932050200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356dce68-90c1-4688-b92e-5b4124516a7d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920932745600, "endTime": 12920932761600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "be7f5757-4141-4b95-87de-64cea5c8197e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be7f5757-4141-4b95-87de-64cea5c8197e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920932745600, "endTime": 12920932761600}, "additional": {"logType": "info", "children": [], "durationId": "356dce68-90c1-4688-b92e-5b4124516a7d", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "abd2300c-ef88-474a-a7e1-57afc0a8af1e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920932826700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e77a95-77f7-4663-96dc-86e58bc7b429", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920933472800, "endTime": 12920933485400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "b8d41122-1cf0-4117-87b5-16799a548985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d41122-1cf0-4117-87b5-16799a548985", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920933472800, "endTime": 12920933485400}, "additional": {"logType": "info", "children": [], "durationId": "43e77a95-77f7-4663-96dc-86e58bc7b429", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "0327012e-170a-45e8-bb5a-8f77bf4e08aa", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920933535300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f71403-4def-4d7a-97c8-db997ece1776", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12920934192300, "endTime": 12920934203500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "3a1ddbca-b0e6-43bc-a568-ba3d3d684ba0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a1ddbca-b0e6-43bc-a568-ba3d3d684ba0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920934192300, "endTime": 12920934203500}, "additional": {"logType": "info", "children": [], "durationId": "58f71403-4def-4d7a-97c8-db997ece1776", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "3d459125-2221-43eb-bc1f-fc5ac17a96aa", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12908401334600, "endTime": 12920934808800}, "additional": {"logType": "info", "children": ["302ea049-3ff2-4243-b439-c8d2d90ef4d8", "07eaa189-c0e0-4c16-bd0a-26e437e27941", "85e91a49-eb9e-4318-90da-e75fcb2197db", "34ab1ed0-cbd1-4f44-844a-49754b6b735a", "802bb5c2-3b5a-441e-92e0-1deea443e594", "233221ac-eae3-4966-80a7-e42650f512ac", "9d1ca79a-5641-4314-9bea-4915cf6226ee", "9920ab5d-6423-4f5b-b188-f03217068c03", "a0cd763c-1958-4c6b-bb5e-d8e27b38f960", "be7f5757-4141-4b95-87de-64cea5c8197e", "b8d41122-1cf0-4117-87b5-16799a548985", "3a1ddbca-b0e6-43bc-a568-ba3d3d684ba0", "0dab4428-52d7-424d-886b-6bb7b788d469", "1ec652f8-357c-4a94-bda2-63753287b467", "f5a5ecbd-c016-431a-86ea-6a6e5514a725", "1ea55c9b-0181-41f8-9b4a-21ad8e9c33be"], "durationId": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "parent": "5fb9f4c5-b97d-42ed-bea8-fae8c3d82727"}}, {"head": {"id": "575a5dc7-afa2-4e32-8dc9-78965050c8f6", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12910726503200, "endTime": 12911912197000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "0dab4428-52d7-424d-886b-6bb7b788d469"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dab4428-52d7-424d-886b-6bb7b788d469", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12910726503200, "endTime": 12911912197000}, "additional": {"logType": "info", "children": [], "durationId": "575a5dc7-afa2-4e32-8dc9-78965050c8f6", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "137c5f3f-00c8-4b9d-aaaa-c75a043e1eb0", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12911912571300, "endTime": 12912095911800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "1ec652f8-357c-4a94-bda2-63753287b467"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ec652f8-357c-4a94-bda2-63753287b467", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12911912571300, "endTime": 12912095911800}, "additional": {"logType": "info", "children": [], "durationId": "137c5f3f-00c8-4b9d-aaaa-c75a043e1eb0", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "d7f99c3f-5cad-448c-8eee-327651e30559", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12912096110600, "endTime": 12912096592700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "f5a5ecbd-c016-431a-86ea-6a6e5514a725"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5a5ecbd-c016-431a-86ea-6a6e5514a725", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12912096110600, "endTime": 12912096592700}, "additional": {"logType": "info", "children": [], "durationId": "d7f99c3f-5cad-448c-8eee-327651e30559", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "cfb1d3fe-edcc-4817-b296-ba9286cf7ceb", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker6", "startTime": 12912096727500, "endTime": 12920928028200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d8cd3285-2fb4-4a7e-84a6-a3e944412174", "logId": "1ea55c9b-0181-41f8-9b4a-21ad8e9c33be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ea55c9b-0181-41f8-9b4a-21ad8e9c33be", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12912096727500, "endTime": 12920928028200}, "additional": {"logType": "info", "children": [], "durationId": "cfb1d3fe-edcc-4817-b296-ba9286cf7ceb", "parent": "3d459125-2221-43eb-bc1f-fc5ac17a96aa"}}, {"head": {"id": "5fb9f4c5-b97d-42ed-bea8-fae8c3d82727", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12908354402500, "endTime": 12920937520400, "totalTime": 12583104800}, "additional": {"logType": "info", "children": ["3d459125-2221-43eb-bc1f-fc5ac17a96aa"], "durationId": "86806f67-e33c-4821-92fa-cb7efd140bee"}}, {"head": {"id": "a4392f20-4fcc-4c51-b321-1dec12f3be51", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920942431700, "endTime": 12920942668700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "3e34aee8-d735-418f-9bef-e1f546cd8f4c", "logId": "54b55b2d-1a2e-4f0a-8e91-07ccea74c9fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e34aee8-d735-418f-9bef-e1f546cd8f4c", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920942387100}, "additional": {"logType": "detail", "children": [], "durationId": "a4392f20-4fcc-4c51-b321-1dec12f3be51"}}, {"head": {"id": "94d98288-0c0e-4c84-966d-4812c9c58ba4", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920942444600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "787e3692-5a46-4a7c-ba72-8bd9d683eb85", "name": "entry : PreviewBuild cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920942545400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f48a1a7-1120-4385-a0c8-4d5716dda0e6", "name": "runTaskFromQueue task cost before running: 13 s 544 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920942619300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b55b2d-1a2e-4f0a-8e91-07ccea74c9fb", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920942431700, "endTime": 12920942668700, "totalTime": 169100}, "additional": {"logType": "info", "children": [], "durationId": "a4392f20-4fcc-4c51-b321-1dec12f3be51"}}, {"head": {"id": "311329cf-3928-44c2-9dc2-339a3ba2f8d1", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920952719300, "endTime": 12920952738000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5338204-2e81-419f-aa83-62a7c2064629", "logId": "3234df2d-4329-4507-9fa2-c0eb8c08bb56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3234df2d-4329-4507-9fa2-c0eb8c08bb56", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920952719300, "endTime": 12920952738000}, "additional": {"logType": "info", "children": [], "durationId": "311329cf-3928-44c2-9dc2-339a3ba2f8d1"}}, {"head": {"id": "6dbe6dd1-704c-449d-ba5c-89e94adf97a5", "name": "BUILD SUCCESSFUL in 13 s 555 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920952778400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "3adceba1-1c03-470b-af85-70ff59078d26", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12907398702900, "endTime": 12920953013300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 14}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f2d291d0-1f79-438b-9f50-566fe96bca9f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953038200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59fecd55-27bc-4bf2-8a49-b463acc8e8db", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953101400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcca804b-8346-488a-9a35-b2fa06e10efb", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953145800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee7d878f-debb-4ecf-8ef4-f0fd566cca35", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953186800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2fcde8a-e198-4337-9d2e-2c89dd7e6a29", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953226000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a96546-7fab-4e94-b63a-aaa013752d7b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953263300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb3aec5-a01c-4a69-b23f-739192d68627", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953308000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be308028-de6d-4db8-8d7b-d3f09b7ba552", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920953920900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad2780ef-57f5-40ca-950b-514fefde913a", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920967899700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c19e4dec-4cd2-403c-936f-cbad9b6b7664", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920970528600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f938ab-cf38-4eed-bf64-43548039c96e", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920970850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80f1c13-0a0b-4b89-b648-c17725bd46b7", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920989329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e9e95e-be2f-487c-99ed-8c775b5258f4", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:37 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920989999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eccc183-9f8f-4b3c-bfab-8cd2bb80a6f9", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920990209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39b9ee0-2372-4241-ba65-69d9ec324331", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920990932800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ed75053-7bc9-4693-bbb9-1d987f31409a", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920991679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30ae550a-0f80-41ea-a409-659c5933b372", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920992033600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fdbcb58-b982-473f-afb7-a4de50de0f9d", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920992312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd307719-e600-4891-b897-3dc11a59306d", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920992607400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dea6d63-db41-435f-9503-3e91da8d7528", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920995420100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0619e594-fdd5-4175-8129-badc209955b0", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920996117800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fa9afe-9b5f-4de4-a6ba-c54ea7ce29d1", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12920996395100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18a7373b-e784-49ef-9f13-911ecc2d8995", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921010479200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c920d38b-aa62-4028-8eb8-b713be67c982", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921011413300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34f1aaa8-f67d-4385-9d13-db26084d7860", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921011508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5e5792d-0b66-4278-851b-42823fa480c6", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921011764700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb13174-e8a3-4502-994a-6ec67ce42893", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921012468800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "569cca15-a2fb-4ae7-8539-67dd4a669026", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921016110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b833f5ec-a4a1-4cce-9f5a-c563a671ab82", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921016401400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8aba05-d890-4edb-a67b-72c5a9de4a3a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921016659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47c49e13-3d5e-41e7-8794-feebfc36c985", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921016944000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c524a3f-057b-4732-9afa-5f7279f87af8", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:25 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12921017229500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}