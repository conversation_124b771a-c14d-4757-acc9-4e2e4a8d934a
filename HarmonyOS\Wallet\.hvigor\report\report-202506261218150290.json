{"version": "2.0", "ppid": 24836, "events": [{"head": {"id": "3fa61ab7-66c6-4a06-9cab-db9de1a7826b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142968737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ae0054f-a166-4ff7-a7d8-348beb54ec7f", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13143068476300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd60c70-df42-4e30-bc93-4f20275a4fa8", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13143068750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a895b8-bf54-4a0e-be51-4a11f93a0340", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154893732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154903427000, "endTime": 13155110320300}, "additional": {"children": ["8ebe0d3f-03a9-43b6-b303-fdf5b6f54445", "c33e9879-61c1-4d20-954b-c3a6749ead81", "4a103146-e724-4437-93f4-a7cb391ded3b", "48eec867-cb0a-4f30-ab56-96b5f54d8a94", "c7fade95-027a-4224-ba33-659fb2441d2d", "cf0dea60-4460-4b26-980b-88b8d02beaae", "957be3bd-79bd-4997-8a0e-91883b623f4b"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ebe0d3f-03a9-43b6-b303-fdf5b6f54445", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154903428400, "endTime": 13154925453000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "36e49cef-2867-4957-a7fd-884d35b92b2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c33e9879-61c1-4d20-954b-c3a6749ead81", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154925482700, "endTime": 13155108957400}, "additional": {"children": ["0d68cbfa-d948-4c70-b0b1-ad00322b8327", "9157c29e-bf14-46a5-a9d2-14dbd015bfed", "567a755d-bea8-4c93-afd1-9e8857028d73", "a4a80487-38c2-4e50-82be-7279927ebe0d", "80c0e43c-23b8-4ea9-a79f-04c07786365c", "0f70b009-91c8-4509-86b2-98d7537131aa", "8a4f2090-d6b0-4ac3-b930-fc4bcfe9506a", "cb4fcae6-71f7-42ee-b94f-983d8ae3c025", "17050505-2d18-4551-8910-aabd0f018a27"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a103146-e724-4437-93f4-a7cb391ded3b", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155108985100, "endTime": 13155110284000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "43ce8bc0-0aad-43e6-88fe-a673e1496e26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48eec867-cb0a-4f30-ab56-96b5f54d8a94", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155110290700, "endTime": 13155110314300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "94e96df1-c8be-4164-9042-0f66256449bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7fade95-027a-4224-ba33-659fb2441d2d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154906536900, "endTime": 13154906586800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "d916bea3-8423-4520-8bf9-7e762ed7a9c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d916bea3-8423-4520-8bf9-7e762ed7a9c3", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154906536900, "endTime": 13154906586800}, "additional": {"logType": "info", "children": [], "durationId": "c7fade95-027a-4224-ba33-659fb2441d2d", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "cf0dea60-4460-4b26-980b-88b8d02beaae", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154916913100, "endTime": 13154916947000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "fdb9eef9-a03f-4eb1-b6e5-108cf97a341a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdb9eef9-a03f-4eb1-b6e5-108cf97a341a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154916913100, "endTime": 13154916947000}, "additional": {"logType": "info", "children": [], "durationId": "cf0dea60-4460-4b26-980b-88b8d02beaae", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "18884e51-e966-4de3-b69f-3e28b2035c08", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154917082500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "760835bf-ce6d-4b1c-a241-aa13a273e5c1", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154925204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36e49cef-2867-4957-a7fd-884d35b92b2c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154903428400, "endTime": 13154925453000}, "additional": {"logType": "info", "children": [], "durationId": "8ebe0d3f-03a9-43b6-b303-fdf5b6f54445", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "0d68cbfa-d948-4c70-b0b1-ad00322b8327", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154938531400, "endTime": 13154938555500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "d70e0a1c-0cf1-4573-a278-ca94d56e3b51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9157c29e-bf14-46a5-a9d2-14dbd015bfed", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154938609200, "endTime": 13154944844000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "5b53f636-136d-4749-86ff-4cb882cf761a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "567a755d-bea8-4c93-afd1-9e8857028d73", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154944863100, "endTime": 13155040430600}, "additional": {"children": ["f80cffcf-9b11-43bf-aea4-4b3236fa48f2", "2fce0e26-fec5-4bcf-b14e-afe9ffd9fffb", "af08d867-5e18-4ed9-98ab-f9b8a8bf889f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "45ae7ba9-99c5-4ba7-87fa-d08d909cdc13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4a80487-38c2-4e50-82be-7279927ebe0d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155040448500, "endTime": 13155069890500}, "additional": {"children": ["c2320302-57cf-4a3e-8043-5e5b302dff5e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "0e066818-ec40-44b3-8b09-c0b4fc1a3bae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80c0e43c-23b8-4ea9-a79f-04c07786365c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155069898300, "endTime": 13155084847800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "4f9c114d-ae33-4547-95f6-b1e38b8a08df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f70b009-91c8-4509-86b2-98d7537131aa", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155085986200, "endTime": 13155094499400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "b798e37e-8b84-47d2-8da8-2f58f61792c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a4f2090-d6b0-4ac3-b930-fc4bcfe9506a", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155094522100, "endTime": 13155108741500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "07b81baa-8f31-4029-a94a-4491b08e43b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb4fcae6-71f7-42ee-b94f-983d8ae3c025", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155108784300, "endTime": 13155108941000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "ab4e64a7-82b6-4826-b179-96f343505f87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d70e0a1c-0cf1-4573-a278-ca94d56e3b51", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154938531400, "endTime": 13154938555500}, "additional": {"logType": "info", "children": [], "durationId": "0d68cbfa-d948-4c70-b0b1-ad00322b8327", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "5b53f636-136d-4749-86ff-4cb882cf761a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154938609200, "endTime": 13154944844000}, "additional": {"logType": "info", "children": [], "durationId": "9157c29e-bf14-46a5-a9d2-14dbd015bfed", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "f80cffcf-9b11-43bf-aea4-4b3236fa48f2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154945670000, "endTime": 13154945694800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "567a755d-bea8-4c93-afd1-9e8857028d73", "logId": "4302cf8e-354f-4b47-88db-3e9d2c536226"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4302cf8e-354f-4b47-88db-3e9d2c536226", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154945670000, "endTime": 13154945694800}, "additional": {"logType": "info", "children": [], "durationId": "f80cffcf-9b11-43bf-aea4-4b3236fa48f2", "parent": "45ae7ba9-99c5-4ba7-87fa-d08d909cdc13"}}, {"head": {"id": "2fce0e26-fec5-4bcf-b14e-afe9ffd9fffb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154948702900, "endTime": 13155038334400}, "additional": {"children": ["4bdc93ed-f44f-4438-8226-ee80608b7e4b", "8583d746-6948-4a85-95a1-37945ba1a54f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "567a755d-bea8-4c93-afd1-9e8857028d73", "logId": "70962f37-1bfa-4195-a2f5-64a43c7bbf3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bdc93ed-f44f-4438-8226-ee80608b7e4b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154948704600, "endTime": 13154956652000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2fce0e26-fec5-4bcf-b14e-afe9ffd9fffb", "logId": "c3e5224a-92a8-431d-b1bf-a8c8d286ea73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8583d746-6948-4a85-95a1-37945ba1a54f", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154956674500, "endTime": 13155038309500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2fce0e26-fec5-4bcf-b14e-afe9ffd9fffb", "logId": "a5b0eed2-b312-441a-ab8a-d14666184950"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d55da66-6e6a-44c7-a446-b2d914d73551", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154948713500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb819719-24a8-4c0e-b5a6-af5c4b0513be", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154956495100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3e5224a-92a8-431d-b1bf-a8c8d286ea73", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154948704600, "endTime": 13154956652000}, "additional": {"logType": "info", "children": [], "durationId": "4bdc93ed-f44f-4438-8226-ee80608b7e4b", "parent": "70962f37-1bfa-4195-a2f5-64a43c7bbf3b"}}, {"head": {"id": "15da07cf-4f2f-44bb-b75b-cad262139a07", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154956693600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99766692-576d-451e-9d53-a304ccaf77e1", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154965547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1982e423-56ce-47f1-8793-db49250b9c80", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154965733100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3fe7ec-eb62-40a4-b89c-a75c7c28060e", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154965931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c3f3a06-6f96-454b-9626-706217c1ad17", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154966302200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041dcda5-9c53-4655-adbb-5feb7909d006", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154968452500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd462b2c-b2b4-4067-ad78-837f0fb77167", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154973517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a7a104-dc1f-4ec6-b6b3-35c689ab89d5", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154987548600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17f8e536-b5d6-43e3-854a-7612c0d8ccee", "name": "Sdk init in 39 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155013520900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b4d1884-0c87-4646-a84c-858e19db4e99", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155013806600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 18}, "markType": "other"}}, {"head": {"id": "0202dffa-58fe-4d8a-8e21-4f9abc9cee01", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155013861700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 18}, "markType": "other"}}, {"head": {"id": "b8c9ed01-3523-4a0a-b348-b7832b4c1a06", "name": "Project task initialization takes 23 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155037730400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed90a4a-903d-4c5d-a477-43cdc337642f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155037962900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2feffe34-b321-4cd4-a07e-9a702b2b67ff", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155038081900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df35cf08-bc0e-4cfa-bb7a-ac381089d8c8", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155038206300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b0eed2-b312-441a-ab8a-d14666184950", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154956674500, "endTime": 13155038309500}, "additional": {"logType": "info", "children": [], "durationId": "8583d746-6948-4a85-95a1-37945ba1a54f", "parent": "70962f37-1bfa-4195-a2f5-64a43c7bbf3b"}}, {"head": {"id": "70962f37-1bfa-4195-a2f5-64a43c7bbf3b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154948702900, "endTime": 13155038334400}, "additional": {"logType": "info", "children": ["c3e5224a-92a8-431d-b1bf-a8c8d286ea73", "a5b0eed2-b312-441a-ab8a-d14666184950"], "durationId": "2fce0e26-fec5-4bcf-b14e-afe9ffd9fffb", "parent": "45ae7ba9-99c5-4ba7-87fa-d08d909cdc13"}}, {"head": {"id": "af08d867-5e18-4ed9-98ab-f9b8a8bf889f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155040372200, "endTime": 13155040400100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "567a755d-bea8-4c93-afd1-9e8857028d73", "logId": "8e12c1b5-160e-414c-8b5e-6f6991a7f68e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e12c1b5-160e-414c-8b5e-6f6991a7f68e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155040372200, "endTime": 13155040400100}, "additional": {"logType": "info", "children": [], "durationId": "af08d867-5e18-4ed9-98ab-f9b8a8bf889f", "parent": "45ae7ba9-99c5-4ba7-87fa-d08d909cdc13"}}, {"head": {"id": "45ae7ba9-99c5-4ba7-87fa-d08d909cdc13", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154944863100, "endTime": 13155040430600}, "additional": {"logType": "info", "children": ["4302cf8e-354f-4b47-88db-3e9d2c536226", "70962f37-1bfa-4195-a2f5-64a43c7bbf3b", "8e12c1b5-160e-414c-8b5e-6f6991a7f68e"], "durationId": "567a755d-bea8-4c93-afd1-9e8857028d73", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "c2320302-57cf-4a3e-8043-5e5b302dff5e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155041483200, "endTime": 13155069877400}, "additional": {"children": ["2b92e86f-5a59-4484-9a82-03d716f3c21b", "641dbb16-be48-45d0-8cb9-f1d25af775e1", "40b402f0-e769-4b9c-aac6-f80694863a03"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4a80487-38c2-4e50-82be-7279927ebe0d", "logId": "ebbaa0e3-cffc-4811-878b-1e4ec2c277a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b92e86f-5a59-4484-9a82-03d716f3c21b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155044786500, "endTime": 13155044815200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2320302-57cf-4a3e-8043-5e5b302dff5e", "logId": "684b55c1-75d8-4ca2-a546-bff9211dc0ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "684b55c1-75d8-4ca2-a546-bff9211dc0ba", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155044786500, "endTime": 13155044815200}, "additional": {"logType": "info", "children": [], "durationId": "2b92e86f-5a59-4484-9a82-03d716f3c21b", "parent": "ebbaa0e3-cffc-4811-878b-1e4ec2c277a4"}}, {"head": {"id": "641dbb16-be48-45d0-8cb9-f1d25af775e1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155046687000, "endTime": 13155068489600}, "additional": {"children": ["e077a32a-13f0-4f80-ac86-4a715f1137b0", "f760e249-8434-43a7-9dd0-f126b091d798"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2320302-57cf-4a3e-8043-5e5b302dff5e", "logId": "59643584-986c-4430-a86a-70462b46606b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e077a32a-13f0-4f80-ac86-4a715f1137b0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155046688200, "endTime": 13155051283900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "641dbb16-be48-45d0-8cb9-f1d25af775e1", "logId": "1d25917d-502a-4d6e-a696-036eae9e27ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f760e249-8434-43a7-9dd0-f126b091d798", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155051301300, "endTime": 13155068475400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "641dbb16-be48-45d0-8cb9-f1d25af775e1", "logId": "fd72ce40-49b7-4e9d-ac5b-9376c45d2527"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b69c5b36-1604-4a23-a381-90a8891d3381", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155046695700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7fcff20-757f-4dce-bc84-9b5074143bc7", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155051130400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d25917d-502a-4d6e-a696-036eae9e27ae", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155046688200, "endTime": 13155051283900}, "additional": {"logType": "info", "children": [], "durationId": "e077a32a-13f0-4f80-ac86-4a715f1137b0", "parent": "59643584-986c-4430-a86a-70462b46606b"}}, {"head": {"id": "570a3d2e-b65c-475c-a7ab-75cded3c6248", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155051317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b319b3d0-71ac-4740-8d74-e82040fc7227", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155060648900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f8cd96-be6f-484f-b8ad-d509fe415c12", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155060900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a992c5e7-ffc9-4ecd-81bc-c22861513f31", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155061338300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9b5c08b-2231-4448-bdcc-0a86199e7086", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155061654800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf18edd-2a3d-4dba-8370-8f00ea457066", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155061800600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c29ec8-978c-4e79-810d-7ff12dcedc53", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155061923400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64156a2c-7dc6-4566-8794-4bf71c800cb1", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155062050600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f8d3645-64df-4873-9d48-bd75dc7dc98f", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155068106200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5ad71f-2ac8-43d6-946c-333ff234f07b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155068297800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77bcf9d8-8c8d-4c93-a4c4-97a470cf2201", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155068371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1795cb9c-7d9f-4b30-bc0e-b4f9bc57cf3d", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155068421800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd72ce40-49b7-4e9d-ac5b-9376c45d2527", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155051301300, "endTime": 13155068475400}, "additional": {"logType": "info", "children": [], "durationId": "f760e249-8434-43a7-9dd0-f126b091d798", "parent": "59643584-986c-4430-a86a-70462b46606b"}}, {"head": {"id": "59643584-986c-4430-a86a-70462b46606b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155046687000, "endTime": 13155068489600}, "additional": {"logType": "info", "children": ["1d25917d-502a-4d6e-a696-036eae9e27ae", "fd72ce40-49b7-4e9d-ac5b-9376c45d2527"], "durationId": "641dbb16-be48-45d0-8cb9-f1d25af775e1", "parent": "ebbaa0e3-cffc-4811-878b-1e4ec2c277a4"}}, {"head": {"id": "40b402f0-e769-4b9c-aac6-f80694863a03", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155069851100, "endTime": 13155069863000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2320302-57cf-4a3e-8043-5e5b302dff5e", "logId": "d63476b2-eb25-4fa6-a2cf-f7db8192d282"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d63476b2-eb25-4fa6-a2cf-f7db8192d282", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155069851100, "endTime": 13155069863000}, "additional": {"logType": "info", "children": [], "durationId": "40b402f0-e769-4b9c-aac6-f80694863a03", "parent": "ebbaa0e3-cffc-4811-878b-1e4ec2c277a4"}}, {"head": {"id": "ebbaa0e3-cffc-4811-878b-1e4ec2c277a4", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155041483200, "endTime": 13155069877400}, "additional": {"logType": "info", "children": ["684b55c1-75d8-4ca2-a546-bff9211dc0ba", "59643584-986c-4430-a86a-70462b46606b", "d63476b2-eb25-4fa6-a2cf-f7db8192d282"], "durationId": "c2320302-57cf-4a3e-8043-5e5b302dff5e", "parent": "0e066818-ec40-44b3-8b09-c0b4fc1a3bae"}}, {"head": {"id": "0e066818-ec40-44b3-8b09-c0b4fc1a3bae", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155040448500, "endTime": 13155069890500}, "additional": {"logType": "info", "children": ["ebbaa0e3-cffc-4811-878b-1e4ec2c277a4"], "durationId": "a4a80487-38c2-4e50-82be-7279927ebe0d", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "04121971-12e1-4e23-95ac-4d93507377e2", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155083959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fecdfdef-1a23-4558-a44b-e15dca228e97", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155084732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9c114d-ae33-4547-95f6-b1e38b8a08df", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155069898300, "endTime": 13155084847800}, "additional": {"logType": "info", "children": [], "durationId": "80c0e43c-23b8-4ea9-a79f-04c07786365c", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "17050505-2d18-4551-8910-aabd0f018a27", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155085721900, "endTime": 13155085965400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33e9879-61c1-4d20-954b-c3a6749ead81", "logId": "13000ae7-f51e-443f-a401-95b693ee5bc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "985399e6-f2e9-4e7f-8066-95cc264c<PERSON>e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155085752900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13000ae7-f51e-443f-a401-95b693ee5bc0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155085721900, "endTime": 13155085965400}, "additional": {"logType": "info", "children": [], "durationId": "17050505-2d18-4551-8910-aabd0f018a27", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "f9ab16fb-4421-4e61-9296-640e090b2920", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155087514700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92055abc-4c23-4551-8391-06b45a49ed98", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155093732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b798e37e-8b84-47d2-8da8-2f58f61792c4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155085986200, "endTime": 13155094499400}, "additional": {"logType": "info", "children": [], "durationId": "0f70b009-91c8-4509-86b2-98d7537131aa", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "264db164-5b14-4c15-ae97-a6a6a8366f67", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155094537500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "583f1dfc-9d1f-4a99-8dfc-237a3afdb6a5", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155099833100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3ca256-44ec-436a-b502-52c084ce27f1", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155099966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745e01e0-d0e1-43e4-996a-9f866d7d5ab1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155100349800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0b6764-5dd6-45d5-bb2f-219875b571c7", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155105579500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f27375-ddd5-49a6-a8ef-98f0faeb9c19", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155105732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b81baa-8f31-4029-a94a-4491b08e43b0", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155094522100, "endTime": 13155108741500}, "additional": {"logType": "info", "children": [], "durationId": "8a4f2090-d6b0-4ac3-b930-fc4bcfe9506a", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "fd2a1df9-a92b-4e73-af92-7668ff9fc70b", "name": "Configuration phase cost:171 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155108821400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab4e64a7-82b6-4826-b179-96f343505f87", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155108784300, "endTime": 13155108941000}, "additional": {"logType": "info", "children": [], "durationId": "cb4fcae6-71f7-42ee-b94f-983d8ae3c025", "parent": "239406ca-8f70-48a9-970c-5e1576b2e2c7"}}, {"head": {"id": "239406ca-8f70-48a9-970c-5e1576b2e2c7", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154925482700, "endTime": 13155108957400}, "additional": {"logType": "info", "children": ["d70e0a1c-0cf1-4573-a278-ca94d56e3b51", "5b53f636-136d-4749-86ff-4cb882cf761a", "45ae7ba9-99c5-4ba7-87fa-d08d909cdc13", "0e066818-ec40-44b3-8b09-c0b4fc1a3bae", "4f9c114d-ae33-4547-95f6-b1e38b8a08df", "b798e37e-8b84-47d2-8da8-2f58f61792c4", "07b81baa-8f31-4029-a94a-4491b08e43b0", "ab4e64a7-82b6-4826-b179-96f343505f87", "13000ae7-f51e-443f-a401-95b693ee5bc0"], "durationId": "c33e9879-61c1-4d20-954b-c3a6749ead81", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "957be3bd-79bd-4997-8a0e-91883b623f4b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155110254300, "endTime": 13155110270600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6", "logId": "9eed4b9a-531f-46a2-a91e-2d34dd4aeae0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9eed4b9a-531f-46a2-a91e-2d34dd4aeae0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155110254300, "endTime": 13155110270600}, "additional": {"logType": "info", "children": [], "durationId": "957be3bd-79bd-4997-8a0e-91883b623f4b", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "43ce8bc0-0aad-43e6-88fe-a673e1496e26", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155108985100, "endTime": 13155110284000}, "additional": {"logType": "info", "children": [], "durationId": "4a103146-e724-4437-93f4-a7cb391ded3b", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "94e96df1-c8be-4164-9042-0f66256449bc", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155110290700, "endTime": 13155110314300}, "additional": {"logType": "info", "children": [], "durationId": "48eec867-cb0a-4f30-ab56-96b5f54d8a94", "parent": "7a28c662-0a57-43cc-81cc-04180d8ac9d9"}}, {"head": {"id": "7a28c662-0a57-43cc-81cc-04180d8ac9d9", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154903427000, "endTime": 13155110320300}, "additional": {"logType": "info", "children": ["36e49cef-2867-4957-a7fd-884d35b92b2c", "239406ca-8f70-48a9-970c-5e1576b2e2c7", "43ce8bc0-0aad-43e6-88fe-a673e1496e26", "94e96df1-c8be-4164-9042-0f66256449bc", "d916bea3-8423-4520-8bf9-7e762ed7a9c3", "fdb9eef9-a03f-4eb1-b6e5-108cf97a341a", "9eed4b9a-531f-46a2-a91e-2d34dd4aeae0"], "durationId": "a7a9c3c2-4820-47e1-9b73-752a52c48ef6"}}, {"head": {"id": "70187a55-f254-46d4-bf73-7029fb7b124a", "name": "Configuration task cost before running: 212 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155110478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4059715f-534a-4321-85b4-28ebd7e36f41", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155116237200, "endTime": 13155125446000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "aeb30378-9700-43ba-809e-534894d7c05d", "logId": "1a4c8a5d-aa31-45cf-81fa-87d9e9010d7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aeb30378-9700-43ba-809e-534894d7c05d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155112053200}, "additional": {"logType": "detail", "children": [], "durationId": "4059715f-534a-4321-85b4-28ebd7e36f41"}}, {"head": {"id": "2ef6bdde-0a3a-404d-9530-33eb53e5c538", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155112572600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d215c833-5630-4af1-87c4-d48c94d4731f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155112694400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad02724-9c2e-41c4-93a0-3d87a1765094", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155112761700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16097552-d5a3-4495-85ec-f80c17f9bd04", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155116255600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fee299f-1813-4f83-960a-eb65ac82bd80", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155125136800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "401a7419-b27a-4ec1-afdd-2355bbeb45a9", "name": "entry : default@PreBuild cost memory 0.29754638671875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155125319600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4c8a5d-aa31-45cf-81fa-87d9e9010d7b", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155116237200, "endTime": 13155125446000}, "additional": {"logType": "info", "children": [], "durationId": "4059715f-534a-4321-85b4-28ebd7e36f41"}}, {"head": {"id": "9268b2bd-b210-45bb-b7e5-ec78a2e45c9c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155131080100, "endTime": 13155134648000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7a93883a-fc1d-44c5-afe0-c82e02b7a409", "logId": "a7df32cb-418b-48ab-a7fb-bba965f7675b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a93883a-fc1d-44c5-afe0-c82e02b7a409", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155129481900}, "additional": {"logType": "detail", "children": [], "durationId": "9268b2bd-b210-45bb-b7e5-ec78a2e45c9c"}}, {"head": {"id": "7d0b3f80-f2c9-4b4e-8a03-e575ec97d745", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155130079100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec44d96b-afd4-4c14-93c0-e52b2974dc9d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155130210100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c14545-10e8-4d63-9114-7792a317671d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155130296700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d0c511-2a81-4a5b-82b9-47ffda409d29", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155131093300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72af733f-51b6-4b03-b2c1-593ffa0af4af", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155134364100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7297a28-6e0b-4ba7-b1c4-a8e851d1937c", "name": "entry : default@MergeProfile cost memory 0.1337432861328125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155134555000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7df32cb-418b-48ab-a7fb-bba965f7675b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155131080100, "endTime": 13155134648000}, "additional": {"logType": "info", "children": [], "durationId": "9268b2bd-b210-45bb-b7e5-ec78a2e45c9c"}}, {"head": {"id": "7a6ffd26-e2ca-4e38-9634-50a43ec7b776", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155138801900, "endTime": 13155141594600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "912d01ed-887c-4582-a8f8-8bc639716c47", "logId": "1423f921-4411-490f-8d53-14a57cb8d590"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "912d01ed-887c-4582-a8f8-8bc639716c47", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155137043100}, "additional": {"logType": "detail", "children": [], "durationId": "7a6ffd26-e2ca-4e38-9634-50a43ec7b776"}}, {"head": {"id": "2d0740d5-ce89-4519-966c-f8413418c906", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155137647100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2752f813-7cb3-4cbf-8c41-7cca59354be7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155137786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7570282-5aba-4a43-89a2-45231a318164", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155137857200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6564d790-16dc-4c9d-b113-6e8cb60887aa", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155138817500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f2dc5af-cc53-4e59-b425-7c420757918f", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155139826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd2e6d38-7f89-40ff-a504-522e3abcc0cb", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155141361800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afa77f24-4cbb-4ee7-ac4f-fc4955b60444", "name": "entry : default@CreateBuildProfile cost memory 0.1011505126953125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155141505800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1423f921-4411-490f-8d53-14a57cb8d590", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155138801900, "endTime": 13155141594600}, "additional": {"logType": "info", "children": [], "durationId": "7a6ffd26-e2ca-4e38-9634-50a43ec7b776"}}, {"head": {"id": "5075e726-61d0-418b-9392-a8baff219643", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145038800, "endTime": 13155145577300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c0e54917-d5c8-4960-ae47-bd568d3819e6", "logId": "057d688e-ac81-46a6-9c9d-76c5fb3a4213"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0e54917-d5c8-4960-ae47-bd568d3819e6", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155143420000}, "additional": {"logType": "detail", "children": [], "durationId": "5075e726-61d0-418b-9392-a8baff219643"}}, {"head": {"id": "1f08091a-7e8a-44ea-94fa-d6204576549f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155144018000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33712ca-2dc5-4eff-ac79-3d858a2aa788", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155144142100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1766d214-5009-4d7d-b62b-4c5aa97adfa9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155144201000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c0069f0-1cc3-42b5-8216-4551d6a5f704", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb1545c0-8c4e-4e13-baba-f81c9fe8f56b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145204400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb19e15-82dc-450a-b6e9-326d019b72a7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145267700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5804b172-7a8c-40ec-8d8b-9735e604941d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145318000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87d0726-9d8d-436d-ae80-c57cdd0460c7", "name": "entry : default@PreCheckSyscap cost memory 0.05080413818359375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145418800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c13eaf-733f-4f7e-83bb-6bc449a41423", "name": "runTaskFromQueue task cost before running: 247 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145512500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057d688e-ac81-46a6-9c9d-76c5fb3a4213", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155145038800, "endTime": 13155145577300, "totalTime": 450600}, "additional": {"logType": "info", "children": [], "durationId": "5075e726-61d0-418b-9392-a8baff219643"}}, {"head": {"id": "903c483e-7f92-473c-b40d-019e05ae229b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155158236900, "endTime": 13155159497700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e2ad3ab8-95ad-420b-afb2-47f404e49196", "logId": "e0639abe-6638-4eb4-941b-7151bc0fc257"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2ad3ab8-95ad-420b-afb2-47f404e49196", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155147744800}, "additional": {"logType": "detail", "children": [], "durationId": "903c483e-7f92-473c-b40d-019e05ae229b"}}, {"head": {"id": "5df2d5c3-2de9-4261-a8a1-6192e5d3b681", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155148274900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1865a5b-b178-4d53-8e35-3fc8033eac88", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155148406500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fe2a86-1634-4836-8ef3-40b4b6742a4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155148468100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703fbf79-0c5b-4ada-8465-a12acc3104f6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155158259300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c3f04b-c006-416f-b4b2-2acba47284de", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155158649700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed6d54b-0a88-467a-a8f8-bbf1840542a9", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155159304300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d89e7a4-a211-4009-8124-33722d21bea7", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07050323486328125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155159422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0639abe-6638-4eb4-941b-7151bc0fc257", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155158236900, "endTime": 13155159497700}, "additional": {"logType": "info", "children": [], "durationId": "903c483e-7f92-473c-b40d-019e05ae229b"}}, {"head": {"id": "3353c239-e56f-4c9f-99e5-5dd86999bd69", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155163147000, "endTime": 13155164586800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "bbd3d16a-80e7-4441-af65-391250d16cdc", "logId": "3d845d40-b03d-44b6-beca-0a20f271d988"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbd3d16a-80e7-4441-af65-391250d16cdc", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155161131700}, "additional": {"logType": "detail", "children": [], "durationId": "3353c239-e56f-4c9f-99e5-5dd86999bd69"}}, {"head": {"id": "8aa4b29e-ce6e-4c0f-86eb-3f14fffba3e3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155161695400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0874ab8-8a13-4cc0-b674-e57f8cfe4069", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155161809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ace7244-ff18-435a-bcf4-69c16bade4fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155161873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2dbd278-8691-451a-95e5-5e0e2181058a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155163161400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aaf3fc5-2133-47da-85a0-c40638316bbb", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155164334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a03d3ad8-5e85-4730-a7ea-4576acb817e6", "name": "entry : default@ProcessProfile cost memory 0.05858612060546875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155164504800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d845d40-b03d-44b6-beca-0a20f271d988", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155163147000, "endTime": 13155164586800}, "additional": {"logType": "info", "children": [], "durationId": "3353c239-e56f-4c9f-99e5-5dd86999bd69"}}, {"head": {"id": "e94fa4ab-22ed-45d6-b876-f740a2b015d4", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155169211100, "endTime": 13155175588800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7a4b6681-9dd3-4781-9420-597d6d6e3955", "logId": "998171af-a851-4848-82ea-93fd0ef5a890"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a4b6681-9dd3-4781-9420-597d6d6e3955", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155166522400}, "additional": {"logType": "detail", "children": [], "durationId": "e94fa4ab-22ed-45d6-b876-f740a2b015d4"}}, {"head": {"id": "0161a29f-c28e-431b-904f-313044ded0f3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155167048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035863c8-b1e4-459e-9110-1e0f53247537", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155167157300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c0582d-476a-484c-be4c-194346d17bf8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155167243200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d21c1fc-5568-4607-8fb4-d2f500a45a6f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155169222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4d353a-f3e8-482c-917d-cc05c7d39ba2", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155175334400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8771036f-b99e-45b2-861f-5a7b7040aac6", "name": "entry : default@ProcessRouterMap cost memory 0.21851348876953125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155175502900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "998171af-a851-4848-82ea-93fd0ef5a890", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155169211100, "endTime": 13155175588800}, "additional": {"logType": "info", "children": [], "durationId": "e94fa4ab-22ed-45d6-b876-f740a2b015d4"}}, {"head": {"id": "957ec79d-5568-469e-b596-a4833ba7bf01", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155183615000, "endTime": 13155186831900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "012f5fc4-c84c-4e47-85f6-d3bfa973066d", "logId": "a916e25f-380e-4c9c-bea4-c7cbe50b723e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "012f5fc4-c84c-4e47-85f6-d3bfa973066d", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155178729800}, "additional": {"logType": "detail", "children": [], "durationId": "957ec79d-5568-469e-b596-a4833ba7bf01"}}, {"head": {"id": "fc255cb4-cd8d-4ec8-a950-09ed1f809a7c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155179268300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9a855f-0116-415b-8022-6ef6c2eeab59", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155179430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15300857-1147-4ce2-a94f-d7a849b68e88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155179510800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29591f85-2ea3-47da-ac61-c79b8e95012a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155180669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c20668-1345-482e-afe8-0ba40f8d0a5e", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155184936500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21faf28b-f3bb-460d-8575-cba6e50cfcf6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155185150300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e6b9b9-8d94-467c-b3e9-9c174553fd04", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155185214700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee7803c3-8da3-455b-ab3f-b5c42bc0472e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155185267900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "305c6637-7b70-4abf-a2d0-e36ebf3ef74d", "name": "entry : default@PreviewProcessResource cost memory 0.0899505615234375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155185377500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94aa8fd-a2ea-49c1-b556-e416bc664eba", "name": "runTaskFromQueue task cost before running: 288 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155186741700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a916e25f-380e-4c9c-bea4-c7cbe50b723e", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155183615000, "endTime": 13155186831900, "totalTime": 1877800}, "additional": {"logType": "info", "children": [], "durationId": "957ec79d-5568-469e-b596-a4833ba7bf01"}}, {"head": {"id": "06eb05a7-cbd2-4dea-ba89-16d6c2cb02c3", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155194429900, "endTime": 13155215126400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a2b4c1d5-3195-4940-8711-3e2134c34aab", "logId": "612d3602-eb89-4f33-b216-c0240c9b8e05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2b4c1d5-3195-4940-8711-3e2134c34aab", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155190149400}, "additional": {"logType": "detail", "children": [], "durationId": "06eb05a7-cbd2-4dea-ba89-16d6c2cb02c3"}}, {"head": {"id": "6706691f-f518-436b-9d03-14aedb82caa5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155190673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf8af72-1d13-46b0-ba92-f97a8503c41f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155190770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b0c64f3-171e-40c6-b242-f37c4961517f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155190826400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a598a8d2-fbd2-41b6-8ed8-6fff3dc49174", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155194443300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d31c5c4c-5dbe-458e-a729-9be1c9415ff4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155214890000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6902112-09ec-47fe-9b95-fef78df916e1", "name": "entry : default@GenerateLoaderJson cost memory 0.**********71875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155215042400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "612d3602-eb89-4f33-b216-c0240c9b8e05", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155194429900, "endTime": 13155215126400}, "additional": {"logType": "info", "children": [], "durationId": "06eb05a7-cbd2-4dea-ba89-16d6c2cb02c3"}}, {"head": {"id": "aef26327-29db-4eff-b316-8146ce819497", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155227570400, "endTime": 13155270477400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "db1278c1-0cd5-488c-b5da-cf497f43075d", "logId": "01fab1d6-b6b9-4fde-ac0c-90fe38eef7f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db1278c1-0cd5-488c-b5da-cf497f43075d", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155222766600}, "additional": {"logType": "detail", "children": [], "durationId": "aef26327-29db-4eff-b316-8146ce819497"}}, {"head": {"id": "1f1af405-b710-416d-a69e-570af927b713", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155223310800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3891a829-6f2c-4847-b309-80704cdac8cf", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155223432100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd54a99a-465f-44be-a761-6f9f752a760b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155223502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405dc6ee-8360-4f28-9b7a-89e013797f7b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155224508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdabec9-0d67-44af-88de-7d6c603c2eae", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155227621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2013074-1d13-4c82-b819-ea5886dc9ec8", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155269991300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0221bed-ae3f-4f34-b028-9f266caf7c30", "name": "entry : default@PreviewCompileResource cost memory -0.18471527099609375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155270198700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01fab1d6-b6b9-4fde-ac0c-90fe38eef7f6", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155227570400, "endTime": 13155270477400}, "additional": {"logType": "info", "children": [], "durationId": "aef26327-29db-4eff-b316-8146ce819497"}}, {"head": {"id": "b4570c92-8021-4a68-a5d3-2e3b974a9566", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274363600, "endTime": 13155274824100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "5d2002d0-ca45-45c6-80fa-1e13b8a6713b", "logId": "63412c9a-f95a-40fe-aae1-14b8d88af3a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d2002d0-ca45-45c6-80fa-1e13b8a6713b", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155273507200}, "additional": {"logType": "detail", "children": [], "durationId": "b4570c92-8021-4a68-a5d3-2e3b974a9566"}}, {"head": {"id": "8776c559-ad34-4c98-b8ff-6479c0f44869", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb18a496-2835-49e2-8139-10e83aa6d0d2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274176900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf09911-6697-420d-91a6-e9564a3ce0f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "784048b5-c800-4797-b9c4-ef4b8c475819", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274373600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f82ee81-5288-4c00-8b1d-4e51de781907", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e28b1f2-871f-41f9-9013-2369e87ae829", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274552500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da5999e-2b6a-4ea0-afd7-18aa53887cdd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274602600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e42c0d-e12c-4365-ab9d-dbe2e58e5867", "name": "entry : default@PreviewHookCompileResource cost memory 0.05167388916015625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03b4376-6a17-407c-8199-39e31e49cd9d", "name": "runTaskFromQueue task cost before running: 376 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274765200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63412c9a-f95a-40fe-aae1-14b8d88af3a6", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155274363600, "endTime": 13155274824100, "totalTime": 380800}, "additional": {"logType": "info", "children": [], "durationId": "b4570c92-8021-4a68-a5d3-2e3b974a9566"}}, {"head": {"id": "0811c7f1-e906-42d5-a78c-99c115131761", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155278676500, "endTime": 13155281216500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c48dbf69-b101-499d-ad9c-7bed60a2d2c7", "logId": "b3c0cdba-4b16-481c-9667-686fec4af368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c48dbf69-b101-499d-ad9c-7bed60a2d2c7", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155277130800}, "additional": {"logType": "detail", "children": [], "durationId": "0811c7f1-e906-42d5-a78c-99c115131761"}}, {"head": {"id": "cbaf008d-ea60-4a31-836b-156a953efaec", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155277820600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3440753f-8f74-444a-a1d8-9ebba5b11699", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155277945200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d69f2d01-1052-46cf-b01f-a7fbb5c5caa6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155278002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb0215d-1278-438e-bdb8-7c3f1b21cb03", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155278687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfdd0ae7-2815-44f0-8ba3-48cf936ca75d", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155280951900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf67a64-143f-48c4-95eb-d24b42320913", "name": "entry : default@CopyPreviewProfile cost memory 0.1301422119140625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155281141500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3c0cdba-4b16-481c-9667-686fec4af368", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155278676500, "endTime": 13155281216500}, "additional": {"logType": "info", "children": [], "durationId": "0811c7f1-e906-42d5-a78c-99c115131761"}}, {"head": {"id": "5b1b45dd-4387-4500-8173-621b2fb4ba10", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155284875900, "endTime": 13155285380800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "41d80e25-9b8c-42af-9ef0-1438305cc01a", "logId": "9b79a2f2-f76b-4eca-9699-3de297a5684f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41d80e25-9b8c-42af-9ef0-1438305cc01a", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155283450600}, "additional": {"logType": "detail", "children": [], "durationId": "5b1b45dd-4387-4500-8173-621b2fb4ba10"}}, {"head": {"id": "c35077ea-96de-4a0a-8fc3-a12ad6143652", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155283955200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ec48c6c-47b2-42b0-976b-b96f24df111a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155284070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef8075c-7dab-491b-b90e-e76c73f32e7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155284127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87c6c452-fcee-4278-90a1-7664d90628cb", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155284886300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daac55c7-4c8b-456a-a101-53a3b18c545f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155285018400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e53268c-78c9-4593-a695-6ec4bba1096b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155285072900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b92e72-b88d-47ea-ab3c-3232ed2a7f36", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155285121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48afa47e-af7d-44ba-b6b1-b102853814e7", "name": "entry : default@ReplacePreviewerPage cost memory 0.0516204833984375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155285221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65837eb8-1847-4780-829a-4f14d84813e5", "name": "runTaskFromQueue task cost before running: 387 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155285321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b79a2f2-f76b-4eca-9699-3de297a5684f", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155284875900, "endTime": 13155285380800, "totalTime": 420000}, "additional": {"logType": "info", "children": [], "durationId": "5b1b45dd-4387-4500-8173-621b2fb4ba10"}}, {"head": {"id": "c852eab2-4f47-47d6-a45f-897d2f48c4cb", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155287425500, "endTime": 13155287762000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "acde9475-1638-4412-b6ec-dd1fa84d289b", "logId": "d8b75c1f-57b5-4b58-bb2a-927cd40a4ab6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acde9475-1638-4412-b6ec-dd1fa84d289b", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155287344700}, "additional": {"logType": "detail", "children": [], "durationId": "c852eab2-4f47-47d6-a45f-897d2f48c4cb"}}, {"head": {"id": "6cc43156-d693-4d94-8068-abb4ec537d18", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155287440700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43459194-5ea0-4568-9153-50dfd56744eb", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155287606500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf0134c-8e6f-4314-aea6-259e3463e14a", "name": "runTaskFromQueue task cost before running: 389 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155287705300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8b75c1f-57b5-4b58-bb2a-927cd40a4ab6", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155287425500, "endTime": 13155287762000, "totalTime": 254400}, "additional": {"logType": "info", "children": [], "durationId": "c852eab2-4f47-47d6-a45f-897d2f48c4cb"}}, {"head": {"id": "16b54e26-c993-4770-8b56-e04b4bf52a61", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155290832600, "endTime": 13155293379700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5724ab79-bf3d-4cce-b168-8c7ce2e8d254", "logId": "6be3e1e0-e347-4db7-ac0e-6d021d83b9b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5724ab79-bf3d-4cce-b168-8c7ce2e8d254", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155289446500}, "additional": {"logType": "detail", "children": [], "durationId": "16b54e26-c993-4770-8b56-e04b4bf52a61"}}, {"head": {"id": "45a878a5-de55-4b6a-bd02-f315bf3d89ee", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155289933400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f22708-6c9c-48ac-9552-5a6b0b3e60d9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155290030400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81500429-c1a5-4fff-b78b-1028462b11f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155290090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e4eecf-0a54-4a40-9fef-63cbb3f294a5", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": **********1500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e894c4-3236-4f5b-96bf-764fe7507390", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155293159000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6966360c-19aa-4d9d-9d6e-8c55a21b240b", "name": "entry : default@PreviewUpdateAssets cost memory 0.109130859375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155293281200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be3e1e0-e347-4db7-ac0e-6d021d83b9b6", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155290832600, "endTime": 13155293379700}, "additional": {"logType": "info", "children": [], "durationId": "16b54e26-c993-4770-8b56-e04b4bf52a61"}}, {"head": {"id": "4ba89d80-8ffc-49f3-ba84-e1215d7db40f", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155301431300, "endTime": 13168240764400}, "additional": {"children": ["480b9c27-cef3-4eaf-957e-b0e4930e0dbc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "3d59cc0c-7502-4dfe-858c-10aca752864a", "logId": "10940323-86c6-4d3f-b3f9-775d320cfb27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d59cc0c-7502-4dfe-858c-10aca752864a", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155295473200}, "additional": {"logType": "detail", "children": [], "durationId": "4ba89d80-8ffc-49f3-ba84-e1215d7db40f"}}, {"head": {"id": "88664193-45a3-4998-9046-f963c1c4579c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155295961200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ec6d29-c478-4ea0-a4d8-fe0c0a57ae56", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155296065000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2512092-abe0-43a8-a4e4-6eb3165679f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155296122700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebed36ad-4296-4715-83a1-aa7957cff285", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155301448800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5049a922-be02-4319-b9e5-c0ad0ca52ccf", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155338291400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2597c15-f80d-4c95-abd6-0d054cd16cee", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155338464000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13155352795300, "endTime": 13168236965700}, "additional": {"children": ["5c13a189-72fd-4376-af44-21987b1ed002", "51a3e53e-dac3-4930-b943-54e1187f3193", "628e9aa1-df50-4771-aede-16aebdcb65de", "4af363e3-69af-4aaa-9923-0ec9feec8d12", "7c5c496f-c6fb-4143-bfd4-847c2f0a5604", "426200e2-ad5b-4b13-bf66-4de8cf89fada", "139ef5f4-3bbb-45ee-9670-98c7f2a595b9", "118951fc-c526-4540-8de8-7155f4c49c61", "c8bfe293-e62e-4438-b136-a9f447bb9993", "5bd4fe3a-3e84-4492-90dc-0de2fcd2b9bd", "41b9c781-631b-494b-b5c5-d570ec5e19b4", "ac8b2135-f7b5-4425-89b0-f8172ab7a946", "7826620a-fb24-42f6-be04-d8f5bf04a3fd"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4ba89d80-8ffc-49f3-ba84-e1215d7db40f", "logId": "2b09c29f-1acf-4dea-a0f0-98557720f301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17a7f0ef-34ba-4f57-a172-3222927b545f", "name": "entry : default@PreviewArkTS cost memory 0.953369140625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155354956700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77dd1396-56e8-4689-8daf-8b85682535c1", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13158905186700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c13a189-72fd-4376-af44-21987b1ed002", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13158906198000, "endTime": 13158906225400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "b89f0542-64af-43de-8577-df81dff8bfe7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b89f0542-64af-43de-8577-df81dff8bfe7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13158906198000, "endTime": 13158906225400}, "additional": {"logType": "info", "children": [], "durationId": "5c13a189-72fd-4376-af44-21987b1ed002", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "1dcea75e-8959-4005-9182-c9fe08a78c00", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13164430386000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51a3e53e-dac3-4930-b943-54e1187f3193", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13164432669400, "endTime": 13164432901700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "dcfd4a1e-b2c0-4a72-8498-a4d9d3138668"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcfd4a1e-b2c0-4a72-8498-a4d9d3138668", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13164432669400, "endTime": 13164432901700}, "additional": {"logType": "info", "children": [], "durationId": "51a3e53e-dac3-4930-b943-54e1187f3193", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "f2b499d5-c881-4a5d-91e1-0366770ef077", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13164762400900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628e9aa1-df50-4771-aede-16aebdcb65de", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13164763749500, "endTime": 13164763780700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "3d52fb1d-78d7-42fd-89c6-104ec9d3cdde"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d52fb1d-78d7-42fd-89c6-104ec9d3cdde", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13164763749500, "endTime": 13164763780700}, "additional": {"logType": "info", "children": [], "durationId": "628e9aa1-df50-4771-aede-16aebdcb65de", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "242304ca-ce4d-4f02-83ee-6ae4831a801f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13164910259200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af363e3-69af-4aaa-9923-0ec9feec8d12", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13164912084900, "endTime": 13164912116600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "827d19b9-a546-415d-bb3a-927831484bf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "827d19b9-a546-415d-bb3a-927831484bf5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13164912084900, "endTime": 13164912116600}, "additional": {"logType": "info", "children": [], "durationId": "4af363e3-69af-4aaa-9923-0ec9feec8d12", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "83d29809-0126-4df8-ab93-805b323f7d19", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165041844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c5c496f-c6fb-4143-bfd4-847c2f0a5604", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13165043141400, "endTime": 13165043174600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "22659d6d-66c2-4f40-9435-a3da2a78b6a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22659d6d-66c2-4f40-9435-a3da2a78b6a9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165043141400, "endTime": 13165043174600}, "additional": {"logType": "info", "children": [], "durationId": "7c5c496f-c6fb-4143-bfd4-847c2f0a5604", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "3c53564a-6f2b-47c6-9b29-bd1ab57b12f8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165232475600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "426200e2-ad5b-4b13-bf66-4de8cf89fada", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13165233588400, "endTime": 13165233614800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "f48eb37c-7064-4360-bd8e-96897388cf8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f48eb37c-7064-4360-bd8e-96897388cf8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165233588400, "endTime": 13165233614800}, "additional": {"logType": "info", "children": [], "durationId": "426200e2-ad5b-4b13-bf66-4de8cf89fada", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "6010b593-6355-4a80-87a9-2bc1a79a8909", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165379108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "139ef5f4-3bbb-45ee-9670-98c7f2a595b9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13165380499200, "endTime": 13165380642400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "ecaa1f65-2cfd-4dac-94cd-ce654ab009cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecaa1f65-2cfd-4dac-94cd-ce654ab009cc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165380499200, "endTime": 13165380642400}, "additional": {"logType": "info", "children": [], "durationId": "139ef5f4-3bbb-45ee-9670-98c7f2a595b9", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "f450c4f8-5c7a-4898-b787-3c74750c6c76", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165489051100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118951fc-c526-4540-8de8-7155f4c49c61", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13165492477100, "endTime": 13165492527300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "dc27955c-1cf6-430c-893f-0d9c64d1952e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc27955c-1cf6-430c-893f-0d9c64d1952e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13165492477100, "endTime": 13165492527300}, "additional": {"logType": "info", "children": [], "durationId": "118951fc-c526-4540-8de8-7155f4c49c61", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "98dd458e-d8aa-4e74-9581-0f1f84f747f3", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168235229000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8bfe293-e62e-4438-b136-a9f447bb9993", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13168236722400, "endTime": 13168236755800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "9ff74718-1d70-4999-85d7-0de21d22695a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ff74718-1d70-4999-85d7-0de21d22695a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168236722400, "endTime": 13168236755800}, "additional": {"logType": "info", "children": [], "durationId": "c8bfe293-e62e-4438-b136-a9f447bb9993", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "2b09c29f-1acf-4dea-a0f0-98557720f301", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13155352795300, "endTime": 13168236965700}, "additional": {"logType": "info", "children": ["b89f0542-64af-43de-8577-df81dff8bfe7", "dcfd4a1e-b2c0-4a72-8498-a4d9d3138668", "3d52fb1d-78d7-42fd-89c6-104ec9d3cdde", "827d19b9-a546-415d-bb3a-927831484bf5", "22659d6d-66c2-4f40-9435-a3da2a78b6a9", "f48eb37c-7064-4360-bd8e-96897388cf8d", "ecaa1f65-2cfd-4dac-94cd-ce654ab009cc", "dc27955c-1cf6-430c-893f-0d9c64d1952e", "9ff74718-1d70-4999-85d7-0de21d22695a", "4c7744f1-1dcc-4cd4-86ad-5ef6d4aa1689", "b1a42ddd-4b3a-4cf4-b899-6860122e5fb0", "428bfbff-93e9-44eb-824b-1090e56a4bd1", "e7dd4a62-ff05-4a28-b98b-94153d0ce619"], "durationId": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "parent": "10940323-86c6-4d3f-b3f9-775d320cfb27"}}, {"head": {"id": "5bd4fe3a-3e84-4492-90dc-0de2fcd2b9bd", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13157774532500, "endTime": 13158876420700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "4c7744f1-1dcc-4cd4-86ad-5ef6d4aa1689"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c7744f1-1dcc-4cd4-86ad-5ef6d4aa1689", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13157774532500, "endTime": 13158876420700}, "additional": {"logType": "info", "children": [], "durationId": "5bd4fe3a-3e84-4492-90dc-0de2fcd2b9bd", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "41b9c781-631b-494b-b5c5-d570ec5e19b4", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13158876617100, "endTime": 13158881036200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "b1a42ddd-4b3a-4cf4-b899-6860122e5fb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1a42ddd-4b3a-4cf4-b899-6860122e5fb0", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13158876617100, "endTime": 13158881036200}, "additional": {"logType": "info", "children": [], "durationId": "41b9c781-631b-494b-b5c5-d570ec5e19b4", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "ac8b2135-f7b5-4425-89b0-f8172ab7a946", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13158881126600, "endTime": 13158881131900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "428bfbff-93e9-44eb-824b-1090e56a4bd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "428bfbff-93e9-44eb-824b-1090e56a4bd1", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13158881126600, "endTime": 13158881131900}, "additional": {"logType": "info", "children": [], "durationId": "ac8b2135-f7b5-4425-89b0-f8172ab7a946", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "7826620a-fb24-42f6-be04-d8f5bf04a3fd", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker2", "startTime": 13158881192800, "endTime": 13168235309300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "480b9c27-cef3-4eaf-957e-b0e4930e0dbc", "logId": "e7dd4a62-ff05-4a28-b98b-94153d0ce619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7dd4a62-ff05-4a28-b98b-94153d0ce619", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13158881192800, "endTime": 13168235309300}, "additional": {"logType": "info", "children": [], "durationId": "7826620a-fb24-42f6-be04-d8f5bf04a3fd", "parent": "2b09c29f-1acf-4dea-a0f0-98557720f301"}}, {"head": {"id": "10940323-86c6-4d3f-b3f9-775d320cfb27", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13155301431300, "endTime": 13168240764400, "totalTime": 12939320700}, "additional": {"logType": "info", "children": ["2b09c29f-1acf-4dea-a0f0-98557720f301"], "durationId": "4ba89d80-8ffc-49f3-ba84-e1215d7db40f"}}, {"head": {"id": "8581eacc-192e-4211-98c0-7c4bf850db24", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168247345100, "endTime": 13168247959200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "800e55ea-fba0-4dfc-a9f8-1bd608885757", "logId": "c05561e6-6be2-40cb-ba26-3887b7ac48e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "800e55ea-fba0-4dfc-a9f8-1bd608885757", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168247233800}, "additional": {"logType": "detail", "children": [], "durationId": "8581eacc-192e-4211-98c0-7c4bf850db24"}}, {"head": {"id": "4d4882a7-6681-4bde-bd70-f1f7d3ec0566", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168247371800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1094b85b-3362-4c5c-b42e-722d5374bf8f", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168247632200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a739fec-b594-4f94-9800-4d116c79d4a4", "name": "runTaskFromQueue task cost before running: 13 s 349 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168247806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05561e6-6be2-40cb-ba26-3887b7ac48e0", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168247345100, "endTime": 13168247959200, "totalTime": 415100}, "additional": {"logType": "info", "children": [], "durationId": "8581eacc-192e-4211-98c0-7c4bf850db24"}}, {"head": {"id": "5a940299-c970-4ad2-86fa-d79a70fdfd24", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168263603300, "endTime": 13168263633300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea1e6df4-ce3b-4b2d-b641-23b1640978c3", "logId": "27af67d5-f283-4d9b-97d6-34c9477cc259"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27af67d5-f283-4d9b-97d6-34c9477cc259", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168263603300, "endTime": 13168263633300}, "additional": {"logType": "info", "children": [], "durationId": "5a940299-c970-4ad2-86fa-d79a70fdfd24"}}, {"head": {"id": "219869dd-572a-4989-835b-fb0071d82028", "name": "BUILD SUCCESSFUL in 13 s 365 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168263699300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "0f26d963-7b10-4ef5-a8b1-4b5c6b324f75", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13154899258600, "endTime": 13168264039400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 18}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "3845ae84-d63f-45d7-8db9-a1dad374f1c1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fef3573b-2fd8-4155-964a-9dbfcc5346c4", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264165600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19feeac-e758-4e49-90bc-e4c9f0cdfebd", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264230900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a9d659-ed04-424e-9ef2-5495b582340e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264290200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354e6d4a-ce9c-468f-9aa9-2c44847aba4f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264346500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08794ba-07aa-4c0a-946d-b19cea1e5597", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbfd5ab5-a453-4f9d-8980-9963382be50b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4665cfd-521b-427a-8a80-718cda39369e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264540600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5c5d80a-f8b3-4c19-954d-b27c2056efbd", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264604000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29963c73-79c4-4662-a27e-02458b942eaa", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168264665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba9f8c5-e934-4817-9b23-3a7a805d2d79", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168268880100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b95bdf-5388-47d8-a451-14671d497d17", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168270233400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "400aff46-a8eb-46f4-aae7-d6bf3ea36cfd", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168270779000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a64260-4e58-4965-a86d-22c385cc920d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168289705500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d72eb54c-caf0-42f4-ae7f-e0d794104ec4", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168290830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349ed1bd-0f06-4e0b-bb62-f90495907835", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168291332400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5275d7b-5fc6-4fcb-87f1-34cf5e621f48", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168291690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68dee53c-0151-45e4-8a84-8969b1fce726", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168292678200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78431197-d17e-42d6-bec5-a6a5d2e67121", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168298502200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba69d401-2840-4d78-91e6-ecac3614d1f1", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168298958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb060d8-77e9-41b4-8694-c6522c86a9b9", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168299303200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa3029f-e913-48c2-a809-e20967bb7a62", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168299657300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1455e888-373b-4e50-ac4b-f934c466fb80", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:36 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13168300224200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}