package com.icss.wallet.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.icss.wallet.entity.User;
import com.icss.wallet.mapper.UserMapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

import static com.baomidou.mybatisplus.extension.toolkit.Db.save;

@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    public User findByPhone(String phone) {
        return baseMapper.findByPhone(phone);
    }

    /**
     * 更新用户最后登录时间
     * @param userId 用户ID
     */
    public void updateLastLoginTime(Long userId) {
        User user = new User();
        user.setUserId(userId);
        user.setLastLoginTime(new Date());
        baseMapper.updateById(user);
    }

    /**
     * 检查手机号是否已注册
     * @param phone 手机号
     * @return 是否已注册
     */
    public boolean isPhoneRegistered(String phone) {
        return findByPhone(phone) != null;
    }

    /**
     * 注册新用户
     * @param user 用户信息
     * @return 是否注册成功
     */
    public boolean register(User user) {
        if (isPhoneRegistered(user.getPhone())) {
            return false;
        }

        // 设置默认值
        user.setStatus(1);
        user.setPayLimit(new BigDecimal("50000.00"));

        return save(user);
    }
}