/* 电子钱包管理系统 - 设计规范 */

/* ==================== 色彩系统 ==================== */
:root {
  /* 主色调 */
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 辅助色 */
  --secondary-color: #764ba2;
  --accent-color: #ff6b6b;
  --accent-light: #ff8e8e;
  --accent-dark: #e74c3c;
  
  /* 功能色 */
  --success-color: #4CAF50;
  --success-light: #81C784;
  --success-dark: #388E3C;
  --warning-color: #FF9800;
  --warning-light: #FFB74D;
  --warning-dark: #F57C00;
  --error-color: #F44336;
  --error-light: #EF5350;
  --error-dark: #D32F2F;
  --info-color: #2196F3;
  --info-light: #64B5F6;
  --info-dark: #1976D2;
  
  /* 中性色 */
  --text-primary: #2c3e50;
  --text-secondary: #5a6c7d;
  --text-tertiary: #7f8c8d;
  --text-disabled: #bdc3c7;
  --text-inverse: #ffffff;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #ecf0f1;
  --bg-overlay: rgba(0, 0, 0, 0.6);
  --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  /* 边框色 */
  --border-light: #ecf0f1;
  --border-medium: #d5dbdb;
  --border-dark: #95a5a6;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 40px rgba(0, 0, 0, 0.16);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.24);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* 字体大小 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-md: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  --font-3xl: 32px;
  
  /* 字体权重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==================== 通用样式类 ==================== */

/* 文字颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

/* 背景色 */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-gradient { background: var(--bg-gradient); }
.bg-primary-gradient { background: var(--primary-gradient); }

/* 阴影 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* 圆角 */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 间距 */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 过渡动画 */
.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* ==================== 组件样式 ==================== */

/* 卡片组件 */
.wallet-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
}

.wallet-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* 按钮组件 */
.wallet-btn {
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-medium);
  transition: var(--transition-normal);
  border: none;
  cursor: pointer;
}

.wallet-btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.wallet-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.wallet-btn-success {
  background: var(--success-color);
  color: var(--text-inverse);
}

.wallet-btn-warning {
  background: var(--warning-color);
  color: var(--text-inverse);
}

.wallet-btn-error {
  background: var(--error-color);
  color: var(--text-inverse);
}

/* 输入框组件 */
.wallet-input {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: var(--transition-normal);
  font-size: var(--font-md);
}

.wallet-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

/* 标签组件 */
.wallet-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: var(--font-medium);
}

.wallet-tag-success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-dark);
}

.wallet-tag-warning {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-dark);
}

.wallet-tag-error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-dark);
}

.wallet-tag-info {
  background: rgba(33, 150, 243, 0.1);
  color: var(--info-dark);
}

/* ==================== 动画效果 ==================== */

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 滑入动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 摇摆动画 */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* 动画类 */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.6s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* ==================== 响应式断点 ==================== */
@media (max-width: 480px) {
  :root {
    --font-xs: 11px;
    --font-sm: 13px;
    --font-md: 15px;
    --font-lg: 17px;
    --font-xl: 19px;
    --font-2xl: 22px;
    --font-3xl: 28px;
    
    --spacing-xs: 3px;
    --spacing-sm: 6px;
    --spacing-md: 12px;
    --spacing-lg: 18px;
    --spacing-xl: 24px;
    --spacing-2xl: 36px;
  }
}

@media (max-width: 768px) {
  .wallet-card {
    padding: var(--spacing-md);
  }
  
  .wallet-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
  }
}

/* ==================== 工具类 ==================== */

/* 显示/隐藏 */
.hidden { display: none !important; }
.visible { display: block !important; }

/* 文字对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Flex布局 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* 宽度高度 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

/* 位置 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 溢出 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* 光标 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }
