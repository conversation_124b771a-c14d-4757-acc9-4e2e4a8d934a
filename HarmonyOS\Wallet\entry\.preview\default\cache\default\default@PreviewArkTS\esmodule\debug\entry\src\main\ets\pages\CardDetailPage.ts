if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CardDetailPage_Params {
    params?: RouterParams;
    cardId?: number;
    bankName?: string;
    cardNumber?: string;
    cardType?: number;
    cardHolder?: string;
    phone?: string;
    status?: number;
    isDefault?: number;
    expiryDate?: string;
    cvv?: string;
    createTime?: string;
    showFullCardNumber?: boolean;
}
import router from "@ohos:router";
interface RouterParams {
    cardId?: number;
    bankName?: string;
    cardNumber?: string;
    cardType?: number; // 1-储蓄卡, 2-信用卡
    cardHolder?: string;
    phone?: string;
    status?: number; // 0-未绑定, 1-已绑定
    isDefault?: number; // 0-非默认, 1-默认
    expiryDate?: string;
    cvv?: string;
    createTime?: string;
}
class CardDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.params = router.getParams() as RouterParams;
        this.__cardId = new ObservedPropertySimplePU(this.params.cardId || 0, this, "cardId");
        this.__bankName = new ObservedPropertySimplePU(this.params.bankName || '', this, "bankName");
        this.__cardNumber = new ObservedPropertySimplePU(this.params.cardNumber || '', this, "cardNumber");
        this.__cardType = new ObservedPropertySimplePU(this.params.cardType || 1, this, "cardType");
        this.__cardHolder = new ObservedPropertySimplePU(this.params.cardHolder || '', this, "cardHolder");
        this.__phone = new ObservedPropertySimplePU(this.params.phone || '', this, "phone");
        this.__status = new ObservedPropertySimplePU(this.params.status || 0, this, "status");
        this.__isDefault = new ObservedPropertySimplePU(this.params.isDefault || 0, this, "isDefault");
        this.__expiryDate = new ObservedPropertySimplePU(this.params.expiryDate || '', this, "expiryDate");
        this.__cvv = new ObservedPropertySimplePU(this.params.cvv || '', this, "cvv");
        this.__createTime = new ObservedPropertySimplePU(this.params.createTime || '', this, "createTime");
        this.__showFullCardNumber = new ObservedPropertySimplePU(false, this, "showFullCardNumber");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CardDetailPage_Params) {
        if (params.params !== undefined) {
            this.params = params.params;
        }
        if (params.cardId !== undefined) {
            this.cardId = params.cardId;
        }
        if (params.bankName !== undefined) {
            this.bankName = params.bankName;
        }
        if (params.cardNumber !== undefined) {
            this.cardNumber = params.cardNumber;
        }
        if (params.cardType !== undefined) {
            this.cardType = params.cardType;
        }
        if (params.cardHolder !== undefined) {
            this.cardHolder = params.cardHolder;
        }
        if (params.phone !== undefined) {
            this.phone = params.phone;
        }
        if (params.status !== undefined) {
            this.status = params.status;
        }
        if (params.isDefault !== undefined) {
            this.isDefault = params.isDefault;
        }
        if (params.expiryDate !== undefined) {
            this.expiryDate = params.expiryDate;
        }
        if (params.cvv !== undefined) {
            this.cvv = params.cvv;
        }
        if (params.createTime !== undefined) {
            this.createTime = params.createTime;
        }
        if (params.showFullCardNumber !== undefined) {
            this.showFullCardNumber = params.showFullCardNumber;
        }
    }
    updateStateVars(params: CardDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cardId.purgeDependencyOnElmtId(rmElmtId);
        this.__bankName.purgeDependencyOnElmtId(rmElmtId);
        this.__cardNumber.purgeDependencyOnElmtId(rmElmtId);
        this.__cardType.purgeDependencyOnElmtId(rmElmtId);
        this.__cardHolder.purgeDependencyOnElmtId(rmElmtId);
        this.__phone.purgeDependencyOnElmtId(rmElmtId);
        this.__status.purgeDependencyOnElmtId(rmElmtId);
        this.__isDefault.purgeDependencyOnElmtId(rmElmtId);
        this.__expiryDate.purgeDependencyOnElmtId(rmElmtId);
        this.__cvv.purgeDependencyOnElmtId(rmElmtId);
        this.__createTime.purgeDependencyOnElmtId(rmElmtId);
        this.__showFullCardNumber.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cardId.aboutToBeDeleted();
        this.__bankName.aboutToBeDeleted();
        this.__cardNumber.aboutToBeDeleted();
        this.__cardType.aboutToBeDeleted();
        this.__cardHolder.aboutToBeDeleted();
        this.__phone.aboutToBeDeleted();
        this.__status.aboutToBeDeleted();
        this.__isDefault.aboutToBeDeleted();
        this.__expiryDate.aboutToBeDeleted();
        this.__cvv.aboutToBeDeleted();
        this.__createTime.aboutToBeDeleted();
        this.__showFullCardNumber.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private params: RouterParams;
    private __cardId: ObservedPropertySimplePU<number>;
    get cardId() {
        return this.__cardId.get();
    }
    set cardId(newValue: number) {
        this.__cardId.set(newValue);
    }
    private __bankName: ObservedPropertySimplePU<string>;
    get bankName() {
        return this.__bankName.get();
    }
    set bankName(newValue: string) {
        this.__bankName.set(newValue);
    }
    private __cardNumber: ObservedPropertySimplePU<string>;
    get cardNumber() {
        return this.__cardNumber.get();
    }
    set cardNumber(newValue: string) {
        this.__cardNumber.set(newValue);
    }
    private __cardType: ObservedPropertySimplePU<number>;
    get cardType() {
        return this.__cardType.get();
    }
    set cardType(newValue: number) {
        this.__cardType.set(newValue);
    }
    private __cardHolder: ObservedPropertySimplePU<string>;
    get cardHolder() {
        return this.__cardHolder.get();
    }
    set cardHolder(newValue: string) {
        this.__cardHolder.set(newValue);
    }
    private __phone: ObservedPropertySimplePU<string>;
    get phone() {
        return this.__phone.get();
    }
    set phone(newValue: string) {
        this.__phone.set(newValue);
    }
    private __status: ObservedPropertySimplePU<number>;
    get status() {
        return this.__status.get();
    }
    set status(newValue: number) {
        this.__status.set(newValue);
    }
    private __isDefault: ObservedPropertySimplePU<number>;
    get isDefault() {
        return this.__isDefault.get();
    }
    set isDefault(newValue: number) {
        this.__isDefault.set(newValue);
    }
    private __expiryDate: ObservedPropertySimplePU<string>;
    get expiryDate() {
        return this.__expiryDate.get();
    }
    set expiryDate(newValue: string) {
        this.__expiryDate.set(newValue);
    }
    private __cvv: ObservedPropertySimplePU<string>;
    get cvv() {
        return this.__cvv.get();
    }
    set cvv(newValue: string) {
        this.__cvv.set(newValue);
    }
    private __createTime: ObservedPropertySimplePU<string>;
    get createTime() {
        return this.__createTime.get();
    }
    set createTime(newValue: string) {
        this.__createTime.set(newValue);
    }
    private __showFullCardNumber: ObservedPropertySimplePU<boolean>;
    get showFullCardNumber() {
        return this.__showFullCardNumber.get();
    }
    set showFullCardNumber(newValue: boolean) {
        this.__showFullCardNumber.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(37:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f7fa');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        // 银行卡展示区域
        this.buildCardDisplay.bind(this)();
        // 银行卡信息区域
        this.buildCardInfo.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(47:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(56:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 16, right: 16 });
            Row.backgroundColor('#ffffff');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777260, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(57:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡详情');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(64:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位符保持居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(72:7)", "entry");
            // 占位符保持居中
            Row.width(24);
            // 占位符保持居中
            Row.height(24);
        }, Row);
        // 占位符保持居中
        Row.pop();
        Row.pop();
    }
    buildCardDisplay(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(84:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡样式展示
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(86:7)", "entry");
            // 银行卡样式展示
            Stack.margin(16);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片背景
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(88:9)", "entry");
            // 卡片背景
            Column.width('100%');
            // 卡片背景
            Column.height(200);
            // 卡片背景
            Column.padding(24);
            // 卡片背景
            Column.backgroundColor(this.getCardColor());
            // 卡片背景
            Column.borderRadius(16);
            // 卡片背景
            Column.justifyContent(FlexAlign.SpaceBetween);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行名称和卡类型
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(90:11)", "entry");
            // 银行名称和卡类型
            Row.width('100%');
            // 银行名称和卡类型
            Row.margin({ bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.bankName);
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(91:13)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#ffffff');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getCardTypeText());
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(97:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#ffffff');
            Text.backgroundColor('rgba(255,255,255,0.2)');
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            Text.borderRadius(12);
        }, Text);
        Text.pop();
        // 银行名称和卡类型
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡号显示
            Text.create(this.getDisplayCardNumber());
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(108:11)", "entry");
            // 卡号显示
            Text.fontSize(20);
            // 卡号显示
            Text.fontWeight(FontWeight.Medium);
            // 卡号显示
            Text.fontColor('#ffffff');
            // 卡号显示
            Text.letterSpacing(2);
            // 卡号显示
            Text.margin({ bottom: 20 });
        }, Text);
        // 卡号显示
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 持卡人姓名和状态
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(116:11)", "entry");
            // 持卡人姓名和状态
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardHolder);
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(117:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#ffffff');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.status === 1 ? '已绑定' : '未绑定');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(122:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#ffffff');
            Text.backgroundColor('rgba(255,255,255,0.2)');
            Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
            Text.borderRadius(12);
        }, Text);
        Text.pop();
        // 持卡人姓名和状态
        Row.pop();
        // 卡片背景
        Column.pop();
        // 银行卡样式展示
        Stack.pop();
        Column.pop();
    }
    buildCardInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(145:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.padding(20);
            Column.margin(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(147:7)", "entry");
            // 标题
            Row.width('100%');
            // 标题
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡信息');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(148:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
        }, Text);
        Text.pop();
        // 标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 信息列表
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(157:7)", "entry");
            // 信息列表
            Column.width('100%');
        }, Column);
        this.buildInfoItem.bind(this)('银行名称', this.bankName);
        this.buildInfoItem.bind(this)('卡片类型', this.getCardTypeText());
        this.buildInfoItem.bind(this)('持卡人姓名', this.cardHolder);
        this.buildInfoItem.bind(this)('卡号', this.getDisplayCardNumber(), true);
        this.buildInfoItem.bind(this)('绑定状态', this.status === 1 ? '已绑定' : '未绑定');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.createTime) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildInfoItem.bind(this)('绑定时间', this.formatTime(this.createTime));
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 信息列表
        Column.pop();
        Column.pop();
    }
    buildInfoItem(label: string, value: string, isCardNumber: boolean = false, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(178:5)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
            Row.border({
                width: { bottom: 1 },
                color: '#f0f0f0'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(179:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(184:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (isCardNumber) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create(this.showFullCardNumber ? { "id": 16777262, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" } : { "id": 16777263, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                        Image.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(191:9)", "entry");
                        Image.width(20);
                        Image.height(20);
                        Image.margin({ left: 8 });
                        Image.onClick(() => {
                            this.showFullCardNumber = !this.showFullCardNumber;
                        });
                    }, Image);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    // 获取卡片类型文本
    getCardTypeText(): string {
        return this.cardType === 1 ? '储蓄卡' : '信用卡';
    }
    // 获取显示的卡号
    getDisplayCardNumber(): string {
        if (this.showFullCardNumber) {
            return this.cardNumber;
        }
        if (this.cardNumber.length >= 4) {
            return `**** **** **** ${this.cardNumber.slice(-4)}`;
        }
        return this.cardNumber;
    }
    // 获取卡片颜色
    getCardColor(): string {
        switch (this.bankName) {
            case '中国工商银行': return '#c41e3a';
            case '中国建设银行': return '#003e7e';
            case '中国银行': return '#b8860b';
            case '中国农业银行': return '#00a651';
            case '招商银行': return '#d32f2f';
            case '交通银行': return '#1976d2';
            case '中信银行': return '#e53935';
            case '光大银行': return '#7b1fa2';
            case '华夏银行': return '#f57c00';
            case '民生银行': return '#388e3c';
            case '兴业银行': return '#303f9f';
            case '浦发银行': return '#1976d2';
            default: return '#1274ca';
        }
    }
    // 格式化时间
    formatTime(timeStr: string): string {
        try {
            const date = new Date(timeStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}/${month}/${day} ${hours}:${minutes}`;
        }
        catch (error) {
            return timeStr;
        }
    }
    // 获取银行图标
    getBankIcon(): Resource {
        switch (this.bankName) {
            case '中国银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '建设银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '工商银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '农业银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '招商银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '交通银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '中信银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '光大银行': return { "id": 16777276, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '华夏银行': return { "id": 16777279, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '民生银行': return { "id": 16777278, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '兴业银行': return { "id": 16777280, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '浦发银行': return { "id": 16777277, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            default: return { "id": 16777256, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CardDetailPage";
    }
}
registerNamedRoute(() => new CardDetailPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/CardDetailPage", pageFullPath: "entry/src/main/ets/pages/CardDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
