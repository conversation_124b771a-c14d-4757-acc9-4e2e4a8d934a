package com.icss.wallet.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.BankCard;
import com.icss.wallet.mapper.BankCardMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class BankCardService {
    @Autowired
    private BankCardMapper bankCardMapper;

    public List<BankCard> getAllCards() {
        return bankCardMapper.selectList(null);
    }

    public List<BankCard> getCardsByUser(Long userId) {
        return bankCardMapper.selectList(new QueryWrapper<BankCard>()
                .eq("user_id", userId));
    }

    public List<BankCard> getBoundCardsByUser(Long userId) {
        return bankCardMapper.selectList(new QueryWrapper<BankCard>()
                .eq("user_id", userId)
                .eq("status", 1));
    }

    public void addCard(BankCard bankCard) {
        // 检查必填字段
        if (bankCard.getCardNumber() == null || bankCard.getBankName() == null) {
            throw new RuntimeException("银行卡号和银行名称不能为空");
        }

        // 检查是否已存在
        BankCard existingCard = bankCardMapper.selectOne(new QueryWrapper<BankCard>()
                .eq("card_number", bankCard.getCardNumber()));
        if (existingCard != null) {
            throw new RuntimeException("该银行卡已存在");
        }

        // 设置默认值
        if (bankCard.getStatus() == null) {
            bankCard.setStatus(0); // 默认未绑定
        }
        if (bankCard.getCreateTime() == null) {
            bankCard.setCreateTime(new Date());
        }
        if (bankCard.getUpdateTime() == null) {
            bankCard.setUpdateTime(new Date());
        }

        bankCardMapper.insert(bankCard);
    }

    public void bindCard(BankCard bankCard) {
        // 检查必填字段
        if (bankCard.getCardNumber() == null || bankCard.getBankName() == null) {
            throw new RuntimeException("银行卡号和银行名称不能为空");
        }

        // 检查是否已存在
        BankCard existingCard = bankCardMapper.selectOne(new QueryWrapper<BankCard>()
                .eq("card_number", bankCard.getCardNumber()));
        if (existingCard == null) {
            throw new RuntimeException("该银行卡不存在，请先添加");
        }

        existingCard.setStatus(1); // 1表示已绑定
        existingCard.setUpdateTime(new Date());
        bankCardMapper.updateById(existingCard);
    }

    public void bindCardById(Long cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new RuntimeException("银行卡不存在");
        }

        card.setStatus(1); // 1表示已绑定
        card.setUpdateTime(new Date());
        bankCardMapper.updateById(card);
    }




    // 新增：根据ID获取银行卡
    public BankCard getCardById(Long cardId) {
        return bankCardMapper.selectById(cardId);
    }

    // 新增：删除银行卡
    public void deleteCard(Long cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new RuntimeException("银行卡不存在");
        }

        // 检查是否为默认卡
        if (card.getIsDefault() != null && card.getIsDefault() == 1) {
            throw new RuntimeException("默认银行卡不能删除，请先设置其他卡为默认卡");
        }

        // 检查是否为已绑定的卡
        if (card.getStatus() != null && card.getStatus() == 1) {
            throw new RuntimeException("已绑定的银行卡不能直接删除，请先解绑");
        }

        // 执行删除
        bankCardMapper.deleteById(cardId);
    }

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询银行卡信息（包含用户信息）
     */
    public IPage<BankCard> getBankCardsWithUserInfo(int pageNum, int pageSize, String phone, String cardNumber, Integer isBound) {
        Page<BankCard> page = new Page<>(pageNum, pageSize);
        return bankCardMapper.selectBankCardsWithUserInfo(page, phone, cardNumber, isBound);
    }

    /**
     * 管理员获取银行卡统计信息
     */
    public java.util.Map<String, Object> getBankCardStatistics() {
        return bankCardMapper.getBankCardStatistics();
    }

    /**
     * 管理员绑定银行卡
     */
    @Transactional
    public boolean bindCard(Long cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new RuntimeException("银行卡不存在");
        }

        card.setStatus(1); // 1表示已绑定
        card.setUpdateTime(new Date());
        return bankCardMapper.updateById(card) > 0;
    }

    /**
     * 管理员解绑银行卡
     */
    @Transactional
    public boolean unbindCard(Long cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new RuntimeException("银行卡不存在");
        }

        // 如果是默认卡，先取消默认状态
        if (card.getIsDefault() == 1) {
            card.setIsDefault(0);
        }

        card.setStatus(0); // 0表示未绑定
        card.setUpdateTime(new Date());
        return bankCardMapper.updateById(card) > 0;
    }

    /**
     * 管理员设置默认银行卡
     */
    @Transactional
    public boolean setDefaultCard(Long cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new RuntimeException("银行卡不存在");
        }

        if (card.getStatus() != 1) {
            throw new RuntimeException("只有已绑定的银行卡才能设为默认");
        }

        // 先取消该用户的其他默认卡
        UpdateWrapper<BankCard> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", card.getUserId())
                    .eq("is_default", 1);
        BankCard updateCard = new BankCard();
        updateCard.setIsDefault(0);
        updateCard.setUpdateTime(new Date());
        bankCardMapper.update(updateCard, updateWrapper);

        // 设置当前卡为默认
        card.setIsDefault(1);
        card.setUpdateTime(new Date());
        return bankCardMapper.updateById(card) > 0;
    }

    /**
     * 保存银行卡
     */
    public boolean save(BankCard bankCard) {
        bankCard.setCreateTime(new Date());
        bankCard.setUpdateTime(new Date());
        return bankCardMapper.insert(bankCard) > 0;
    }

    /**
     * 根据ID删除银行卡
     */
    public boolean removeById(Long cardId) {
        return bankCardMapper.deleteById(cardId) > 0;
    }
}