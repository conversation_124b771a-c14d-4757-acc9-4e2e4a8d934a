<template>
  <div class="card-detail-container">
    <h1>银行卡详情页面测试</h1>
    <p>当前路由: {{ route.path }}</p>
    <p>路由参数: {{ JSON.stringify(route.params) }}</p>
    <p>cardId: {{ cardId }}</p>
    
    <el-card>
      <template #header>
        <div>银行卡信息</div>
      </template>
      <div v-if="cardData.cardId">
        <p>银行卡ID: {{ cardData.cardId }}</p>
        <p>银行名称: {{ cardData.bankName }}</p>
        <p>卡号: {{ cardData.cardNumber }}</p>
        <p>持卡人: {{ cardData.cardHolder }}</p>
      </div>
      <div v-else>
        <p>暂无数据</p>
      </div>
    </el-card>
    
    <el-button @click="goBack" style="margin-top: 20px;">返回</el-button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const cardId = ref(route.params.id)

const cardData = ref({
  cardId: '',
  bankName: '',
  cardNumber: '',
  cardHolder: ''
})

const goBack = () => {
  router.push('/home/<USER>')
}

onMounted(() => {
  console.log('CardDetailSimple页面加载')
  console.log('路由:', route)
  console.log('参数:', route.params)
  console.log('cardId:', cardId.value)
  
  // 设置测试数据
  if (cardId.value) {
    cardData.value = {
      cardId: cardId.value,
      bankName: '测试银行',
      cardNumber: '1234 5678 9012 3456',
      cardHolder: '测试用户'
    }
  }
})
</script>

<style scoped>
.card-detail-container {
  padding: 20px;
}
</style>
