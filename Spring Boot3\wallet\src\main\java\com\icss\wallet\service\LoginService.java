package com.icss.wallet.service;


import com.icss.wallet.entity.Admin;
import com.icss.wallet.mapper.AdminMapper;
import com.icss.wallet.result.MD5Util;
import com.icss.wallet.result.TokenUtil;
import com.icss.wallet.service.SmsCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Transactional
public class LoginService {
    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private SmsCodeService smsCodeService;

    public String login(String username, String password) {
        // 先尝试通过用户名查找
        Admin admin = adminMapper.findByUsername(username);

        // 如果通过用户名找不到，再尝试通过手机号查找
        if (admin == null) {
            admin = adminMapper.findByPhone(username);
        }

        if (admin == null) {
            throw new RuntimeException("管理员账号不存在");
        }

        // 使用明文密码进行比较
        System.out.println("登录验证 - 输入: " + username + ", 输入密码: " + password + ", 数据库密码: " + admin.getPassword());
        if (!admin.getPassword().equals(password)) {
            throw new RuntimeException("密码错误");
        }

        if (admin.getStatus() == 0) {
            throw new RuntimeException("账号已被禁用");
        }

        // 更新最后登录时间
        admin.setLastLoginTime(new Date());
        adminMapper.updateById(admin);

        return TokenUtil.generateToken(admin.getAdminId().toString(), admin.getUsername());
    }

    /**
     * 管理员手机号验证码登录
     * @param phone 手机号
     * @param code 验证码
     * @return Token
     */
    public String loginWithCode(String phone, String code) {
        // 验证验证码
        boolean isValidCode = smsCodeService.verifyCode(phone, code, 4); // 4表示管理员登录验证码
        if (!isValidCode) {
            throw new RuntimeException("验证码错误或已过期");
        }

        // 查找管理员
        Admin admin = adminMapper.findByPhone(phone);
        if (admin == null) {
            throw new RuntimeException("管理员账号不存在");
        }

        if (admin.getStatus() == 0) {
            throw new RuntimeException("账号已被禁用");
        }

        // 更新最后登录时间
        admin.setLastLoginTime(new Date());
        adminMapper.updateById(admin);

        return TokenUtil.generateToken(admin.getAdminId().toString(), admin.getUsername());
    }

    /**
     * 管理员注册（用户名密码方式）
     * @param username 用户名
     * @param password 密码
     * @param phone 手机号
     * @param realName 真实姓名
     * @param role 角色 (admin/operator)
     * @return 注册结果
     */
    public String register(String username, String password, String phone, String realName, String role) {
        // 检查用户名是否已存在
        Admin existingAdmin = adminMapper.findByUsername(username);
        if (existingAdmin != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查手机号是否已存在
        Admin existingPhoneAdmin = adminMapper.findByPhone(phone);
        if (existingPhoneAdmin != null) {
            throw new RuntimeException("手机号已被注册");
        }

        // 验证角色
        if (!"admin".equals(role) && !"operator".equals(role)) {
            throw new RuntimeException("无效的角色类型");
        }

        // 创建新管理员
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword(password); // 明文密码
        admin.setPhone(phone);
        admin.setRealName(realName);
        admin.setRole(role);
        admin.setStatus(1); // 默认启用
        admin.setCreateTime(new Date());
        admin.setUpdateTime(new Date());

        // 保存到数据库
        int result = adminMapper.insertAdmin(admin);
        if (result > 0) {
            // 重新查询获取生成的ID
            Admin savedAdmin = adminMapper.findByUsername(username);
            return TokenUtil.generateToken(savedAdmin.getAdminId().toString(), savedAdmin.getUsername());
        } else {
            throw new RuntimeException("注册失败");
        }
    }

    /**
     * 管理员手机号验证码注册
     * @param phone 手机号
     * @param code 验证码
     * @param username 用户名
     * @param realName 真实姓名
     * @param role 角色
     * @return Token
     */
    public String registerWithCode(String phone, String code, String username, String realName, String role) {
        // 验证验证码
        boolean isValidCode = smsCodeService.verifyCode(phone, code, 5); // 5表示管理员注册验证码
        if (!isValidCode) {
            throw new RuntimeException("验证码错误或已过期");
        }

        // 检查用户名是否已存在
        Admin existingAdmin = adminMapper.findByUsername(username);
        if (existingAdmin != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查手机号是否已存在
        Admin existingPhoneAdmin = adminMapper.findByPhone(phone);
        if (existingPhoneAdmin != null) {
            throw new RuntimeException("手机号已被注册");
        }

        // 验证角色
        if (!"admin".equals(role) && !"operator".equals(role)) {
            throw new RuntimeException("无效的角色类型");
        }

        // 创建新管理员（无密码注册，后续可通过验证码登录或设置密码）
        Admin admin = new Admin();
        admin.setUsername(username);
        admin.setPassword("123456"); // 默认明文密码，建议首次登录后修改
        admin.setPhone(phone);
        admin.setRealName(realName);
        admin.setRole(role);
        admin.setStatus(1); // 默认启用
        admin.setCreateTime(new Date());
        admin.setUpdateTime(new Date());

        // 保存到数据库
        int result = adminMapper.insertAdmin(admin);
        if (result > 0) {
            // 重新查询获取生成的ID
            Admin savedAdmin = adminMapper.findByUsername(username);
            return TokenUtil.generateToken(savedAdmin.getAdminId().toString(), savedAdmin.getUsername());
        } else {
            throw new RuntimeException("注册失败");
        }
    }
}