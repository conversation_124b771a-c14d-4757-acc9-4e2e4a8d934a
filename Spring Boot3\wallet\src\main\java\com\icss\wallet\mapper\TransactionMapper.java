package com.icss.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Transaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


@Mapper
public interface TransactionMapper extends BaseMapper<Transaction> {
    IPage<Transaction> searchTransactions(
            @Param("page") Page<Transaction> page,
            @Param("phone") String phone,
            @Param("type") Integer type);

    /**
     * Vue前端专用：分页查询交易记录（支持更多筛选条件）
     */
    IPage<Transaction> searchTransactionsPage(
            Page<Transaction> page,
            @Param("phone") String phone,
            @Param("type") Integer type,
            @Param("status") Integer status,
            @Param("transNo") String transNo,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    /**
     * HarmonyOS前端专用：查询用户交易记录（包含用户信息和当前余额）
     */
    @Select("<script>" +
            "SELECT t.trans_id as id, t.trans_id, t.trans_no, t.user_id, t.amount, t.type, t.status, " +
            "t.target_info, t.remark as description, t.create_time, t.update_time, " +
            "u.phone, " +
            "w.balance " +
            "FROM transaction t " +
            "LEFT JOIN user u ON t.user_id = u.user_id " +
            "LEFT JOIN wallet w ON t.user_id = w.user_id " +
            "WHERE t.user_id = #{userId} " +
            "<if test='type != null and type > 0'>" +
            "AND t.type = #{type} " +
            "</if>" +
            "ORDER BY t.create_time DESC" +
            "</script>")
    IPage<Transaction> selectTransactionsWithUserInfo(
            Page<Transaction> page,
            @Param("userId") Long userId,
            @Param("type") Integer type);

    /**
     * HarmonyOS前端专用：获取用户月度统计数据
     */
    @Select("<script>" +
            "SELECT " +
            "COALESCE(SUM(CASE WHEN type = 1 THEN amount ELSE 0 END), 0) as income, " +
            "COALESCE(SUM(CASE WHEN type IN (2, 3, 4) THEN amount ELSE 0 END), 0) as expense " +
            "FROM transaction " +
            "WHERE user_id = #{userId} " +
            "AND YEAR(create_time) = #{year} " +
            "AND MONTH(create_time) = #{month} " +
            "<if test='type != null and type > 0'>" +
            "AND type = #{type} " +
            "</if>" +
            "</script>")
    java.util.Map<String, Object> getMonthlyStats(
            @Param("userId") Long userId,
            @Param("year") int year,
            @Param("month") int month,
            @Param("type") Integer type);

    /**
     * Vue前端专用：获取总交易金额（只计算成功的交易）
     */
    java.math.BigDecimal getTotalAmount();
}