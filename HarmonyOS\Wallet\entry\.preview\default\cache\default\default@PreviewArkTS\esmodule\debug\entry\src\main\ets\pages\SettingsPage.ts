if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SettingsPage_Params {
    userInfo?: UserInfo;
    showPasswordDialog?: boolean;
    showLimitDialog?: boolean;
    showLogoutDialog?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
interface UserInfo {
    name: string;
    phone: string;
    avatar: Resource;
}
export class SettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU({
            name: '张三',
            phone: '138****1234',
            avatar: { "id": 16777261, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }
        }, this, "userInfo");
        this.__showPasswordDialog = new ObservedPropertySimplePU(false, this, "showPasswordDialog");
        this.__showLimitDialog = new ObservedPropertySimplePU(false, this, "showLimitDialog");
        this.__showLogoutDialog = new ObservedPropertySimplePU(false, this, "showLogoutDialog");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SettingsPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.showPasswordDialog !== undefined) {
            this.showPasswordDialog = params.showPasswordDialog;
        }
        if (params.showLimitDialog !== undefined) {
            this.showLimitDialog = params.showLimitDialog;
        }
        if (params.showLogoutDialog !== undefined) {
            this.showLogoutDialog = params.showLogoutDialog;
        }
    }
    updateStateVars(params: SettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__showPasswordDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showLimitDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showLogoutDialog.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__showPasswordDialog.aboutToBeDeleted();
        this.__showLimitDialog.aboutToBeDeleted();
        this.__showLogoutDialog.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<UserInfo>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: UserInfo) {
        this.__userInfo.set(newValue);
    }
    private __showPasswordDialog: ObservedPropertySimplePU<boolean>;
    get showPasswordDialog() {
        return this.__showPasswordDialog.get();
    }
    set showPasswordDialog(newValue: boolean) {
        this.__showPasswordDialog.set(newValue);
    }
    private __showLimitDialog: ObservedPropertySimplePU<boolean>;
    get showLimitDialog() {
        return this.__showLimitDialog.get();
    }
    set showLimitDialog(newValue: boolean) {
        this.__showLimitDialog.set(newValue);
    }
    private __showLogoutDialog: ObservedPropertySimplePU<boolean>;
    get showLogoutDialog() {
        return this.__showLogoutDialog.get();
    }
    set showLogoutDialog(newValue: boolean) {
        this.__showLogoutDialog.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 根容器
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(24:5)", "entry");
            // 根容器
            Column.width('100%');
            // 根容器
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(26:7)", "entry");
            // 主内容区域
            Column.width('100%');
            // 主内容区域
            Column.height('100%');
            // 主内容区域
            Column.backgroundColor('#f5f5f5');
        }, Column);
        // 用户信息卡片
        this.UserInfoCard.bind(this)();
        // 设置选项列表
        this.SettingsList.bind(this)();
        // 退出登录按钮
        this.LogoutButton.bind(this)();
        // 主内容区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 弹窗区域（覆盖在主内容之上）
            if (this.showPasswordDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.PasswordDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showLimitDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LimitDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showLogoutDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.LogoutDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 根容器
        Column.pop();
    }
    UserInfoCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(59:5)", "entry");
            Row.width('90%');
            Row.padding(20);
            Row.backgroundColor('#ffffff');
            Row.borderRadius(12);
            Row.margin({ top: 20 });
            Row.shadow({ radius: 4, color: '#00000010', offsetX: 0, offsetY: 2 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.userInfo.avatar);
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(60:7)", "entry");
            Image.width(60);
            Image.height(60);
            Image.borderRadius(30);
            Image.margin({ right: 15 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(66:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo.name);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(67:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.userInfo.phone);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(70:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    SettingsList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(86:5)", "entry");
            Column.width('90%');
            Column.margin({ top: 20 });
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.shadow({ radius: 4, color: '#00000010', offsetX: 0, offsetY: 2 });
        }, Column);
        this.SettingItem.bind(this)('支付密码设置', { "id": 16777265, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            this.showPasswordDialog = true;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/SettingsPage.ets(91:7)", "entry");
            Divider.strokeWidth(0.5);
            Divider.color('#f0f0f0');
        }, Divider);
        this.SettingItem.bind(this)('支付限额设置', { "id": 16777268, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            this.showLimitDialog = true;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/SettingsPage.ets(99:7)", "entry");
            Divider.strokeWidth(0.5);
            Divider.color('#f0f0f0');
        }, Divider);
        this.SettingItem.bind(this)('修改登录密码', { "id": 16777267, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            router.pushUrl({ url: 'pages/ChangePasswordPage' });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/SettingsPage.ets(107:7)", "entry");
            Divider.strokeWidth(0.5);
            Divider.color('#f0f0f0');
        }, Divider);
        this.SettingItem.bind(this)('关于我们', { "id": 16777266, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, () => {
            router.pushUrl({ url: 'pages/AboutPage' });
        });
        Column.pop();
    }
    SettingItem(title: string, icon: Resource, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(124:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.padding({ left: 15, right: 15 });
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(icon);
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(125:7)", "entry");
            Image.width(20);
            Image.height(20);
            Image.margin({ right: 15 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(130:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777269, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/SettingsPage.ets(135:7)", "entry");
            Image.width(16);
            Image.height(16);
        }, Image);
        Row.pop();
    }
    LogoutButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(147:5)", "entry");
            Button.width('90%');
            Button.height(50);
            Button.backgroundColor('#ffffff');
            Button.margin({ top: 30 });
            Button.onClick(() => {
                this.showLogoutDialog = true;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('退出登录');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(148:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FF5252');
        }, Text);
        Text.pop();
        Button.pop();
    }
    PasswordDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(163:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '20%' });
            Column.zIndex(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置支付密码');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(164:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请输入6位数字支付密码');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(169:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 10 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '新支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(174:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.margin({ bottom: 10 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '确认支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(181:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.margin({ bottom: 20 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(188:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(189:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.onClick(() => {
                this.showPasswordDialog = false;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('取消');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(190:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(201:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#ee1f98e5');
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.showPasswordDialog = false;
                promptAction.showToast({ message: '支付密码设置成功', duration: 1000 });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(202:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        Button.pop();
        Row.pop();
        Column.pop();
    }
    LimitDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(226:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '15%' });
            Column.zIndex(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付限额设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(227:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('单笔支付限额');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(232:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.margin({ left: '10%', bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入单笔限额' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(238:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Number);
            TextInput.margin({ bottom: 10 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('每日支付限额');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(244:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.margin({ left: '10%', bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入每日限额' });
            TextInput.debugLine("entry/src/main/ets/pages/SettingsPage.ets(250:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Number);
            TextInput.margin({ bottom: 20 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(256:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(257:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.onClick(() => {
                this.showLimitDialog = false;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('取消');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(258:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(269:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#ee1f98e5');
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.showLimitDialog = false;
                promptAction.showToast({ message: '限额设置成功', duration: 1000 });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(270:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        Button.pop();
        Row.pop();
        Column.pop();
    }
    LogoutDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(294:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '30%' });
            Column.zIndex(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认退出登录？');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(295:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(300:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.backgroundColor('#FF5252');
            Button.onClick(() => {
                this.showLogoutDialog = false;
                router.replaceUrl({ url: 'pages/LoginPage' });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认退出');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(301:9)", "entry");
            Text.fontSize(16);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(313:7)", "entry");
            Button.width('80%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.margin({ top: 10 });
            Button.onClick(() => {
                this.showLogoutDialog = false;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('取消');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(314:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SettingsPage";
    }
}
registerNamedRoute(() => new SettingsPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/SettingsPage", pageFullPath: "entry/src/main/ets/pages/SettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
