{"version": "2.0", "ppid": 4888, "events": [{"head": {"id": "91cf6ff2-251f-410a-9fdf-ae60375d626c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754136418200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8814b126-0b97-4748-8db5-d3ca657ffe12", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8878874138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122377dd-6063-4aef-a134-600e2419aad0", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8878874360300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2cfa8c-1cb9-4f35-b06f-334f828bc587", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879892926000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26c4165-3de4-426a-bb10-80cf883ed5de", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879899813900, "endTime": 8880062766100}, "additional": {"children": ["b6c25bad-a8f4-44a6-8721-863514804fa8", "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "f86f6c3c-b366-4bec-b4f7-160f87a4eace", "01b4fc14-e115-479f-b54b-1f99f1f29fa9", "150f42ba-2a37-4fc1-a68a-7de3d265a2c9", "36c885d3-8a23-48d0-be50-1b251d9d45be", "7cff0cb6-bf26-4b13-87b7-44ae24877d55"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e02090d0-558e-44d8-b3ad-6c4617a06159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6c25bad-a8f4-44a6-8721-863514804fa8", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879899816100, "endTime": 8879912403700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "9ccdb1e6-139a-436d-b25d-b9e09ed4b7a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879912432200, "endTime": 8880061384800}, "additional": {"children": ["6c005d03-d2de-40d7-8581-252ff805cb99", "b48d77a2-72f1-418a-9a3b-c722528bbbe2", "4ed5b621-5817-4b99-a4fd-7207eb12a8df", "a55733e4-ab68-4ca5-a505-5b3689639f2d", "d4919444-291b-4177-820d-f140d41f7136", "159103d9-63bb-4dbc-ab6d-2a4c39b45e71", "6d3acdd0-71f1-4a7e-a39e-631aa42c903e", "9830694a-3d2d-4fa2-8f0d-ea21ac7ee8db", "2faa1206-8bf5-4840-8305-1fbc5f048e72"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f86f6c3c-b366-4bec-b4f7-160f87a4eace", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880061426800, "endTime": 8880062758200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "ddefb769-c3a2-4bc0-99ac-fab33e9411b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01b4fc14-e115-479f-b54b-1f99f1f29fa9", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880062762400, "endTime": 8880062763500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "a224b599-05fd-4ef1-b0e4-b8fc78853db0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "150f42ba-2a37-4fc1-a68a-7de3d265a2c9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879903111400, "endTime": 8879903167500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "f1cd2ae9-c867-48b6-8fde-f0e8302d0972"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1cd2ae9-c867-48b6-8fde-f0e8302d0972", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879903111400, "endTime": 8879903167500}, "additional": {"logType": "info", "children": [], "durationId": "150f42ba-2a37-4fc1-a68a-7de3d265a2c9", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "36c885d3-8a23-48d0-be50-1b251d9d45be", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879908311400, "endTime": 8879908336200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "42bcd3fe-2ac2-4be6-b828-59f1af0a8b21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42bcd3fe-2ac2-4be6-b828-59f1af0a8b21", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879908311400, "endTime": 8879908336200}, "additional": {"logType": "info", "children": [], "durationId": "36c885d3-8a23-48d0-be50-1b251d9d45be", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "dd17ea07-4a35-404f-b1a1-63c0f54b8b6d", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879908387200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08706277-c9a2-472d-b429-4f8d14b75179", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879912205700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ccdb1e6-139a-436d-b25d-b9e09ed4b7a9", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879899816100, "endTime": 8879912403700}, "additional": {"logType": "info", "children": [], "durationId": "b6c25bad-a8f4-44a6-8721-863514804fa8", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "6c005d03-d2de-40d7-8581-252ff805cb99", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879917929100, "endTime": 8879917939100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "8663045f-9fdc-4de9-8659-7506862c8652"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b48d77a2-72f1-418a-9a3b-c722528bbbe2", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879917952400, "endTime": 8879921462800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "99667c08-bdfc-4ff5-9919-8682225093b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ed5b621-5817-4b99-a4fd-7207eb12a8df", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879921475500, "endTime": 8879995266000}, "additional": {"children": ["395297d7-2f4a-4b0c-8e9e-b3e4ae57656a", "df85a6b2-7280-4687-bfc9-293b7b7811d0", "bfffe201-cb4a-4bde-9fc4-e1dc83a5c940"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "ac40e133-ff8f-408c-b8a2-5466fd1e9e6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a55733e4-ab68-4ca5-a505-5b3689639f2d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879995292900, "endTime": 8880022094400}, "additional": {"children": ["e76ee25d-cfbf-4891-8417-d01e77cc511e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "90f24631-3090-486e-be45-41daf753e99c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4919444-291b-4177-820d-f140d41f7136", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880022102000, "endTime": 8880037118700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "071ed3de-b807-40f4-a1eb-093581888a3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "159103d9-63bb-4dbc-ab6d-2a4c39b45e71", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880038272300, "endTime": 8880046461500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "f1a303ff-4c6c-45d0-ba25-7aba7ef48247"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d3acdd0-71f1-4a7e-a39e-631aa42c903e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880046489800, "endTime": 8880061140500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "005fc27f-9d25-4cc3-9925-48c641c3092c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9830694a-3d2d-4fa2-8f0d-ea21ac7ee8db", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880061182700, "endTime": 8880061361300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "608f51cf-328f-46eb-bb1a-bb7eba2579f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8663045f-9fdc-4de9-8659-7506862c8652", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879917929100, "endTime": 8879917939100}, "additional": {"logType": "info", "children": [], "durationId": "6c005d03-d2de-40d7-8581-252ff805cb99", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "99667c08-bdfc-4ff5-9919-8682225093b1", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879917952400, "endTime": 8879921462800}, "additional": {"logType": "info", "children": [], "durationId": "b48d77a2-72f1-418a-9a3b-c722528bbbe2", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "395297d7-2f4a-4b0c-8e9e-b3e4ae57656a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879922011200, "endTime": 8879922030000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ed5b621-5817-4b99-a4fd-7207eb12a8df", "logId": "f0207188-7aff-4bcf-a2a5-227555793bfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0207188-7aff-4bcf-a2a5-227555793bfd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879922011200, "endTime": 8879922030000}, "additional": {"logType": "info", "children": [], "durationId": "395297d7-2f4a-4b0c-8e9e-b3e4ae57656a", "parent": "ac40e133-ff8f-408c-b8a2-5466fd1e9e6c"}}, {"head": {"id": "df85a6b2-7280-4687-bfc9-293b7b7811d0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879923704800, "endTime": 8879994005300}, "additional": {"children": ["91db0505-fc74-4a50-9767-4e9989dcbf38", "8773a671-a54b-4534-a672-b3ce3d3ad0e3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ed5b621-5817-4b99-a4fd-7207eb12a8df", "logId": "858fd1df-0b05-4e98-b681-f0f5e8a737c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91db0505-fc74-4a50-9767-4e9989dcbf38", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879923706000, "endTime": 8879927792600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df85a6b2-7280-4687-bfc9-293b7b7811d0", "logId": "7e49b001-91f3-4e72-8620-7f9ace167a74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8773a671-a54b-4534-a672-b3ce3d3ad0e3", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879927814800, "endTime": 8879993972600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df85a6b2-7280-4687-bfc9-293b7b7811d0", "logId": "cec8782d-484f-416f-ad13-c72782409acb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "784888f9-9079-4670-ad47-14549ef614b4", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879923710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ded853c-1670-4685-b6d4-1d3d1c212bc3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879927636500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e49b001-91f3-4e72-8620-7f9ace167a74", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879923706000, "endTime": 8879927792600}, "additional": {"logType": "info", "children": [], "durationId": "91db0505-fc74-4a50-9767-4e9989dcbf38", "parent": "858fd1df-0b05-4e98-b681-f0f5e8a737c1"}}, {"head": {"id": "6d9495ce-68cf-4bea-9634-eba9aacae868", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879927834500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dac1b949-b0ad-40df-be13-e44ecf683224", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879933530600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4edc2c0-ed88-4d2b-9d88-c5acccf4884d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879933617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207a4e40-75e0-4909-82c8-495c140c2828", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879933723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715032c0-dd42-4972-bd74-2a4806f74a21", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879933802400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b677e0-30f1-453e-b5af-396f7f415597", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879936154100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94184a93-1b45-4d30-b432-6536a35139e9", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879939451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4610034d-a6ef-4f22-a9c7-162302789a56", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879947815000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618b0c92-5f5d-47d1-b25a-00ea4a43a502", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879972405700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "905018aa-499d-487f-8028-73baae062071", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879972535000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 7}, "markType": "other"}}, {"head": {"id": "236a7733-0ff9-431e-9ac7-adc9bad83754", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879972546900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 7}, "markType": "other"}}, {"head": {"id": "1a9c922c-6372-4ae7-a6d2-606e46e40ced", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879993383200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d80807-ce2f-4127-93ae-2ccf650646f8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879993640700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9cedfa3-7121-4129-9be3-37e47f6082a1", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879993774800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d3f92f8-4725-4ef3-b162-4d5367c1d179", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879993868900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cec8782d-484f-416f-ad13-c72782409acb", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879927814800, "endTime": 8879993972600}, "additional": {"logType": "info", "children": [], "durationId": "8773a671-a54b-4534-a672-b3ce3d3ad0e3", "parent": "858fd1df-0b05-4e98-b681-f0f5e8a737c1"}}, {"head": {"id": "858fd1df-0b05-4e98-b681-f0f5e8a737c1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879923704800, "endTime": 8879994005300}, "additional": {"logType": "info", "children": ["7e49b001-91f3-4e72-8620-7f9ace167a74", "cec8782d-484f-416f-ad13-c72782409acb"], "durationId": "df85a6b2-7280-4687-bfc9-293b7b7811d0", "parent": "ac40e133-ff8f-408c-b8a2-5466fd1e9e6c"}}, {"head": {"id": "bfffe201-cb4a-4bde-9fc4-e1dc83a5c940", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879995216600, "endTime": 8879995241800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ed5b621-5817-4b99-a4fd-7207eb12a8df", "logId": "5917aae1-0a79-49d3-ab8c-d23d0a9acc96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5917aae1-0a79-49d3-ab8c-d23d0a9acc96", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879995216600, "endTime": 8879995241800}, "additional": {"logType": "info", "children": [], "durationId": "bfffe201-cb4a-4bde-9fc4-e1dc83a5c940", "parent": "ac40e133-ff8f-408c-b8a2-5466fd1e9e6c"}}, {"head": {"id": "ac40e133-ff8f-408c-b8a2-5466fd1e9e6c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879921475500, "endTime": 8879995266000}, "additional": {"logType": "info", "children": ["f0207188-7aff-4bcf-a2a5-227555793bfd", "858fd1df-0b05-4e98-b681-f0f5e8a737c1", "5917aae1-0a79-49d3-ab8c-d23d0a9acc96"], "durationId": "4ed5b621-5817-4b99-a4fd-7207eb12a8df", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "e76ee25d-cfbf-4891-8417-d01e77cc511e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879996127000, "endTime": 8880022081200}, "additional": {"children": ["5a26338a-8d21-4326-b25d-e43561d60c88", "a0dd97cb-c838-45a2-bda8-edcc4d504710", "4d97b902-6c96-4e53-912d-bbc45afeaa59"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a55733e4-ab68-4ca5-a505-5b3689639f2d", "logId": "ec4425f4-b031-4791-8be9-ef21ccd42186"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a26338a-8d21-4326-b25d-e43561d60c88", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879999290000, "endTime": 8879999305200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e76ee25d-cfbf-4891-8417-d01e77cc511e", "logId": "e6b8f968-9d95-4239-b4e6-a7aafb407bf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6b8f968-9d95-4239-b4e6-a7aafb407bf0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879999290000, "endTime": 8879999305200}, "additional": {"logType": "info", "children": [], "durationId": "5a26338a-8d21-4326-b25d-e43561d60c88", "parent": "ec4425f4-b031-4791-8be9-ef21ccd42186"}}, {"head": {"id": "a0dd97cb-c838-45a2-bda8-edcc4d504710", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880001087700, "endTime": 8880019842400}, "additional": {"children": ["ed75407d-947b-4563-a277-46ab97474310", "c4653dbc-e342-4abd-a60d-4cdf59a268db"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e76ee25d-cfbf-4891-8417-d01e77cc511e", "logId": "2bd70a0a-fd51-47e0-869a-fd5c4017ca89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed75407d-947b-4563-a277-46ab97474310", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880001089000, "endTime": 8880006839400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a0dd97cb-c838-45a2-bda8-edcc4d504710", "logId": "94a875c0-0b51-4999-8da1-2d2c492cf10c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4653dbc-e342-4abd-a60d-4cdf59a268db", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880006865300, "endTime": 8880019798200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a0dd97cb-c838-45a2-bda8-edcc4d504710", "logId": "5f32a41e-f8b2-4496-855c-1e601ea35986"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c21f0bf-b4a7-4c82-bbd8-ac5120a4bbff", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880001093500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "400f2692-e655-4d1b-a5d9-7af43b0ad200", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880006674500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a875c0-0b51-4999-8da1-2d2c492cf10c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880001089000, "endTime": 8880006839400}, "additional": {"logType": "info", "children": [], "durationId": "ed75407d-947b-4563-a277-46ab97474310", "parent": "2bd70a0a-fd51-47e0-869a-fd5c4017ca89"}}, {"head": {"id": "a4fb510d-1e5f-44be-a67c-532606f3df70", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880006883200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b2a3cd8-a07e-4f76-9cd9-722f6dca7d82", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880015446500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d0699f-e748-41a2-a284-bf379421cf18", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880015598200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d6ae345-8c56-4455-a73d-e53fe17128ac", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880015802300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a5b09d-bc19-4a5a-8a12-23d9da50d152", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880015953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13abe23f-b499-4841-ba58-80928b4f2302", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880016017400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba6e17f-a719-4956-b858-0aada24a7707", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880016063700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072d59f1-e9b9-40ad-b482-d51b844e1246", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880016114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3290eee3-4056-4e27-b97e-72982eb8367d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880019311500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4197988-b05a-426c-a7b0-50b8e92c2511", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880019559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94570985-0709-4b42-96bc-47e0ecc30c51", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880019654000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d847f47-ac01-4344-b7a9-713156366f42", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880019712400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f32a41e-f8b2-4496-855c-1e601ea35986", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880006865300, "endTime": 8880019798200}, "additional": {"logType": "info", "children": [], "durationId": "c4653dbc-e342-4abd-a60d-4cdf59a268db", "parent": "2bd70a0a-fd51-47e0-869a-fd5c4017ca89"}}, {"head": {"id": "2bd70a0a-fd51-47e0-869a-fd5c4017ca89", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880001087700, "endTime": 8880019842400}, "additional": {"logType": "info", "children": ["94a875c0-0b51-4999-8da1-2d2c492cf10c", "5f32a41e-f8b2-4496-855c-1e601ea35986"], "durationId": "a0dd97cb-c838-45a2-bda8-edcc4d504710", "parent": "ec4425f4-b031-4791-8be9-ef21ccd42186"}}, {"head": {"id": "4d97b902-6c96-4e53-912d-bbc45afeaa59", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880022052800, "endTime": 8880022066800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e76ee25d-cfbf-4891-8417-d01e77cc511e", "logId": "f45fd4e4-fc00-47f0-ab3d-d346b4462d6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f45fd4e4-fc00-47f0-ab3d-d346b4462d6d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880022052800, "endTime": 8880022066800}, "additional": {"logType": "info", "children": [], "durationId": "4d97b902-6c96-4e53-912d-bbc45afeaa59", "parent": "ec4425f4-b031-4791-8be9-ef21ccd42186"}}, {"head": {"id": "ec4425f4-b031-4791-8be9-ef21ccd42186", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879996127000, "endTime": 8880022081200}, "additional": {"logType": "info", "children": ["e6b8f968-9d95-4239-b4e6-a7aafb407bf0", "2bd70a0a-fd51-47e0-869a-fd5c4017ca89", "f45fd4e4-fc00-47f0-ab3d-d346b4462d6d"], "durationId": "e76ee25d-cfbf-4891-8417-d01e77cc511e", "parent": "90f24631-3090-486e-be45-41daf753e99c"}}, {"head": {"id": "90f24631-3090-486e-be45-41daf753e99c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879995292900, "endTime": 8880022094400}, "additional": {"logType": "info", "children": ["ec4425f4-b031-4791-8be9-ef21ccd42186"], "durationId": "a55733e4-ab68-4ca5-a505-5b3689639f2d", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "d178a487-a52d-45fd-b88c-f85dd1909ca9", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880036474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb9f3dc-4c35-4560-95e7-a3ed05b3756f", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880037011100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "071ed3de-b807-40f4-a1eb-093581888a3a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880022102000, "endTime": 8880037118700}, "additional": {"logType": "info", "children": [], "durationId": "d4919444-291b-4177-820d-f140d41f7136", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "2faa1206-8bf5-4840-8305-1fbc5f048e72", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880038043000, "endTime": 8880038256400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "logId": "db94b302-1d22-4030-b269-d53222fdca6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ea9f2a3-2b44-4c9d-b6ed-5f519678b988", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880038068700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db94b302-1d22-4030-b269-d53222fdca6e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880038043000, "endTime": 8880038256400}, "additional": {"logType": "info", "children": [], "durationId": "2faa1206-8bf5-4840-8305-1fbc5f048e72", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "7322fff8-1b1d-47af-83ba-fe36bb0dac8a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880039591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95877dd9-b45d-4723-a01e-83f41ae7f559", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880045640600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a303ff-4c6c-45d0-ba25-7aba7ef48247", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880038272300, "endTime": 8880046461500}, "additional": {"logType": "info", "children": [], "durationId": "159103d9-63bb-4dbc-ab6d-2a4c39b45e71", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "7c55a44f-683d-479f-8bfe-48411b124186", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880046503200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aded0d04-36e0-49ab-8043-ebd709c2aa8b", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880051807700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b20626-e660-4553-9a14-15185e54ff33", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880052079600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca8bfdc-d9c1-4545-81eb-2bd51b145df7", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880052831200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d5319e0-ec0e-4d5c-9cfa-e84ffed29d2c", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880057935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56c6091-d50d-4007-a9cd-4b715d1f98ca", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880058072900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005fc27f-9d25-4cc3-9925-48c641c3092c", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880046489800, "endTime": 8880061140500}, "additional": {"logType": "info", "children": [], "durationId": "6d3acdd0-71f1-4a7e-a39e-631aa42c903e", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "bdca3c09-74c7-4d47-9a4e-a166fe2c667d", "name": "Configuration phase cost:144 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880061214000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "608f51cf-328f-46eb-bb1a-bb7eba2579f6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880061182700, "endTime": 8880061361300}, "additional": {"logType": "info", "children": [], "durationId": "9830694a-3d2d-4fa2-8f0d-ea21ac7ee8db", "parent": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3"}}, {"head": {"id": "b7f5e65a-b09c-4881-b094-79b8e14a6bd3", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879912432200, "endTime": 8880061384800}, "additional": {"logType": "info", "children": ["8663045f-9fdc-4de9-8659-7506862c8652", "99667c08-bdfc-4ff5-9919-8682225093b1", "ac40e133-ff8f-408c-b8a2-5466fd1e9e6c", "90f24631-3090-486e-be45-41daf753e99c", "071ed3de-b807-40f4-a1eb-093581888a3a", "f1a303ff-4c6c-45d0-ba25-7aba7ef48247", "005fc27f-9d25-4cc3-9925-48c641c3092c", "608f51cf-328f-46eb-bb1a-bb7eba2579f6", "db94b302-1d22-4030-b269-d53222fdca6e"], "durationId": "1dab8f0c-1fbc-4ecf-b733-7cc1d57be256", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "7cff0cb6-bf26-4b13-87b7-44ae24877d55", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880062734200, "endTime": 8880062747400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b26c4165-3de4-426a-bb10-80cf883ed5de", "logId": "f41c1a0f-c156-4df5-88b5-76bb2846cb35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f41c1a0f-c156-4df5-88b5-76bb2846cb35", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880062734200, "endTime": 8880062747400}, "additional": {"logType": "info", "children": [], "durationId": "7cff0cb6-bf26-4b13-87b7-44ae24877d55", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "ddefb769-c3a2-4bc0-99ac-fab33e9411b3", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880061426800, "endTime": 8880062758200}, "additional": {"logType": "info", "children": [], "durationId": "f86f6c3c-b366-4bec-b4f7-160f87a4eace", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "a224b599-05fd-4ef1-b0e4-b8fc78853db0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880062762400, "endTime": 8880062763500}, "additional": {"logType": "info", "children": [], "durationId": "01b4fc14-e115-479f-b54b-1f99f1f29fa9", "parent": "e02090d0-558e-44d8-b3ad-6c4617a06159"}}, {"head": {"id": "e02090d0-558e-44d8-b3ad-6c4617a06159", "name": "init", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879899813900, "endTime": 8880062766100}, "additional": {"logType": "info", "children": ["9ccdb1e6-139a-436d-b25d-b9e09ed4b7a9", "b7f5e65a-b09c-4881-b094-79b8e14a6bd3", "ddefb769-c3a2-4bc0-99ac-fab33e9411b3", "a224b599-05fd-4ef1-b0e4-b8fc78853db0", "f1cd2ae9-c867-48b6-8fde-f0e8302d0972", "42bcd3fe-2ac2-4be6-b828-59f1af0a8b21", "f41c1a0f-c156-4df5-88b5-76bb2846cb35"], "durationId": "b26c4165-3de4-426a-bb10-80cf883ed5de"}}, {"head": {"id": "edfe0fc2-946d-494d-8940-012e33171786", "name": "Configuration task cost before running: 166 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880062877100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c7153b9-25ca-4bb8-840e-bba564ba75c9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880067318400, "endTime": 8880074896200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ee015a77-0691-450d-a2a2-2f7ed04bac96", "logId": "01fbb4a3-4c60-48b7-aa7e-2e75ff922a95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee015a77-0691-450d-a2a2-2f7ed04bac96", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880064447900}, "additional": {"logType": "detail", "children": [], "durationId": "8c7153b9-25ca-4bb8-840e-bba564ba75c9"}}, {"head": {"id": "45aa5b5c-a109-4440-9b05-562c7139e49e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880064885500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba346fa9-3aa3-48f6-9930-966d8eed6089", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880064966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39cb5805-7df9-496a-82e6-8a81f6e42376", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880065010500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd2d7dd-ee29-4623-b760-c456e7ccd4c8", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880067326800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e758413c-28b2-44ce-bcc1-45a38808ab24", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880074686900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a9fa7e-2b9e-4d25-a2f2-8155cfd0c87d", "name": "entry : default@PreBuild cost memory 0.2725067138671875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880074822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01fbb4a3-4c60-48b7-aa7e-2e75ff922a95", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880067318400, "endTime": 8880074896200}, "additional": {"logType": "info", "children": [], "durationId": "8c7153b9-25ca-4bb8-840e-bba564ba75c9"}}, {"head": {"id": "87a47579-4b96-40be-89b2-9b0ff0145860", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880079887300, "endTime": 8880082583100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d0d25ce6-8d29-4e8d-aafa-3af404874caa", "logId": "cd50a6c1-bfd6-47e6-a8c3-110b4781e23d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0d25ce6-8d29-4e8d-aafa-3af404874caa", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880078645700}, "additional": {"logType": "detail", "children": [], "durationId": "87a47579-4b96-40be-89b2-9b0ff0145860"}}, {"head": {"id": "83601834-5fe3-4b84-8630-6a53097a3e68", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880079137500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "201ec7eb-9cb9-432b-aaf3-6a4c24d2a04b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880079232700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd01932-4270-418e-b5d9-aa15cc4558f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880079280600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c49c81-6bc8-4d99-a873-e173158a71fd", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880079896400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23b3acc9-e9c5-447f-8183-9e2d9831318c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880082430300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec5c8e8-65bc-4386-a81e-5a603aea4e07", "name": "entry : default@MergeProfile cost memory 0.12776947021484375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880082524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd50a6c1-bfd6-47e6-a8c3-110b4781e23d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880079887300, "endTime": 8880082583100}, "additional": {"logType": "info", "children": [], "durationId": "87a47579-4b96-40be-89b2-9b0ff0145860"}}, {"head": {"id": "71e00472-cf4e-414c-aae2-8783b80c69a3", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880086194900, "endTime": 8880088438500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9920c58d-a618-460f-8741-6fd0c4e9c862", "logId": "55475cb2-6ace-4907-bce7-14ef202f95ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9920c58d-a618-460f-8741-6fd0c4e9c862", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880084369400}, "additional": {"logType": "detail", "children": [], "durationId": "71e00472-cf4e-414c-aae2-8783b80c69a3"}}, {"head": {"id": "997fb8f0-f6a8-45fd-9ad9-2cde30fec53f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880084831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0980d19f-ae05-4d4d-bca1-225d4a3eebdc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880084951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f77995f-04e3-401b-8163-af50aa42f51a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880085042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6db3858-478a-4349-b263-579a8c10f75c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880086208200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1739e52-7f44-4f12-a6f6-fbd4e492742b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880087100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b3d3375-c033-4520-acad-3424eaed83cd", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880088274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "589b4906-5efb-45a9-a50e-dfc17b3037ea", "name": "entry : default@CreateBuildProfile cost memory 0.09659576416015625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880088370300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55475cb2-6ace-4907-bce7-14ef202f95ea", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880086194900, "endTime": 8880088438500}, "additional": {"logType": "info", "children": [], "durationId": "71e00472-cf4e-414c-aae2-8783b80c69a3"}}, {"head": {"id": "cce56ed7-d251-42f0-ba8f-11ac804df744", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091041000, "endTime": 8880091402200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f892145d-78b8-494f-8700-bb153ee3a150", "logId": "53fbf31b-6aaa-4d9e-8ceb-fb6712feb12b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f892145d-78b8-494f-8700-bb153ee3a150", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880089788400}, "additional": {"logType": "detail", "children": [], "durationId": "cce56ed7-d251-42f0-ba8f-11ac804df744"}}, {"head": {"id": "5751696b-905f-4f6c-814d-292c18e27b66", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880090249200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb897be1-1c58-4d39-8fda-7d0292cc00c6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880090329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd4a68c-9183-41e7-a7ee-4e1314865580", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880090376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8441ec-9275-4d07-9d55-30cfe44e0879", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091048800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f5d899-310d-4a80-aeb2-732bdce5bb83", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091143100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36c1181c-66dd-4fed-830f-5bcf7051c58f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091186900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c16301d-ca07-446d-a5f7-22e773ae9f1d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d0d1dc-4ff4-4041-9c35-db08e18df42c", "name": "entry : default@PreCheckSyscap cost memory 0.05034637451171875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091288400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "024adebd-6f14-4a8c-a4dc-cc80650749eb", "name": "runTaskFromQueue task cost before running: 195 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091355600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53fbf31b-6aaa-4d9e-8ceb-fb6712feb12b", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880091041000, "endTime": 8880091402200, "totalTime": 299500}, "additional": {"logType": "info", "children": [], "durationId": "cce56ed7-d251-42f0-ba8f-11ac804df744"}}, {"head": {"id": "42402854-0ab0-4137-9253-df6c296c6f57", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880101661900, "endTime": 8880102787000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "57a98709-dfbd-4fee-b68c-9cc06bb3515d", "logId": "72a91ae3-0464-4914-9d6d-2a44a7233c75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57a98709-dfbd-4fee-b68c-9cc06bb3515d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880092755300}, "additional": {"logType": "detail", "children": [], "durationId": "42402854-0ab0-4137-9253-df6c296c6f57"}}, {"head": {"id": "3cdcd8e0-a256-489a-9f6d-40b6f91d2f4d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880093217800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e6a23b-505a-45dd-aa34-dc883c7e937a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880093309700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0211ad-f9a3-4847-a896-bb50028a2731", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880094224000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2005fad7-5af5-490b-b81f-11be439bd1eb", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880101682500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb5958a8-7cbf-4bf7-8a6b-28a905ff61e6", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880101960700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aaa9b4b-1d71-43a8-b0dc-4f922e96733c", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880102611600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ca1fce0-8cb4-4725-94fd-85b488d67aed", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06896209716796875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880102721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a91ae3-0464-4914-9d6d-2a44a7233c75", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880101661900, "endTime": 8880102787000}, "additional": {"logType": "info", "children": [], "durationId": "42402854-0ab0-4137-9253-df6c296c6f57"}}, {"head": {"id": "288838f7-d14e-4c21-98bc-3bb054c55c1b", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880105931600, "endTime": 8880106960500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9a7e9429-42ad-45ad-ba6b-f54438f7daf2", "logId": "76e66679-0205-49e0-8a43-e853eaa69724"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a7e9429-42ad-45ad-ba6b-f54438f7daf2", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880104336100}, "additional": {"logType": "detail", "children": [], "durationId": "288838f7-d14e-4c21-98bc-3bb054c55c1b"}}, {"head": {"id": "7068ea3e-30d5-424d-8b20-dd1166b0e94d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880104810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb024243-b91b-418f-a5c2-1d34858632b2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880104890900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b42162-efa8-4710-b4ab-d4c5b583365c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880104936900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64429dcc-acb6-4ca7-b6eb-21dfe1ca8d03", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880105938400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0858a58-c6f8-4ad0-ac3b-6734706b18e7", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880106824400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c38b5ac-444f-4406-b282-95b6ca0e99cd", "name": "entry : default@ProcessProfile cost memory 0.0562286376953125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880106906000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e66679-0205-49e0-8a43-e853eaa69724", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880105931600, "endTime": 8880106960500}, "additional": {"logType": "info", "children": [], "durationId": "288838f7-d14e-4c21-98bc-3bb054c55c1b"}}, {"head": {"id": "06b112b2-6241-411e-9ba2-dc72a2bb90dc", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880110321000, "endTime": 8880115941800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d3a0de50-cf90-415f-a86d-7286bbdfa403", "logId": "8adb68e7-d045-44fd-b273-d05270210162"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3a0de50-cf90-415f-a86d-7286bbdfa403", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880108275500}, "additional": {"logType": "detail", "children": [], "durationId": "06b112b2-6241-411e-9ba2-dc72a2bb90dc"}}, {"head": {"id": "67934dc5-1634-4cc3-8bd3-3be919cdf4d3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880108703400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9055f8be-5dc4-41a1-9c4c-d6c9811f4bb7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880108773800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b98cd1-ea18-4d82-bcf7-9551789fdb4d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880108817600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d25d07e-09dc-4988-a943-e09d50548877", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880110332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae4e8d2e-e192-4bbe-9416-bd987e75a951", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880115758000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b148a4-adef-41a4-bcb4-9676b6102ad9", "name": "entry : default@ProcessRouterMap cost memory 0.20742034912109375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880115875100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8adb68e7-d045-44fd-b273-d05270210162", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880110321000, "endTime": 8880115941800}, "additional": {"logType": "info", "children": [], "durationId": "06b112b2-6241-411e-9ba2-dc72a2bb90dc"}}, {"head": {"id": "a94b173d-28fe-46f7-9213-693dc2d3a3d8", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880122507300, "endTime": 8880125155900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "55d80376-f54d-44f5-b9b5-d9692627b28e", "logId": "2e909f51-df6f-4298-a607-c0ec25ab074a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55d80376-f54d-44f5-b9b5-d9692627b28e", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880118639400}, "additional": {"logType": "detail", "children": [], "durationId": "a94b173d-28fe-46f7-9213-693dc2d3a3d8"}}, {"head": {"id": "5bd04afe-6e9a-485f-ac64-b61551962d05", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880119149900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71bf96a-2c4a-4904-8d90-1321815dc2f8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880119263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3984d55-bfe3-420a-8af5-c31992f8d5b5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880119321600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502fbee4-33f8-497d-a544-176b64ad8c71", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880120190400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63158ca-b8bd-4086-a8be-abb4ef0d74ad", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880123639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efba279b-5a11-41a5-a586-561ed2dcb03a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880123765000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b015be2d-0153-48a2-b69e-d29672874c17", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880123815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c65ac111-2b71-4670-b3ca-519d52a181d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880123854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cbd8f16-873b-4c08-a016-a879273de68b", "name": "entry : default@PreviewProcessResource cost memory 0.0882415771484375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880123919400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e35e63-d0e8-4e0d-a0bd-73d6b77129f5", "name": "runTaskFromQueue task cost before running: 229 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880125079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e909f51-df6f-4298-a607-c0ec25ab074a", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880122507300, "endTime": 8880125155900, "totalTime": 1465600}, "additional": {"logType": "info", "children": [], "durationId": "a94b173d-28fe-46f7-9213-693dc2d3a3d8"}}, {"head": {"id": "66a6edb1-93fd-49ca-a7f7-b780ce4688a7", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880132593500, "endTime": 8880151726000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aca1406a-444d-417e-b3c2-8f1c0dc0d262", "logId": "2d7d594e-ccbb-4354-a6d3-743622598142"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aca1406a-444d-417e-b3c2-8f1c0dc0d262", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880128675000}, "additional": {"logType": "detail", "children": [], "durationId": "66a6edb1-93fd-49ca-a7f7-b780ce4688a7"}}, {"head": {"id": "e75533de-ec04-45ee-99dc-28025d1cf3d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880129167800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6828ae-c23a-4ca5-9d8d-130ab14ce51e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880129297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73343aba-1015-452d-a642-de507ef8cdc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880129351700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6e30da-4efc-488d-9d82-c3d4be611697", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880132603800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88859808-3836-437a-a57f-2ebc449a6465", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880151432100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9061c830-0f02-4f94-98ab-2e578d99ea44", "name": "entry : default@GenerateLoaderJson cost memory 0.8179092407226562", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880151633900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7d594e-ccbb-4354-a6d3-743622598142", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880132593500, "endTime": 8880151726000}, "additional": {"logType": "info", "children": [], "durationId": "66a6edb1-93fd-49ca-a7f7-b780ce4688a7"}}, {"head": {"id": "8b6a3a55-1bc8-4e8c-8a91-3d7533386c7d", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880162483900, "endTime": 8880199521700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d5ea6ca6-3862-4a0f-8d3d-c78c3d2b123d", "logId": "37013e22-78d7-4aa4-a5bb-e2be1af9788c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5ea6ca6-3862-4a0f-8d3d-c78c3d2b123d", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880158588400}, "additional": {"logType": "detail", "children": [], "durationId": "8b6a3a55-1bc8-4e8c-8a91-3d7533386c7d"}}, {"head": {"id": "daaceea3-0f31-474e-8ed4-3b4058707aa0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880159021200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e55c5f30-f00c-41bc-a6ce-1866371ef259", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880159100600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48165a0b-fd0d-4c6d-9cb3-9b8ffab1b88f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880159145900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c65812-5701-462b-b19b-381b4d917d7d", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880159964700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a1839b-a445-4e04-85f2-99cffb51afb9", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880162506700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf0c089-402a-4d3b-b828-fd917c9facc4", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 37 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880199257400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deab9bff-e047-435d-a5c9-8143a7a62272", "name": "entry : default@PreviewCompileResource cost memory -0.36005401611328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880199418700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37013e22-78d7-4aa4-a5bb-e2be1af9788c", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880162483900, "endTime": 8880199521700}, "additional": {"logType": "info", "children": [], "durationId": "8b6a3a55-1bc8-4e8c-8a91-3d7533386c7d"}}, {"head": {"id": "64119f6d-3999-435f-9395-12cb88af3300", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202824600, "endTime": 8880203456200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "90d00b5e-dc0a-4663-a7f8-fa89d5db0692", "logId": "33ee64e6-1012-4fbc-b401-b4d7a2b52e8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90d00b5e-dc0a-4663-a7f8-fa89d5db0692", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880201895700}, "additional": {"logType": "detail", "children": [], "durationId": "64119f6d-3999-435f-9395-12cb88af3300"}}, {"head": {"id": "1db41874-fd4f-41f0-a2ef-c5cd503e119e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202420400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa3a63b-b119-44bd-9df3-9e74ce120687", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "643d475e-757c-45a0-b946-9e61ec9279ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202655400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b850fd35-6c84-4566-b8f7-94cf4f1fce64", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202837700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7447b741-5cf3-448e-bcae-adc0d95091c7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202951600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e495fe-e583-4111-b8ce-16853ac37f9b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880203028700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aca8d8e-85a2-49da-ab73-e1d82326c8e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880203078800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a03a1c4-7fad-47b2-ae03-4fd8fab3e0a0", "name": "entry : default@PreviewHookCompileResource cost memory 0.05146026611328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880203212000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e18f02c-2b37-41de-a9a9-fd6fd2f17e75", "name": "runTaskFromQueue task cost before running: 307 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880203358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ee64e6-1012-4fbc-b401-b4d7a2b52e8b", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880202824600, "endTime": 8880203456200, "totalTime": 487600}, "additional": {"logType": "info", "children": [], "durationId": "64119f6d-3999-435f-9395-12cb88af3300"}}, {"head": {"id": "852c9aa8-c87f-4edd-8399-e655a05f0e24", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880207732700, "endTime": 8880210941500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "6b37016a-9a1f-4406-8d12-fb542e0ae1c6", "logId": "64e17b7f-f3b5-49bc-a105-c7b22ecf35c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b37016a-9a1f-4406-8d12-fb542e0ae1c6", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880206465600}, "additional": {"logType": "detail", "children": [], "durationId": "852c9aa8-c87f-4edd-8399-e655a05f0e24"}}, {"head": {"id": "516dda68-2b09-4c8d-8de1-361ebdeb24d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880207003700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecbe3178-4433-46c0-8ae0-ea92587c3436", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880207102900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f50bef4-e98d-4df9-8b57-6b59780dc81e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880207151000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19fb31b8-14d0-4659-8b14-4e04729f2abf", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880207742700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac56a87-cfe3-441e-92a3-7988c26f348e", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880210586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66bfcd11-f4a7-409c-a959-d4cc0d8fdb2f", "name": "entry : default@CopyPreviewProfile cost memory -1.7295150756835938", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880210830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e17b7f-f3b5-49bc-a105-c7b22ecf35c2", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880207732700, "endTime": 8880210941500}, "additional": {"logType": "info", "children": [], "durationId": "852c9aa8-c87f-4edd-8399-e655a05f0e24"}}, {"head": {"id": "5b774899-738a-40da-9cf2-f7cf476d8fc2", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214227100, "endTime": 8880214598900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "9a82eafe-def9-4aad-8688-a808af65069a", "logId": "75e94a40-1c21-4e8b-9f70-e0aa500844ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a82eafe-def9-4aad-8688-a808af65069a", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880212979300}, "additional": {"logType": "detail", "children": [], "durationId": "5b774899-738a-40da-9cf2-f7cf476d8fc2"}}, {"head": {"id": "16041cb3-e097-4a69-af9f-4418464f475a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880213454000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa6bc03-b1c2-4dbc-a7b4-e1812846cf39", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880213533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a2eef1-73fa-430a-ae48-83d96159f2b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880213576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912c3007-5c91-44fd-a4d9-614fb06b1b4a", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214235300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d43315b8-5998-4a7c-ac78-cbfd1cd5f1d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214329300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d0f3b1-fc91-436b-8a52-173f7803823e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214371600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f45c6a99-dc93-4c35-9276-bab294508242", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214408500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d0ee93-2b74-4b39-8619-68ae875b256b", "name": "entry : default@ReplacePreviewerPage cost memory 0.0514068603515625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe62a8e8-d822-4a79-9f8d-dc2935666918", "name": "runTaskFromQueue task cost before running: 318 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e94a40-1c21-4e8b-9f70-e0aa500844ea", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880214227100, "endTime": 8880214598900, "totalTime": 310900}, "additional": {"logType": "info", "children": [], "durationId": "5b774899-738a-40da-9cf2-f7cf476d8fc2"}}, {"head": {"id": "49e776bb-0c97-496f-9a54-4d17ebfe7511", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880215917100, "endTime": 8880216121100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ad5228c2-8e79-4e82-9a59-68df817dd28a", "logId": "601e6a4b-e136-4858-ab30-1d71a3ea2f13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad5228c2-8e79-4e82-9a59-68df817dd28a", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880215883300}, "additional": {"logType": "detail", "children": [], "durationId": "49e776bb-0c97-496f-9a54-4d17ebfe7511"}}, {"head": {"id": "056e822a-eb7e-4929-9f42-1b8fa891a788", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880215922000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502b9853-237c-488a-9099-b071924af9d2", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880216006500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514eb40f-6ad4-491d-82cd-f80bf31f0270", "name": "runTaskFromQueue task cost before running: 320 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880216074400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "601e6a4b-e136-4858-ab30-1d71a3ea2f13", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880215917100, "endTime": 8880216121100, "totalTime": 139600}, "additional": {"logType": "info", "children": [], "durationId": "49e776bb-0c97-496f-9a54-4d17ebfe7511"}}, {"head": {"id": "14eb1bab-f578-4d9b-b521-8cec9dd6d858", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880218599800, "endTime": 8880220921500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5b97b8f3-befd-43f1-8c71-49fa9c9f8526", "logId": "253f6251-bcf5-47ab-8e89-64bd48a1a1c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b97b8f3-befd-43f1-8c71-49fa9c9f8526", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880217398500}, "additional": {"logType": "detail", "children": [], "durationId": "14eb1bab-f578-4d9b-b521-8cec9dd6d858"}}, {"head": {"id": "a682166c-5650-47a0-a6ff-ab063463bc3a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880217810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323f912b-ab65-4a3c-9ff7-295df3bcb161", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880217881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea84957-e4be-4f44-9e0c-f42f42aa9cd4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880217926900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65b6a63-09e3-479d-af34-03505334cdc4", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880218608900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b93aaaf-fb0d-4cb6-bb1b-b6f98ed5235f", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880220749200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb91d85-989f-47aa-8602-27cefd5f7514", "name": "entry : default@PreviewUpdateAssets cost memory 0.10211181640625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880220862100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253f6251-bcf5-47ab-8e89-64bd48a1a1c5", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880218599800, "endTime": 8880220921500}, "additional": {"logType": "info", "children": [], "durationId": "14eb1bab-f578-4d9b-b521-8cec9dd6d858"}}, {"head": {"id": "4a37f7b1-ae9b-4004-bed9-f0896b98beca", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880227993100, "endTime": 8891528395600}, "additional": {"children": ["c86ee5f1-c454-4baa-a07e-2712c525906b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist."], "detailId": "bdd0b3d6-b183-421d-94e6-91c4b8eb242b", "logId": "113534b1-1125-471d-94f1-dacbe1e7593c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdd0b3d6-b183-421d-94e6-91c4b8eb242b", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880222794200}, "additional": {"logType": "detail", "children": [], "durationId": "4a37f7b1-ae9b-4004-bed9-f0896b98beca"}}, {"head": {"id": "e15a85fa-8667-4aa7-b5d7-d07a34f659ea", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880223244600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123aed23-800f-4bd8-9b49-4ce0b9a5eca0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880223314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42af710-c90b-4d17-a83a-adbf20882ee8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880223357600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce06d659-d3e6-431a-9e98-24e25ec9f52b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880228005900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b178eee-2d81-4937-acf4-5033c95e97bd", "name": "entry:default@PreviewArkTS is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets' does not exist.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880261699400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b296419d-bdfe-443d-a2be-36007264b7c3", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880261849800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86ee5f1-c454-4baa-a07e-2712c525906b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8880274176100, "endTime": 8891525800500}, "additional": {"children": ["9b35587e-b8a4-40d1-84a2-298a0b4130b7", "6a31a895-6195-4b13-9912-3fec1aaa00f1", "6af1df0f-578f-4136-81ab-5c7fe8571dbc", "951677d7-6f3e-4f74-8f0c-7bd6759f1452", "d0c279c3-6fcb-4019-8696-94da84ccf157", "e4c5d7f6-797d-451d-a5f2-966fa37ebfc4", "c23783ad-e55c-4cc3-9914-f169a4d81a1a", "d5f4b946-9c86-4105-91b6-6ada1bd4337e", "3dd9cc4a-6a18-4695-8dcc-2bb84b47c14d", "63f2234a-dda7-4fe5-8c96-496f571dad42", "0498d82c-5011-4b02-b30d-8527c6101940", "9d8fb270-64e3-41bb-958c-48525a531abd", "3f3ee5b3-1dd0-4abe-b5b5-a1ed61f579bb"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4a37f7b1-ae9b-4004-bed9-f0896b98beca", "logId": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92698fc5-e28d-4e46-a5f3-33eca10d39da", "name": "entry : default@PreviewArkTS cost memory 0.42014312744140625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880276146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95fbcd94-1f17-4eb1-8092-e44ec3084991", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8883779542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b35587e-b8a4-40d1-84a2-298a0b4130b7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8883780528800, "endTime": 8883780545300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "77baba29-28e1-45ac-a5dd-ab4119e65659"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77baba29-28e1-45ac-a5dd-ab4119e65659", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8883780528800, "endTime": 8883780545300}, "additional": {"logType": "info", "children": [], "durationId": "9b35587e-b8a4-40d1-84a2-298a0b4130b7", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "70c0a89c-c02f-4602-adf1-012c259abace", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8888578493800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a31a895-6195-4b13-9912-3fec1aaa00f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8888579688200, "endTime": 8888579707300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "876e9c05-a08e-4942-9346-d9b3dad57eb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "876e9c05-a08e-4942-9346-d9b3dad57eb1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8888579688200, "endTime": 8888579707300}, "additional": {"logType": "info", "children": [], "durationId": "6a31a895-6195-4b13-9912-3fec1aaa00f1", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "f687f7ea-704f-4243-a6fc-99b7e4fa7da4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8888823613600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af1df0f-578f-4136-81ab-5c7fe8571dbc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8888824721300, "endTime": 8888824743100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "ac9db276-383d-4038-b15e-1c47ecd736db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac9db276-383d-4038-b15e-1c47ecd736db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8888824721300, "endTime": 8888824743100}, "additional": {"logType": "info", "children": [], "durationId": "6af1df0f-578f-4136-81ab-5c7fe8571dbc", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "b4d84fff-7746-44fa-a0b5-1666abe95cbd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8888940451300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "951677d7-6f3e-4f74-8f0c-7bd6759f1452", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8888941537500, "endTime": 8888941561700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "272db71b-6a38-4e82-b761-91d8e2323522"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "272db71b-6a38-4e82-b761-91d8e2323522", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8888941537500, "endTime": 8888941561700}, "additional": {"logType": "info", "children": [], "durationId": "951677d7-6f3e-4f74-8f0c-7bd6759f1452", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "26a8a9a6-2c29-4bc4-95ea-168f5a8c85c9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889037166400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c279c3-6fcb-4019-8696-94da84ccf157", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8889038119100, "endTime": 8889038139300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "f6e72d6d-ac3c-4c24-a05b-e65a865f183d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6e72d6d-ac3c-4c24-a05b-e65a865f183d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889038119100, "endTime": 8889038139300}, "additional": {"logType": "info", "children": [], "durationId": "d0c279c3-6fcb-4019-8696-94da84ccf157", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "2297e905-84dc-4fd8-bf79-01c314e5c859", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889108747500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c5d7f6-797d-451d-a5f2-966fa37ebfc4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8889110448000, "endTime": 8889110476500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "871cf5e6-0d19-4c5e-923d-cc2ae6aff7f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "871cf5e6-0d19-4c5e-923d-cc2ae6aff7f5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889110448000, "endTime": 8889110476500}, "additional": {"logType": "info", "children": [], "durationId": "e4c5d7f6-797d-451d-a5f2-966fa37ebfc4", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "0c7425c9-e194-4924-9fbd-34650590c4bd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889292264000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c23783ad-e55c-4cc3-9914-f169a4d81a1a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8889293928800, "endTime": 8889293950700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "956a7de3-169a-4c3e-a82d-9fdc8d1a1b1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "956a7de3-169a-4c3e-a82d-9fdc8d1a1b1a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889293928800, "endTime": 8889293950700}, "additional": {"logType": "info", "children": [], "durationId": "c23783ad-e55c-4cc3-9914-f169a4d81a1a", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "0b245b5b-51a6-4e9d-9363-64645198e82e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889379321400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f4b946-9c86-4105-91b6-6ada1bd4337e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8889380472600, "endTime": 8889380502400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "492c3dee-8c7d-442a-b3d5-0904da896913"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "492c3dee-8c7d-442a-b3d5-0904da896913", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8889380472600, "endTime": 8889380502400}, "additional": {"logType": "info", "children": [], "durationId": "d5f4b946-9c86-4105-91b6-6ada1bd4337e", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "37b26133-640e-4de2-b458-e9e914c5f6b4", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891524476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd9cc4a-6a18-4695-8dcc-2bb84b47c14d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8891525676400, "endTime": 8891525700000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "c7229a80-d280-4d80-b1a3-49ab27c72008"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7229a80-d280-4d80-b1a3-49ab27c72008", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891525676400, "endTime": 8891525700000}, "additional": {"logType": "info", "children": [], "durationId": "3dd9cc4a-6a18-4695-8dcc-2bb84b47c14d", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8880274176100, "endTime": 8891525800500}, "additional": {"logType": "info", "children": ["77baba29-28e1-45ac-a5dd-ab4119e65659", "876e9c05-a08e-4942-9346-d9b3dad57eb1", "ac9db276-383d-4038-b15e-1c47ecd736db", "272db71b-6a38-4e82-b761-91d8e2323522", "f6e72d6d-ac3c-4c24-a05b-e65a865f183d", "871cf5e6-0d19-4c5e-923d-cc2ae6aff7f5", "956a7de3-169a-4c3e-a82d-9fdc8d1a1b1a", "492c3dee-8c7d-442a-b3d5-0904da896913", "c7229a80-d280-4d80-b1a3-49ab27c72008", "1e247463-2e25-4176-8884-f24dc13dbcd2", "bc69be44-4212-43d8-a36e-a96b4f4f9c52", "74b73ce9-cb17-4eb2-b556-23efe5124d85", "b4192a64-bb38-4977-a3c9-7d33ca8d3a24"], "durationId": "c86ee5f1-c454-4baa-a07e-2712c525906b", "parent": "113534b1-1125-471d-94f1-dacbe1e7593c"}}, {"head": {"id": "63f2234a-dda7-4fe5-8c96-496f571dad42", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8882700564100, "endTime": 8883750796000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "1e247463-2e25-4176-8884-f24dc13dbcd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e247463-2e25-4176-8884-f24dc13dbcd2", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8882700564100, "endTime": 8883750796000}, "additional": {"logType": "info", "children": [], "durationId": "63f2234a-dda7-4fe5-8c96-496f571dad42", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "0498d82c-5011-4b02-b30d-8527c6101940", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8883750995500, "endTime": 8883755590600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "bc69be44-4212-43d8-a36e-a96b4f4f9c52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc69be44-4212-43d8-a36e-a96b4f4f9c52", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8883750995500, "endTime": 8883755590600}, "additional": {"logType": "info", "children": [], "durationId": "0498d82c-5011-4b02-b30d-8527c6101940", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "9d8fb270-64e3-41bb-958c-48525a531abd", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8883755685000, "endTime": 8883755690600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "74b73ce9-cb17-4eb2-b556-23efe5124d85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74b73ce9-cb17-4eb2-b556-23efe5124d85", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8883755685000, "endTime": 8883755690600}, "additional": {"logType": "info", "children": [], "durationId": "9d8fb270-64e3-41bb-958c-48525a531abd", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "3f3ee5b3-1dd0-4abe-b5b5-a1ed61f579bb", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker13", "startTime": 8883755747600, "endTime": 8891524558600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c86ee5f1-c454-4baa-a07e-2712c525906b", "logId": "b4192a64-bb38-4977-a3c9-7d33ca8d3a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4192a64-bb38-4977-a3c9-7d33ca8d3a24", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8883755747600, "endTime": 8891524558600}, "additional": {"logType": "info", "children": [], "durationId": "3f3ee5b3-1dd0-4abe-b5b5-a1ed61f579bb", "parent": "1e121313-8051-4f3f-a6c2-d00f84bcfc8b"}}, {"head": {"id": "113534b1-1125-471d-94f1-dacbe1e7593c", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8880227993100, "endTime": 8891528395600, "totalTime": 11300390500}, "additional": {"logType": "info", "children": ["1e121313-8051-4f3f-a6c2-d00f84bcfc8b"], "durationId": "4a37f7b1-ae9b-4004-bed9-f0896b98beca"}}, {"head": {"id": "7ffb7e29-e1f6-4b3e-9000-46cceca84668", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891532519100, "endTime": 8891532742000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "3418020d-70ce-46bb-a246-f5607bf3acc6", "logId": "ac140f69-6737-4477-a865-b5458c98e204"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3418020d-70ce-46bb-a246-f5607bf3acc6", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891532487700}, "additional": {"logType": "detail", "children": [], "durationId": "7ffb7e29-e1f6-4b3e-9000-46cceca84668"}}, {"head": {"id": "d09397fd-2932-4df9-93d8-08758cf14497", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891532527500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3203ccf0-41d5-4c50-b7de-561d2da44f81", "name": "entry : PreviewBuild cost memory 0.0116729736328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891532623300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7afb979e-ae52-4a25-9c2e-582908fe2f2c", "name": "runTaskFromQueue task cost before running: 11 s 636 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891532695400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac140f69-6737-4477-a865-b5458c98e204", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891532519100, "endTime": 8891532742000, "totalTime": 160800}, "additional": {"logType": "info", "children": [], "durationId": "7ffb7e29-e1f6-4b3e-9000-46cceca84668"}}, {"head": {"id": "6c466a8b-4f1f-4af2-8c55-c46a931833e6", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542203300, "endTime": 8891542229400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abc36073-737b-4fd3-8031-0ad7668df91b", "logId": "e0461807-7ff1-479e-adfa-6207300682d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0461807-7ff1-479e-adfa-6207300682d3", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542203300, "endTime": 8891542229400}, "additional": {"logType": "info", "children": [], "durationId": "6c466a8b-4f1f-4af2-8c55-c46a931833e6"}}, {"head": {"id": "ddd6ccd2-fe7f-4394-82a4-35d4cbdd9256", "name": "BUILD SUCCESSFUL in 11 s 646 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542273300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "31bc9495-0f00-406f-93fc-ee439ec186bc", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8879897001500, "endTime": 8891542503000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 7}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d3b205ef-1e3a-4333-a7b8-ade1a04aae68", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542518400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b26c3ab-ca66-4dd8-96bc-6bd5b491baee", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dceef4e7-3374-4db7-8653-ec6434c87ebc", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542626700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68fb5f67-edc4-48fc-bf50-a7f1f99dd718", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542663000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8555470b-aa24-4cbb-b1f7-602fafa5dbaf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542698400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda7bcc7-919b-4ee3-bba1-9dbbdd4b5ced", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542731700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7742907-f2b0-4340-8a1d-e7f5d61e786e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542767300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c541362-9878-4a95-991b-4f65d22883d8", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa66fad-fecd-44c3-8c87-f551d7199ef0", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542827700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8dcd50-a27f-421b-b256-f60b3e6a18df", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891542882200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d042761-24bc-4101-9a35-6b753e3caeea", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891545755300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ec3ac9-86ca-435c-972f-d7f5ff8039b7", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891546505700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3636b0-4f87-43f4-9a9b-7132fc8711e6", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891546769200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ef7bae-2df7-4911-ab6d-11ada5c2a7df", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891560593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d636aeb9-8341-450d-be77-bb26f4127a7d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891561507400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c724c2b9-0ce2-43fd-ad4a-bbe7293a7c2f", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891561789800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88837186-be74-4b98-956f-db27fe8e3e75", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891562043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1145b4ea-6936-4891-885e-d36603321a02", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891562731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c7ef134-f64b-4c29-b5d0-3848d33f95bb", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891566377300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a052822a-01c8-41a8-944c-10db76bafd89", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891566647900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c67de1b6-57ec-4b48-8984-c5c1020141d1", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891566900200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c53ec09f-6b35-407c-a94a-f6124512ea34", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891567194800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c0c7e1-e1d5-4323-91dc-e90a9dee4a17", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:25 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8891567509100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}