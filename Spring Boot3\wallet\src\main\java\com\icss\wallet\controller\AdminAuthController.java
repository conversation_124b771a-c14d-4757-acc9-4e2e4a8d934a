package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.wallet.entity.Admin;
import com.icss.wallet.result.R;
import com.icss.wallet.result.MD5Util;
import com.icss.wallet.service.LoginService;
import com.icss.wallet.service.AdminService;
import com.icss.wallet.service.SmsCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员认证控制器
 * 提供管理员登录、注册、密码重置等功能
 */
@CrossOrigin
@RestController
@RequestMapping("/admin/auth")
public class AdminAuthController {
    
    @Autowired
    private LoginService loginService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private SmsCodeService smsCodeService;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public R login(@RequestParam String username, @RequestParam String password) {
        try {
            String token = loginService.login(username, password);
            // 获取管理员信息
            Admin admin = adminService.getAdminByUsername(username);
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("adminInfo", admin);
            return R.success("登录成功", result);
        } catch (RuntimeException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("登录失败，请稍后重试");
        }
    }

    /**
     * 管理员手机号验证码登录
     */
    @PostMapping("/login-with-code")
    public R<Map<String, Object>> loginWithCode(@RequestParam("phone") String phone,
                                                @RequestParam("code") String code) {
        try {
            System.out.println("验证码登录请求 - 手机号: " + phone + ", 验证码: " + code);

            // 验证验证码
            boolean isValidCode = smsCodeService.verifyCode(phone, code, 4); // 4表示管理员登录验证码
            if (!isValidCode) {
                System.out.println("验证码验证失败");
                return R.failure("验证码错误或已过期");
            }
            System.out.println("验证码验证成功");

            // 查找管理员
            Admin admin = adminService.getAdminByPhone(phone);
            System.out.println("查询管理员结果: " + (admin != null ? admin.toString() : "null"));

            if (admin == null) {
                return R.failure("管理员账号不存在");
            }

            if (admin.getStatus() == 0) {
                return R.failure("账号已被禁用");
            }

            // 更新最后登录时间
            admin.setLastLoginTime(new java.util.Date());
            adminService.updateAdmin(admin);

            // 生成token
            String token = "admin-token-" + admin.getAdminId() + "-" + System.currentTimeMillis();

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("adminInfo", admin);

            return R.success("登录成功", result);
        } catch (Exception e) {
            System.out.println("登录异常: " + e.getMessage());
            e.printStackTrace();
            return R.failure("登录失败: " + e.getMessage());
        }
    }

    /**
     * 测试查询管理员（调试用）
     */
    @GetMapping("/test-admin/{phone}")
    public R testAdmin(@PathVariable String phone) {
        try {
            System.out.println("测试查询管理员，手机号: " + phone);
            Admin admin = adminService.getAdminByPhone(phone);
            System.out.println("查询结果: " + (admin != null ? admin.toString() : "null"));

            if (admin != null) {
                return R.success("查询成功", admin);
            } else {
                return R.failure("管理员不存在");
            }
        } catch (Exception e) {
            System.out.println("查询异常: " + e.getMessage());
            e.printStackTrace();
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试用户名查询管理员（调试用）
     */
    @GetMapping("/debug/test-username/{username}")
    public R testAdminByUsername(@PathVariable String username) {
        try {
            System.out.println("测试查询管理员，用户名: " + username);
            Admin admin = adminService.getAdminByUsername(username);
            System.out.println("查询结果: " + (admin != null ? admin.toString() : "null"));

            if (admin != null) {
                return R.success("查询成功", admin);
            } else {
                return R.failure("管理员不存在");
            }
        } catch (Exception e) {
            System.out.println("查询异常: " + e.getMessage());
            e.printStackTrace();
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 测试密码验证（调试用）
     */
    @GetMapping("/debug/test-password/{username}/{password}")
    public R testPassword(@PathVariable String username, @PathVariable String password) {
        try {
            System.out.println("测试密码验证，用户名: " + username + ", 密码: " + password);

            Admin admin = adminService.getAdminByUsername(username);
            if (admin == null) {
                return R.failure("管理员不存在");
            }

            System.out.println("数据库中的密码: " + admin.getPassword());

            // 测试MD5加密
            String md5Password = MD5Util.encode(password);
            System.out.println("输入密码的MD5: " + md5Password);

            boolean isMatch = admin.getPassword().equals(md5Password);
            System.out.println("密码匹配结果: " + isMatch);

            Map<String, Object> result = new HashMap<>();
            result.put("dbPassword", admin.getPassword());
            result.put("inputPassword", password);
            result.put("md5Password", md5Password);
            result.put("isMatch", isMatch);

            return R.success("密码验证测试完成", result);
        } catch (Exception e) {
            System.out.println("密码验证异常: " + e.getMessage());
            e.printStackTrace();
            return R.failure("密码验证失败: " + e.getMessage());
        }
    }

    /**
     * 生成MD5密码（调试用）
     */
    @GetMapping("/debug/generate-md5/{password}")
    public R generateMD5(@PathVariable String password) {
        try {
            String md5 = MD5Util.encode(password);
            System.out.println("原始密码: " + password + " -> MD5: " + md5);

            Map<String, Object> result = new HashMap<>();
            result.put("originalPassword", password);
            result.put("md5Password", md5);

            return R.success("MD5生成成功", result);
        } catch (Exception e) {
            return R.failure("MD5生成失败: " + e.getMessage());
        }
    }

    /**
     * 发送管理员验证码
     */
    @PostMapping("/send-code")
    public R sendCode(@RequestParam String phone, @RequestParam Integer type) {
        try {
            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return R.failure("手机号格式不正确");
            }

            // 验证码类型：4-管理员登录，5-管理员注册
            if (type != 4 && type != 5) {
                return R.failure("无效的验证码类型");
            }

            // 如果是登录验证码，检查管理员是否存在
            if (type == 4) {
                Admin admin = adminService.getAdminByPhone(phone);
                if (admin == null) {
                    return R.failure("管理员账号不存在");
                }
                if (admin.getStatus() == 0) {
                    return R.failure("账号已被禁用");
                }
            }

            // 如果是注册验证码，检查手机号是否已注册
            if (type == 5) {
                if (adminService.isPhoneExists(phone)) {
                    return R.failure("手机号已被注册");
                }
            }

            String code = smsCodeService.sendCode(phone, type);
            return R.success("验证码发送成功", code); // 开发环境返回验证码，生产环境不应返回
        } catch (Exception e) {
            return R.failure("验证码发送失败: " + e.getMessage());
        }
    }

    /**
     * 管理员注册（用户名密码方式）
     */
    @PostMapping("/register")
    public R register(@RequestBody AdminRegisterRequest request) {
        try {
            // 验证必填字段
            if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
                return R.failure("用户名不能为空");
            }
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                return R.failure("密码不能为空");
            }
            if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                return R.failure("手机号不能为空");
            }
            if (request.getRealName() == null || request.getRealName().trim().isEmpty()) {
                return R.failure("真实姓名不能为空");
            }

            // 验证手机号格式
            if (!request.getPhone().matches("^1[3-9]\\d{9}$")) {
                return R.failure("手机号格式不正确");
            }

            // 验证用户名长度
            if (request.getUsername().length() < 3 || request.getUsername().length() > 20) {
                return R.failure("用户名长度必须在3-20位之间");
            }

            // 验证密码长度
            if (request.getPassword().length() < 6 || request.getPassword().length() > 20) {
                return R.failure("密码长度必须在6-20位之间");
            }

            // 设置默认角色
            String role = request.getRole();
            if (role == null || role.trim().isEmpty()) {
                role = "operator";
            }

            String token = loginService.register(
                request.getUsername().trim(),
                request.getPassword(),
                request.getPhone().trim(),
                request.getRealName().trim(),
                role
            );

            // 获取新注册的管理员信息
            Admin admin = adminService.getAdminByUsername(request.getUsername().trim());

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("adminInfo", admin);

            return R.success("注册成功", result);
        } catch (RuntimeException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("注册失败，请稍后重试");
        }
    }

    /**
     * 管理员验证码注册
     */
    @PostMapping("/register-with-code")
    public R registerWithCode(@RequestBody AdminCodeRegisterRequest request) {
        try {
            // 验证必填字段
            if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                return R.failure("手机号不能为空");
            }
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                return R.failure("验证码不能为空");
            }
            if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
                return R.failure("用户名不能为空");
            }
            if (request.getRealName() == null || request.getRealName().trim().isEmpty()) {
                return R.failure("真实姓名不能为空");
            }

            // 验证手机号格式
            if (!request.getPhone().matches("^1[3-9]\\d{9}$")) {
                return R.failure("手机号格式不正确");
            }

            // 验证用户名长度
            if (request.getUsername().length() < 3 || request.getUsername().length() > 20) {
                return R.failure("用户名长度必须在3-20位之间");
            }

            // 设置默认角色
            String role = request.getRole();
            if (role == null || role.trim().isEmpty()) {
                role = "operator";
            }

            String token = loginService.registerWithCode(
                request.getPhone().trim(),
                request.getCode().trim(),
                request.getUsername().trim(),
                request.getRealName().trim(),
                role
            );

            // 获取新注册的管理员信息
            Admin admin = adminService.getAdminByUsername(request.getUsername().trim());

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("adminInfo", admin);

            return R.success("注册成功", result);
        } catch (RuntimeException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("注册失败，请稍后重试");
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public R checkUsername(@RequestParam String username) {
        try {
            if (username == null || username.trim().isEmpty()) {
                return R.failure("用户名不能为空");
            }

            Admin admin = adminService.getAdminByUsername(username.trim());
            if (admin != null) {
                return R.failure("用户名已存在");
            }

            return R.success("用户名可用");
        } catch (Exception e) {
            return R.failure("检查用户名失败");
        }
    }

    /**
     * 检查手机号是否可用
     */
    @GetMapping("/check-phone")
    public R checkPhone(@RequestParam String phone) {
        try {
            if (phone == null || phone.trim().isEmpty()) {
                return R.failure("手机号不能为空");
            }

            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return R.failure("手机号格式不正确");
            }

            Admin admin = adminService.getAdminByPhone(phone.trim());
            if (admin != null) {
                return R.failure("手机号已被注册");
            }

            return R.success("手机号可用");
        } catch (Exception e) {
            return R.failure("检查手机号失败");
        }
    }

    /**
     * 获取当前登录管理员信息
     */
    @GetMapping("/current")
    public R getCurrentAdmin(@RequestParam String token) {
        try {
            // 这里应该根据token解析出管理员信息
            // 简化处理，实际应该验证token的有效性
            return R.success("获取成功", null);
        } catch (Exception e) {
            return R.failure("获取管理员信息失败");
        }
    }

    /**
     * 管理员注册请求对象（用户名密码方式）
     */
    public static class AdminRegisterRequest {
        private String username;
        private String password;
        private String phone;
        private String realName;
        private String role;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }
    }

    /**
     * 管理员验证码注册请求对象
     */
    public static class AdminCodeRegisterRequest {
        private String phone;
        private String code;
        private String username;
        private String realName;
        private String role;

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }
    }
}
