<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="36aebe99-82c9-4da3-b26c-9d0d446e2f2b" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="ArkTS File" />
      </list>
    </option>
  </component>
  <component name="HiLogStateProjectLevelPreference">
    <panelStates>
      <hilogPanelState>
        <option name="filterName" value="No filters" />
        <option name="logPanelType" value="ONLINE" />
      </hilogPanelState>
    </panelStates>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2yLnwOw5CF4hmHcA5EMzdOak4SS" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.entry.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ace.nodejs.version": "18.20.1",
    "last_opened_file_path": "D:/AAAqimo/wallet/HarmonyOS/Wallet",
    "resolution": "1080*2340",
    "shape": "phone"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\media" />
      <recent name="D:\HarmonyOSProject\Wallet\entry\src\main\ets\pages" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\HarmonyOSProject\Wallet\entry\src\main\resources\base\media" />
    </key>
  </component>
  <component name="RunManager" selected="Application.entry">
    <configuration name="entry" type="HotReLoadTask" factoryName="Hot Reload">
      <MODULE_NAME />
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <HOT_RELOAD_MODULE_NAME>entry</HOT_RELOAD_MODULE_NAME>
      <method v="2">
        <option name="Build.Hvigor.HotReloadBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="entry" type="OhosDebugTask" factoryName="OpenHarmony App">
      <MODULE_NAME>entry</MODULE_NAME>
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <method v="2">
        <option name="Build.Hvigor.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
</project>