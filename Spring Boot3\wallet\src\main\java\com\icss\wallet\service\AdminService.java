package com.icss.wallet.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.icss.wallet.entity.Admin;
import com.icss.wallet.mapper.AdminMapper;
import com.icss.wallet.result.MD5Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class AdminService {
    @Autowired
    private AdminMapper adminMapper;

    public void addAdmin(Admin admin) {
        if (adminMapper.selectCount(new QueryWrapper<Admin>()
                .eq("username", admin.getUsername())) > 0) {
            throw new RuntimeException("用户名已存在");
        }

        admin.setPassword(MD5Util.encode(admin.getPassword()));
        admin.setCreateTime(new Date());
        adminMapper.insert(admin);
    }

    public void deleteAdmin(Long adminId) {
        adminMapper.deleteById(adminId);
    }

    public void updateAdmin(Admin admin) {
        admin.setUpdateTime(new Date());
        if (admin.getPassword() != null) {
            admin.setPassword(MD5Util.encode(admin.getPassword()));
        }
        adminMapper.updateById(admin);
    }

    public Admin getById(Long adminId) {
        return adminMapper.selectById(adminId);
    }

    public IPage<Admin> page(Page<Admin> page) {
        // 数据库修复后，可以使用正常的分页查询
        return adminMapper.selectPage(page, new QueryWrapper<Admin>().orderByDesc("admin_id"));
    }

    /**
     * 根据用户名查找管理员
     * @param username 用户名
     * @return 管理员信息
     */
    public Admin getAdminByUsername(String username) {
        System.out.println("AdminService.getAdminByUsername() 查询用户名: " + username);
        Admin result = adminMapper.findByUsername(username);
        System.out.println("AdminService.getAdminByUsername() 查询结果: " + (result != null ? result.toString() : "null"));
        return result;
    }

    /**
     * 根据手机号查找管理员
     * @param phone 手机号
     * @return 管理员信息
     */
    public Admin getAdminByPhone(String phone) {
        System.out.println("AdminService.getAdminByPhone() 查询手机号: " + phone);
        Admin result = adminMapper.findByPhone(phone);
        System.out.println("AdminService.getAdminByPhone() 查询结果: " + (result != null ? result.toString() : "null"));
        return result;
    }

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    public boolean isUsernameExists(String username) {
        return adminMapper.selectCount(new QueryWrapper<Admin>()
                .eq("username", username)) > 0;
    }

    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    public boolean isPhoneExists(String phone) {
        return adminMapper.selectCount(new QueryWrapper<Admin>()
                .eq("phone", phone)) > 0;
    }

    /**
     * 获取管理员基本信息列表
     * @return 管理员基本信息列表
     */
    public List<Admin> getBasicInfo() {
        return adminMapper.selectBasicInfo();
    }
}