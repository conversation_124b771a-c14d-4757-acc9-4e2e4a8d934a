# 管理员登录功能实现说明

## 概述

本项目已成功实现独立的管理员登录页面，采用方案二：独立管理员登录页面的设计。管理员和普通用户拥有完全分离的登录入口和权限体系。

## 功能特性

### 1. 双重身份认证体系
- **普通用户登录**：基于手机号的用户认证 (`/login`)
- **管理员登录**：基于用户名的管理员认证 (`/admin-login`)

### 2. 独立的登录页面
- **普通用户登录页面**：`/login` - 支持密码登录和验证码登录
- **管理员登录页面**：`/admin-login` - 专门的管理员登录界面

### 3. 权限控制系统
- 基于Token的身份验证
- 路由守卫保护敏感页面
- 自动权限检查和重定向

### 4. 用户状态管理
- 统一的认证工具类 (`utils/auth.js`)
- 本地存储管理
- 用户类型识别

## 文件结构

```
VUE3/wallet/src/
├── views/
│   ├── Login.vue              # 普通用户登录页面
│   ├── AdminLogin.vue         # 管理员登录页面
│   └── AdminDashboard.vue     # 管理员仪表板
├── utils/
│   └── auth.js               # 认证工具类
└── router/
    └── index.js              # 路由配置（含权限守卫）
```

## 管理员账号信息

系统中预设的管理员账号：

| 用户名 | 密码 | 真实姓名 | 角色 | 状态 |
|--------|------|----------|------|------|
| admin | admin123 | 张建国 | admin | 正常 |
| limanager | manager123 | 李雪梅 | admin | 正常 |
| wangop | operator123 | 王志强 | operator | 正常 |

> **注意**：数据库中密码已MD5加密，上表显示的是原始密码用于测试登录。

## 使用方法

### 1. 普通用户登录
1. 访问 `http://localhost:5173/login`
2. 输入手机号和密码，或使用验证码登录
3. 登录成功后跳转到 `/home`

### 2. 管理员登录
1. 访问 `http://localhost:5173/admin-login`
2. 或在普通用户登录页面点击"管理员登录"链接
3. 输入管理员用户名和密码
4. 登录成功后跳转到 `/admin-dashboard`

### 3. 页面切换
- 在普通用户登录页面可以切换到管理员登录
- 在管理员登录页面可以切换到普通用户登录

## API接口

### 普通用户登录
```javascript
// 密码登录
POST http://localhost:8091/user/login
params: { username: "手机号", password: "密码" }

// 验证码登录
POST http://localhost:8091/user/loginWithCode
params: { phone: "手机号", code: "验证码" }
```

### 管理员登录
```javascript
// 管理员登录
POST http://localhost:8091/auth1/login1
params: { username: "用户名", password: "密码" }
```

## 权限控制

### 路由保护
- `/admin-*` 路径：仅管理员可访问
- `/home/<USER>
- 未登录用户自动重定向到对应登录页面

### Token管理
- 普通用户Token：存储在 `localStorage.userToken`
- 管理员Token：存储在 `localStorage.adminToken`
- 用户类型：存储在 `localStorage.userType`

## 认证工具类使用

```javascript
import { 
  isLoggedIn, 
  isAdmin, 
  isUser, 
  getCurrentUser, 
  logout,
  setUserLogin,
  setAdminLogin 
} from '@/utils/auth.js';

// 检查登录状态
if (isLoggedIn()) {
  console.log('用户已登录');
}

// 检查是否为管理员
if (isAdmin()) {
  console.log('当前用户是管理员');
}

// 获取当前用户信息
const user = getCurrentUser();
console.log(user);

// 退出登录
logout();
```

## 样式设计

### 普通用户登录页面
- 蓝紫色渐变背景
- 现代化卡片设计
- 支持密码和验证码两种登录方式

### 管理员登录页面
- 深色渐变背景（体现管理员身份）
- 红色主题色（区别于普通用户）
- 简洁专业的设计风格

### 管理员仪表板
- 功能卡片布局
- 快速访问链接
- 响应式设计

## 安全特性

1. **密码加密**：管理员密码使用MD5加密存储
2. **Token验证**：使用JWT Token进行身份验证
3. **权限分离**：管理员和普通用户完全独立的权限体系
4. **状态检查**：登录时检查账号状态，防止禁用账号登录
5. **路由守卫**：自动检查访问权限，未授权访问自动重定向

## 开发说明

### 添加新的管理员路由
1. 在 `router/index.js` 中添加路由，路径以 `/admin` 开头
2. 路由守卫会自动保护该路径，仅管理员可访问

### 扩展认证功能
1. 修改 `utils/auth.js` 添加新的认证方法
2. 更新路由守卫逻辑以支持新的权限检查

### 自定义登录后跳转
修改 `utils/auth.js` 中的 `getDefaultPath` 方法来自定义登录后的默认跳转路径。

## 测试建议

1. **功能测试**：
   - 测试普通用户登录流程
   - 测试管理员登录流程
   - 测试权限控制和路由保护

2. **安全测试**：
   - 尝试未授权访问管理员页面
   - 测试Token过期处理
   - 验证密码错误处理

3. **用户体验测试**：
   - 测试页面切换流程
   - 验证错误提示信息
   - 检查响应式设计

## 注意事项

1. 确保后端API服务正常运行（端口8091）
2. 管理员密码在数据库中已加密，请使用提供的测试账号
3. 本地存储的Token在浏览器关闭后仍然有效，如需清除请手动退出登录
4. 开发环境下CORS已配置，生产环境需要相应调整
