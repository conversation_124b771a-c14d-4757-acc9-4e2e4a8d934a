package com.icss.wallet.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 简化的CORS配置 - 专门用于开发环境
 * 避免复杂的配置冲突
 */
@Configuration
public class SimpleCorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                // 开发环境允许所有源
                .allowedOriginPatterns("http://localhost:*", "http://127.0.0.1:*")
                // 允许的HTTP方法
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                // 允许的请求头
                .allowedHeaders("*")
                // 不允许凭证（避免与通配符冲突）
                .allowCredentials(false)
                // 预检请求缓存时间
                .maxAge(3600)
                // 暴露的响应头
                .exposedHeaders("*");
    }
}
