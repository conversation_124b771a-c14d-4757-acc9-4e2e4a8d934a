package com.icss.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Payment;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PaymentMapper extends BaseMapper<Payment> {
    
    @Select("SELECT * FROM payment WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Payment> findByUserId(@Param("userId") Long userId);
    
    @Select("SELECT * FROM payment WHERE payment_no = #{paymentNo}")
    Payment findByPaymentNo(@Param("paymentNo") String paymentNo);

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询支付记录（包含用户信息）
     */
    @Select("<script>" +
            "SELECT p.*, u.phone, u.real_name " +
            "FROM payment p " +
            "LEFT JOIN user u ON p.user_id = u.user_id " +
            "WHERE 1=1 " +
            "<if test='paymentNo != null and paymentNo != \"\"'>" +
            "AND p.payment_no LIKE CONCAT('%', #{paymentNo}, '%') " +
            "</if>" +
            "<if test='orderNo != null and orderNo != \"\"'>" +
            "AND p.order_no LIKE CONCAT('%', #{orderNo}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND p.status = #{status} " +
            "</if>" +
            "ORDER BY p.create_time DESC" +
            "</script>")
    IPage<Payment> selectPaymentsWithUserInfo(Page<Payment> page,
                                            @Param("paymentNo") String paymentNo,
                                            @Param("orderNo") String orderNo,
                                            @Param("status") Integer status);

    /**
     * 管理员获取支付统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalPayments, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as successPayments, " +
            "COUNT(CASE WHEN status = 2 THEN 1 END) as failedPayments, " +
            "COALESCE(SUM(CASE WHEN status = 1 THEN amount ELSE 0 END), 0) as totalAmount " +
            "FROM payment")
    java.util.Map<String, Object> getPaymentStatistics();

    /**
     * 管理员修改支付记录
     */
    @Update("UPDATE payment SET " +
            "payment_no = #{paymentNo}, " +
            "amount = #{amount}, " +
            "merchant_name = #{merchantName}, " +
            "order_no = #{orderNo}, " +
            "description = #{description}, " +
            "status = #{status}, " +
            "update_time = NOW() " +
            "WHERE payment_id = #{paymentId}")
    int updatePaymentById(Payment payment);

    /**
     * 管理员删除支付记录
     */
    @Delete("DELETE FROM payment WHERE payment_id = #{paymentId}")
    int deletePaymentById(@Param("paymentId") Long paymentId);
}
