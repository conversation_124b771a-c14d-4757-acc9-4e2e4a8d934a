<template>
  <div class="payment-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><Money /></el-icon>
            支付管理
          </h2>
          <p>安全便捷的支付体验，支持钱包支付和银行卡支付</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 支付方式选择 -->
    <div class="payment-methods">
      <el-card class="method-card wallet-method" shadow="hover" @click="selectPaymentMethod('wallet')" 
               :class="{ active: selectedMethod === 'wallet' }">
        <div class="method-content">
          <div class="method-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="method-info">
            <h3>钱包支付</h3>
            <p>余额：¥{{ formatAmount(walletBalance) }}</p>
            <p class="method-desc">快速安全，即时到账</p>
          </div>
          <div class="method-status">
            <el-tag v-if="walletStatus === 1" type="success">可用</el-tag>
            <el-tag v-else type="danger">不可用</el-tag>
          </div>
        </div>
      </el-card>

      <el-card class="method-card bankcard-method" shadow="hover" @click="selectPaymentMethod('bankcard')"
               :class="{ active: selectedMethod === 'bankcard' }">
        <div class="method-content">
          <div class="method-icon">
            <el-icon><CreditCard /></el-icon>
          </div>
          <div class="method-info">
            <h3>银行卡支付</h3>
            <p>{{ activeBankCards }}张可用银行卡</p>
            <p class="method-desc">支持主流银行，安全保障</p>
          </div>
          <div class="method-status">
            <el-tag v-if="activeBankCards > 0" type="success">可用</el-tag>
            <el-tag v-else type="danger">无可用卡</el-tag>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 支付表单 -->
    <el-card class="payment-form-card" shadow="always" v-if="selectedMethod">
      <template #header>
        <div class="form-header">
          <el-icon v-if="selectedMethod === 'wallet'"><Wallet /></el-icon>
          <el-icon v-else><CreditCard /></el-icon>
          <span>{{ selectedMethod === 'wallet' ? '钱包支付' : '银行卡支付' }}</span>
        </div>
      </template>

      <!-- 钱包支付表单 -->
      <el-form v-if="selectedMethod === 'wallet'" :model="walletPayForm" :rules="walletPayRules" 
               ref="walletPayFormRef" label-width="120px" class="payment-form">
        <el-form-item label="支付金额" prop="amount">
          <el-input
            v-model="walletPayForm.amount"
            placeholder="请输入支付金额"
            type="number"
            step="0.01"
            min="0.01"
            :max="walletBalance"
            size="large"
          >
            <template #prepend>¥</template>
            <template #append>
              <el-button @click="setQuickAmount" type="text">快捷金额</el-button>
            </template>
          </el-input>
          <div class="amount-tips">
            钱包余额：¥{{ formatAmount(walletBalance) }}
          </div>
        </el-form-item>

        <el-form-item label="商户名称" prop="merchantName">
          <el-input
            v-model="walletPayForm.merchantName"
            placeholder="请输入商户名称"
            size="large"
            maxlength="50"
          >
            <template #prepend>
              <el-icon><Shop /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="支付描述" prop="description">
          <el-input
            v-model="walletPayForm.description"
            placeholder="请输入支付描述（可选）"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            resize="none"
          />
        </el-form-item>

        <el-form-item label="支付密码" prop="payPassword">
          <el-input
            v-model="walletPayForm.payPassword"
            placeholder="请输入6位支付密码"
            type="password"
            show-password
            maxlength="6"
            size="large"
          >
            <template #prepend>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="large" @click="handleWalletPayment" 
                     :loading="paymentLoading" class="pay-button">
            <el-icon><Wallet /></el-icon>
            确认支付 ¥{{ walletPayForm.amount || '0.00' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 银行卡支付表单 -->
      <el-form v-if="selectedMethod === 'bankcard'" :model="bankcardPayForm" :rules="bankcardPayRules" 
               ref="bankcardPayFormRef" label-width="120px" class="payment-form">
        <el-form-item label="支付金额" prop="amount">
          <el-input
            v-model="bankcardPayForm.amount"
            placeholder="请输入支付金额"
            type="number"
            step="0.01"
            min="0.01"
            max="100000"
            size="large"
          >
            <template #prepend>¥</template>
            <template #append>
              <el-button @click="setQuickAmount" type="text">快捷金额</el-button>
            </template>
          </el-input>
          <div class="amount-tips">单次支付限额：¥100,000</div>
        </el-form-item>

        <el-form-item label="选择银行卡" prop="bankCardId">
          <el-select v-model="bankcardPayForm.bankCardId" placeholder="请选择银行卡"
                     size="large" style="width: 100%">
            <el-option
              v-for="card in activeBankCardsList"
              :key="card.cardId"
              :value="card.cardId"
              :label="`${card.bankName} (${maskCardNumber(card.cardNumber)})`"
            >
              <div class="bank-card-option">
                <div class="card-info">
                  <span class="bank-name">{{ card.bankName }}</span>
                  <span class="card-number">{{ maskCardNumber(card.cardNumber) }}</span>
                </div>
                <div class="card-type">
                  <el-tag :type="card.cardType === 1 ? 'success' : 'warning'" size="small">
                    {{ card.cardType === 1 ? '借记卡' : '信用卡' }}
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
          <!-- 显示已选择的银行卡信息 -->
          <div v-if="selectedBankCard" class="selected-card-info">
            <el-tag type="info" size="small">
              已选择：{{ selectedBankCard.bankName }} ({{ maskCardNumber(selectedBankCard.cardNumber) }})
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="商户名称" prop="merchantName">
          <el-input
            v-model="bankcardPayForm.merchantName"
            placeholder="请输入商户名称"
            size="large"
            maxlength="50"
          >
            <template #prepend>
              <el-icon><Shop /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="支付描述" prop="description">
          <el-input
            v-model="bankcardPayForm.description"
            placeholder="请输入支付描述（可选）"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
            resize="none"
          />
        </el-form-item>

        <el-form-item label="支付密码" prop="payPassword">
          <el-input
            v-model="bankcardPayForm.payPassword"
            placeholder="请输入6位支付密码"
            type="password"
            show-password
            maxlength="6"
            size="large"
          >
            <template #prepend>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="large" @click="handleBankcardPayment" 
                     :loading="paymentLoading" class="pay-button">
            <el-icon><CreditCard /></el-icon>
            确认支付 ¥{{ bankcardPayForm.amount || '0.00' }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 快捷金额选择对话框 -->
    <el-dialog v-model="quickAmountDialogVisible" title="选择金额" width="400px">
      <div class="quick-amounts">
        <el-button 
          v-for="amount in quickAmounts" 
          :key="amount"
          @click="selectQuickAmount(amount)"
          class="amount-btn"
        >
          ¥{{ amount }}
        </el-button>
      </div>
      <template #footer>
        <el-button @click="quickAmountDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 支付记录 -->
    <el-card class="payment-history-card" shadow="hover" style="margin-top: 30px;">
      <template #header>
        <div class="card-header">
          <span>支付记录</span>
          <div class="header-actions">
            <el-button type="text" @click="refreshPaymentHistory">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="text" @click="exportPaymentHistory">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-bar">
        <el-form :model="filterForm" inline>
          <el-form-item label="支付方式">
            <el-select v-model="filterForm.paymentType" placeholder="全部" clearable style="width: 120px;">
              <el-option label="钱包支付" :value="1" />
              <el-option label="银行卡支付" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="支付状态">
            <el-select v-model="filterForm.status" placeholder="全部" clearable style="width: 120px;">
              <el-option label="待支付" :value="0" />
              <el-option label="支付成功" :value="1" />
              <el-option label="支付失败" :value="2" />
              <el-option label="已取消" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilter">筛选</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 支付记录表格 -->
      <el-table :data="filteredPaymentHistory" style="width: 100%" empty-text="暂无支付记录" v-loading="historyLoading">
        <el-table-column prop="paymentNo" label="支付单号" width="180" />
        <el-table-column prop="merchantName" label="商户名称" width="150" />
        <el-table-column prop="amount" label="支付金额" width="120" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentType" label="支付方式" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.paymentType === 1 ? 'success' : 'info'">
              {{ scope.row.paymentType === 1 ? '钱包' : '银行卡' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewPaymentDetail(scope.row)">
              详情
            </el-button>
            <el-button v-if="scope.row.status === 0" type="text" size="small" @click="cancelPayment(scope.row)">
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalRecords"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Wallet, CreditCard, Lock, Shop, Refresh, Download, Money } from '@element-plus/icons-vue'
import request from '@/utils/request'

// 数据
const selectedMethod = ref('')
const walletBalance = ref(0)
const walletStatus = ref(1)
const bankCards = ref([])
const paymentHistory = ref([])
const filteredPaymentHistory = ref([])

// 加载状态
const paymentLoading = ref(false)
const historyLoading = ref(false)

// 对话框状态
const quickAmountDialogVisible = ref(false)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalRecords = ref(0)

// 快捷金额
const quickAmounts = [10, 50, 100, 200, 500, 1000]

// 钱包支付表单
const walletPayForm = reactive({
  amount: '',
  merchantName: '',
  description: '',
  payPassword: ''
})

// 银行卡支付表单
const bankcardPayForm = reactive({
  amount: '',
  bankCardId: '',
  merchantName: '',
  description: '',
  payPassword: ''
})

// 筛选表单
const filterForm = reactive({
  paymentType: '',
  status: '',
  dateRange: []
})

// 表单引用
const walletPayFormRef = ref()
const bankcardPayFormRef = ref()

// 计算属性
const activeBankCards = computed(() => bankCards.value.filter(card => card.status === 1).length)
const activeBankCardsList = computed(() => bankCards.value.filter(card => card.status === 1))
const selectedBankCard = computed(() => {
  if (!bankcardPayForm.bankCardId) return null
  return bankCards.value.find(card => card.cardId === bankcardPayForm.bankCardId)
})

// 验证规则
const walletPayRules = reactive({
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || isNaN(value) || parseFloat(value) <= 0) {
          callback(new Error('支付金额必须大于0'))
        } else if (parseFloat(value) > walletBalance.value) {
          callback(new Error('支付金额不能超过钱包余额'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  merchantName: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' },
    { min: 6, max: 6, message: '支付密码必须是6位', trigger: 'blur' }
  ]
})

const bankcardPayRules = reactive({
  amount: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value || isNaN(value) || parseFloat(value) <= 0) {
          callback(new Error('支付金额必须大于0'))
        } else if (parseFloat(value) > 100000) {
          callback(new Error('单次支付金额不能超过10万元'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  bankCardId: [
    { required: true, message: '请选择银行卡', trigger: 'change' }
  ],
  merchantName: [
    { required: true, message: '请输入商户名称', trigger: 'blur' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' },
    { min: 6, max: 6, message: '支付密码必须是6位', trigger: 'blur' }
  ]
})

// 方法
const getUserId = () => {
  return 1 // 简化处理，实际应该从localStorage获取
}

const formatAmount = (amount) => {
  return parseFloat(amount || 0).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const maskCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  return cardNumber.replace(/(\d{4})\d*(\d{4})/, '$1 **** **** $2')
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  switch (status) {
    case 0: return 'warning'
    case 1: return 'success'
    case 2: return 'danger'
    case 3: return 'info'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 0: return '待支付'
    case 1: return '支付成功'
    case 2: return '支付失败'
    case 3: return '已取消'
    default: return '未知'
  }
}

// 选择支付方式
const selectPaymentMethod = (method) => {
  selectedMethod.value = method
  // 清空表单
  if (method === 'wallet') {
    Object.assign(walletPayForm, {
      amount: '',
      merchantName: '',
      description: '',
      payPassword: ''
    })
  } else {
    Object.assign(bankcardPayForm, {
      amount: '',
      bankCardId: '',
      merchantName: '',
      description: '',
      payPassword: ''
    })
  }
}

// 设置快捷金额
const setQuickAmount = () => {
  quickAmountDialogVisible.value = true
}

// 选择快捷金额
const selectQuickAmount = (amount) => {
  if (selectedMethod.value === 'wallet') {
    walletPayForm.amount = amount.toString()
  } else {
    bankcardPayForm.amount = amount.toString()
  }
  quickAmountDialogVisible.value = false
}

// 获取钱包余额
const getWalletBalance = async () => {
  try {
    const userId = getUserId()
    const response = await request.get(`/wallet/balance/${userId}`)
    if (response.data && response.data.code === 0) {
      const wallet = response.data.data
      walletBalance.value = wallet.balance
      walletStatus.value = wallet.status
    } else {
      ElMessage.error(response.data?.msg || '获取钱包余额失败')
    }
  } catch (error) {
    console.error('获取钱包余额失败:', error)
    if (error.message.includes('No static resource')) {
      ElMessage.error('钱包接口未找到，请检查后端服务是否正确启动')
    } else {
      ElMessage.error('获取钱包余额失败: ' + error.message)
    }
  }
}

// 获取银行卡列表
const getBankCards = async () => {
  try {
    const userId = getUserId()
    const response = await request.get(`/bankCards/user/${userId}`)
    if (response.data && response.data.code === 0) {
      bankCards.value = response.data.data || []
    } else {
      ElMessage.error(response.data?.msg || '获取银行卡列表失败')
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error)

    // 如果后端连接失败，使用模拟数据进行测试
    if (error.message.includes('No static resource') || error.code === 'ERR_NETWORK') {
      console.log('使用模拟银行卡数据进行测试')
      bankCards.value = [
        {
          cardId: 1,
          bankName: '中国工商银行',
          cardNumber: '6222021985051512345',
          cardType: 1,
          status: 1
        },
        {
          cardId: 2,
          bankName: '招商银行',
          cardNumber: '6225881985051512346',
          cardType: 1,
          status: 1
        },
        {
          cardId: 3,
          bankName: '中国建设银行',
          cardNumber: '6227001992032015678',
          cardType: 2,
          status: 1
        }
      ]
      ElMessage.warning('后端服务连接失败，使用模拟数据进行测试')
    } else {
      ElMessage.error('获取银行卡列表失败: ' + error.message)
    }
  }
}

// 处理钱包支付
const handleWalletPayment = async () => {
  try {
    const valid = await walletPayFormRef.value.validate()
    if (!valid) return

    await ElMessageBox.confirm(
      `确认使用钱包支付 ¥${walletPayForm.amount} 给 ${walletPayForm.merchantName}？`,
      '确认支付',
      {
        confirmButtonText: '确认支付',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    paymentLoading.value = true

    // 先创建支付订单
    const userId = getUserId()
    const createResponse = await request.post('/payment/create', null, {
      params: {
        userId: userId,
        amount: walletPayForm.amount,
        merchantName: walletPayForm.merchantName,
        orderNo: 'ORDER' + Date.now(),
        description: walletPayForm.description || '钱包支付'
      }
    })

    if (createResponse.data && createResponse.data.code === 0) {
      const payment = createResponse.data.data

      // 执行钱包支付
      const payResponse = await request.post('/payment/wallet', null, {
        params: {
          paymentNo: payment.paymentNo,
          payPassword: walletPayForm.payPassword
        }
      })

      if (payResponse.data && payResponse.data.code === 0) {
        ElMessage.success('钱包支付成功！')
        // 清空表单
        Object.assign(walletPayForm, {
          amount: '',
          merchantName: '',
          description: '',
          payPassword: ''
        })
        // 刷新数据
        getWalletBalance()
        getPaymentHistory()
      } else {
        ElMessage.error(payResponse.data?.msg || '钱包支付失败')
      }
    } else {
      ElMessage.error(createResponse.data?.msg || '创建支付订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('钱包支付失败:', error)
      ElMessage.error('钱包支付失败')
    }
  } finally {
    paymentLoading.value = false
  }
}

// 处理银行卡支付
const handleBankcardPayment = async () => {
  try {
    const valid = await bankcardPayFormRef.value.validate()
    if (!valid) return

    const selectedCard = bankCards.value.find(card => card.cardId === bankcardPayForm.bankCardId)
    if (!selectedCard) {
      ElMessage.error('请选择有效的银行卡')
      return
    }

    await ElMessageBox.confirm(
      `确认使用银行卡 ${selectedCard.bankName} (${maskCardNumber(selectedCard.cardNumber)}) 支付 ¥${bankcardPayForm.amount} 给 ${bankcardPayForm.merchantName}？`,
      '确认支付',
      {
        confirmButtonText: '确认支付',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    paymentLoading.value = true

    // 先创建支付订单
    const userId = getUserId()
    const createResponse = await request.post('/payment/create', null, {
      params: {
        userId: userId,
        amount: bankcardPayForm.amount,
        merchantName: bankcardPayForm.merchantName,
        orderNo: 'ORDER' + Date.now(),
        description: bankcardPayForm.description || '银行卡支付'
      }
    })

    if (createResponse.data && createResponse.data.code === 0) {
      const payment = createResponse.data.data

      // 执行银行卡支付
      const payResponse = await request.post('/payment/bankcard', null, {
        params: {
          paymentNo: payment.paymentNo,
          bankCardId: bankcardPayForm.bankCardId,
          payPassword: bankcardPayForm.payPassword
        }
      })

      if (payResponse.data && payResponse.data.code === 0) {
        ElMessage.success('银行卡支付成功！')
        // 清空表单
        Object.assign(bankcardPayForm, {
          amount: '',
          bankCardId: '',
          merchantName: '',
          description: '',
          payPassword: ''
        })
        // 刷新数据
        getPaymentHistory()
      } else {
        ElMessage.error(payResponse.data?.msg || '银行卡支付失败')
      }
    } else {
      ElMessage.error(createResponse.data?.msg || '创建支付订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('银行卡支付失败:', error)
      ElMessage.error('银行卡支付失败')
    }
  } finally {
    paymentLoading.value = false
  }
}

// 获取支付记录
const getPaymentHistory = async () => {
  try {
    historyLoading.value = true
    const userId = getUserId()

    // 先测试后端连接
    try {
      const testResponse = await request.get('/test/hello')
      console.log('后端连接测试成功:', testResponse.data)
    } catch (testError) {
      console.error('后端连接测试失败:', testError)
      ElMessage.error('后端服务连接失败，请检查服务是否启动')
      return
    }

    const response = await request.get(`/payment/history/${userId}`)
    if (response.data && response.data.code === 0) {
      paymentHistory.value = response.data.data || []
      totalRecords.value = paymentHistory.value.length
      applyFilter()
    } else {
      console.error('获取支付记录失败:', response.data?.msg)
      ElMessage.error('获取支付记录失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取支付记录失败:', error)
    if (error.message.includes('No static resource')) {
      ElMessage.error('支付接口未找到，请检查后端PaymentController是否正确启动')
    } else {
      ElMessage.error('获取支付记录失败: ' + error.message)
    }
  } finally {
    historyLoading.value = false
  }
}

// 刷新支付记录
const refreshPaymentHistory = () => {
  getPaymentHistory()
}

// 应用筛选
const applyFilter = () => {
  let filtered = [...paymentHistory.value]

  // 支付方式筛选
  if (filterForm.paymentType !== '') {
    filtered = filtered.filter(item => item.paymentType === filterForm.paymentType)
  }

  // 状态筛选
  if (filterForm.status !== '') {
    filtered = filtered.filter(item => item.status === filterForm.status)
  }

  // 时间范围筛选
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const startDate = new Date(filterForm.dateRange[0])
    const endDate = new Date(filterForm.dateRange[1])
    endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间

    filtered = filtered.filter(item => {
      const itemDate = new Date(item.createTime)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  filteredPaymentHistory.value = filtered
  totalRecords.value = filtered.length
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    paymentType: '',
    status: '',
    dateRange: []
  })
  applyFilter()
}

// 导出支付记录
const exportPaymentHistory = () => {
  ElMessage.info('导出功能开发中...')
}

// 查看支付详情
const viewPaymentDetail = (payment) => {
  ElMessageBox.alert(
    `支付单号：${payment.paymentNo}\n商户名称：${payment.merchantName}\n支付金额：¥${formatAmount(payment.amount)}\n支付方式：${payment.paymentType === 1 ? '钱包支付' : '银行卡支付'}\n支付状态：${getStatusText(payment.status)}\n创建时间：${formatDateTime(payment.createTime)}\n支付描述：${payment.description || '无'}`,
    '支付详情',
    {
      confirmButtonText: '确定'
    }
  )
}

// 取消支付
const cancelPayment = async (payment) => {
  try {
    await ElMessageBox.confirm(
      `确认取消支付订单 ${payment.paymentNo}？`,
      '取消支付',
      {
        confirmButtonText: '确认取消',
        cancelButtonText: '不取消',
        type: 'warning',
      }
    )

    // 这里应该调用取消支付的API
    ElMessage.success('支付订单已取消')
    getPaymentHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消支付失败:', error)
      ElMessage.error('取消支付失败')
    }
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 刷新数据
const refreshData = () => {
  getWalletBalance()
  getBankCards()
  getPaymentHistory()
}

// 生命周期
onMounted(() => {
  getWalletBalance()
  getBankCards()
  getPaymentHistory()
})
</script>

<style scoped>
.payment-manage {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 28px;
}

.page-header p {
  color: #909399;
  margin: 0;
  font-size: 16px;
}

/* 支付方式选择 */
.payment-methods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.method-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.method-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.method-card.active {
  border-color: #409eff;
  box-shadow: 0 0 20px rgba(64, 158, 255, 0.3);
}

.method-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.method-icon {
  font-size: 48px;
  margin-right: 20px;
  color: #409eff;
}

.wallet-method .method-icon {
  color: #67c23a;
}

.bankcard-method .method-icon {
  color: #e6a23c;
}

.method-info {
  flex: 1;
}

.method-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.method-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.method-desc {
  color: #909399 !important;
  font-size: 12px !important;
}

.method-status {
  margin-left: 15px;
}

/* 支付表单 */
.payment-form-card {
  margin-bottom: 30px;
}

.form-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
}

.payment-form {
  max-width: 600px;
  margin: 0 auto;
}

.amount-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.pay-button {
  width: 100%;
  height: 50px;
  font-size: 18px;
  font-weight: bold;
}

/* 银行卡选项 */
.bank-card-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-info {
  display: flex;
  flex-direction: column;
}

.bank-name {
  font-weight: bold;
  color: #303133;
}

.card-number {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 快捷金额 */
.quick-amounts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.amount-btn {
  height: 50px;
  font-size: 16px;
  font-weight: bold;
}

/* 支付记录 */
.payment-history-card {
  margin-top: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 选中银行卡信息样式 */
.selected-card-info {
  margin-top: 8px;
}

.selected-card-info .el-tag {
  font-size: 12px;
}

/* 银行卡选择器样式优化 */
.bank-card-option {
  padding: 8px 0;
}

.bank-card-option .card-info {
  margin-bottom: 4px;
}

.bank-card-option .bank-name {
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.bank-card-option .card-number {
  color: #909399;
  font-family: 'Courier New', monospace;
}
</style>
