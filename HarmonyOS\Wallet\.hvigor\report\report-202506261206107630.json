{"version": "2.0", "ppid": 13468, "events": [{"head": {"id": "790b83e7-aee7-4098-bc3c-4b33e2c8c9e2", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264319761700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c1021eb-8ce5-44a8-9ae3-56764e741829", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12387405956900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe312898-5eaa-40f1-a1ff-cdc022613c6b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12387407762500, "endTime": 12387407808400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "3a4c9a1c-0127-4099-a9bc-c220ca01761b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a4c9a1c-0127-4099-a9bc-c220ca01761b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12387407762500, "endTime": 12387407808400}, "additional": {"logType": "info", "children": [], "durationId": "fe312898-5eaa-40f1-a1ff-cdc022613c6b"}}, {"head": {"id": "fd45fdf0-afd1-4fdb-9146-3780665c475b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388533340300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e205bcf-e49b-4a9d-ab6f-d6de67823cfb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388535007800, "endTime": 12388535029600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "cd8e9fce-c7dc-498a-b2bf-40f406e8e700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd8e9fce-c7dc-498a-b2bf-40f406e8e700", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388535007800, "endTime": 12388535029600}, "additional": {"logType": "info", "children": [], "durationId": "2e205bcf-e49b-4a9d-ab6f-d6de67823cfb"}}, {"head": {"id": "fd5eb2fe-c32e-461d-8d43-e27599fe9d20", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388535118700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4ff64d-648f-4d5d-a168-a173ebe9d565", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388536069400, "endTime": 12388536090600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "4e4e84ec-b054-4698-8318-8441153113d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e4e84ec-b054-4698-8318-8441153113d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388536069400, "endTime": 12388536090600}, "additional": {"logType": "info", "children": [], "durationId": "ca4ff64d-648f-4d5d-a168-a173ebe9d565"}}, {"head": {"id": "bfc8c322-17fd-4f84-9d15-a103b97f51e5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388536179400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66cfbdcc-be75-4d5c-a9bb-e9e618f30b70", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388537556000, "endTime": 12388537593400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "9da8cd59-81f7-4d9e-8bfd-321cc380de8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9da8cd59-81f7-4d9e-8bfd-321cc380de8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12388537556000, "endTime": 12388537593400}, "additional": {"logType": "info", "children": [], "durationId": "66cfbdcc-be75-4d5c-a9bb-e9e618f30b70"}}, {"head": {"id": "23f4c188-359f-418e-a4b7-8977b2268570", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389045611100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09377d52-ef13-479c-b354-9bc1fedcaccb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389047782500, "endTime": 12389047811800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "b4211898-f788-4e26-9b9e-b694ae4920da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4211898-f788-4e26-9b9e-b694ae4920da", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389047782500, "endTime": 12389047811800}, "additional": {"logType": "info", "children": [], "durationId": "09377d52-ef13-479c-b354-9bc1fedcaccb"}}, {"head": {"id": "ac1137a1-ece2-47ea-9572-6c8df3c03a0d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389047942200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72baca5f-d95b-4bd4-aa26-2449f0bcc44b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389049539600, "endTime": 12389049574400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "de8c88db-62cb-4f71-a39f-231bf8b9573c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de8c88db-62cb-4f71-a39f-231bf8b9573c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389049539600, "endTime": 12389049574400}, "additional": {"logType": "info", "children": [], "durationId": "72baca5f-d95b-4bd4-aa26-2449f0bcc44b"}}, {"head": {"id": "63ffbe56-1991-48db-9ea6-91ee9d73ddb1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389049744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b037d9eb-773b-41be-bd91-ba654f4495af", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389051475500, "endTime": 12389051521400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "4773f2ab-c2ad-402c-b393-0b2567c1269a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4773f2ab-c2ad-402c-b393-0b2567c1269a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389051475500, "endTime": 12389051521400}, "additional": {"logType": "info", "children": [], "durationId": "b037d9eb-773b-41be-bd91-ba654f4495af"}}, {"head": {"id": "02fbccca-8eb9-465a-8692-7ecc764b5d12", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389051717600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a085c57f-8cd6-44f5-831a-6877f83a05f2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389053226900, "endTime": 12389053256800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "b7a6cb02-8e8e-477c-9d10-bd9f1ec901ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7a6cb02-8e8e-477c-9d10-bd9f1ec901ef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389053226900, "endTime": 12389053256800}, "additional": {"logType": "info", "children": [], "durationId": "a085c57f-8cd6-44f5-831a-6877f83a05f2"}}, {"head": {"id": "76407339-d13a-4690-9ffe-b1e4b34354e6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389053376100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f2e57a-4074-4e2d-864d-5331542e82dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389054872000, "endTime": 12389054932900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "c2e70881-64c6-439b-8bed-ac2ecb9fd627"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2e70881-64c6-439b-8bed-ac2ecb9fd627", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389054872000, "endTime": 12389054932900}, "additional": {"logType": "info", "children": [], "durationId": "89f2e57a-4074-4e2d-864d-5331542e82dc"}}, {"head": {"id": "6049133f-3c3a-4060-bf03-8b91484e4a89", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389055155100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b10be18-533d-4a58-b5b0-616cf1f29be5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389056753100, "endTime": 12389056804600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "bd9bdda9-a986-4092-9cbf-54e4e42b8991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd9bdda9-a986-4092-9cbf-54e4e42b8991", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389056753100, "endTime": 12389056804600}, "additional": {"logType": "info", "children": [], "durationId": "7b10be18-533d-4a58-b5b0-616cf1f29be5"}}, {"head": {"id": "a6e59757-bd73-489e-95cb-92cfc3c9ab71", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389056983200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8257a7d-fc57-4ba7-9405-13d94b464b51", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389058578600, "endTime": 12389058645700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "c97397d6-c3c5-48b9-b0b7-4c3b93c7a53f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c97397d6-c3c5-48b9-b0b7-4c3b93c7a53f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389058578600, "endTime": 12389058645700}, "additional": {"logType": "info", "children": [], "durationId": "a8257a7d-fc57-4ba7-9405-13d94b464b51"}}, {"head": {"id": "bf12ef90-f5d2-407e-9e83-cb6710310429", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389058854400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdca0abb-8629-482a-b989-cbad4ff1d2dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389060488300, "endTime": 12389060526800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "ccdefdce-0cdb-4c0f-a294-7eb63fc5c3d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccdefdce-0cdb-4c0f-a294-7eb63fc5c3d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12389060488300, "endTime": 12389060526800}, "additional": {"logType": "info", "children": [], "durationId": "fdca0abb-8629-482a-b989-cbad4ff1d2dc"}}, {"head": {"id": "62b73179-9fec-4a05-91d8-f1a2b935b694", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12429674094000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcda06da-1621-4578-96ee-ed3a44fa0117", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12429674574100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb80d1c-dcb0-4605-902a-c6f3a4faa80a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430628004800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430634371200, "endTime": 12430820694200}, "additional": {"children": ["03e5c4ce-7e64-421d-9d38-eee97c9bd4e9", "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "9f6e60ab-3c88-431a-b1c6-3da9a524a4d4", "7d518450-3d4d-4376-9e98-fc64417cec21", "8e34f560-db93-415f-90cc-02806f000dc0", "ed55417d-19a2-48c0-9875-b64a3fec207e", "41b72be9-857f-4443-9bf1-8ac5e36ab567"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03e5c4ce-7e64-421d-9d38-eee97c9bd4e9", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430634372800, "endTime": 12430646826800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "5f1c54da-e04f-476d-8e13-ad31366ba1fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430646844700, "endTime": 12430819335200}, "additional": {"children": ["d68b9e84-a640-479a-afdd-555f9069ea46", "e9d89657-4d8f-495a-ab9a-0bbd149ccd3f", "c08e7079-acd3-4665-98df-c562205c7dc0", "88080851-5e4c-4a7e-ae88-8b24a7795ee3", "ac6b9d7a-bc5e-4610-b339-5a361a693941", "d9f7a619-df0e-458a-ab1d-d4bd7398559b", "820ef771-4796-4e52-b3c4-f71df1cd0105", "bcbfa3e1-ef5c-40cb-bb94-0c63d28457e7", "5faf1f70-d2e0-458d-8f3c-8439877efdf6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "995c0738-6c76-49fc-9246-0e538d8af06d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f6e60ab-3c88-431a-b1c6-3da9a524a4d4", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430819369200, "endTime": 12430820659500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "265957d9-8d8b-4b91-8078-91d50e506510"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d518450-3d4d-4376-9e98-fc64417cec21", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430820667000, "endTime": 12430820688800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "94f24a28-c128-47dc-bcdb-c8fe5a9a687d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e34f560-db93-415f-90cc-02806f000dc0", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430637275400, "endTime": 12430637315600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "0cc6a02b-c5f7-4785-bcf0-4f8131181bf4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cc6a02b-c5f7-4785-bcf0-4f8131181bf4", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430637275400, "endTime": 12430637315600}, "additional": {"logType": "info", "children": [], "durationId": "8e34f560-db93-415f-90cc-02806f000dc0", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "ed55417d-19a2-48c0-9875-b64a3fec207e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430642611200, "endTime": 12430642626600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "a880d1a4-e788-40e6-8d6c-e68c58722f18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a880d1a4-e788-40e6-8d6c-e68c58722f18", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430642611200, "endTime": 12430642626600}, "additional": {"logType": "info", "children": [], "durationId": "ed55417d-19a2-48c0-9875-b64a3fec207e", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "048e9bd4-6d0e-400f-ba8c-f344491b0495", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430642685700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c039fab-7249-47c9-9614-d799ecad16de", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430646705400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f1c54da-e04f-476d-8e13-ad31366ba1fc", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430634372800, "endTime": 12430646826800}, "additional": {"logType": "info", "children": [], "durationId": "03e5c4ce-7e64-421d-9d38-eee97c9bd4e9", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "d68b9e84-a640-479a-afdd-555f9069ea46", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430654488300, "endTime": 12430654498700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "973e1e77-45ef-42b5-b52d-22c5d4497340"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9d89657-4d8f-495a-ab9a-0bbd149ccd3f", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430654513300, "endTime": 12430658579700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "8af786f0-2f8b-424e-9bb1-79b9bd78a86b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c08e7079-acd3-4665-98df-c562205c7dc0", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430658593100, "endTime": 12430736234500}, "additional": {"children": ["27d5ab23-90dc-42fa-a054-23bd898de403", "901da18c-6e22-4cda-a3ad-e0845befb915", "9d8b61fd-887a-4be0-88da-9d36a30fb3f6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "11ae62ee-fa97-4f23-85da-61c028c3c67a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88080851-5e4c-4a7e-ae88-8b24a7795ee3", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430736250900, "endTime": 12430775825000}, "additional": {"children": ["ee7a1dcc-0827-4f98-9713-02e612c48164"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "0f7a20cf-1981-4ccd-9876-c3d48208f596"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac6b9d7a-bc5e-4610-b339-5a361a693941", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430775835100, "endTime": 12430794443900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "21a2179f-9af6-4561-8041-0661db876e8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9f7a619-df0e-458a-ab1d-d4bd7398559b", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430795482300, "endTime": 12430803426200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "0f7606f9-999c-4d3e-a28a-42847269b01b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "820ef771-4796-4e52-b3c4-f71df1cd0105", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430803451700, "endTime": 12430819177900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "24408068-1cf9-4e65-9f76-474a3ea596f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bcbfa3e1-ef5c-40cb-bb94-0c63d28457e7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430819201800, "endTime": 12430819321700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "7b0a374f-9122-43f5-884f-331d54d10fce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "973e1e77-45ef-42b5-b52d-22c5d4497340", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430654488300, "endTime": 12430654498700}, "additional": {"logType": "info", "children": [], "durationId": "d68b9e84-a640-479a-afdd-555f9069ea46", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "8af786f0-2f8b-424e-9bb1-79b9bd78a86b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430654513300, "endTime": 12430658579700}, "additional": {"logType": "info", "children": [], "durationId": "e9d89657-4d8f-495a-ab9a-0bbd149ccd3f", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "27d5ab23-90dc-42fa-a054-23bd898de403", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430659254600, "endTime": 12430659277800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c08e7079-acd3-4665-98df-c562205c7dc0", "logId": "a8702ede-47b7-48cf-813f-743d2f592645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8702ede-47b7-48cf-813f-743d2f592645", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430659254600, "endTime": 12430659277800}, "additional": {"logType": "info", "children": [], "durationId": "27d5ab23-90dc-42fa-a054-23bd898de403", "parent": "11ae62ee-fa97-4f23-85da-61c028c3c67a"}}, {"head": {"id": "901da18c-6e22-4cda-a3ad-e0845befb915", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430663290200, "endTime": 12430735596400}, "additional": {"children": ["5b9bb7a6-42e0-4f14-b3d4-a52940f437c3", "677ec29f-b82f-4c3f-96a3-a4bd751ada56"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c08e7079-acd3-4665-98df-c562205c7dc0", "logId": "8ff186fe-fde8-4211-81a0-7452ccd14693"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b9bb7a6-42e0-4f14-b3d4-a52940f437c3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430663292100, "endTime": 12430668165500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "901da18c-6e22-4cda-a3ad-e0845befb915", "logId": "e6b9544f-30eb-45ba-9dfc-5c418c4a2628"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "677ec29f-b82f-4c3f-96a3-a4bd751ada56", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430668185800, "endTime": 12430735580700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "901da18c-6e22-4cda-a3ad-e0845befb915", "logId": "f74a26e7-7afa-4e55-a539-a351f53bb727"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "207d942c-0e4a-4048-b502-1dd95f63d9be", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430663304600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972bdd57-7187-44df-8135-c5f2278cb20e", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430668017400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b9544f-30eb-45ba-9dfc-5c418c4a2628", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430663292100, "endTime": 12430668165500}, "additional": {"logType": "info", "children": [], "durationId": "5b9bb7a6-42e0-4f14-b3d4-a52940f437c3", "parent": "8ff186fe-fde8-4211-81a0-7452ccd14693"}}, {"head": {"id": "fd098b2d-e32e-4c53-b2e3-0ebd87a2e2a0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430668202800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efcb381c-f80c-4c38-88a1-025a819f49ee", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430674043400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e55ba8f1-e1a4-4686-a428-06118d7d7aa2", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430674156000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60db6648-3aaf-4e13-a20a-c6178bfab06f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430675699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87c55960-d5aa-4b82-a053-ad0e421de0d9", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430675835400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "760790ad-393e-48c8-bf61-4d6e4ddecf67", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430677518800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56ca56ca-13a9-402d-a322-01e18d0d22b7", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430681850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46afd71e-c851-4aaa-8be4-c8d7ad32e3f1", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430691096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f613d97-8107-4a17-b197-8a575565af59", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430714596200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08a5a225-3b81-45f3-bfc6-4551f9e435ac", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430714767600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 6}, "markType": "other"}}, {"head": {"id": "455c4304-05c4-418c-a822-5c97b14521a0", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430714788400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 6}, "markType": "other"}}, {"head": {"id": "d879ffdd-f81f-4b20-b906-dd67aef29d08", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430735290000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc2a61e-386e-4282-8b7d-db8ec074cfe0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430735426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15326823-709e-4f6b-9b53-92137eb089c1", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430735488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be7b548-5629-4a38-b3d5-9b443c4ce183", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430735538700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74a26e7-7afa-4e55-a539-a351f53bb727", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430668185800, "endTime": 12430735580700}, "additional": {"logType": "info", "children": [], "durationId": "677ec29f-b82f-4c3f-96a3-a4bd751ada56", "parent": "8ff186fe-fde8-4211-81a0-7452ccd14693"}}, {"head": {"id": "8ff186fe-fde8-4211-81a0-7452ccd14693", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430663290200, "endTime": 12430735596400}, "additional": {"logType": "info", "children": ["e6b9544f-30eb-45ba-9dfc-5c418c4a2628", "f74a26e7-7afa-4e55-a539-a351f53bb727"], "durationId": "901da18c-6e22-4cda-a3ad-e0845befb915", "parent": "11ae62ee-fa97-4f23-85da-61c028c3c67a"}}, {"head": {"id": "9d8b61fd-887a-4be0-88da-9d36a30fb3f6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430736195200, "endTime": 12430736219200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c08e7079-acd3-4665-98df-c562205c7dc0", "logId": "414d65ed-014f-4866-91a4-54395981d503"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "414d65ed-014f-4866-91a4-54395981d503", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430736195200, "endTime": 12430736219200}, "additional": {"logType": "info", "children": [], "durationId": "9d8b61fd-887a-4be0-88da-9d36a30fb3f6", "parent": "11ae62ee-fa97-4f23-85da-61c028c3c67a"}}, {"head": {"id": "11ae62ee-fa97-4f23-85da-61c028c3c67a", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430658593100, "endTime": 12430736234500}, "additional": {"logType": "info", "children": ["a8702ede-47b7-48cf-813f-743d2f592645", "8ff186fe-fde8-4211-81a0-7452ccd14693", "414d65ed-014f-4866-91a4-54395981d503"], "durationId": "c08e7079-acd3-4665-98df-c562205c7dc0", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "ee7a1dcc-0827-4f98-9713-02e612c48164", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430736795800, "endTime": 12430775808900}, "additional": {"children": ["98f3f8a8-c7ad-406a-94e6-4fe3de7c7d22", "f77377d0-95a8-4a1a-84fe-54c4d053d36a", "3b7b2ef7-e06d-47ae-ac25-db5232cf32d6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88080851-5e4c-4a7e-ae88-8b24a7795ee3", "logId": "91b83930-96ce-4680-9f8c-c50650ea7f54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98f3f8a8-c7ad-406a-94e6-4fe3de7c7d22", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430739448200, "endTime": 12430739463800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee7a1dcc-0827-4f98-9713-02e612c48164", "logId": "485c86b4-36c1-4327-aade-79d6936927c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "485c86b4-36c1-4327-aade-79d6936927c7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430739448200, "endTime": 12430739463800}, "additional": {"logType": "info", "children": [], "durationId": "98f3f8a8-c7ad-406a-94e6-4fe3de7c7d22", "parent": "91b83930-96ce-4680-9f8c-c50650ea7f54"}}, {"head": {"id": "f77377d0-95a8-4a1a-84fe-54c4d053d36a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430745944000, "endTime": 12430771972300}, "additional": {"children": ["bc6b5868-15d4-4b63-8580-ff2d8734cfe4", "0fa677bd-7498-4750-bfd9-7f53e4919ffe"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee7a1dcc-0827-4f98-9713-02e612c48164", "logId": "9d284771-e708-4812-aecf-e471842b2ec3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc6b5868-15d4-4b63-8580-ff2d8734cfe4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430745945500, "endTime": 12430753577600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f77377d0-95a8-4a1a-84fe-54c4d053d36a", "logId": "7b8071b2-3550-4a60-a95f-36f3645d8529"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fa677bd-7498-4750-bfd9-7f53e4919ffe", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430753624500, "endTime": 12430771957100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f77377d0-95a8-4a1a-84fe-54c4d053d36a", "logId": "bfa6b9ca-8d1a-44c5-b8d5-fdca91482d79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "696861cd-c4b7-4fb1-8fb4-6b89a53dded4", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430745957000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aace8fdb-d817-4603-a974-e24375ceb4e4", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430753388800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8071b2-3550-4a60-a95f-36f3645d8529", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430745945500, "endTime": 12430753577600}, "additional": {"logType": "info", "children": [], "durationId": "bc6b5868-15d4-4b63-8580-ff2d8734cfe4", "parent": "9d284771-e708-4812-aecf-e471842b2ec3"}}, {"head": {"id": "8deb9bee-2ce1-47c4-9a95-43a3d9e4697f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430753641700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "723759c4-51e1-4361-b61c-a7bcec46a864", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430766867700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16b5f544-fbf7-485f-a05e-f3ab8df81b28", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430767089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eecfe09d-b6ee-4157-a5f4-7c52815ec9af", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430767438300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9711402a-b321-46f9-add5-a48213c8bbab", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430767613800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545ae35c-636f-4c79-9a02-c4e81fa0fece", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430767773500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c351323-d89a-48e4-9c3b-060e32a402da", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430767948600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62688435-b8b9-4d68-8672-f516b0752de9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430768022300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3e42af-e573-487d-b2e8-fb4b6f78875d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430771327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00295b23-dcf9-49db-8bdf-1ebd5327f706", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430771603200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a16cce9-7e35-456f-b809-9ab853ed0fbe", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430771697500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46f7c6c-e13d-4261-9d05-2a4b104a0eb3", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430771893300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa6b9ca-8d1a-44c5-b8d5-fdca91482d79", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430753624500, "endTime": 12430771957100}, "additional": {"logType": "info", "children": [], "durationId": "0fa677bd-7498-4750-bfd9-7f53e4919ffe", "parent": "9d284771-e708-4812-aecf-e471842b2ec3"}}, {"head": {"id": "9d284771-e708-4812-aecf-e471842b2ec3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430745944000, "endTime": 12430771972300}, "additional": {"logType": "info", "children": ["7b8071b2-3550-4a60-a95f-36f3645d8529", "bfa6b9ca-8d1a-44c5-b8d5-fdca91482d79"], "durationId": "f77377d0-95a8-4a1a-84fe-54c4d053d36a", "parent": "91b83930-96ce-4680-9f8c-c50650ea7f54"}}, {"head": {"id": "3b7b2ef7-e06d-47ae-ac25-db5232cf32d6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430775764000, "endTime": 12430775786600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee7a1dcc-0827-4f98-9713-02e612c48164", "logId": "453aa6f7-486e-4311-b981-652a878118df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "453aa6f7-486e-4311-b981-652a878118df", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430775764000, "endTime": 12430775786600}, "additional": {"logType": "info", "children": [], "durationId": "3b7b2ef7-e06d-47ae-ac25-db5232cf32d6", "parent": "91b83930-96ce-4680-9f8c-c50650ea7f54"}}, {"head": {"id": "91b83930-96ce-4680-9f8c-c50650ea7f54", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430736795800, "endTime": 12430775808900}, "additional": {"logType": "info", "children": ["485c86b4-36c1-4327-aade-79d6936927c7", "9d284771-e708-4812-aecf-e471842b2ec3", "453aa6f7-486e-4311-b981-652a878118df"], "durationId": "ee7a1dcc-0827-4f98-9713-02e612c48164", "parent": "0f7a20cf-1981-4ccd-9876-c3d48208f596"}}, {"head": {"id": "0f7a20cf-1981-4ccd-9876-c3d48208f596", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430736250900, "endTime": 12430775825000}, "additional": {"logType": "info", "children": ["91b83930-96ce-4680-9f8c-c50650ea7f54"], "durationId": "88080851-5e4c-4a7e-ae88-8b24a7795ee3", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "94ff8f4c-0853-4e3d-a1c7-668ec7d8118c", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430793618600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e6083db-9ac0-4528-bebd-a26cc01948a5", "name": "hvigorfile, resolve hvigorfile dependencies in 19 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430794332300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a2179f-9af6-4561-8041-0661db876e8d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430775835100, "endTime": 12430794443900}, "additional": {"logType": "info", "children": [], "durationId": "ac6b9d7a-bc5e-4610-b339-5a361a693941", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "5faf1f70-d2e0-458d-8f3c-8439877efdf6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430795273500, "endTime": 12430795467700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "logId": "a99ad06d-3964-41e0-88aa-f0f4dda95d14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "593a22bc-c3ef-44e1-9b5f-dae4e5ef68a2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430795304200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99ad06d-3964-41e0-88aa-f0f4dda95d14", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430795273500, "endTime": 12430795467700}, "additional": {"logType": "info", "children": [], "durationId": "5faf1f70-d2e0-458d-8f3c-8439877efdf6", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "8d761d76-c5a2-47c0-9d41-53675fd8bd35", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430796889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b308c0-d30a-4669-8b1b-c503a45627b0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430802618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f7606f9-999c-4d3e-a28a-42847269b01b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430795482300, "endTime": 12430803426200}, "additional": {"logType": "info", "children": [], "durationId": "d9f7a619-df0e-458a-ab1d-d4bd7398559b", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "9effc3d0-b6f5-4755-ba6b-ee2c30adb549", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430803481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d3882ae-f93f-4a24-a5bd-8a90c6e94329", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430809096100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af441a3-f3db-4f48-b4ec-e3509c58b5cd", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430809223600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc4e531-5d80-4929-a4de-03ed357914e3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430809532000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c71a5688-6569-4d15-a0e7-dae58a08b429", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430816125800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "838b103b-a5eb-4e16-af7d-fba6b24cff28", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430816260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24408068-1cf9-4e65-9f76-474a3ea596f7", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430803451700, "endTime": 12430819177900}, "additional": {"logType": "info", "children": [], "durationId": "820ef771-4796-4e52-b3c4-f71df1cd0105", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "293c2f89-e9e9-4d8e-9c8c-406a30ca5dec", "name": "Configuration phase cost:165 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430819231800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b0a374f-9122-43f5-884f-331d54d10fce", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430819201800, "endTime": 12430819321700}, "additional": {"logType": "info", "children": [], "durationId": "bcbfa3e1-ef5c-40cb-bb94-0c63d28457e7", "parent": "995c0738-6c76-49fc-9246-0e538d8af06d"}}, {"head": {"id": "995c0738-6c76-49fc-9246-0e538d8af06d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430646844700, "endTime": 12430819335200}, "additional": {"logType": "info", "children": ["973e1e77-45ef-42b5-b52d-22c5d4497340", "8af786f0-2f8b-424e-9bb1-79b9bd78a86b", "11ae62ee-fa97-4f23-85da-61c028c3c67a", "0f7a20cf-1981-4ccd-9876-c3d48208f596", "21a2179f-9af6-4561-8041-0661db876e8d", "0f7606f9-999c-4d3e-a28a-42847269b01b", "24408068-1cf9-4e65-9f76-474a3ea596f7", "7b0a374f-9122-43f5-884f-331d54d10fce", "a99ad06d-3964-41e0-88aa-f0f4dda95d14"], "durationId": "b42ac0ef-5801-4618-a3cf-42a859b7e7d7", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "41b72be9-857f-4443-9bf1-8ac5e36ab567", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430820615800, "endTime": 12430820629600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955", "logId": "0f6c7c0f-f8f4-4f50-b0ea-767853f52d9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f6c7c0f-f8f4-4f50-b0ea-767853f52d9a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430820615800, "endTime": 12430820629600}, "additional": {"logType": "info", "children": [], "durationId": "41b72be9-857f-4443-9bf1-8ac5e36ab567", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "265957d9-8d8b-4b91-8078-91d50e506510", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430819369200, "endTime": 12430820659500}, "additional": {"logType": "info", "children": [], "durationId": "9f6e60ab-3c88-431a-b1c6-3da9a524a4d4", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "94f24a28-c128-47dc-bcdb-c8fe5a9a687d", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430820667000, "endTime": 12430820688800}, "additional": {"logType": "info", "children": [], "durationId": "7d518450-3d4d-4376-9e98-fc64417cec21", "parent": "5d07deb2-c213-4f57-a87d-344c2dc987fb"}}, {"head": {"id": "5d07deb2-c213-4f57-a87d-344c2dc987fb", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430634371200, "endTime": 12430820694200}, "additional": {"logType": "info", "children": ["5f1c54da-e04f-476d-8e13-ad31366ba1fc", "995c0738-6c76-49fc-9246-0e538d8af06d", "265957d9-8d8b-4b91-8078-91d50e506510", "94f24a28-c128-47dc-bcdb-c8fe5a9a687d", "0cc6a02b-c5f7-4785-bcf0-4f8131181bf4", "a880d1a4-e788-40e6-8d6c-e68c58722f18", "0f6c7c0f-f8f4-4f50-b0ea-767853f52d9a"], "durationId": "9ebc0a4b-12b5-42b7-b875-b3be4ceb7955"}}, {"head": {"id": "fc9d9ed9-4079-436d-97b7-c332958d3d71", "name": "Configuration task cost before running: 190 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430820825300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1157e333-831b-4e5c-bfc4-281826a4875f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430825793300, "endTime": 12430834325200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a34bc28f-ff16-407e-adf4-c73fb0e85b84", "logId": "87bcf939-d9e4-4398-a3ff-c4c8e33c3e53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a34bc28f-ff16-407e-adf4-c73fb0e85b84", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430822171200}, "additional": {"logType": "detail", "children": [], "durationId": "1157e333-831b-4e5c-bfc4-281826a4875f"}}, {"head": {"id": "e85288aa-8448-4229-972b-0116c48d5fb7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430822687200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2333531-c32e-4f9a-afe2-5e5c18c5e43c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430822767700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "624979e7-a3eb-4522-b93d-3587e5ecc7fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430822818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f0eb6a-216a-4067-ad13-5bccff8e7192", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430825812200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c15363f8-e8fb-46ce-a000-bb2d6e407116", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430834065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80b3f7a-5858-4af4-8500-6616702f0c37", "name": "entry : default@PreBuild cost memory 0.30648040771484375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430834235300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87bcf939-d9e4-4398-a3ff-c4c8e33c3e53", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430825793300, "endTime": 12430834325200}, "additional": {"logType": "info", "children": [], "durationId": "1157e333-831b-4e5c-bfc4-281826a4875f"}}, {"head": {"id": "d1577cb9-c81c-49fc-9384-8e75b9762029", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430839621900, "endTime": 12430842733200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a6d5d475-4d1e-4a5b-a974-478f186a0fc8", "logId": "78b56716-1f4f-4a37-8383-86a1c8e25e98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6d5d475-4d1e-4a5b-a974-478f186a0fc8", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430838250200}, "additional": {"logType": "detail", "children": [], "durationId": "d1577cb9-c81c-49fc-9384-8e75b9762029"}}, {"head": {"id": "411ec849-8e9e-4a48-b0ee-d01feefbbac5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430838786600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee6787c-a60f-4fd0-8c81-c6a6a15e319a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430838876300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b31f1ed-188a-4b56-af2a-cf6af7e5bb69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430838929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f364561-673e-441b-bb26-21cca5489b42", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430839632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb56331-084c-4232-8eb5-27bca1aa8c8d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430842524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3caa6f8f-5aea-465f-9993-4a6290dabc8e", "name": "entry : default@MergeProfile cost memory 0.1282501220703125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430842657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78b56716-1f4f-4a37-8383-86a1c8e25e98", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430839621900, "endTime": 12430842733200}, "additional": {"logType": "info", "children": [], "durationId": "d1577cb9-c81c-49fc-9384-8e75b9762029"}}, {"head": {"id": "fa1d5897-d5b2-4a80-8ee6-71a6cab7c702", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430845979900, "endTime": 12430848203500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "741dbe3a-443d-40dd-a643-e6b49fa47ab4", "logId": "9e12a9d1-bd6a-48a5-98e8-a85106e5adfc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "741dbe3a-443d-40dd-a643-e6b49fa47ab4", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430844504100}, "additional": {"logType": "detail", "children": [], "durationId": "fa1d5897-d5b2-4a80-8ee6-71a6cab7c702"}}, {"head": {"id": "362d95a2-229d-49c9-97aa-1c92745d4573", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430845050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b01501-02cc-4896-a1b7-6db053343560", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430845133200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39bc5df4-5229-4aeb-8ac9-473a88b567ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430845183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c214908-856c-4f2b-9d62-ebfcc5004bd3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430845988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0f8b9d-eb17-4149-bab5-724725946efc", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430846822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "977dc1df-5e1c-406f-a4b3-a8ca2410b6f9", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430847990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9690b4-c8e8-47b8-bad8-5d53d187b92d", "name": "entry : default@CreateBuildProfile cost memory 0.10066986083984375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430848100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e12a9d1-bd6a-48a5-98e8-a85106e5adfc", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430845979900, "endTime": 12430848203500}, "additional": {"logType": "info", "children": [], "durationId": "fa1d5897-d5b2-4a80-8ee6-71a6cab7c702"}}, {"head": {"id": "5fe9989b-b3a9-4ec8-b36d-bddd4db160e1", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852558400, "endTime": 12430852968200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e5c48431-5478-4d4d-85ca-d69119c029aa", "logId": "8389e0a1-80e1-46a9-9d20-5622965a93ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5c48431-5478-4d4d-85ca-d69119c029aa", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430851098400}, "additional": {"logType": "detail", "children": [], "durationId": "5fe9989b-b3a9-4ec8-b36d-bddd4db160e1"}}, {"head": {"id": "e73b6b4a-89ab-4553-afd7-e450606ac804", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430851652000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "878b8827-3187-4a60-8f35-ea359021b7c6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430851759700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05dbac6f-d8bb-4a22-b777-1f745aa0af38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430851814500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3452ae12-edf1-4f53-aa55-a0cee9153c46", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852568200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96688bbf-1480-400a-85e0-e6a4e5958c8e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852676200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbbf617a-b616-4906-ad6a-9cffb81c5ef6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852729100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd6889e-7f37-460a-b587-06939af13392", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852770000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae072d6d-47bd-49d8-b9b7-29b32f80062b", "name": "entry : default@PreCheckSyscap cost memory 0.05034637451171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b56238c6-ffee-4d73-9267-c5c3d1c295e3", "name": "runTaskFromQueue task cost before running: 222 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8389e0a1-80e1-46a9-9d20-5622965a93ed", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430852558400, "endTime": 12430852968200, "totalTime": 337200}, "additional": {"logType": "info", "children": [], "durationId": "5fe9989b-b3a9-4ec8-b36d-bddd4db160e1"}}, {"head": {"id": "64956c4e-29fa-4ae9-bf3b-9262c5cde847", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430863377800, "endTime": 12430864760700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "881c7998-cd6e-4022-90e7-46adc1f8e5e3", "logId": "7bd7cf8b-60ec-4560-b4ba-33678ad2c7c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "881c7998-cd6e-4022-90e7-46adc1f8e5e3", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430854446300}, "additional": {"logType": "detail", "children": [], "durationId": "64956c4e-29fa-4ae9-bf3b-9262c5cde847"}}, {"head": {"id": "d73efc5e-964d-4cab-9d69-b4f37256def8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430854929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e2bb8f7-3a86-4f71-87a9-7aeb4c28250f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430855010700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e665c39-a35c-4edf-bdac-173ea7c5615a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430855059100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53ce2f84-5b1d-479d-b188-6f8fd0d69899", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430863398300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d105f7c6-9f86-4b36-baa2-6480e7284b8e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430863898500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "848e48da-55f6-48e3-992b-94e66fdac033", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430864560800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79de1768-18dc-4dce-86f9-49b4ef2b886e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06966400146484375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430864680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd7cf8b-60ec-4560-b4ba-33678ad2c7c5", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430863377800, "endTime": 12430864760700}, "additional": {"logType": "info", "children": [], "durationId": "64956c4e-29fa-4ae9-bf3b-9262c5cde847"}}, {"head": {"id": "7bc47742-e0d6-4a15-9bd6-75bb66e2102a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430868534600, "endTime": 12430869577300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "760a0d99-6039-447a-97a6-6a5edadaecd9", "logId": "293aeff2-fff8-4565-a3ad-0c841ddd0dd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "760a0d99-6039-447a-97a6-6a5edadaecd9", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430866725700}, "additional": {"logType": "detail", "children": [], "durationId": "7bc47742-e0d6-4a15-9bd6-75bb66e2102a"}}, {"head": {"id": "bb9d2201-5072-48cd-8a12-228f1ad83cfa", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430867254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eab80cd5-7fe5-440b-94fe-c6fb5a8b135b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430867369200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f79c87-08af-49e1-9997-c6e6786d2969", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430867422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dfe6486-f42b-403f-8717-7ebf67b9d8d6", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430868543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bbc588e-59a2-40ee-8ae6-599640d5c51b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430869426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d945aa08-7209-49e6-bcef-64f7a2299d96", "name": "entry : default@ProcessProfile cost memory 0.05651092529296875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430869514500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293aeff2-fff8-4565-a3ad-0c841ddd0dd0", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430868534600, "endTime": 12430869577300}, "additional": {"logType": "info", "children": [], "durationId": "7bc47742-e0d6-4a15-9bd6-75bb66e2102a"}}, {"head": {"id": "7efd6614-270e-46f1-8532-1cd3f749a006", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430874605900, "endTime": 12430880998500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ea5e6c10-047e-45e3-908f-07b020257935", "logId": "70903034-b1b0-481f-8e69-565dd0fdeef1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea5e6c10-047e-45e3-908f-07b020257935", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430871244700}, "additional": {"logType": "detail", "children": [], "durationId": "7efd6614-270e-46f1-8532-1cd3f749a006"}}, {"head": {"id": "7d01fbcd-e5a1-4de7-996b-f95313299564", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430872156600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ef32969-fd3c-45ff-bb68-a5cc12f4ff0f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430872374200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e27e594-545e-4b8c-8339-9c416d476b4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430872481400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed36330f-b03d-4c25-8983-a80c02272d45", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430874619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd0ec5f-4b49-45a0-9e8e-c7494172813a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430880788900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f865cf-b5a0-4616-98d7-911d260f33ee", "name": "entry : default@ProcessRouterMap cost memory 0.20728302001953125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430880924700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70903034-b1b0-481f-8e69-565dd0fdeef1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430874605900, "endTime": 12430880998500}, "additional": {"logType": "info", "children": [], "durationId": "7efd6614-270e-46f1-8532-1cd3f749a006"}}, {"head": {"id": "d6f15067-b1a6-49f1-89bb-5eb7640474e3", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430890175300, "endTime": 12430893526100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d058cbe4-14e3-4331-b63e-62ae24b1f8de", "logId": "0cf16240-07c1-4249-bd02-67ac5cdd7a81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d058cbe4-14e3-4331-b63e-62ae24b1f8de", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430885155300}, "additional": {"logType": "detail", "children": [], "durationId": "d6f15067-b1a6-49f1-89bb-5eb7640474e3"}}, {"head": {"id": "37d53d3c-992c-4a9b-a64c-68b705d791ec", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430885708600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30a1a470-eea1-428e-ae28-315c12e70af1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430885795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ee0781-ead3-4457-9b98-d1c7a451fd3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430885850700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ae5b0c-a0aa-4101-858c-572cc2ded64f", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430886952200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75248a10-7136-4191-98fe-f7444232f723", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430891664700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2977296c-8542-4d71-a2a2-294e61fb4fda", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430891822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f347922-a620-4f06-a77a-d1795e8b6f98", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430891884600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d2ef59-fa5c-45ee-b29e-13a750b97b50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430891930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf7b10e-8a9d-48bd-b4c7-7c48b4ef65e4", "name": "entry : default@PreviewProcessResource cost memory 0.08931732177734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430892010000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f587e506-52a5-454f-b56c-b56bd4a8d940", "name": "runTaskFromQueue task cost before running: 263 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430893443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf16240-07c1-4249-bd02-67ac5cdd7a81", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430890175300, "endTime": 12430893526100, "totalTime": 1892000}, "additional": {"logType": "info", "children": [], "durationId": "d6f15067-b1a6-49f1-89bb-5eb7640474e3"}}, {"head": {"id": "e14e54bc-6857-46d0-a495-aa8294fe769c", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430901047100, "endTime": 12430924007300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9c213d66-b02f-4331-bc21-87cbe09882cd", "logId": "5fa438fc-fc0d-4a5e-bd00-2d5c63b475a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c213d66-b02f-4331-bc21-87cbe09882cd", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430896393100}, "additional": {"logType": "detail", "children": [], "durationId": "e14e54bc-6857-46d0-a495-aa8294fe769c"}}, {"head": {"id": "4dfde7d9-da3c-4d13-8143-60e0c4dfffce", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430896941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cb7a1bc-4314-4372-a9ed-9cd6833d44ad", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430897019200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dd83d50-9b7f-4638-9cdb-40b9984c7854", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430897069600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "734016f9-e366-49cb-8e37-00669adb0fe7", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430901062800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a03e0b-a332-4bef-9d81-234b85194bc2", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430923764500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd89a29-94ae-4525-b93d-101e6c150286", "name": "entry : default@GenerateLoaderJson cost memory 0.8340606689453125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430923919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa438fc-fc0d-4a5e-bd00-2d5c63b475a2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430901047100, "endTime": 12430924007300}, "additional": {"logType": "info", "children": [], "durationId": "e14e54bc-6857-46d0-a495-aa8294fe769c"}}, {"head": {"id": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430937268700, "endTime": 12431669754100}, "additional": {"children": ["bffa43ac-6771-4c07-ba1d-a4e101ea2956", "ed0431b0-8694-4e3f-95e1-1e6d3855eff0", "1f145124-a119-4b8c-b622-70a222ee04c3", "d5ce0c03-38ec-4e7d-9930-7c31e52a459f", "d3569421-5b1a-435a-8084-688240c5f5af"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "3bb6e869-737d-41a3-95a1-9b9827adbd3e", "logId": "6f0392c8-35a1-4ee1-bb7a-9db022157689"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bb6e869-737d-41a3-95a1-9b9827adbd3e", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430932861900}, "additional": {"logType": "detail", "children": [], "durationId": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5"}}, {"head": {"id": "4b65daa8-60ad-4397-8e31-3ec21bc85bd4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430933456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66faa521-0188-464f-9fd4-1478000a5fc0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430933558700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20fd4843-8da5-4a40-8139-fd9d20312df5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430933616200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6220453d-9025-42c0-8798-9484eb3cf187", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430934584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f8047b1-2c33-4963-8d96-535748ad1c12", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430937315300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3899d5dd-41e5-4217-9c4f-ecc046f65936", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430978157600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fced5f5-7ac6-45cf-8ed0-f0e01e82b4f2", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 41 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430978312400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bffa43ac-6771-4c07-ba1d-a4e101ea2956", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430979363100, "endTime": 12431005516200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5", "logId": "056f79c5-0e69-425e-bdf0-8b546bbe2da4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "056f79c5-0e69-425e-bdf0-8b546bbe2da4", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430979363100, "endTime": 12431005516200}, "additional": {"logType": "info", "children": [], "durationId": "bffa43ac-6771-4c07-ba1d-a4e101ea2956", "parent": "6f0392c8-35a1-4ee1-bb7a-9db022157689"}}, {"head": {"id": "55262f4c-dfa4-475c-96fe-2739b6b9cdbe", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431005799100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0431b0-8694-4e3f-95e1-1e6d3855eff0", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431006823800, "endTime": 12431139384500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5", "logId": "47a7b21f-24e0-417e-8dd5-09a5e1d684d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5614f08e-48c2-4371-babe-471cdb47039b", "name": "current process  memoryUsage: {\n  rss: 118202368,\n  heapTotal: 122454016,\n  heapUsed: 104821864,\n  external: 3092851,\n  arrayBuffers: 86718\n} os memoryUsage :12.444690704345703", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431007822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47e3657-de4c-4a5d-a76e-f6270d695cf8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431136305000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a7b21f-24e0-417e-8dd5-09a5e1d684d3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431006823800, "endTime": 12431139384500}, "additional": {"logType": "info", "children": [], "durationId": "ed0431b0-8694-4e3f-95e1-1e6d3855eff0", "parent": "6f0392c8-35a1-4ee1-bb7a-9db022157689"}}, {"head": {"id": "9fa0e95a-4672-429b-a84b-4bf4f8ea81da", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431139568600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f145124-a119-4b8c-b622-70a222ee04c3", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431140972700, "endTime": 12431312857100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5", "logId": "6d0fba94-2ac3-44a0-b24a-d385d5d23ba9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd6c41e5-f70a-4e31-8739-0e4b10c14b5f", "name": "current process  memoryUsage: {\n  rss: 118243328,\n  heapTotal: 122454016,\n  heapUsed: 105114232,\n  external: 3092977,\n  arrayBuffers: 86859\n} os memoryUsage :12.44921875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431142036400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c493b019-9d0d-499b-a206-d17aeef14349", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431310310900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d0fba94-2ac3-44a0-b24a-d385d5d23ba9", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431140972700, "endTime": 12431312857100}, "additional": {"logType": "info", "children": [], "durationId": "1f145124-a119-4b8c-b622-70a222ee04c3", "parent": "6f0392c8-35a1-4ee1-bb7a-9db022157689"}}, {"head": {"id": "ad31d709-676c-4767-957b-2b7495c1b0ff", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431313034800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5ce0c03-38ec-4e7d-9930-7c31e52a459f", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431314616700, "endTime": 12431487261900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5", "logId": "3d96ae3c-a5e0-4599-b239-28a3e348aab3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b83ad96-9309-483b-945e-2a40bddfee06", "name": "current process  memoryUsage: {\n  rss: 118247424,\n  heapTotal: 122454016,\n  heapUsed: 105394400,\n  external: 3101295,\n  arrayBuffers: 95241\n} os memoryUsage :12.459827423095703", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431315819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69955e85-71f4-4eac-a8e9-953e4d4e1fb4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431483661300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d96ae3c-a5e0-4599-b239-28a3e348aab3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431314616700, "endTime": 12431487261900}, "additional": {"logType": "info", "children": [], "durationId": "d5ce0c03-38ec-4e7d-9930-7c31e52a459f", "parent": "6f0392c8-35a1-4ee1-bb7a-9db022157689"}}, {"head": {"id": "bbbdb8d4-1458-4fae-a5ce-8710a023c637", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431488013000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3569421-5b1a-435a-8084-688240c5f5af", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431489468300, "endTime": 12431668493000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5", "logId": "11b9d27d-8cd3-4e86-a4cc-621b4f3ef495"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1baa5198-0ce8-459f-9890-78747885b189", "name": "current process  memoryUsage: {\n  rss: 118374400,\n  heapTotal: 122454016,\n  heapUsed: 105719840,\n  external: 3101421,\n  arrayBuffers: 96286\n} os memoryUsage :12.460819244384766", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431491660900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd4a2d92-7a99-45d9-8c8e-d589f836d191", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431664380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b9d27d-8cd3-4e86-a4cc-621b4f3ef495", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431489468300, "endTime": 12431668493000}, "additional": {"logType": "info", "children": [], "durationId": "d3569421-5b1a-435a-8084-688240c5f5af", "parent": "6f0392c8-35a1-4ee1-bb7a-9db022157689"}}, {"head": {"id": "2d4eb26b-95ec-4aea-85d8-449b072389ea", "name": "entry : default@PreviewCompileResource cost memory 1.11553955078125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431669477700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "970f0cd1-8db9-4666-bc35-3d0f8d3e730c", "name": "runTaskFromQueue task cost before running: 1 s 39 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431669671200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0392c8-35a1-4ee1-bb7a-9db022157689", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430937268700, "endTime": 12431669754100, "totalTime": 732344800}, "additional": {"logType": "info", "children": ["056f79c5-0e69-425e-bdf0-8b546bbe2da4", "47a7b21f-24e0-417e-8dd5-09a5e1d684d3", "6d0fba94-2ac3-44a0-b24a-d385d5d23ba9", "3d96ae3c-a5e0-4599-b239-28a3e348aab3", "11b9d27d-8cd3-4e86-a4cc-621b4f3ef495"], "durationId": "129f21e9-375f-4f2b-aaaa-4cf68aecaee5"}}, {"head": {"id": "7a198572-3169-4742-9c41-85932e06080d", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673352800, "endTime": 12431674763400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a1a81053-774f-4b71-9c2a-947559702c11", "logId": "e7b2749b-19d8-4fd3-900e-d0d1f0e9ecdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1a81053-774f-4b71-9c2a-947559702c11", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431672312600}, "additional": {"logType": "detail", "children": [], "durationId": "7a198572-3169-4742-9c41-85932e06080d"}}, {"head": {"id": "fecc45a3-980f-4441-bb7d-398b09793fcb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431672890200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1032adb1-c396-48a1-b66f-b35e13642db6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673071200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3786b273-d272-4648-997c-4acb196b2968", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673180200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d25bb40-6ea1-4eca-b253-d3583f88ac1c", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4682fbd6-81fc-43cf-a070-3aed9b5bbf48", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673540900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1560d89e-bda8-4ddc-8fcf-66b6d1b3580c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673628900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc16bed6-536e-43e8-a3cf-7d6f07fe747c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8529ec2-45b3-45db-9e54-9850ed1dff69", "name": "entry : default@PreviewHookCompileResource cost memory 0.05344390869140625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673845600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf2b1c41-6afa-444e-a93e-5d94b1e8262d", "name": "runTaskFromQueue task cost before running: 1 s 43 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431674002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b2749b-19d8-4fd3-900e-d0d1f0e9ecdc", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431673352800, "endTime": 12431674763400, "totalTime": 597700}, "additional": {"logType": "info", "children": [], "durationId": "7a198572-3169-4742-9c41-85932e06080d"}}, {"head": {"id": "b5f8fa12-1463-4760-a315-4ba5068edc53", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431678464500, "endTime": 12431684967800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "b9f6d30e-7697-4b07-9992-5271e85ab479", "logId": "0b8bb94d-bb8e-4ad8-9a1b-b0300a3319d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9f6d30e-7697-4b07-9992-5271e85ab479", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431676963400}, "additional": {"logType": "detail", "children": [], "durationId": "b5f8fa12-1463-4760-a315-4ba5068edc53"}}, {"head": {"id": "05f56bfa-f706-4fdf-aa45-5966f9ddaf31", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431677627600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752bdcb9-6b38-49ba-9dbf-6c481e397eb5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431677741100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "848d0850-95d4-4597-83da-45682156bf28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431677809600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44a2a6c9-352b-4271-9ee8-e1ff06565a19", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431678475400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8d5d91-f603-4c32-b5c0-0acee224918a", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431679726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14434a0-7551-4632-a15c-bffda99597f8", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431679838400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "658c0cc4-7612-45d7-bc4a-f4f86320fde0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431679915900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d696fd2a-a79a-4efd-9813-4a8f0ffef53d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431679963700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a8a1872-d0e5-445c-b8fb-a3c63337737e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431680021500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0256a43-5834-4a4b-aecc-a3350f9a08e5", "name": "entry : default@CopyPreviewProfile cost memory 0.22603607177734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431684737000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e187c37-bc7f-4192-a907-86878c8e98fc", "name": "runTaskFromQueue task cost before running: 1 s 54 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431684900300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8bb94d-bb8e-4ad8-9a1b-b0300a3319d3", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431678464500, "endTime": 12431684967800, "totalTime": 6404900}, "additional": {"logType": "info", "children": [], "durationId": "b5f8fa12-1463-4760-a315-4ba5068edc53"}}, {"head": {"id": "53574d95-27dc-4e12-b9dc-f444dc135b87", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688103000, "endTime": 12431688528900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "1d94d96b-2aac-414a-bac7-60f2388e8679", "logId": "80c5cf8f-3540-473e-bcb2-f72dd0727e4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d94d96b-2aac-414a-bac7-60f2388e8679", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431686737600}, "additional": {"logType": "detail", "children": [], "durationId": "53574d95-27dc-4e12-b9dc-f444dc135b87"}}, {"head": {"id": "4b79f642-9ba1-49d7-8363-338310b508c5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431687275200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6eabce8-4522-4a9a-aff3-ce3e3d07c80e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431687362400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d76bee4-8c7e-4092-a6ef-d380f5739c51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431687415700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c7c49f-cd68-428e-b470-ec91d4161f59", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688111800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6564489-9ca0-48ae-b635-fc8d068e361b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688217000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258797d1-6c81-4bc2-9fb3-54e307ccca37", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1588eba-dee9-4457-8572-7fb790126606", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688312300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976708ae-86e3-42a6-8c33-8c30422598b4", "name": "entry : default@ReplacePreviewerPage cost memory 0.05141448974609375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59cab81c-f467-4113-b73e-0c7085ae9b6d", "name": "runTaskFromQueue task cost before running: 1 s 58 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80c5cf8f-3540-473e-bcb2-f72dd0727e4e", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431688103000, "endTime": 12431688528900, "totalTime": 347500}, "additional": {"logType": "info", "children": [], "durationId": "53574d95-27dc-4e12-b9dc-f444dc135b87"}}, {"head": {"id": "3c4b7f91-e420-40a6-93ed-36a614389b74", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431689959100, "endTime": 12431690222600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2b3cdf43-e97e-4029-bce6-831873025c0d", "logId": "ecd56eab-2083-4d58-90c3-146a81b8fd07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b3cdf43-e97e-4029-bce6-831873025c0d", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431689899500}, "additional": {"logType": "detail", "children": [], "durationId": "3c4b7f91-e420-40a6-93ed-36a614389b74"}}, {"head": {"id": "c265cd6e-a546-49e0-9669-78cb302a5cda", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431689966800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb8a0328-06c2-4b6a-9a1b-ac9409fee616", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431690089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55af57a7-6bab-47bf-ae93-dee5639e4fa9", "name": "runTaskFromQueue task cost before running: 1 s 59 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431690169000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd56eab-2083-4d58-90c3-146a81b8fd07", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431689959100, "endTime": 12431690222600, "totalTime": 187600}, "additional": {"logType": "info", "children": [], "durationId": "3c4b7f91-e420-40a6-93ed-36a614389b74"}}, {"head": {"id": "2090fb66-e6a0-439c-94b8-59387351f1d1", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431693441600, "endTime": 12431696390900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "9a68d1c2-0dcc-459a-8c68-947918822628", "logId": "6ccd4664-82aa-450e-95cc-aa650a539d6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a68d1c2-0dcc-459a-8c68-947918822628", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431691910600}, "additional": {"logType": "detail", "children": [], "durationId": "2090fb66-e6a0-439c-94b8-59387351f1d1"}}, {"head": {"id": "106fa687-fed4-489d-89f8-17cc43994406", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431692552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc61d91b-6cb0-4b70-b814-8eb4580774d2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431692662700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1c9f249-4b6d-47c2-952e-ad9cb99df7f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431692714100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf37d6e0-54dd-47e0-a516-9f9f400fd760", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431693451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ece9e6-c061-4cf9-9cb8-9fd2adccf722", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431695177900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f1bbfae-211b-474e-980e-0e42e2a0e3bc", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431695273000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3fd820-556b-41a4-9665-6ab42f7e79c2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431695346200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a7fa49-ba6d-4ff5-bf97-9b8004185608", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431695394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a673ff-b1c7-48c8-b5f9-1e4ba5c9b390", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431695438300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f0a338-5045-4a84-a7c0-1b679cfff95c", "name": "entry : default@PreviewUpdateAssets cost memory 0.14532470703125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431696237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34b4e71-0dd3-43d7-b907-4a1a39f63b4f", "name": "runTaskFromQueue task cost before running: 1 s 66 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431696336200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ccd4664-82aa-450e-95cc-aa650a539d6d", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431693441600, "endTime": 12431696390900, "totalTime": 2874900}, "additional": {"logType": "info", "children": [], "durationId": "2090fb66-e6a0-439c-94b8-59387351f1d1"}}, {"head": {"id": "e206cc10-ba13-45ea-b8c2-dc8376b7c7b7", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431704725300, "endTime": 12443575559800}, "additional": {"children": ["e4fca91d-07d3-40b2-90e8-8fc33c756e11"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "f5270253-c919-4820-8346-f38d1a8da21e", "logId": "b740edf7-91ec-4f18-8298-cd069122c82d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5270253-c919-4820-8346-f38d1a8da21e", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431698297600}, "additional": {"logType": "detail", "children": [], "durationId": "e206cc10-ba13-45ea-b8c2-dc8376b7c7b7"}}, {"head": {"id": "eb6dae0a-f03c-43bb-82b9-95d7beab71a1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431698783400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fdcc63d-ef96-40ad-9f60-9f9e57556930", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431698870200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3050abc2-b002-4fde-8ad8-ec763632553e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431698919200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c887d36-d640-45e4-8b61-2b26f083a22a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431704737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9b7411-324a-4a7a-abba-a3bbfcb1d7b5", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431735763900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee04ec76-fe58-4904-be47-f32e71072016", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431735930500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12431751962600, "endTime": 12443572797200}, "additional": {"children": ["1418817e-8e6e-4546-9c40-1d04fb18a87a", "6587b643-a291-4d42-934b-fc6de6180f30", "ed795794-95b4-4d4d-b63f-f92cd9e3154d", "daef08a4-7c06-4e16-bb90-d9736e5e7d22", "ee730d16-f1df-4a9b-b205-9fb7e5b33eb8", "5944b5a9-c044-4055-af63-c81c38cd4f89", "106f39e6-20e1-4d76-b4a7-3253799cce4a", "ed413b0a-5a9f-408f-b0c9-d88b84be50ca", "620b9518-c57b-43f3-939c-487b73fab6d4", "0f56c790-de34-40bb-9bbd-e8f1ef1492e5", "ad36df50-dae4-4500-b897-5f7bb22dfcbd", "d77eb15a-7229-47f9-bd0d-d70f82370fdb", "c910692a-8542-411f-aade-3b23f0a4878c", "b2155e31-fde2-41a0-9a10-26d63c6fc50f", "9f840de4-a9ff-44d6-81a5-d7feb7ac0085", "ed587db6-42ff-47bd-846a-6292df282105"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e206cc10-ba13-45ea-b8c2-dc8376b7c7b7", "logId": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae1b4459-4e73-4624-969a-e120e9f0db5d", "name": "entry : default@PreviewArkTS cost memory 0.30844879150390625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431753982000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8ce94f-2430-4e31-ab6a-5815bf2e7c9b", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12435566975400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1418817e-8e6e-4546-9c40-1d04fb18a87a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12435568018500, "endTime": 12435568036100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "2b2d8330-66c6-4da5-b5de-1291042ab603"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b2d8330-66c6-4da5-b5de-1291042ab603", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12435568018500, "endTime": 12435568036100}, "additional": {"logType": "info", "children": [], "durationId": "1418817e-8e6e-4546-9c40-1d04fb18a87a", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "730cb5b8-8ab2-47cc-a7c4-ef9437075965", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440161916000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6587b643-a291-4d42-934b-fc6de6180f30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440163098200, "endTime": 12440163128500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "a545ba9a-d475-4bf7-828e-6c87170c8dfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a545ba9a-d475-4bf7-828e-6c87170c8dfb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440163098200, "endTime": 12440163128500}, "additional": {"logType": "info", "children": [], "durationId": "6587b643-a291-4d42-934b-fc6de6180f30", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "e8100fa9-c9c1-4ee7-ac45-f30ec3a1c32b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440163223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed795794-95b4-4d4d-b63f-f92cd9e3154d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440164165100, "endTime": 12440164183200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "243db067-11cd-47e9-aafe-1ed6fe8af568"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "243db067-11cd-47e9-aafe-1ed6fe8af568", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440164165100, "endTime": 12440164183200}, "additional": {"logType": "info", "children": [], "durationId": "ed795794-95b4-4d4d-b63f-f92cd9e3154d", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "5965fc0f-8f3f-4843-be97-8d26abc564ca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440164262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daef08a4-7c06-4e16-bb90-d9736e5e7d22", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440165032700, "endTime": 12440165048500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "1f9ff4e7-02d3-4efe-a1dd-119e919bcebe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f9ff4e7-02d3-4efe-a1dd-119e919bcebe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440165032700, "endTime": 12440165048500}, "additional": {"logType": "info", "children": [], "durationId": "daef08a4-7c06-4e16-bb90-d9736e5e7d22", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "6b6ff615-8711-4763-8ac3-045a4b135315", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440441482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee730d16-f1df-4a9b-b205-9fb7e5b33eb8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440442646800, "endTime": 12440442670300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "5dbcf3e9-290d-4e32-873f-1fd23b7b4c31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dbcf3e9-290d-4e32-873f-1fd23b7b4c31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440442646800, "endTime": 12440442670300}, "additional": {"logType": "info", "children": [], "durationId": "ee730d16-f1df-4a9b-b205-9fb7e5b33eb8", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "2b80d4f5-f1de-499e-8fb0-194fbbd195cd", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440696151700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5944b5a9-c044-4055-af63-c81c38cd4f89", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440697263400, "endTime": 12440697285700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "da8857f2-5f1e-474c-a6a0-4892e51d9d0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da8857f2-5f1e-474c-a6a0-4892e51d9d0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440697263400, "endTime": 12440697285700}, "additional": {"logType": "info", "children": [], "durationId": "5944b5a9-c044-4055-af63-c81c38cd4f89", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "95e8f684-83fb-480c-aa8a-17faf3d0dae3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440812699200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106f39e6-20e1-4d76-b4a7-3253799cce4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440814041300, "endTime": 12440814065900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "f828c62c-40a8-4abb-810c-9047c2d209ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f828c62c-40a8-4abb-810c-9047c2d209ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440814041300, "endTime": 12440814065900}, "additional": {"logType": "info", "children": [], "durationId": "106f39e6-20e1-4d76-b4a7-3253799cce4a", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "e3cfe5f2-45b8-41f3-8aed-61dd4f35680b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440904334500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed413b0a-5a9f-408f-b0c9-d88b84be50ca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12440905486100, "endTime": 12440905510400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "d9a7314e-5327-4bca-88f9-836736882e62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9a7314e-5327-4bca-88f9-836736882e62", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12440905486100, "endTime": 12440905510400}, "additional": {"logType": "info", "children": [], "durationId": "ed413b0a-5a9f-408f-b0c9-d88b84be50ca", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "c5a186d0-8a90-442d-b20f-30802c23fe4b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12441064537700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620b9518-c57b-43f3-939c-487b73fab6d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12441066002800, "endTime": 12441066026000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "97fb91f9-78d1-4801-a1e4-25f69a6e9d7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97fb91f9-78d1-4801-a1e4-25f69a6e9d7e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12441066002800, "endTime": 12441066026000}, "additional": {"logType": "info", "children": [], "durationId": "620b9518-c57b-43f3-939c-487b73fab6d4", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "413c46c6-7746-4870-ba01-6bfe269039c9", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12441221712000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f56c790-de34-40bb-9bbd-e8f1ef1492e5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12441223674500, "endTime": 12441223712200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "5f48a8f2-f263-4682-8168-7dc24d7dd245"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f48a8f2-f263-4682-8168-7dc24d7dd245", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12441223674500, "endTime": 12441223712200}, "additional": {"logType": "info", "children": [], "durationId": "0f56c790-de34-40bb-9bbd-e8f1ef1492e5", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "b41ce22e-fb51-4b2c-baed-d9d101256034", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12441336207800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad36df50-dae4-4500-b897-5f7bb22dfcbd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12441338524000, "endTime": 12441338566800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "78fb8022-7dba-406d-b134-55ff2e443ff4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78fb8022-7dba-406d-b134-55ff2e443ff4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12441338524000, "endTime": 12441338566800}, "additional": {"logType": "info", "children": [], "durationId": "ad36df50-dae4-4500-b897-5f7bb22dfcbd", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "b508183c-373a-4110-bdf4-1f0c8717be76", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443571495600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d77eb15a-7229-47f9-bd0d-d70f82370fdb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12443572628200, "endTime": 12443572651600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "9f0fbf7f-c3ff-4c1d-9b92-3977a7633419"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f0fbf7f-c3ff-4c1d-9b92-3977a7633419", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443572628200, "endTime": 12443572651600}, "additional": {"logType": "info", "children": [], "durationId": "d77eb15a-7229-47f9-bd0d-d70f82370fdb", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "e4fe0223-bd57-4c2b-b480-33d30e4bac35", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12431751962600, "endTime": 12443572797200}, "additional": {"logType": "info", "children": ["2b2d8330-66c6-4da5-b5de-1291042ab603", "a545ba9a-d475-4bf7-828e-6c87170c8dfb", "243db067-11cd-47e9-aafe-1ed6fe8af568", "1f9ff4e7-02d3-4efe-a1dd-119e919bcebe", "5dbcf3e9-290d-4e32-873f-1fd23b7b4c31", "da8857f2-5f1e-474c-a6a0-4892e51d9d0e", "f828c62c-40a8-4abb-810c-9047c2d209ee", "d9a7314e-5327-4bca-88f9-836736882e62", "97fb91f9-78d1-4801-a1e4-25f69a6e9d7e", "5f48a8f2-f263-4682-8168-7dc24d7dd245", "78fb8022-7dba-406d-b134-55ff2e443ff4", "9f0fbf7f-c3ff-4c1d-9b92-3977a7633419", "68927e58-7345-439d-afd5-b2963aa847d5", "862d270c-eb2f-4384-8417-0e613a0416bf", "6479e705-20c8-4d0a-9b06-c1e6e1adca60", "c30813fd-65b4-4ee7-93db-c65b82c89315"], "durationId": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "parent": "b740edf7-91ec-4f18-8298-cd069122c82d"}}, {"head": {"id": "c910692a-8542-411f-aade-3b23f0a4878c", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12434379049700, "endTime": 12435534285200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "68927e58-7345-439d-afd5-b2963aa847d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68927e58-7345-439d-afd5-b2963aa847d5", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12434379049700, "endTime": 12435534285200}, "additional": {"logType": "info", "children": [], "durationId": "c910692a-8542-411f-aade-3b23f0a4878c", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "b2155e31-fde2-41a0-9a10-26d63c6fc50f", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12435534481300, "endTime": 12435542512700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "862d270c-eb2f-4384-8417-0e613a0416bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "862d270c-eb2f-4384-8417-0e613a0416bf", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12435534481300, "endTime": 12435542512700}, "additional": {"logType": "info", "children": [], "durationId": "b2155e31-fde2-41a0-9a10-26d63c6fc50f", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "9f840de4-a9ff-44d6-81a5-d7feb7ac0085", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12435542609200, "endTime": 12435542669300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "6479e705-20c8-4d0a-9b06-c1e6e1adca60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6479e705-20c8-4d0a-9b06-c1e6e1adca60", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12435542609200, "endTime": 12435542669300}, "additional": {"logType": "info", "children": [], "durationId": "9f840de4-a9ff-44d6-81a5-d7feb7ac0085", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "ed587db6-42ff-47bd-846a-6292df282105", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker5", "startTime": 12435542727700, "endTime": 12443571584700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4fca91d-07d3-40b2-90e8-8fc33c756e11", "logId": "c30813fd-65b4-4ee7-93db-c65b82c89315"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c30813fd-65b4-4ee7-93db-c65b82c89315", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12435542727700, "endTime": 12443571584700}, "additional": {"logType": "info", "children": [], "durationId": "ed587db6-42ff-47bd-846a-6292df282105", "parent": "e4fe0223-bd57-4c2b-b480-33d30e4bac35"}}, {"head": {"id": "b740edf7-91ec-4f18-8298-cd069122c82d", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12431704725300, "endTime": 12443575559800, "totalTime": 11870817000}, "additional": {"logType": "info", "children": ["e4fe0223-bd57-4c2b-b480-33d30e4bac35"], "durationId": "e206cc10-ba13-45ea-b8c2-dc8376b7c7b7"}}, {"head": {"id": "3af924b9-8035-4aba-ac48-5ddf4c756cb1", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443579934800, "endTime": 12443580200300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8d48e3b9-7c74-4545-8e55-24232fea21d6", "logId": "97045a1e-feba-4907-b29d-95b113735ff1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d48e3b9-7c74-4545-8e55-24232fea21d6", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443579885400}, "additional": {"logType": "detail", "children": [], "durationId": "3af924b9-8035-4aba-ac48-5ddf4c756cb1"}}, {"head": {"id": "e90d4aa1-b5e7-41d5-8f94-c6ea131e3f22", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443579945400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1656cb9a-4f4e-406b-8a75-b01cc013a0a1", "name": "entry : PreviewBuild cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443580058500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee647fc5-f77e-4ded-bed3-adeafb812ce4", "name": "runTaskFromQueue task cost before running: 12 s 949 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443580142800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97045a1e-feba-4907-b29d-95b113735ff1", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443579934800, "endTime": 12443580200300, "totalTime": 185000}, "additional": {"logType": "info", "children": [], "durationId": "3af924b9-8035-4aba-ac48-5ddf4c756cb1"}}, {"head": {"id": "c8f6d8c3-eb28-4246-b974-0c4a451d1536", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443590547700, "endTime": 12443590568400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80e52d88-b11d-420d-a089-36436d947b3f", "logId": "4a147ec2-1183-4f58-9c85-24be8f0b2aff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a147ec2-1183-4f58-9c85-24be8f0b2aff", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443590547700, "endTime": 12443590568400}, "additional": {"logType": "info", "children": [], "durationId": "c8f6d8c3-eb28-4246-b974-0c4a451d1536"}}, {"head": {"id": "21d49b27-2ac5-4ea8-a9fa-fbb2dae341c9", "name": "BUILD SUCCESSFUL in 12 s 960 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443590616400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d3d97c57-844f-48b4-99d1-2f8fa2a6a1f2", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12430631225900, "endTime": 12443590875500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 6}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "b8933e5f-750d-4eab-86c4-cf7925d3c452", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443590902600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137384f8-93a4-435e-99a7-37cfdad350a8", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443590964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a705654e-6064-4c73-b8bb-bd12702f9f81", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443591019300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb848ed-ee6b-46bb-882a-9128c32f15a0", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443591062800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1c4ef8-bc5c-49cd-b8c6-bc4f24c89ca0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443591104200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbccb8fc-70ff-41ae-806b-75733277083c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443591139700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "428c6b00-9dba-45d8-9e95-8294f0b848b6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443591174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364cd56a-b77a-468a-9428-d0004dab5532", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443591850900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65b8e3ab-b81b-4515-9d33-113ec8243f5b", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443605923700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b5bd853-8b38-4df8-a6c9-bcdbb968befd", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443609870800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a81ef0f4-3a77-4974-a883-e22e7b50b183", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443610325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a1d9bb-9aa6-43cb-a383-186c2b3974bc", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443630746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53671408-55e0-4f40-8cd9-1d44b7fd2807", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:41 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443631705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9478ecc4-529a-4564-b29a-4b47c47399df", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443631948600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6972bce3-88c1-47ff-be53-fdb4e4d757ae", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443632775700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f18fe9-df81-446d-95cc-eb268215d526", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443633631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ffb886-1c67-4cb4-a79f-3c903268d618", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443634030400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78fae6a3-f28a-4274-8f05-3ce40e892863", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443634333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c9321c-75c8-4cb2-88b1-215703a2aa90", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443635166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec1a740-c4e1-4e60-ac7e-86880b01ee12", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443638303000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "182b22a7-37bf-434e-8312-5c4e4ca05aa6", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443639116200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44def803-dddf-411e-9170-fba5c9b44f6a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443639421600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72fef009-e449-4cd3-a0f8-fd87a5f0b565", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443653581400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d15fa2-3368-424a-aa73-3c6c52397241", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443654975700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75299a6-c091-4a7e-80b7-892dd4deedc3", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443655083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf4cc74-7989-43c7-81e4-0ec04a7b2640", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443655379800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aec80cb-de84-4133-9706-9e90bc6f4524", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443656109000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "221fc8ce-7f56-4322-99f2-2aba245772f4", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443659710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93ee6cb-e5f3-4a3c-9bc6-f94d1bb02144", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443659982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b7ac38-893f-49ce-8690-8bc4a3c65fac", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443660279900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ca214d-2306-40ba-95e1-1a105095d1d3", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443660578400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08bbea0a-42a2-4d8f-8690-1e7155bf32a7", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12443660866000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}