package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icss.wallet.entity.Transaction;
import com.icss.wallet.result.R;
import com.icss.wallet.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
@CrossOrigin
@RestController
@RequestMapping("/transactions")
public class TransactionController {
    @Autowired
    private TransactionService transactionService;

    @GetMapping
    public R search(
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) Integer type,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        IPage<Transaction> result = transactionService.search(phone, type, pageNum, pageSize);
        return R.success("查询成功", result);
    }

    /**
     * Vue前端专用：分页查询交易记录（别名接口）
     */
    @GetMapping("/page")
    public R searchPage(
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String transNo,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        IPage<Transaction> result = transactionService.searchPage(phone, type, status, transNo, startDate, endDate, pageNum, pageSize);
        return R.success("查询成功", result);
    }

    @GetMapping("/user/{userId}")
    public R getByUserId(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        IPage<Transaction> result = transactionService.getByUserId(userId, pageNum, pageSize);
        return R.success("查询成功", result);
    }

    /**
     * HarmonyOS前端专用：根据用户ID和交易类型查询交易记录
     */
    @GetMapping("/user/{userId}/withType")
    public R getByUserIdWithType(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) Integer type) {
        try {
            IPage<Transaction> result = transactionService.getByUserIdWithType(userId, pageNum, pageSize, type);
            return R.success("查询成功", result);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * HarmonyOS前端专用：获取用户月度统计数据
     */
    @GetMapping("/stats/monthly/{userId}")
    public R getMonthlyStats(
            @PathVariable Long userId,
            @RequestParam int year,
            @RequestParam int month,
            @RequestParam(required = false) Integer type) {
        try {
            java.util.Map<String, Object> stats = transactionService.getMonthlyStats(userId, year, month, type);
            return R.success("获取统计数据成功", stats);
        } catch (Exception e) {
            return R.failure("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * Vue前端专用：获取交易统计数据
     */
    @GetMapping("/statistics")
    public R getStatistics() {
        try {
            java.util.Map<String, Object> stats = transactionService.getTransactionStatistics();
            return R.success("获取统计数据成功", stats);
        } catch (Exception e) {
            return R.failure("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * Vue前端专用：获取总交易金额
     */
    @GetMapping("/totalAmount")
    public R getTotalAmount() {
        try {
            java.math.BigDecimal totalAmount = transactionService.getTotalAmount();
            return R.success("获取总交易金额成功", totalAmount);
        } catch (Exception e) {
            return R.failure("获取总交易金额失败: " + e.getMessage());
        }
    }

    /**
     * Vue前端专用：按类型统计交易数量
     */
    @GetMapping("/count")
    public R getTransactionCount(@RequestParam(required = false) Integer type) {
        try {
            Long count = transactionService.getTransactionCount(type);
            return R.success("获取交易数量成功", count);
        } catch (Exception e) {
            return R.failure("获取交易数量失败: " + e.getMessage());
        }
    }
}