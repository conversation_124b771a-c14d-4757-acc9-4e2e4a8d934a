package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icss.wallet.entity.Payment;
import com.icss.wallet.result.R;
import com.icss.wallet.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@CrossOrigin(origins = "*", allowedHeaders = "*")
@RestController
@RequestMapping("/payment")
public class PaymentController {
    
    @Autowired
    private PaymentService paymentService;
    
    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public R<Payment> createPayment(@RequestParam Long userId,
                                   @RequestParam BigDecimal amount,
                                   @RequestParam String merchantName,
                                   @RequestParam String orderNo,
                                   @RequestParam(required = false) String description) {
        try {
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return R.failure("支付金额必须大于0");
            }
            
            Payment payment = paymentService.createPayment(userId, amount, merchantName, orderNo, description);
            return R.success("支付订单创建成功", payment);
        } catch (Exception e) {
            return R.failure("创建支付订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 钱包支付
     */
    @PostMapping("/wallet")
    public R payWithWallet(@RequestParam String paymentNo,
                          @RequestParam String payPassword) {
        try {
            boolean success = paymentService.payWithWallet(paymentNo, payPassword);
            if (success) {
                return R.success("钱包支付成功");
            } else {
                return R.failure("钱包支付失败，请检查余额或支付密码");
            }
        } catch (Exception e) {
            return R.failure("钱包支付失败: " + e.getMessage());
        }
    }
    
    /**
     * 银行卡支付
     */
    @PostMapping("/bankcard")
    public R payWithBankCard(@RequestParam String paymentNo,
                            @RequestParam Long bankCardId,
                            @RequestParam String payPassword) {
        try {
            boolean success = paymentService.payWithBankCard(paymentNo, bankCardId, payPassword);
            if (success) {
                return R.success("银行卡支付成功");
            } else {
                return R.failure("银行卡支付失败，请检查银行卡状态或支付密码");
            }
        } catch (Exception e) {
            return R.failure("银行卡支付失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支付记录
     */
    @GetMapping("/history/{userId}")
    public R<List<Payment>> getPaymentHistory(@PathVariable Long userId) {
        try {
            List<Payment> payments = paymentService.getPaymentHistory(userId);
            return R.success("获取支付记录成功", payments);
        } catch (Exception e) {
            return R.failure("获取支付记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支付详情
     */
    @GetMapping("/detail/{paymentNo}")
    public R<Payment> getPaymentDetail(@PathVariable String paymentNo) {
        try {
            Payment payment = paymentService.getPaymentByNo(paymentNo);
            if (payment == null) {
                return R.failure("支付订单不存在");
            }
            return R.success("获取支付详情成功", payment);
        } catch (Exception e) {
            return R.failure("获取支付详情失败: " + e.getMessage());
        }
    }

    // ==================== 管理员专用API ====================

    /**
     * 管理员分页查询支付记录（包含用户信息）
     */
    @GetMapping("/admin/page")
    public R getPaymentsWithUserInfo(@RequestParam(defaultValue = "1") int pageNum,
                                    @RequestParam(defaultValue = "10") int pageSize,
                                    @RequestParam(required = false) String paymentNo,
                                    @RequestParam(required = false) String orderNo,
                                    @RequestParam(required = false) Integer status) {
        try {
            IPage<Payment> page = paymentService.getPaymentsWithUserInfo(pageNum, pageSize, paymentNo, orderNo, status);
            return R.success("查询成功", page);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 管理员获取支付统计信息
     */
    @GetMapping("/admin/statistics")
    public R getPaymentStatistics() {
        try {
            java.util.Map<String, Object> statistics = paymentService.getPaymentStatistics();
            return R.success("获取统计信息成功", statistics);
        } catch (Exception e) {
            return R.failure("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 管理员添加支付记录
     */
    @PostMapping("/admin/add")
    public R addPayment(@RequestBody Payment payment) {
        try {
            payment.setStatus(0); // 默认为待支付状态
            payment.setCreateTime(new Date());
            payment.setUpdateTime(new Date());
            boolean result = paymentService.save(payment);
            if (result) {
                return R.success("添加支付记录成功");
            } else {
                return R.failure("添加支付记录失败");
            }
        } catch (Exception e) {
            return R.failure("添加支付记录失败: " + e.getMessage());
        }
    }

    /**
     * 管理员修改支付记录
     */
    @PutMapping("/admin/{paymentId}")
    public R updatePayment(@PathVariable Long paymentId, @RequestBody Payment payment) {
        try {
            payment.setPaymentId(paymentId);
            boolean result = paymentService.updatePayment(payment);
            if (result) {
                return R.success("修改支付记录成功");
            } else {
                return R.failure("修改支付记录失败");
            }
        } catch (Exception e) {
            return R.failure("修改支付记录失败: " + e.getMessage());
        }
    }

    /**
     * 管理员删除支付记录
     */
    @DeleteMapping("/admin/{paymentId}")
    public R deletePayment(@PathVariable Long paymentId) {
        try {
            boolean result = paymentService.deletePayment(paymentId);
            if (result) {
                return R.success("删除支付记录成功");
            } else {
                return R.failure("删除支付记录失败");
            }
        } catch (Exception e) {
            return R.failure("删除支付记录失败: " + e.getMessage());
        }
    }

    /**
     * 管理员处理支付状态
     */
    @PostMapping("/admin/process")
    public R processPayment(@RequestBody java.util.Map<String, Object> request) {
        try {
            Long paymentId = Long.valueOf(request.get("paymentId").toString());
            Integer status = Integer.valueOf(request.get("status").toString());

            boolean result = paymentService.processPayment(paymentId, status);
            if (result) {
                return R.success("处理支付状态成功");
            } else {
                return R.failure("处理支付状态失败");
            }
        } catch (Exception e) {
            return R.failure("处理支付状态失败: " + e.getMessage());
        }
    }

}
