<script setup>
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Message, CircleCheck, Timer, InfoFilled, Wallet, User, Lock, Phone, Key } from '@element-plus/icons-vue';
import axios from "axios";
import { setUserLogin, getDefaultPath, USER_TYPES } from "../utils/auth.js";

const router = useRouter();
const activeTab = ref('login'); // 默认显示登录标签页
const loginType = ref('password'); // 登录方式：password-密码登录，code-验证码登录

// 登录表单
const loginForm = reactive({
  username: "",
  password: "",
  code: "",
});

// 注册表单
const registerForm = reactive({
  username: "",
  password: "",
  confirmPassword: "",
  phone: "",
});

// 验证码相关
const codeCountdown = ref(0);
const codeSending = ref(false);
const currentCode = ref(''); // 当前生成的验证码

// 登录验证规则
const loginRules = reactive({
  username: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "验证码为6位数字", trigger: "blur" }
  ]
});

// 注册验证规则
const registerRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
  ],
  payPassword: [
    { required: true, message: "请输入支付密码", trigger: "blur" },
    { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error("两次输入密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

const loginFormRef = ref();
const registerFormRef = ref();
const loading = ref(false);

// 处理登录
const handleLogin = () => {
  if (loginType.value === 'password') {
    handlePasswordLogin();
  } else {
    handleCodeLogin();
  }
}

// 管理员密码登录
const handlePasswordLogin = () => {
  console.log('管理员密码登录:', loginForm.username, loginForm.password)
  if (loginForm.username == '' || loginForm.password == '') {
    ElMessage.error('用户名和密码不能为空')
    return
  }

  loading.value = true;

  axios.post('http://localhost:8091/admin/auth/login', null, {
    params: {
      username: loginForm.username,
      password: loginForm.password
    }
  })
  .then(function (response) {
    console.log('管理员密码登录响应:', response.data);
    if (response.data.code == 0) {
      console.log('管理员密码登录成功');
      ElMessage.success('登录成功');

      // 存储管理员登录信息
      const adminData = response.data.data;
      localStorage.setItem('adminToken', adminData.token);
      localStorage.setItem('adminInfo', JSON.stringify(adminData.adminInfo));
      localStorage.setItem('userType', 'admin');

      // 跳转
      router.push('/home').catch(err => {
        console.error('路由跳转失败:', err);
      });
    } else {
      console.error('管理员密码登录失败:', response.data);
      ElMessage.error(response.data.msg || '登录失败');
    }
  })
  .catch(function (error) {
    console.error('管理员密码登录请求错误:', error);
    if (error.response) {
      console.error('响应错误:', error.response.data);
      ElMessage.error(`登录失败: ${error.response.data.msg || error.response.status}`);
    } else if (error.request) {
      console.error('请求错误:', error.request);
      ElMessage.error('网络请求失败，请检查网络连接');
    } else {
      console.error('其他错误:', error.message);
      ElMessage.error('登录请求失败');
    }
  })
  .finally(() => {
    loading.value = false;
  });
}

// 验证码登录
const handleCodeLogin = () => {
  console.log('管理员验证码登录:', loginForm.username, loginForm.code)
  if (loginForm.username == '' || loginForm.code == '') {
    ElMessage.error('手机号和验证码不能为空')
    return
  }

  // 验证手机号格式
  if (!/^1[3-9]\d{9}$/.test(loginForm.username)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }

  loading.value = true;

  axios.post('http://localhost:8091/admin/auth/login-with-code', null, {
    params: {
      phone: loginForm.username,
      code: loginForm.code
    }
  })
  .then(function (response) {
    console.log('管理员验证码登录响应:', response.data);
    if (response.data.code == 0) {
      console.log('管理员验证码登录成功');
      ElMessage.success('登录成功');

      // 存储管理员登录信息
      const adminData = response.data.data;
      localStorage.setItem('adminToken', adminData.token);
      localStorage.setItem('adminInfo', JSON.stringify(adminData.adminInfo));
      localStorage.setItem('userType', 'admin');

      // 跳转到管理员仪表板
      router.push('/home').catch(err => {
        console.error('路由跳转失败:', err);
      });
    } else {
      console.error('管理员验证码登录失败:', response.data);
      ElMessage.error(response.data.msg || '验证码登录失败');
    }
  })
  .catch(function (error) {
    console.error('管理员验证码登录请求错误:', error);
    if (error.response) {
      console.error('响应错误:', error.response.data);
      ElMessage.error(`登录失败: ${error.response.data.msg || error.response.status}`);
    } else if (error.request) {
      console.error('请求错误:', error.request);
      ElMessage.error('网络请求失败，请检查网络连接');
    } else {
      console.error('其他错误:', error.message);
      ElMessage.error('验证码登录失败');
    }
  })
  .finally(() => {
    loading.value = false;
  })
}

// 发送验证码
const sendCode = () => {
  if (!loginForm.username) {
    ElMessage.error('请先输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.username)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }

  codeSending.value = true;

  axios.post('http://localhost:8091/admin/auth/send-code', null, {
    params: {
      phone: loginForm.username,
      type: 4 // 4表示管理员登录验证码
    }
  })
  .then(function (response) {
    console.log('验证码发送响应:', response.data);
    if (response.data.code == 0) {
      ElMessage.success('验证码发送成功');
      // 显示验证码在前端页面
      currentCode.value = response.data.data;
      console.log('收到验证码:', response.data.data);
      // 开始倒计时
      codeCountdown.value = 60;
      const timer = setInterval(() => {
        codeCountdown.value--;
        if (codeCountdown.value <= 0) {
          clearInterval(timer);
          currentCode.value = ''; // 倒计时结束后清空验证码显示
        }
      }, 1000);
    } else {
      console.error('验证码发送失败:', response.data);
      ElMessage.error(response.data.msg || '验证码发送失败');
    }
  })
  .catch(function (error) {
    console.error('验证码发送请求错误:', error);
    if (error.response) {
      console.error('响应错误:', error.response.data);
      ElMessage.error(`验证码发送失败: ${error.response.data.msg || error.response.status}`);
    } else if (error.request) {
      console.error('请求错误:', error.request);
      ElMessage.error('网络请求失败，请检查网络连接');
    } else {
      console.error('其他错误:', error.message);
      ElMessage.error('验证码发送失败');
    }
  })
  .finally(() => {
    codeSending.value = false;
  })
}

// 处理注册
const handleRegister = () => {
  console.log('注册信息', registerForm);

  // 验证表单
  registerFormRef.value.validate((valid) => {
    if (!valid) {
      ElMessage.error('请填写完整的注册信息');
      return;
    }

    if (registerForm.password !== registerForm.confirmPassword) {
      ElMessage.error('两次输入密码不一致');
      return;
    }

    const userData = {
      username: registerForm.username,  // 用户名
      password: registerForm.password,  // 密码
      phone: registerForm.phone,        // 手机号
      realName: registerForm.username,  // 真实姓名（这里用用户名代替）
      role: "admin"                     // 默认角色为管理员
    };

    axios.post('http://localhost:8091/admin/auth/register', userData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(function(response) {
      console.log('注册响应:', response.data);
      if (response.data.code === 0) {  // 修复：成功应该是0
        ElMessage.success('注册成功');
        // 切换到登录标签页
        activeTab.value = 'login';
        // 自动填充登录表单
        loginForm.username = registerForm.phone;
        loginForm.password = registerForm.password;
        // 清空注册表单
        registerForm.username = '';
        registerForm.phone = '';
        registerForm.password = '';
        registerForm.confirmPassword = '';
      } else {
        console.error('注册失败:', response.data);
        ElMessage.error(response.data.msg || '注册失败');
      }
    })
    .catch(function(error) {
      console.error('注册请求错误:', error);
      if (error.response) {
        console.error('响应错误:', error.response.data);
        ElMessage.error(error.response.data?.msg || `注册失败: ${error.response.status}`);
      } else if (error.request) {
        console.error('请求错误:', error.request);
        ElMessage.error('网络请求失败，请检查网络连接');
      } else {
        console.error('其他错误:', error.message);
        ElMessage.error('注册请求失败');
      }
    });
  });
}



</script>

<template>
  <div class="login-container">
    <div class="login-box">
      <!-- Logo和标题区域 -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <h2 class="title">E-Wallet 电子钱包支付系统</h2>
        </div>
        <p class="subtitle">安全便捷的数字支付解决方案</p>
      </div>
      
      <el-tabs v-model="activeTab" class="auth-tabs">
        <el-tab-pane label="登录" name="login">
          <!-- 登录方式切换 -->
          <div class="login-type-switch">
            <el-radio-group v-model="loginType" size="default" class="login-type-buttons">
              <el-radio-button label="password" class="login-type-btn">
                <el-icon><Lock /></el-icon>
                <span>密码登录</span>
              </el-radio-button>
              <el-radio-button label="code" class="login-type-btn">
                <el-icon><Key /></el-icon>
                <span>验证码登录</span>
              </el-radio-button>
            </el-radio-group>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="auth-form"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入手机号"
                size="large"
                class="auth-input"
              >
                <template #prefix>
                  <el-icon><Phone /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 密码登录 -->
            <el-form-item v-if="loginType === 'password'" prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                class="auth-input"
                show-password
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 验证码登录 -->
            <el-form-item v-if="loginType === 'code'" prop="code">
              <div class="code-input-group">
                <el-input
                  v-model="loginForm.code"
                  placeholder="请输入验证码"
                  maxlength="6"
                  size="large"
                  class="auth-input code-input"
                >
                  <template #prefix>
                    <el-icon><Key /></el-icon>
                  </template>
                </el-input>
                <el-button
                  @click="sendCode"
                  :disabled="codeCountdown > 0 || codeSending"
                  :loading="codeSending"
                  size="large"
                  class="send-code-btn"
                >
                  {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
                </el-button>
              </div>
              <!-- 显示验证码 -->
              <div v-if="currentCode" class="code-display">
                <div class="verification-code-card">
                  <!-- 标题区域 -->
                  <div class="code-header">
                    <el-icon class="code-icon"><Message /></el-icon>
                    <span class="code-title">短信验证码</span>
                  </div>

                  <!-- 验证码区域 -->
                  <div class="code-content">
                    <div class="code-number">{{ currentCode }}</div>
                  </div>

                  <!-- 状态信息 -->
                  <div class="code-status-info">
                    <div class="status-item">
                      <el-icon class="success-icon"><CircleCheck /></el-icon>
                      <span>验证码已发送</span>
                    </div>
                    <div class="countdown-item">
                      <el-icon class="timer-icon"><Timer /></el-icon>
                      <span>{{ codeCountdown }}秒后过期</span>
                    </div>
                  </div>

                  <!-- 提示信息 -->
                  <div class="code-tips">
                    <el-icon class="info-icon"><InfoFilled /></el-icon>
                    <span>请在有效期内输入验证码完成登录</span>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="handleLogin"
                class="auth-btn login-btn"
                size="large"
                :loading="loading"
              >
                <el-icon v-if="!loading">
                  <Lock v-if="loginType === 'password'" />
                  <Key v-else />
                </el-icon>
                <span>{{ loginType === 'password' ? '密码登录' : '验证码登录' }}</span>
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="注册" name="register">
          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            class="auth-form"
          >
            <el-form-item prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                size="large"
                class="auth-input"
              >
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="phone">
              <el-input
                v-model="registerForm.phone"
                placeholder="请输入手机号"
                size="large"
                class="auth-input"
              >
                <template #prefix>
                  <el-icon><Phone /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                class="auth-input"
                show-password
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <!-- <el-form-item prop="payPassword">
              <el-input
                v-model="registerForm.payPassword"
                type="password"
                placeholder="请输入支付密码"
                prefix-icon="el-icon-money"
                show-password
              /> 
            </el-form-item>-->
            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                size="large"
                class="auth-input"
                show-password
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="success"
                @click="handleRegister"
                class="auth-btn register-btn"
                size="large"
                :loading="loading"
              >
                <el-icon v-if="!loading"><User /></el-icon>
                <span>立即注册</span>
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>


    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.login-box {
  width: 480px;
  padding: 50px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.login-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Logo和标题区域 */
.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 50%, #26A69A 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  box-shadow: 0 8px 20px rgba(79, 195, 247, 0.4);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
  background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 50%, #26A69A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

/* 标签页样式 */
.auth-tabs {
  margin-top: 30px;
}

.auth-form {
  margin-top: 30px;
}

/* 输入框样式 */
.auth-input {
  margin-bottom: 5px;
}

.auth-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.auth-input :deep(.el-input__wrapper):hover {
  border-color: #c0c4cc;
  background: rgba(255, 255, 255, 0.9);
}

.auth-input :deep(.el-input__wrapper.is-focus) {
  border-color: #4FC3F7;
  box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
  background: white;
}

.auth-input :deep(.el-input__inner) {
  font-size: 16px;
  color: #2c3e50;
}

.auth-input :deep(.el-icon) {
  color: #7f8c8d;
  font-size: 18px;
}

.auth-input :deep(.el-input__wrapper.is-focus .el-icon) {
  color: #667eea;
}

/* 按钮样式 */
.auth-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a7ce8 0%, #6b4190 100%);
}

.register-btn {
 background: linear-gradient(135deg, #899bed 0%, #b58bde 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(119, 105, 230, 0.3);
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(69, 43, 218, 0.4);
 background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 标签页样式优化 */
:deep(.el-tabs__item) {
  font-size: 18px;
  font-weight: 600;
  padding: 0 30px;
  color: #7f8c8d;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item.is-active) {
  color: #667eea;
}

:deep(.el-tabs__active-bar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 3px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 2px;
  background-color: #f5f7fa;
}

/* 登录方式切换样式 */
.login-type-switch {
  text-align: center;
  margin-bottom: 30px;
}

.login-type-buttons {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.login-type-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

:deep(.el-radio-button__inner) {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  transition: all 0.3s ease;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: none;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 12px 0 0 12px;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0 12px 12px 0;
}

/* 验证码输入组样式 */
.code-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  width: 130px;
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.send-code-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
  background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
}

.send-code-btn:disabled {
  background: #bdc3c7;
  color: #7f8c8d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 验证码显示卡片样式 */
.verification-code-card {
  margin-top: 10px;
  padding: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
}

/* 标题区域 */
.code-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.code-icon {
  color: #0ea5e9;
  font-size: 14px;
  margin-right: 6px;
}

.code-title {
  font-size: 13px;
  font-weight: 600;
  color: #0c4a6e;
}

/* 验证码区域 */
.code-content {
  text-align: center;
  margin-bottom: 8px;
}

.code-number {
  font-size: 16px;
  font-weight: bold;
  color: #0ea5e9;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

/* 状态信息 */
.code-status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item, .countdown-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 11px;
}

.status-item {
  color: #059669;
}

.countdown-item {
  color: #dc2626;
}

.success-icon {
  color: #059669;
  font-size: 12px;
}

.timer-icon {
  color: #dc2626;
  font-size: 12px;
}

/* 提示信息 */
.code-tips {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  background: rgba(14, 165, 233, 0.1);
  border-radius: 4px;
  font-size: 10px;
  color: #0c4a6e;
}

.info-icon {
  color: #0ea5e9;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
  }

  .login-box {
    width: 100%;
    max-width: 400px;
    padding: 40px 30px;
    margin: 0 20px;
  }

  .logo-container {
    flex-direction: column;
    gap: 10px;
  }

  .logo-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 13px;
  }

  .code-input-group {
    flex-direction: column;
    gap: 15px;
  }

  .send-code-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
    border-radius: 16px;
  }

  .logo-icon {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }

  .title {
    font-size: 20px;
  }

  .auth-btn {
    height: 45px;
    font-size: 15px;
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
    padding: 0 20px;
  }
}

/* 页面加载动画 */
.login-box {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单项动画 */
.auth-form .el-form-item {
  animation: fadeInLeft 0.5s ease-out;
  animation-fill-mode: both;
}

.auth-form .el-form-item:nth-child(1) { animation-delay: 0.1s; }
.auth-form .el-form-item:nth-child(2) { animation-delay: 0.2s; }
.auth-form .el-form-item:nth-child(3) { animation-delay: 0.3s; }
.auth-form .el-form-item:nth-child(4) { animation-delay: 0.4s; }
.auth-form .el-form-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}


</style>