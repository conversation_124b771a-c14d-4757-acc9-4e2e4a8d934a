<script setup>
import { useRouter } from "vue-router";
import { ref, onMounted } from "vue";
import { Wallet, Bell, Setting } from '@element-plus/icons-vue';

const router = useRouter();

const activeIndex = ref("1");
const searchVal = ref("");
const userInfo = ref({
  realName: "管理员",
  username: "",
  role: ""
});

// 获取用户信息
const getUserInfo = () => {
  try {
    const adminInfo = localStorage.getItem('adminInfo');
    if (adminInfo) {
      const admin = JSON.parse(adminInfo);
      userInfo.value = {
        realName: admin.realName || admin.username || "管理员",
        username: admin.username || "",
        role: admin.role || ""
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

const handleSelect = (key, keyPath) => {
  console.log(key, keyPath);
  if(key =='logout'){
    // 清除登录信息
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminInfo');
    localStorage.removeItem('userType');
    router.push("/login");
  } else if(key == 'info') {
    // 跳转到个人信息页面
    router.push("/profile");
  }
};

const Search = () => {
  console.log("搜索:", searchVal.value);
};

onMounted(() => {
  getUserInfo();
});
</script>

<template>
  <el-menu
    :default-active="activeIndex"
    class="el-menu-demo"
    mode="horizontal"
    :ellipsis="false"
    @select="handleSelect"
    background-color="#393d42fa"
    text-color="#bfcbd9"
    active-text-color="#409eff"
  >
    <el-menu-item index="0" class="logo-menu-item">
      <div class="logo-container">
        <div class="logo-icon">
          <el-icon><Wallet /></el-icon>
        </div>
        <h1 class="system-title">电子钱包管理系统</h1>
      </div>
    </el-menu-item>
    
    <div class="header-center">
      <el-input
        v-model="searchVal"
        placeholder="搜索功能、交易记录..."
        class="search-input"
        @keyup.enter="Search"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div style="flex-grow: 1;"></div>

    <el-menu-item index="1" class="notification-item">
      <el-badge :value="3" class="notification-badge">
        <el-icon size="20"><Bell /></el-icon>
      </el-badge>
    </el-menu-item>

    <el-menu-item index="settings" class="settings-item">
      <el-icon size="20"><Setting /></el-icon>
    </el-menu-item>
    
    <el-sub-menu index="2">
      <template #title>
        <el-avatar :size="30" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
        <span style="margin-left: 8px;">{{ userInfo.realName }}</span>
      </template>
      <el-menu-item index="info">个人信息</el-menu-item>
      <el-menu-item index="settings">系统设置</el-menu-item>
      <el-menu-item index="logout">退出登录</el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>

<style scoped>
.el-menu--horizontal {
  padding: 0 20px;
  border-bottom: none;
  background: #393d42fa;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.el-menu--horizontal > .el-menu-item {
  height: 60px;
  line-height: 60px;
  transition: all 0.3s ease;
  color: #bfcbd9;
}

.el-menu--horizontal > .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #409eff !important;
}

.el-menu--horizontal > .el-menu-item.is-active {
  background: rgba(64, 158, 255, 0.1) !important;
  color: #409eff !important;
}

.el-menu--horizontal > .el-sub-menu {
  height: 60px;
  line-height: 60px;
}

/* Logo样式 */
.logo-menu-item {
  padding: 0 20px !important;
  cursor: default !important;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.system-title {
  color: #bfcbd9;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

/* 搜索框样式 */
.header-center {
  display: flex;
  align-items: center;
  margin-left: 40px;
}

.search-input {
  width: 300px;
}

.search-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  background: rgba(255, 255, 255, 0.2);
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.search-input :deep(.el-input__inner) {
  color: #bfcbd9;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: rgba(191, 203, 217, 0.7);
}

.search-input :deep(.el-icon) {
  color: rgba(191, 203, 217, 0.8);
}

.search-input :deep(.el-input__wrapper.is-focus .el-icon) {
  color: #409eff;
}

/* 通知和设置图标样式 */
.notification-item,
.settings-item {
  padding: 0 15px !important;
  transition: all 0.3s ease;
  color: #bfcbd9;
}

.notification-item:hover,
.settings-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #409eff !important;
  transform: translateY(-1px);
}

.notification-badge {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-center {
    display: none;
  }

  .logo-container {
    gap: 8px;
  }

  .system-title {
    font-size: 16px;
  }

  .logo-icon {
    width: 30px;
    height: 30px;
    font-size: 16px;
  }

  .search-input {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .system-title {
    display: none;
  }

  .el-menu--horizontal {
    padding: 0 10px;
  }

  .notification-item,
  .settings-item {
    padding: 0 8px !important;
  }
}

/* 动画效果 */
.logo-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
  50% {
    box-shadow: 0 2px 16px rgba(64, 158, 255, 0.5);
  }
  100% {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
}
</style>