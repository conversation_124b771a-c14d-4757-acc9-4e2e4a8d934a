<template>
  <div class="wallet-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><Wallet /></el-icon>
            钱包管理
          </h2>
          <p>管理系统钱包信息，包括查看、调整余额、冻结/解冻等操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><Wallet /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalWallets }}</div>
                <div class="stat-label">总钱包数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.activeWallets }}</div>
                <div class="stat-label">正常钱包</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon frozen">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.frozenWallets }}</div>
                <div class="stat-label">冻结钱包</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon balance">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ Number(statistics.totalBalance).toFixed(2) }}</div>
                <div class="stat-label">总余额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20" class="search-row">
        <el-col :span="8">
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入用户姓名"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择钱包状态"
            clearable
            style="width: 100%"
          >
            <el-option label="正常" :value="1" />
            <el-option label="冻结" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="warning" @click="showAdjustDialog = true" :disabled="selectedWallets.length === 0">
            <el-icon><Edit /></el-icon>
            批量调整
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 钱包列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="walletId" label="钱包ID" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.walletId }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="realName" label="用户姓名" width="140">
          <template #default="{ row }">
            <div class="user-info">
              <el-icon class="user-icon"><User /></el-icon>
              <span class="user-name">{{ row.realName || '未设置' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="账户余额" width="150" align="right" sortable>
          <template #default="{ row }">
            <div class="balance-cell">
              <el-icon class="money-icon"><Money /></el-icon>
              <span class="balance-amount" :class="getBalanceClass(row.balance)">
                ¥{{ Number(row.balance).toFixed(2) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="钱包状态" width="120">
          <template #default="{ row }">
            <div class="status-cell">
              <el-icon v-if="row.status === 1" class="status-icon success"><Check /></el-icon>
              <el-icon v-else class="status-icon danger"><Lock /></el-icon>
              <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '正常使用' : '已冻结' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                :type="row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="toggleStatus(row)"
              >
                <el-icon v-if="row.status === 1"><Lock /></el-icon>
                <el-icon v-else><Check /></el-icon>
                {{ row.status === 1 ? '冻结' : '解冻' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量调整余额对话框 -->
    <el-dialog
      v-model="showAdjustDialog"
      title="批量调整余额"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="adjustForm"
        label-width="100px"
      >
        <el-form-item label="调整类型" required>
          <el-radio-group v-model="adjustForm.type">
            <el-radio label="increase">增加</el-radio>
            <el-radio label="decrease">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" required>
          <el-input-number
            v-model="adjustForm.amount"
            :min="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入调整金额"
          />
        </el-form-item>
        <el-form-item label="调整原因" required>
          <el-input
            v-model="adjustForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>
        <el-form-item label="选中钱包">
          <div class="selected-info">
            已选择 {{ selectedWallets.length }} 个钱包
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAdjustDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAdjust">确定调整</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Wallet, Check, Lock, Money, Edit, Refresh, Search, User
} from '@element-plus/icons-vue'

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:8091'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedWallets = ref([])
const showAdjustDialog = ref(false)

// 统计数据
const statistics = ref({
  totalWallets: 0,
  activeWallets: 0,
  frozenWallets: 0,
  totalBalance: 0
})

// 搜索表单
const searchForm = reactive({
  realName: '',
  status: null
})

// 调整表单
const adjustForm = reactive({
  type: 'increase',
  amount: null,
  reason: ''
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await axios.get('/wallet/admin/statistics')
    if (response.data.code === 0) {
      statistics.value = response.data.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

const fetchWallets = async () => {
  try {
    loading.value = true
    const response = await axios.get('/wallet/admin/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        realName: searchForm.realName,
        status: searchForm.status
      }
    })

    if (response.data.code === 0) {
      tableData.value = response.data.data.records
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.msg || '获取钱包列表失败')
    }
  } catch (error) {
    console.error('获取钱包列表失败:', error)
    ElMessage.error('获取钱包列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchWallets()
}

// 重置
const handleReset = () => {
  searchForm.realName = ''
  searchForm.status = null
  currentPage.value = 1
  fetchWallets()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchWallets()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchWallets()
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedWallets.value = selection
}

// 确认调整余额
const confirmAdjust = async () => {
  if (!adjustForm.amount || !adjustForm.reason || selectedWallets.value.length === 0) {
    ElMessage.warning('请填写完整信息并选择要调整的钱包')
    return
  }

  try {
    const walletIds = selectedWallets.value.map(item => item.walletId)
    await axios.post('/wallet/admin/adjust', {
      walletIds,
      type: adjustForm.type,
      amount: adjustForm.amount,
      reason: adjustForm.reason
    })

    ElMessage.success('余额调整成功')
    showAdjustDialog.value = false
    refreshData()

    // 重置表单
    Object.assign(adjustForm, {
      type: 'increase',
      amount: null,
      reason: ''
    })
  } catch (error) {
    console.error('余额调整失败:', error)
    ElMessage.error('余额调整失败')
  }
}

// 切换状态
const toggleStatus = async (row) => {
  const action = row.status === 1 ? '冻结' : '解冻'
  try {
    await ElMessageBox.confirm(`确定要${action}该钱包吗？`, '确认操作')

    await axios.post('/wallet/admin/toggle-status', {
      walletId: row.walletId,
      status: row.status === 1 ? 0 : 1
    })

    ElMessage.success(`${action}成功`)
    fetchWallets()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('状态切换失败')
    }
  }
}

// 刷新数据
const refreshData = () => {
  loadStatistics()
  fetchWallets()
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getBalanceClass = (balance) => {
  const amount = Number(balance)
  if (amount >= 100000) return 'balance-high'
  if (amount >= 10000) return 'balance-medium'
  if (amount >= 1000) return 'balance-low'
  return 'balance-very-low'
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
  fetchWallets()
})
</script>

<style scoped>
.wallet-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.statistics-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
  color: white;
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.stat-card:hover .stat-icon::before {
  transform: scale(1);
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.stat-icon.active {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  box-shadow: 0 4px 15px rgba(56, 239, 125, 0.4);
}

.stat-icon.frozen {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.stat-icon.balance {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  box-shadow: 0 4px 15px rgba(254, 202, 87, 0.4);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 800;
  color: #303133;
  line-height: 1;
  margin-bottom: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 15px;
  color: #606266;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.search-card, .table-card {
  margin-bottom: 24px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  overflow: hidden;
}

.search-row {
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 表格样式优化 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-icon {
  color: #409eff;
  font-size: 16px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.balance-cell {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
}

.money-icon {
  color: #67c23a;
  font-size: 16px;
}

.balance-amount {
  font-weight: 600;
  font-size: 16px;
}

.balance-high {
  color: #67c23a;
}

.balance-medium {
  color: #409eff;
}

.balance-low {
  color: #e6a23c;
}

.balance-very-low {
  color: #f56c6c;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.danger {
  color: #f56c6c;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.selected-info {
  color: #409eff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .wallet-manage {
    padding: 12px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-left h2 {
    font-size: 24px;
  }

  .statistics-cards .el-col {
    margin-bottom: 12px;
  }

  .search-row .el-col {
    margin-bottom: 12px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 16px 0;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
    width: 56px;
    height: 56px;
    font-size: 24px;
  }

  .stat-number {
    font-size: 28px;
  }

  .balance-amount {
    font-size: 14px;
  }

  .user-info {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 16px;
  }

  .header-left h2 {
    font-size: 20px;
    gap: 8px;
  }

  .header-icon {
    font-size: 24px;
    padding: 6px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>
