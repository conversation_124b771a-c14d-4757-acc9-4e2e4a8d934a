# TransactionsManageOptimized.vue 所需API测试脚本

## 📋 API清单

根据TransactionsManageOptimized.vue页面的需求，以下是所有需要的API接口：

### 1. 分页查询交易记录
```
GET http://localhost:8091/transactions/page
```
**参数：**
- pageNum: 页码 (默认1)
- pageSize: 每页大小 (默认10)
- phone: 用户手机号 (可选)
- type: 交易类型 (可选) 1-充值, 2-提现, 3-转账, 4-消费
- status: 交易状态 (可选) 0-失败, 1-成功, 2-处理中
- transNo: 交易流水号 (可选)
- startDate: 开始日期 (可选)
- endDate: 结束日期 (可选)

### 2. 获取交易统计数据
```
GET http://localhost:8091/transactions/statistics
```
**返回数据：**
- totalTransactions: 总交易数
- rechargeCount: 充值交易数
- withdrawCount: 提现交易数
- transferCount: 转账交易数
- consumeCount: 消费交易数
- totalAmount: 总交易金额

### 3. 获取总交易金额
```
GET http://localhost:8091/transactions/totalAmount
```
**返回：** 成功交易的总金额

### 4. 按类型统计交易数量
```
GET http://localhost:8091/transactions/count
GET http://localhost:8091/transactions/count?type=1
```
**参数：**
- type: 交易类型 (可选)，不传则返回总数量

## 🧪 Postman测试命令

### 基础测试
```bash
# 1. 测试分页查询 - 基础查询
GET http://localhost:8091/transactions/page?pageNum=1&pageSize=10

# 2. 测试分页查询 - 按类型筛选
GET http://localhost:8091/transactions/page?pageNum=1&pageSize=10&type=1

# 3. 测试分页查询 - 按状态筛选
GET http://localhost:8091/transactions/page?pageNum=1&pageSize=10&status=1

# 4. 测试统计数据
GET http://localhost:8091/transactions/statistics

# 5. 测试总交易金额
GET http://localhost:8091/transactions/totalAmount

# 6. 测试总交易数量
GET http://localhost:8091/transactions/count

# 7. 测试按类型统计 - 充值
GET http://localhost:8091/transactions/count?type=1

# 8. 测试按类型统计 - 提现
GET http://localhost:8091/transactions/count?type=2

# 9. 测试按类型统计 - 转账
GET http://localhost:8091/transactions/count?type=3

# 10. 测试按类型统计 - 消费
GET http://localhost:8091/transactions/count?type=4
```

### 高级测试
```bash
# 11. 测试复合条件查询
GET http://localhost:8091/transactions/page?pageNum=1&pageSize=5&type=1&status=1

# 12. 测试日期范围查询
GET http://localhost:8091/transactions/page?startDate=2024-01-01&endDate=2024-12-31

# 13. 测试交易流水号搜索
GET http://localhost:8091/transactions/page?transNo=TXN

# 14. 测试手机号搜索
GET http://localhost:8091/transactions/page?phone=138
```

## ✅ 预期结果

### 成功响应格式
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    // 具体数据内容
  }
}
```

### 分页查询响应
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 统计数据响应
```json
{
  "code": 0,
  "msg": "获取统计数据成功",
  "data": {
    "totalTransactions": 1256,
    "rechargeCount": 314,
    "withdrawCount": 289,
    "transferCount": 356,
    "consumeCount": 297,
    "totalAmount": 1234567.89
  }
}
```

## 🔧 故障排除

如果某个API返回错误，请检查：

1. **数据库连接** - 确保MySQL服务正在运行
2. **表结构** - 确保transaction表存在且结构正确
3. **数据** - 确保表中有测试数据
4. **服务状态** - 确保Spring Boot应用正在运行在8091端口

## 📝 测试步骤

1. 确保后台服务运行正常
2. 使用Postman逐一测试上述API
3. 检查返回的数据格式是否正确
4. 验证前端页面是否能正常显示数据

所有API都应该返回正确的数据格式，这样TransactionsManageOptimized.vue页面就能正常工作了！
