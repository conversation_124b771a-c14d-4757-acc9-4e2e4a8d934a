<template>
  <div class="payment-test">
    <h2>支付功能测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <h3>测试结果</h3>
      <div v-for="(result, index) in testResults" :key="index" style="margin-bottom: 10px;">
        <el-tag :type="result.success ? 'success' : 'danger'">
          {{ result.test }}: {{ result.success ? '成功' : '失败' }}
        </el-tag>
        <span style="margin-left: 10px;">{{ result.message }}</span>
      </div>
    </el-card>

    <el-button type="primary" @click="runTests">运行测试</el-button>
    
    <el-card style="margin-top: 20px;" v-if="paymentData.length > 0">
      <h3>支付记录</h3>
      <el-table :data="paymentData" style="width: 100%">
        <el-table-column prop="paymentId" label="ID" width="80" />
        <el-table-column prop="paymentNo" label="支付单号" width="150" />
        <el-table-column prop="orderNo" label="订单号" width="120" />
        <el-table-column prop="amount" label="金额" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="testUpdate(row)">测试更新</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:8091'

const testResults = ref([])
const paymentData = ref([])

const runTests = async () => {
  testResults.value = []
  
  // 测试1: 获取统计信息
  try {
    const response = await axios.get('/payment/admin/statistics')
    testResults.value.push({
      test: '获取统计信息',
      success: response.data.code === 0,
      message: response.data.code === 0 ? '成功获取统计数据' : response.data.msg
    })
  } catch (error) {
    testResults.value.push({
      test: '获取统计信息',
      success: false,
      message: error.message
    })
  }

  // 测试2: 获取支付记录列表
  try {
    const response = await axios.get('/payment/admin/page', {
      params: { pageNum: 1, pageSize: 10 }
    })
    testResults.value.push({
      test: '获取支付记录列表',
      success: response.data.code === 0,
      message: response.data.code === 0 ? `成功获取${response.data.data.records.length}条记录` : response.data.msg
    })
    
    if (response.data.code === 0) {
      paymentData.value = response.data.data.records
    }
  } catch (error) {
    testResults.value.push({
      test: '获取支付记录列表',
      success: false,
      message: error.message
    })
  }

  // 测试3: 创建测试支付记录
  try {
    const testPayment = {
      paymentNo: 'TEST' + Date.now(),
      userId: 1,
      amount: 99.99,
      merchantName: '测试商户',
      orderNo: 'ORDER' + Date.now(),
      description: '测试支付记录',
      status: 0
    }
    
    const response = await axios.post('/payment/admin/add', testPayment)
    testResults.value.push({
      test: '创建支付记录',
      success: response.data.code === 0,
      message: response.data.code === 0 ? '成功创建测试支付记录' : response.data.msg
    })
  } catch (error) {
    testResults.value.push({
      test: '创建支付记录',
      success: false,
      message: error.message
    })
  }
}

const testUpdate = async (row) => {
  try {
    const newStatus = row.status === 0 ? 1 : 0
    console.log(`测试更新支付记录 ${row.paymentId}: ${row.status} -> ${newStatus}`)
    
    const updateData = {
      paymentNo: row.paymentNo,
      userId: row.userId,
      amount: row.amount,
      merchantName: row.merchantName,
      orderNo: row.orderNo,
      description: row.description,
      status: newStatus
    }
    
    const response = await axios.put(`/payment/admin/${row.paymentId}`, updateData)
    console.log('更新响应:', response.data)
    
    if (response.data.code === 0) {
      ElMessage.success('更新成功')
      // 重新获取数据
      const listResponse = await axios.get('/payment/admin/page', {
        params: { pageNum: 1, pageSize: 10 }
      })
      if (listResponse.data.code === 0) {
        paymentData.value = listResponse.data.data.records
      }
    } else {
      ElMessage.error('更新失败: ' + response.data.msg)
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败: ' + error.message)
  }
}

const getStatusType = (status) => {
  const statusMap = {
    0: 'warning', // 待支付
    1: 'success', // 支付成功
    2: 'danger',  // 支付失败
    3: 'info'     // 已取消
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    0: '待支付',
    1: '支付成功',
    2: '支付失败',
    3: '已取消'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.payment-test {
  padding: 20px;
}
</style>
