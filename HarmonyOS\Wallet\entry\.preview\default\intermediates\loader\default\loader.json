{"modulePathMap": {"entry": "D:\\HarmonyOSProject\\Wallet\\entry"}, "compileMode": "esmodule", "projectRootPath": "D:\\HarmonyOSProject\\Wallet", "nodeModulesPath": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "buildConfigPath": ".preview\\config\\buildConfig.json"}