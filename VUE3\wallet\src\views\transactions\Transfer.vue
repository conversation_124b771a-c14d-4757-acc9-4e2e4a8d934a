<template>
  <div class="transfer-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>发起转账</span>
          <el-button type="info" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
        </div>
      </template>

      <div class="transfer-container">
        <el-form
          ref="transferFormRef"
          :model="transferForm"
          :rules="transferRules"
          label-width="120px"
          class="transfer-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="转出账户" prop="fromAccountNumber">
                <el-select
                  v-model="transferForm.fromAccountNumber"
                  placeholder="请选择转出账户"
                  style="width: 100%"
                  @change="handleFromAccountChange"
                >
                  <el-option
                    v-for="account in userAccounts"
                    :key="account.accountNumber"
                    :label="`${account.accountNumber} (${account.bankName})`"
                    :value="account.accountNumber"
                  >
                    <div class="account-option">
                      <div class="account-number">{{ account.accountNumber }}</div>
                      <div class="account-info">
                        <span class="bank-name">{{ account.bankName }}</span>
                        <span class="balance">余额: ¥{{ Number(account.balance).toFixed(2) }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="转入账户" prop="toAccountNumber">
                <el-input
                  v-model="transferForm.toAccountNumber"
                  placeholder="请输入转入账户号"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="转账金额" prop="amount">
                <el-input
                  v-model="transferForm.amount"
                  placeholder="请输入转账金额"
                  type="number"
                  step="0.01"
                  min="0.01"
                >
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="账户余额" v-if="selectedAccountBalance">
                <div class="balance-display">
                  <span class="balance-amount">¥{{ selectedAccountBalance }}</span>
                  <el-button 
                    type="text" 
                    size="small" 
                    @click="setMaxAmount"
                    class="max-button"
                  >
                    全部转出
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="转账备注">
            <el-input
              v-model="transferForm.remark"
              placeholder="请输入转账备注（可选）"
              type="textarea"
              :rows="3"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleTransfer" :loading="transferLoading" size="large">
              <el-icon><Money /></el-icon>
              确认转账
            </el-button>
            <el-button @click="resetForm" size="large">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 转账说明 -->
        <el-card class="transfer-tips" shadow="never">
          <template #header>
            <div class="tips-header">
              <el-icon><InfoFilled /></el-icon>
              <span>转账说明</span>
            </div>
          </template>
          <ul class="tips-list">
            <li>转账金额必须大于0且不超过账户余额</li>
            <li>请仔细核对转入账户号，转账完成后无法撤销</li>
            <li>转账手续费根据银行政策执行</li>
            <li>转账记录可在"转账记录"页面查看</li>
          </ul>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Money, Refresh, InfoFilled } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const transferLoading = ref(false)
const transferFormRef = ref()
const userAccounts = ref([])
const selectedAccountBalance = ref('')

// 转账表单
const transferForm = reactive({
  fromAccountNumber: '',
  toAccountNumber: '',
  amount: '',
  remark: ''
})

// 转账表单验证规则
const transferRules = {
  fromAccountNumber: [
    { required: true, message: '请选择转出账户', trigger: 'change' }
  ],
  toAccountNumber: [
    { required: true, message: '请输入转入账户号', trigger: 'blur' },
    { min: 10, max: 20, message: '账户号长度应为10-20位', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入转账金额', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入转账金额'))
        } else if (isNaN(value) || Number(value) <= 0) {
          callback(new Error('转账金额必须大于0'))
        } else if (Number(value) > 999999.99) {
          callback(new Error('转账金额不能超过999,999.99'))
        } else if (selectedAccountBalance.value && Number(value) > Number(selectedAccountBalance.value)) {
          callback(new Error('转账金额不能超过账户余额'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 获取用户银行账户列表
const fetchUserAccounts = async () => {
  try {
    const response = await axios.get('http://localhost:8091/bankAccounts/page', {
      params: {
        pageNum: 1,
        pageSize: 100,
        status: 1
      }
    })
    if (response.data && response.data.code === 0) {
      // 显示所有正常状态的银行账户
      userAccounts.value = (response.data.data?.records || []).filter(account => account.status === 1)
    } else {
      ElMessage.error('获取账户列表失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取账户列表失败:', error)
    ElMessage.error('获取账户列表失败')
  }
}

// 转出账户变化处理
const handleFromAccountChange = (accountNumber) => {
  const account = userAccounts.value.find(acc => acc.accountNumber === accountNumber)
  selectedAccountBalance.value = account ? Number(account.balance).toFixed(2) : ''
}

// 设置最大金额
const setMaxAmount = () => {
  if (selectedAccountBalance.value) {
    transferForm.amount = selectedAccountBalance.value
  }
}

// 处理转账
const handleTransfer = async () => {
  try {
    await transferFormRef.value.validate()
    
    // 检查是否转给自己
    if (transferForm.fromAccountNumber === transferForm.toAccountNumber) {
      ElMessage.error('不能转账给自己')
      return
    }
    
    // 确认转账
    await ElMessageBox.confirm(
      `确认向账户 ${transferForm.toAccountNumber} 转账 ¥${transferForm.amount} 吗？`,
      '确认转账',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    transferLoading.value = true
    
    const params = new URLSearchParams()
    params.append('fromAccountNumber', transferForm.fromAccountNumber)
    params.append('toAccountNumber', transferForm.toAccountNumber)
    params.append('amount', transferForm.amount)
    
    const response = await axios.post('http://localhost:8091/transfer', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    
    if (response.data && response.data.code === 0) {
      ElMessage.success('转账成功')
      resetForm()
      // 可以选择跳转到转账记录页面
      router.push('/home/<USER>')
    } else {
      ElMessage.error('转账失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消转账')
    } else if (error.message && error.message.includes('validate')) {
      return
    } else {
      console.error('转账失败:', error)
      ElMessage.error('转账失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
    }
  } finally {
    transferLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  transferFormRef.value?.resetFields()
  transferForm.fromAccountNumber = ''
  transferForm.toAccountNumber = ''
  transferForm.amount = ''
  transferForm.remark = ''
  selectedAccountBalance.value = ''
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 初始化
onMounted(() => {
  fetchUserAccounts()
})
</script>

<style scoped>
.transfer-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transfer-container {
  max-width: 800px;
  margin: 0 auto;
}

.transfer-form {
  margin-bottom: 30px;
}

.account-option {
  padding: 5px 0;
}

.account-number {
  font-weight: 500;
  font-size: 14px;
}

.account-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.bank-name {
  color: #666;
}

.balance {
  color: #67c23a;
  font-weight: 500;
}

.balance-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.balance-amount {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.max-button {
  padding: 0;
  font-size: 12px;
}

.transfer-tips {
  margin-top: 20px;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #409eff;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select .el-input__inner) {
  cursor: pointer;
}
</style>
