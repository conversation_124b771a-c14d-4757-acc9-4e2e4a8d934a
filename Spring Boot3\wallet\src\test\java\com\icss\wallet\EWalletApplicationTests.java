package com.icss.wallet;

import com.icss.wallet.entity.BankAccount;
import com.icss.wallet.service.TransferService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@SpringBootTest
class EWalletApplicationTests {

	@Test
	void contextLoads() {
	}

}
