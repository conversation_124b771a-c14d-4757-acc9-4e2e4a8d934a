if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BarPage_Params {
    currentIndex?: number;
    tabController?: TabsController;
}
import { HomePage } from "@normalized:N&&&entry/src/main/ets/pages/HomePage&";
import { WalletPage } from "@normalized:N&&&entry/src/main/ets/pages/WalletPage&";
import { TransactionPage } from "@normalized:N&&&entry/src/main/ets/pages/TransactionPage&";
import { BankCardPage } from "@normalized:N&&&entry/src/main/ets/pages/BankCardPage&";
import { SettingsPage } from "@normalized:N&&&entry/src/main/ets/pages/SettingsPage&";
class BarPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentIndex = new ObservedPropertySimplePU(0, this, "currentIndex");
        this.tabController = new TabsController();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BarPage_Params) {
        if (params.currentIndex !== undefined) {
            this.currentIndex = params.currentIndex;
        }
        if (params.tabController !== undefined) {
            this.tabController = params.tabController;
        }
    }
    updateStateVars(params: BarPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentIndex: ObservedPropertySimplePU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    private tabController: TabsController;
    TabWidget(title: string, targetIndex: number, currentImage: Resource, selectImage: Resource, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BarPage.ets(14:5)", "entry");
            Column.height(55);
            Column.onClick(() => {
                this.currentIndex = targetIndex;
                this.tabController.changeIndex(this.currentIndex);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(targetIndex == this.currentIndex ? selectImage : currentImage);
            Image.debugLine("entry/src/main/ets/pages/BarPage.ets(15:7)", "entry");
            Image.width(25);
            Image.height(25);
            Image.margin({ top: 5 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/BarPage.ets(19:7)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.currentIndex == targetIndex ? '#ee9725cb' : '#333333');
            Text.margin({ top: 6 });
        }, Text);
        Text.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BarPage.ets(32:5)", "entry");
            Column.width('100%');
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({
                barPosition: BarPosition.End,
                index: 0,
                controller: this.tabController
            });
            Tabs.debugLine("entry/src/main/ets/pages/BarPage.ets(33:7)", "entry");
            Tabs.animationDuration(0);
            Tabs.barHeight(55);
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new HomePage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/BarPage.ets", line: 39, col: 11 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "HomePage" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabWidget.call(this, '首页', 0, { "id": 16777238, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, { "id": 16777234, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                } });
            TabContent.debugLine("entry/src/main/ets/pages/BarPage.ets(38:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new WalletPage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/BarPage.ets", line: 43, col: 11 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "WalletPage" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabWidget.call(this, '钱包', 1, { "id": 16777235, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, { "id": 16777236, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                } });
            TabContent.debugLine("entry/src/main/ets/pages/BarPage.ets(42:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new TransactionPage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/BarPage.ets", line: 47, col: 11 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "TransactionPage" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabWidget.call(this, '交易', 2, { "id": 16777233, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, { "id": 16777232, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                } });
            TabContent.debugLine("entry/src/main/ets/pages/BarPage.ets(46:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new BankCardPage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/BarPage.ets", line: 51, col: 11 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "BankCardPage" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabWidget.call(this, '银行卡', 3, { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                } });
            TabContent.debugLine("entry/src/main/ets/pages/BarPage.ets(50:9)", "entry");
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new SettingsPage(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/BarPage.ets", line: 55, col: 11 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {};
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {});
                        }
                    }, { name: "SettingsPage" });
                }
            });
            TabContent.tabBar({ builder: () => {
                    this.TabWidget.call(this, '我的', 4, { "id": 16777229, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }, { "id": 16777231, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                } });
            TabContent.debugLine("entry/src/main/ets/pages/BarPage.ets(54:9)", "entry");
        }, TabContent);
        TabContent.pop();
        Tabs.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BarPage";
    }
}
registerNamedRoute(() => new BarPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/BarPage", pageFullPath: "entry/src/main/ets/pages/BarPage", integratedHsp: "false", moduleType: "followWithHap" });
