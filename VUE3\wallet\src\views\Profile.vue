<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Phone, Lock, Calendar, Shield } from '@element-plus/icons-vue';

const userInfo = ref({
  adminId: '',
  username: '',
  realName: '',
  phone: '',
  role: '',
  status: 1,
  createTime: '',
  lastLoginTime: ''
});

const loading = ref(false);

// 获取用户信息
const getUserInfo = () => {
  try {
    const adminInfo = localStorage.getItem('adminInfo');
    if (adminInfo) {
      const admin = JSON.parse(adminInfo);
      userInfo.value = {
        adminId: admin.adminId || '',
        username: admin.username || '',
        realName: admin.realName || '',
        phone: admin.phone || '',
        role: admin.role || '',
        status: admin.status || 1,
        createTime: admin.createTime || '',
        lastLoginTime: admin.lastLoginTime || ''
      };
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '暂无';
  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
  } catch (error) {
    return timeStr;
  }
};

// 获取角色显示名称
const getRoleDisplayName = (role) => {
  const roleMap = {
    'admin': '超级管理员',
    'operator': '操作员',
    'manager': '管理员'
  };
  return roleMap[role] || role || '未知';
};

// 获取状态显示
const getStatusDisplay = (status) => {
  return status === 1 ? '正常' : '禁用';
};

// 获取状态标签类型
const getStatusType = (status) => {
  return status === 1 ? 'success' : 'danger';
};

onMounted(() => {
  getUserInfo();
});
</script>

<template>
  <div class="profile-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="header-text">
            <h1 class="page-title">个人信息</h1>
            <p class="page-description">查看和管理您的个人账户信息</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人信息卡片 -->
    <div class="profile-content">
      <el-row :gutter="24">
        <!-- 基本信息 -->
        <el-col :span="16">
          <el-card class="info-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="card-title">
                  <el-icon><User /></el-icon>
                  <span>基本信息</span>
                </div>
              </div>
            </template>
            
            <div class="info-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">
                      <el-icon><User /></el-icon>
                      <span>真实姓名</span>
                    </div>
                    <div class="info-value">{{ userInfo.realName || '未设置' }}</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">
                      <el-icon><User /></el-icon>
                      <span>用户名</span>
                    </div>
                    <div class="info-value">{{ userInfo.username || '未设置' }}</div>
                  </div>
                </el-col>
              </el-row>
              
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">
                      <el-icon><Phone /></el-icon>
                      <span>手机号码</span>
                    </div>
                    <div class="info-value">{{ userInfo.phone || '未绑定' }}</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <div class="info-label">
                      <el-icon><Shield /></el-icon>
                      <span>角色权限</span>
                    </div>
                    <div class="info-value">
                      <el-tag :type="userInfo.role === 'admin' ? 'danger' : 'primary'" size="small">
                        {{ getRoleDisplayName(userInfo.role) }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <!-- 账户状态 -->
        <el-col :span="8">
          <el-card class="status-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="card-title">
                  <el-icon><Shield /></el-icon>
                  <span>账户状态</span>
                </div>
              </div>
            </template>
            
            <div class="status-content">
              <div class="status-item">
                <div class="status-label">账户状态</div>
                <div class="status-value">
                  <el-tag :type="getStatusType(userInfo.status)" size="large">
                    {{ getStatusDisplay(userInfo.status) }}
                  </el-tag>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-label">账户ID</div>
                <div class="status-value">{{ userInfo.adminId || '未知' }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 时间信息 -->
      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :span="24">
          <el-card class="time-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="card-title">
                  <el-icon><Calendar /></el-icon>
                  <span>时间信息</span>
                </div>
              </div>
            </template>
            
            <div class="time-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="time-item">
                    <div class="time-label">
                      <el-icon><Calendar /></el-icon>
                      <span>注册时间</span>
                    </div>
                    <div class="time-value">{{ formatTime(userInfo.createTime) }}</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="time-item">
                    <div class="time-label">
                      <el-icon><Calendar /></el-icon>
                      <span>最后登录</span>
                    </div>
                    <div class="time-value">{{ formatTime(userInfo.lastLoginTime) }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 0;
  background: #f5f7fa;
  min-height: calc(100vh - 140px);
}

/* 页面标题样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  backdrop-filter: blur(10px);
}

.header-text {
  color: white;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

/* 卡片样式 */
.profile-content {
  padding: 0 4px;
}

.info-card, .status-card, .time-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
}

.info-card:hover, .status-card:hover, .time-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.card-title .el-icon {
  font-size: 18px;
  color: #667eea;
}

/* 信息项样式 */
.info-content {
  padding: 10px 0;
}

.info-item {
  margin-bottom: 24px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-label .el-icon {
  font-size: 16px;
  color: #667eea;
}

.info-value {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
  padding-left: 24px;
}

/* 状态卡片样式 */
.status-content {
  padding: 10px 0;
}

.status-item {
  margin-bottom: 20px;
  text-align: center;
}

.status-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
}

/* 时间卡片样式 */
.time-content {
  padding: 10px 0;
}

.time-item {
  margin-bottom: 16px;
}

.time-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.time-label .el-icon {
  font-size: 16px;
  color: #667eea;
}

.time-value {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
  padding-left: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 20px;
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-description {
    font-size: 14px;
  }
  
  .header-left {
    gap: 15px;
  }
  
  .page-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
}
</style>
