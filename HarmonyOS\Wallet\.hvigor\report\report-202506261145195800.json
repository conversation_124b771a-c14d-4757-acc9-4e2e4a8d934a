{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "b935f8bb-717a-4762-a1d4-9c4ef2d1d1c8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041454475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a65944-a1a1-4eb0-857c-3a7b4a310a43", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041462530800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f447f89-7326-44fd-a745-ba7b7e7dc288", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041463126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e323d368-afb0-435b-a6d4-bcacd235a097", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179444218300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798f42c9-97cc-4ec5-bd34-444d7662a840", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179452625500, "endTime": 11179652018100}, "additional": {"children": ["0616a738-ef54-4526-87ce-5475a1b7ba21", "223408ca-9453-4654-a477-2e77f88cb5d0", "88ae9c06-efb9-440f-91a9-28cb76c3d79e", "1a73d8af-c18d-477e-82d9-08a4549f6403", "4128fb19-574d-43ec-a65a-556b90895f7d", "11b2a1a1-e731-406f-bfdb-6fdaae40c8d3", "4d421eb6-81cd-4303-9bf5-978ae73e463c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0616a738-ef54-4526-87ce-5475a1b7ba21", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179452628200, "endTime": 11179468567000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "0ef9b368-30b0-44a5-84a7-a937d73290d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "223408ca-9453-4654-a477-2e77f88cb5d0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179468612800, "endTime": 11179650780000}, "additional": {"children": ["4d0d22dc-e8c7-44ad-b65c-e1b3821837d6", "0fe5a652-4eb8-4bb8-9cca-b7b24918305f", "ec490f61-0c7d-4624-ad5d-bebceb2d2f14", "3a19cd4f-68bf-4fd7-930e-dcf9dce474c3", "a803f3bf-0b8c-45d3-8079-bd15d71e3a99", "7ab94d95-ec49-4c48-9806-c0293243e912", "1d4f4642-afbb-4724-8240-825704d2d2d8", "9b452747-fa15-48e1-9d69-d3dc5f5ca60d", "b33b4e4b-4bfe-4e0b-8637-4f22be9e4710"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "d519908a-9252-4241-ad39-f696fe9ae3bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88ae9c06-efb9-440f-91a9-28cb76c3d79e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179650805800, "endTime": 11179651994200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "c0511716-c80d-42e1-919f-564e764f1417"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a73d8af-c18d-477e-82d9-08a4549f6403", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179651998700, "endTime": 11179652013400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "6da9f89b-19ee-4566-9b6c-2aea02767357"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4128fb19-574d-43ec-a65a-556b90895f7d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179456259200, "endTime": 11179456330100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "492f609a-956a-4512-9f4b-c2175b0b535e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "492f609a-956a-4512-9f4b-c2175b0b535e", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179456259200, "endTime": 11179456330100}, "additional": {"logType": "info", "children": [], "durationId": "4128fb19-574d-43ec-a65a-556b90895f7d", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "11b2a1a1-e731-406f-bfdb-6fdaae40c8d3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179462228800, "endTime": 11179462252700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "c9089773-b2c0-4f76-a0db-bb5476ed538a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9089773-b2c0-4f76-a0db-bb5476ed538a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179462228800, "endTime": 11179462252700}, "additional": {"logType": "info", "children": [], "durationId": "11b2a1a1-e731-406f-bfdb-6fdaae40c8d3", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "7c6b66da-3e9a-45a4-978d-0e2e4a87b2bb", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179462312800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da551dc-11a1-4cba-a725-b75d44bdc288", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179468318000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef9b368-30b0-44a5-84a7-a937d73290d8", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179452628200, "endTime": 11179468567000}, "additional": {"logType": "info", "children": [], "durationId": "0616a738-ef54-4526-87ce-5475a1b7ba21", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "4d0d22dc-e8c7-44ad-b65c-e1b3821837d6", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179478026400, "endTime": 11179478048500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "7b99ffee-08b0-4e7f-af92-78dfd746e1c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fe5a652-4eb8-4bb8-9cca-b7b24918305f", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179478074500, "endTime": 11179482211400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "9e1987eb-5d83-487e-8693-e5a302a4f8d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec490f61-0c7d-4624-ad5d-bebceb2d2f14", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179482245900, "endTime": 11179575764100}, "additional": {"children": ["18fb2fdc-5eea-4f8c-8bc5-6e02f61d4fd6", "d9d93952-472f-47bc-9cf4-10f6cf719458", "0fe10e98-4ed1-41ab-8abc-dbdf6dff02da"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "e9eebf38-177f-4647-be6d-91c0225c65dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a19cd4f-68bf-4fd7-930e-dcf9dce474c3", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179575781000, "endTime": 11179603508900}, "additional": {"children": ["bb3768b2-f6c2-466c-ab31-25d22d1b2b6a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "895f7f79-3158-4bc3-827d-c527111902cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a803f3bf-0b8c-45d3-8079-bd15d71e3a99", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179603701200, "endTime": 11179621151700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "6a6cfacb-67d2-408f-99e6-a19cf33e1875"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ab94d95-ec49-4c48-9806-c0293243e912", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179622549200, "endTime": 11179635393900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "2b2c62f4-d849-4396-a4fb-2ec792de9252"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d4f4642-afbb-4724-8240-825704d2d2d8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179635418500, "endTime": 11179650570100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "5be9b5c2-d0d7-40f3-b030-04dcf377ad25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b452747-fa15-48e1-9d69-d3dc5f5ca60d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179650616900, "endTime": 11179650767100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "451cea6a-0d3c-487a-8503-0fb801e5384a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b99ffee-08b0-4e7f-af92-78dfd746e1c1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179478026400, "endTime": 11179478048500}, "additional": {"logType": "info", "children": [], "durationId": "4d0d22dc-e8c7-44ad-b65c-e1b3821837d6", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "9e1987eb-5d83-487e-8693-e5a302a4f8d1", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179478074500, "endTime": 11179482211400}, "additional": {"logType": "info", "children": [], "durationId": "0fe5a652-4eb8-4bb8-9cca-b7b24918305f", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "18fb2fdc-5eea-4f8c-8bc5-6e02f61d4fd6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179483506500, "endTime": 11179483551100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec490f61-0c7d-4624-ad5d-bebceb2d2f14", "logId": "2bc248b8-58a6-4606-a545-487eb8e91c26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bc248b8-58a6-4606-a545-487eb8e91c26", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179483506500, "endTime": 11179483551100}, "additional": {"logType": "info", "children": [], "durationId": "18fb2fdc-5eea-4f8c-8bc5-6e02f61d4fd6", "parent": "e9eebf38-177f-4647-be6d-91c0225c65dc"}}, {"head": {"id": "d9d93952-472f-47bc-9cf4-10f6cf719458", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179486397200, "endTime": 11179574862500}, "additional": {"children": ["c295b0c9-bd94-468d-933d-51bc62b93fb0", "2c102918-adff-45bc-83fc-6da6ee925424"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec490f61-0c7d-4624-ad5d-bebceb2d2f14", "logId": "d884f7ad-c72d-4ff9-a159-4841c2fc6fe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c295b0c9-bd94-468d-933d-51bc62b93fb0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179486398700, "endTime": 11179492013500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9d93952-472f-47bc-9cf4-10f6cf719458", "logId": "dcfe961c-5a26-4690-98ea-821f7b1cfc05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c102918-adff-45bc-83fc-6da6ee925424", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179492052100, "endTime": 11179574844800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9d93952-472f-47bc-9cf4-10f6cf719458", "logId": "ab27d092-07f2-4b07-b39e-74d2e60f0af9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d09d0f85-f7be-421c-834c-103adc2c3655", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179486405000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca6abeb-7c8e-429b-95e1-f77f6baf821c", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179491750400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcfe961c-5a26-4690-98ea-821f7b1cfc05", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179486398700, "endTime": 11179492013500}, "additional": {"logType": "info", "children": [], "durationId": "c295b0c9-bd94-468d-933d-51bc62b93fb0", "parent": "d884f7ad-c72d-4ff9-a159-4841c2fc6fe8"}}, {"head": {"id": "5bd98b50-2170-41e6-a761-c484fdb6e217", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179492076900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7477f612-ab65-44f1-9d39-1b74a8389def", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179499495300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e78ab4-d91a-47e2-bdfb-85f91982e981", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179499713800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f447597-5209-4bb9-a72d-99dbc8e9c618", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179500824200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "389da09c-15cf-4bc0-96be-09988a32ef80", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179501218800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce86bd2-1d70-49c8-b1c0-4a2efa12b8db", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179503307400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea20e4d6-5830-4fd3-b464-87e791f7096b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179507339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0370da04-61a2-4ad1-aaea-8b80db87e09f", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179519651600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3145617c-06e4-4411-8d38-c98379a7d144", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179546333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac96b5d7-70b7-4a73-859d-35f851f45c32", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179546868300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 45}, "markType": "other"}}, {"head": {"id": "69aafee3-4c6c-45de-bbeb-bffc6db1495e", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179546887100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 45}, "markType": "other"}}, {"head": {"id": "7912e053-65f1-4b5a-8537-b0fae8625b42", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179574421100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66067f0-27f0-40de-bbe8-10e725de286e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179574605200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71323e0c-7671-4872-86e5-fb9fa1408f86", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179574696500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f213b21-d35f-4bb3-b888-95be9378240c", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179574777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab27d092-07f2-4b07-b39e-74d2e60f0af9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179492052100, "endTime": 11179574844800}, "additional": {"logType": "info", "children": [], "durationId": "2c102918-adff-45bc-83fc-6da6ee925424", "parent": "d884f7ad-c72d-4ff9-a159-4841c2fc6fe8"}}, {"head": {"id": "d884f7ad-c72d-4ff9-a159-4841c2fc6fe8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179486397200, "endTime": 11179574862500}, "additional": {"logType": "info", "children": ["dcfe961c-5a26-4690-98ea-821f7b1cfc05", "ab27d092-07f2-4b07-b39e-74d2e60f0af9"], "durationId": "d9d93952-472f-47bc-9cf4-10f6cf719458", "parent": "e9eebf38-177f-4647-be6d-91c0225c65dc"}}, {"head": {"id": "0fe10e98-4ed1-41ab-8abc-dbdf6dff02da", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179575723600, "endTime": 11179575743100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec490f61-0c7d-4624-ad5d-bebceb2d2f14", "logId": "632eea10-0ce9-4ee7-b0a5-0537b84ecda7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "632eea10-0ce9-4ee7-b0a5-0537b84ecda7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179575723600, "endTime": 11179575743100}, "additional": {"logType": "info", "children": [], "durationId": "0fe10e98-4ed1-41ab-8abc-dbdf6dff02da", "parent": "e9eebf38-177f-4647-be6d-91c0225c65dc"}}, {"head": {"id": "e9eebf38-177f-4647-be6d-91c0225c65dc", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179482245900, "endTime": 11179575764100}, "additional": {"logType": "info", "children": ["2bc248b8-58a6-4606-a545-487eb8e91c26", "d884f7ad-c72d-4ff9-a159-4841c2fc6fe8", "632eea10-0ce9-4ee7-b0a5-0537b84ecda7"], "durationId": "ec490f61-0c7d-4624-ad5d-bebceb2d2f14", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "bb3768b2-f6c2-466c-ab31-25d22d1b2b6a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179576466800, "endTime": 11179603489600}, "additional": {"children": ["752ebb22-65cb-4aa4-a41a-59bcbd41f78c", "b2811f78-2c24-4ff5-a553-c65e52ac20ec", "d5c0e2f8-dedc-40a6-a235-be1510a244f0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a19cd4f-68bf-4fd7-930e-dcf9dce474c3", "logId": "0f9aebbf-5028-44f3-9789-489d92be55d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "752ebb22-65cb-4aa4-a41a-59bcbd41f78c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179580490000, "endTime": 11179580511400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb3768b2-f6c2-466c-ab31-25d22d1b2b6a", "logId": "72b57dac-d751-42f5-855e-384ff6f55a36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72b57dac-d751-42f5-855e-384ff6f55a36", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179580490000, "endTime": 11179580511400}, "additional": {"logType": "info", "children": [], "durationId": "752ebb22-65cb-4aa4-a41a-59bcbd41f78c", "parent": "0f9aebbf-5028-44f3-9789-489d92be55d2"}}, {"head": {"id": "b2811f78-2c24-4ff5-a553-c65e52ac20ec", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179582579700, "endTime": 11179601446100}, "additional": {"children": ["bd341710-55fb-4c72-a052-89e264bec752", "07d7a7d6-a08d-4204-bc2b-8dd01c38f374"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb3768b2-f6c2-466c-ab31-25d22d1b2b6a", "logId": "8504b84e-3067-4021-a83f-f8b79181bd8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd341710-55fb-4c72-a052-89e264bec752", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179582581200, "endTime": 11179586489900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2811f78-2c24-4ff5-a553-c65e52ac20ec", "logId": "c3229dd4-afa8-40ae-a88c-62fd813cfc59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07d7a7d6-a08d-4204-bc2b-8dd01c38f374", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179586515700, "endTime": 11179601428300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2811f78-2c24-4ff5-a553-c65e52ac20ec", "logId": "bcd4294f-2648-4a53-a38c-61b8a9efd7db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "133b39b5-d7d4-42cb-9947-39de95a7a068", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179582591300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a465403-d02e-4b2a-9280-3739ae828993", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179586282700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3229dd4-afa8-40ae-a88c-62fd813cfc59", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179582581200, "endTime": 11179586489900}, "additional": {"logType": "info", "children": [], "durationId": "bd341710-55fb-4c72-a052-89e264bec752", "parent": "8504b84e-3067-4021-a83f-f8b79181bd8c"}}, {"head": {"id": "64733653-96e0-427d-9982-deb2a9c34826", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179586532200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2a79c21-a8e3-4fe4-9099-10c3d2d42b64", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179594861600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed0f08d-af82-4fc9-b6f4-7d1d84ef0a5e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179595067700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96d3eb6-6e1b-4c17-b9a4-f5bb797ac0b2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179595376300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf68de9c-37c1-4160-b5c5-0094d5edb3a5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179595815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0a76cf-f07b-4c7c-b2ea-cf2d763d6b6c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179595937700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0caf8b8a-dba1-4467-9511-5b7af224add4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179596033200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9709408-4310-45d7-bc55-f1160620f07e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179596158400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2f994c-3963-4ff1-a990-f03bf056b242", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179600976600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dea7d95-194f-46cd-b529-fe49fefca8fc", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179601206800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6545e546-2997-45b9-9336-116f8aa5a484", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179601284300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f557bad-c0da-4653-a994-b667f180326c", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179601351400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd4294f-2648-4a53-a38c-61b8a9efd7db", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179586515700, "endTime": 11179601428300}, "additional": {"logType": "info", "children": [], "durationId": "07d7a7d6-a08d-4204-bc2b-8dd01c38f374", "parent": "8504b84e-3067-4021-a83f-f8b79181bd8c"}}, {"head": {"id": "8504b84e-3067-4021-a83f-f8b79181bd8c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179582579700, "endTime": 11179601446100}, "additional": {"logType": "info", "children": ["c3229dd4-afa8-40ae-a88c-62fd813cfc59", "bcd4294f-2648-4a53-a38c-61b8a9efd7db"], "durationId": "b2811f78-2c24-4ff5-a553-c65e52ac20ec", "parent": "0f9aebbf-5028-44f3-9789-489d92be55d2"}}, {"head": {"id": "d5c0e2f8-dedc-40a6-a235-be1510a244f0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179603448500, "endTime": 11179603464000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb3768b2-f6c2-466c-ab31-25d22d1b2b6a", "logId": "d67e0b78-f87f-4e29-a315-e2a2f6106c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d67e0b78-f87f-4e29-a315-e2a2f6106c3f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179603448500, "endTime": 11179603464000}, "additional": {"logType": "info", "children": [], "durationId": "d5c0e2f8-dedc-40a6-a235-be1510a244f0", "parent": "0f9aebbf-5028-44f3-9789-489d92be55d2"}}, {"head": {"id": "0f9aebbf-5028-44f3-9789-489d92be55d2", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179576466800, "endTime": 11179603489600}, "additional": {"logType": "info", "children": ["72b57dac-d751-42f5-855e-384ff6f55a36", "8504b84e-3067-4021-a83f-f8b79181bd8c", "d67e0b78-f87f-4e29-a315-e2a2f6106c3f"], "durationId": "bb3768b2-f6c2-466c-ab31-25d22d1b2b6a", "parent": "895f7f79-3158-4bc3-827d-c527111902cc"}}, {"head": {"id": "895f7f79-3158-4bc3-827d-c527111902cc", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179575781000, "endTime": 11179603508900}, "additional": {"logType": "info", "children": ["0f9aebbf-5028-44f3-9789-489d92be55d2"], "durationId": "3a19cd4f-68bf-4fd7-930e-dcf9dce474c3", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "5679c876-6e6e-4373-ab19-56f77c637b87", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179620576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4ff0d32-5fc6-4526-bcbe-2fd31e4309ef", "name": "hvigorfile, resolve hvigorfile dependencies in 18 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179621013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a6cfacb-67d2-408f-99e6-a19cf33e1875", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179603701200, "endTime": 11179621151700}, "additional": {"logType": "info", "children": [], "durationId": "a803f3bf-0b8c-45d3-8079-bd15d71e3a99", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "b33b4e4b-4bfe-4e0b-8637-4f22be9e4710", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179622277000, "endTime": 11179622531700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "223408ca-9453-4654-a477-2e77f88cb5d0", "logId": "bd17a09a-1d96-4623-b033-28d6f442e0ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd7db94a-88c7-4968-9c51-75e65d9c588e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179622322400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd17a09a-1d96-4623-b033-28d6f442e0ad", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179622277000, "endTime": 11179622531700}, "additional": {"logType": "info", "children": [], "durationId": "b33b4e4b-4bfe-4e0b-8637-4f22be9e4710", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "b2a7a60a-fefd-4d27-9dbc-5c288521ea09", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179624454900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7615ce6-8c46-465d-9715-b9ca788bcf12", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179634433800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2c62f4-d849-4396-a4fb-2ec792de9252", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179622549200, "endTime": 11179635393900}, "additional": {"logType": "info", "children": [], "durationId": "7ab94d95-ec49-4c48-9806-c0293243e912", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "e938191f-12ed-4538-9a94-0018659ed14a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179635441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e843cc7-7136-4970-97b7-bf9dbfaed54d", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179640503400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4915c30-51c9-4a5f-9527-397fec1f14fc", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179640693900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b883968b-0b23-4d44-8291-d8db0645aae1", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179641089000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a6097fd-8afe-41f3-9447-afdfd5f9a0b3", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179646216600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c170711-d962-4f02-ab96-e0291b5f44c8", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179646432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be9b5c2-d0d7-40f3-b030-04dcf377ad25", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179635418500, "endTime": 11179650570100}, "additional": {"logType": "info", "children": [], "durationId": "1d4f4642-afbb-4724-8240-825704d2d2d8", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "3bda7002-ec9f-4c46-aff6-5da28168b067", "name": "Configuration phase cost:173 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179650646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451cea6a-0d3c-487a-8503-0fb801e5384a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179650616900, "endTime": 11179650767100}, "additional": {"logType": "info", "children": [], "durationId": "9b452747-fa15-48e1-9d69-d3dc5f5ca60d", "parent": "d519908a-9252-4241-ad39-f696fe9ae3bc"}}, {"head": {"id": "d519908a-9252-4241-ad39-f696fe9ae3bc", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179468612800, "endTime": 11179650780000}, "additional": {"logType": "info", "children": ["7b99ffee-08b0-4e7f-af92-78dfd746e1c1", "9e1987eb-5d83-487e-8693-e5a302a4f8d1", "e9eebf38-177f-4647-be6d-91c0225c65dc", "895f7f79-3158-4bc3-827d-c527111902cc", "6a6cfacb-67d2-408f-99e6-a19cf33e1875", "2b2c62f4-d849-4396-a4fb-2ec792de9252", "5be9b5c2-d0d7-40f3-b030-04dcf377ad25", "451cea6a-0d3c-487a-8503-0fb801e5384a", "bd17a09a-1d96-4623-b033-28d6f442e0ad"], "durationId": "223408ca-9453-4654-a477-2e77f88cb5d0", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "4d421eb6-81cd-4303-9bf5-978ae73e463c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179651968300, "endTime": 11179651983100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "798f42c9-97cc-4ec5-bd34-444d7662a840", "logId": "1b13201b-9a9c-4184-b3b1-a86f8331cd80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b13201b-9a9c-4184-b3b1-a86f8331cd80", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179651968300, "endTime": 11179651983100}, "additional": {"logType": "info", "children": [], "durationId": "4d421eb6-81cd-4303-9bf5-978ae73e463c", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "c0511716-c80d-42e1-919f-564e764f1417", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179650805800, "endTime": 11179651994200}, "additional": {"logType": "info", "children": [], "durationId": "88ae9c06-efb9-440f-91a9-28cb76c3d79e", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "6da9f89b-19ee-4566-9b6c-2aea02767357", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179651998700, "endTime": 11179652013400}, "additional": {"logType": "info", "children": [], "durationId": "1a73d8af-c18d-477e-82d9-08a4549f6403", "parent": "7b025b16-a7b0-4100-9725-6ae53fcaf779"}}, {"head": {"id": "7b025b16-a7b0-4100-9725-6ae53fcaf779", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179452625500, "endTime": 11179652018100}, "additional": {"logType": "info", "children": ["0ef9b368-30b0-44a5-84a7-a937d73290d8", "d519908a-9252-4241-ad39-f696fe9ae3bc", "c0511716-c80d-42e1-919f-564e764f1417", "6da9f89b-19ee-4566-9b6c-2aea02767357", "492f609a-956a-4512-9f4b-c2175b0b535e", "c9089773-b2c0-4f76-a0db-bb5476ed538a", "1b13201b-9a9c-4184-b3b1-a86f8331cd80"], "durationId": "798f42c9-97cc-4ec5-bd34-444d7662a840"}}, {"head": {"id": "66a8e8f6-ce82-44ff-83ba-0e14afe7e1e2", "name": "Configuration task cost before running: 204 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179652162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d945b9-09d5-4077-abfa-365e3b848932", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179656750400, "endTime": 11179664947400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "51ffbccf-130b-416d-b0f7-c7024c1c1802", "logId": "32e3b870-d54b-41fc-bff1-3d5fb5c4595d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51ffbccf-130b-416d-b0f7-c7024c1c1802", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179653577400}, "additional": {"logType": "detail", "children": [], "durationId": "b9d945b9-09d5-4077-abfa-365e3b848932"}}, {"head": {"id": "0f8c7dcd-d961-42ef-91a1-200c3ae04ea3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179654019900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa64983-fa75-4c65-bff1-07ff22b5e74b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179654106600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c6aab3-3eca-4f4c-ad3c-ffb6b479d435", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179654156800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5213d52a-fadf-4a8f-94fa-99a3bd950679", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179656768600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4fdda3d-bd69-4751-9ef5-5f7337430232", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179664596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df3066a-9f37-4b18-900b-a85dcf54ff11", "name": "entry : default@PreBuild cost memory -1.4756698608398438", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179664801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e3b870-d54b-41fc-bff1-3d5fb5c4595d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179656750400, "endTime": 11179664947400}, "additional": {"logType": "info", "children": [], "durationId": "b9d945b9-09d5-4077-abfa-365e3b848932"}}, {"head": {"id": "9dc6807b-c9c6-4a8a-8c79-82fef7a0750c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179669924800, "endTime": 11179672784200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1a65215f-4f14-4bd8-a8f2-bb90db1069c5", "logId": "91f465b6-aa64-41d1-87be-f6b8ac664012"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a65215f-4f14-4bd8-a8f2-bb90db1069c5", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179668718500}, "additional": {"logType": "detail", "children": [], "durationId": "9dc6807b-c9c6-4a8a-8c79-82fef7a0750c"}}, {"head": {"id": "051338ec-bdf2-4fb7-9ed9-8082e5e55dfb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179669174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4506f350-793e-4e1c-b710-48b5d86643e7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179669261400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fefe14d7-be7d-42e1-8809-88a4f15672fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179669312300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a7d65af-3d08-44eb-85a3-586c9e49902e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179669935400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a92794d8-d21f-4e31-bcc2-d5f8a18de164", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179672600600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd5cabe1-453d-4004-bdfa-a2d462899e58", "name": "entry : default@MergeProfile cost memory 0.13170623779296875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179672715700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91f465b6-aa64-41d1-87be-f6b8ac664012", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179669924800, "endTime": 11179672784200}, "additional": {"logType": "info", "children": [], "durationId": "9dc6807b-c9c6-4a8a-8c79-82fef7a0750c"}}, {"head": {"id": "b06e25a7-01af-455e-94f9-a3a180cebf13", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179676756400, "endTime": 11179678849700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "36ce0cb8-2167-4b26-9b21-432c72aae698", "logId": "61fa3a6c-1381-46b2-b7f9-38cca8e00518"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36ce0cb8-2167-4b26-9b21-432c72aae698", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179675351700}, "additional": {"logType": "detail", "children": [], "durationId": "b06e25a7-01af-455e-94f9-a3a180cebf13"}}, {"head": {"id": "bdc5195c-44c0-4b89-af9e-01f7fa818412", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179675849100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1fb9b47-9f8a-4823-b704-4e1ea74a90f1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179675952400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a645b14e-7bce-41bf-9fde-6a4a9de18fe8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179676017100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aa585ef-b0f6-4502-8e10-a9050feb8f6c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179676766600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac948b9-589c-4a1e-818e-ff6b0d47996d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179677576500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ccc3d6b-929d-4d15-b892-79e8019dc8b7", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179678696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb91a76-e044-4ec9-99d6-b5b234771ea0", "name": "entry : default@CreateBuildProfile cost memory 0.09983062744140625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179678786100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61fa3a6c-1381-46b2-b7f9-38cca8e00518", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179676756400, "endTime": 11179678849700}, "additional": {"logType": "info", "children": [], "durationId": "b06e25a7-01af-455e-94f9-a3a180cebf13"}}, {"head": {"id": "42cdc6ae-6921-472a-900f-c1f585caacc9", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179681590600, "endTime": 11179682242300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4e81301d-05e7-43ac-a70f-3423d0fbefae", "logId": "e8bdb2a1-a6b4-4330-a9b9-8c611bf57a51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e81301d-05e7-43ac-a70f-3423d0fbefae", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179680203300}, "additional": {"logType": "detail", "children": [], "durationId": "42cdc6ae-6921-472a-900f-c1f585caacc9"}}, {"head": {"id": "9a80cea4-0afb-42e7-b65d-7f0e175765be", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179680647900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a7d87a-6f0f-416a-81e6-ca5b68916782", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179680743900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0bee897-42bf-474f-b028-db986a336771", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179680794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45df715b-75cb-42e2-9d16-6af1896cca10", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179681604400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c843451-2e75-48e8-b56c-099a589df281", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179681762000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4379c9-c75b-4aca-a508-70b10a2ebadc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179681840200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa633bb3-a08f-4b8a-9a03-d36f56e0d93c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179681906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec7096f-0b95-432f-8a1e-9ee0ae8db2e0", "name": "entry : default@PreCheckSyscap cost memory 0.05040740966796875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179682017100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8386c6d2-4660-48b9-bdb1-4131415dedb9", "name": "runTaskFromQueue task cost before running: 234 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179682159200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8bdb2a1-a6b4-4330-a9b9-8c611bf57a51", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179681590600, "endTime": 11179682242300, "totalTime": 533300}, "additional": {"logType": "info", "children": [], "durationId": "42cdc6ae-6921-472a-900f-c1f585caacc9"}}, {"head": {"id": "baf9cc33-e4fc-47b7-8034-ac0c307f39e4", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179692860400, "endTime": 11179693875400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "56d5207e-aa2e-4164-939b-805f37178378", "logId": "bc149387-97bb-48c2-8fe7-261ed353907a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56d5207e-aa2e-4164-939b-805f37178378", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179683994400}, "additional": {"logType": "detail", "children": [], "durationId": "baf9cc33-e4fc-47b7-8034-ac0c307f39e4"}}, {"head": {"id": "8c7562c3-e316-4c07-92f6-5e39059d5638", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179684464800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ceb795d-1bb7-4688-a040-7940b6553bb8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179684556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21f47700-2a43-4124-bf4c-0abb1d5f866b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179684609300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0b3748-fb4d-4a70-9422-65a47290cf55", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179692879700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e99e9e-022b-487f-a1ed-63d24d372a55", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179693102500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5566383d-4dbc-4aac-a919-a5c04b83e389", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179693710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7971b99f-9df0-4c6b-a357-e7b66e9cbf2d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06966400146484375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179693810900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc149387-97bb-48c2-8fe7-261ed353907a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179692860400, "endTime": 11179693875400}, "additional": {"logType": "info", "children": [], "durationId": "baf9cc33-e4fc-47b7-8034-ac0c307f39e4"}}, {"head": {"id": "7afb5ae0-dbc7-4752-b424-b0d000acad0e", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179697024300, "endTime": 11179698123900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8d16f777-34c8-42ec-bd28-972a061a94ea", "logId": "7b33df8f-fe18-4f64-8dcb-d0e070823c47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d16f777-34c8-42ec-bd28-972a061a94ea", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179695342900}, "additional": {"logType": "detail", "children": [], "durationId": "7afb5ae0-dbc7-4752-b424-b0d000acad0e"}}, {"head": {"id": "df777710-39d9-4b45-aff6-fb02670ef547", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179695806900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "306fa019-1d1a-41ad-bf14-2f464935916b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179695905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04defd16-014f-442f-a92e-ddf9c2a4d989", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179695958700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54488a1f-469e-41a4-8fe0-1f339bea0373", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179697034100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d26fc41-ad85-48df-a0ef-0a0411caa71b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179697915900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99806fcf-6740-4ef9-a0d7-04fe85a20578", "name": "entry : default@ProcessProfile cost memory 0.057159423828125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179698016900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b33df8f-fe18-4f64-8dcb-d0e070823c47", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179697024300, "endTime": 11179698123900}, "additional": {"logType": "info", "children": [], "durationId": "7afb5ae0-dbc7-4752-b424-b0d000acad0e"}}, {"head": {"id": "9e8dbd9a-bb51-4f13-9285-fac04e3d2bdf", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179702638500, "endTime": 11179708537100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fe117a28-1955-4571-abf3-1612cd9013ad", "logId": "bec66af6-9ab6-415e-b9bf-4624fecf702d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe117a28-1955-4571-abf3-1612cd9013ad", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179700312100}, "additional": {"logType": "detail", "children": [], "durationId": "9e8dbd9a-bb51-4f13-9285-fac04e3d2bdf"}}, {"head": {"id": "80b34d13-f790-4527-b7f3-c84b4280f02b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179700824100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b447938-837c-4402-9903-089747b19105", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179700936800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06341682-5e15-4695-919b-c1042cc09a20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179700996500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352a384d-0ca1-41ba-8429-e071053cfc54", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179702650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deef173c-850b-41a4-a138-86f575a704fb", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179708294800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e256f6-5071-4cc9-b757-3724060857c3", "name": "entry : default@ProcessRouterMap cost memory 0.22682952880859375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179708449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec66af6-9ab6-415e-b9bf-4624fecf702d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179702638500, "endTime": 11179708537100}, "additional": {"logType": "info", "children": [], "durationId": "9e8dbd9a-bb51-4f13-9285-fac04e3d2bdf"}}, {"head": {"id": "125880dd-92b1-4682-b48c-a983d20fedbe", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179715011100, "endTime": 11179717857200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "cc33e71e-971f-47b4-9676-f8240f0d906d", "logId": "c175b6fb-e46f-4d11-9fdc-26baa08534f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc33e71e-971f-47b4-9676-f8240f0d906d", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179711360200}, "additional": {"logType": "detail", "children": [], "durationId": "125880dd-92b1-4682-b48c-a983d20fedbe"}}, {"head": {"id": "67716661-6743-4563-8b2d-eb55cb129841", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179711820400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54c6f442-9b17-477a-bc53-8e9400c0b479", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179711905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a88db28-0c21-4c49-a801-7d83166f5cc9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179711975200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44997b36-c041-48ba-b607-867ffd3d9bab", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179712764000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f40d19d-09b2-49ee-a810-d67621051245", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179716233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0444cea7-66d9-4ba5-aa6f-9d2466b5ff17", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179716392900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eef2f19-de8f-4a74-84ac-d4a59a504600", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179716454800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac44164c-7cc6-4fde-857a-45ecdc05aad7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179716500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe8c476-d8e0-4ae6-a1ca-4de0b661ccf1", "name": "entry : default@PreviewProcessResource cost memory 0.**********", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179716573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2694d6-6275-4e07-8ac4-3a6294b33e26", "name": "runTaskFromQueue task cost before running: 269 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179717773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c175b6fb-e46f-4d11-9fdc-26baa08534f4", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179715011100, "endTime": 11179717857200, "totalTime": 1632400}, "additional": {"logType": "info", "children": [], "durationId": "125880dd-92b1-4682-b48c-a983d20fedbe"}}, {"head": {"id": "0d3ae5c9-4ddc-4543-966d-9e56272845c0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179726651900, "endTime": 11179756491100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3d4d491b-7c1b-4da9-8033-3990cafb4f16", "logId": "99b21b49-41fb-476e-943b-d16c3ba54048"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d4d491b-7c1b-4da9-8033-3990cafb4f16", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179720426100}, "additional": {"logType": "detail", "children": [], "durationId": "0d3ae5c9-4ddc-4543-966d-9e56272845c0"}}, {"head": {"id": "666b2f70-43bc-49ee-8805-fb6193557b96", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179720878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ced03808-0885-4ad5-8dab-a36b84da8fb4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179720961400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995167da-d987-4e39-a417-90c2452678ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179721010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600548f2-a61a-4f63-bc79-8aee84906eb1", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179726687300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e68d97c-5c66-417d-b7df-2971361b68ec", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179756194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf3b810-ae95-4459-96ba-4ccb9218e8fd", "name": "entry : default@GenerateLoaderJson cost memory -10.099525451660156", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179756393100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b21b49-41fb-476e-943b-d16c3ba54048", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179726651900, "endTime": 11179756491100}, "additional": {"logType": "info", "children": [], "durationId": "0d3ae5c9-4ddc-4543-966d-9e56272845c0"}}, {"head": {"id": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179769386600, "endTime": 11180520671500}, "additional": {"children": ["2336a9e6-1f6b-4023-974f-71e7b47380fa", "a0130211-00b2-4649-8dbf-c494db0a8f4b", "5e2b454e-9a9b-49b5-bde6-58536d1accd2", "0cbc613f-c549-4d92-9d86-e612825f20c0", "92df1a7a-5056-41de-90c0-d037ced3f16b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "535194fe-1d44-4790-a0cd-56962bcb390a", "logId": "1a91fd18-2853-40f4-a37c-10c1d462d4ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "535194fe-1d44-4790-a0cd-56962bcb390a", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179763872500}, "additional": {"logType": "detail", "children": [], "durationId": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d"}}, {"head": {"id": "9521ca6f-9def-4935-aca2-5aa92ad9c5fa", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179764579500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06001c1f-ca53-4756-a403-e0eccaa4b1a0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179764805100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db8d3d4-1b59-43e1-aef8-4a8f1a31ea65", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179764931800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5696bb12-6350-4fa1-96a0-c34d6c0b3c68", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179766388700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb9acba-67b6-4c59-94bc-13da3be9f7b5", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179769444000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a15865-d034-4d3a-940d-f974cd34ddb1", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179812931700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0876194-4294-4ccf-890f-b8ebd1339866", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 43 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179813112700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2336a9e6-1f6b-4023-974f-71e7b47380fa", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179814429400, "endTime": 11179842910600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d", "logId": "070e9376-dd1a-4026-bfc7-1afc70691ef0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "070e9376-dd1a-4026-bfc7-1afc70691ef0", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179814429400, "endTime": 11179842910600}, "additional": {"logType": "info", "children": [], "durationId": "2336a9e6-1f6b-4023-974f-71e7b47380fa", "parent": "1a91fd18-2853-40f4-a37c-10c1d462d4ac"}}, {"head": {"id": "18b2bf02-1413-436e-ac68-1759bdc9fd47", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179843211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0130211-00b2-4649-8dbf-c494db0a8f4b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179844209200, "endTime": 11179976156700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d", "logId": "edcc46fd-8a74-49ef-a822-034d4c7b9982"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00ecb930-82bd-4715-9e33-77de3d7a70a2", "name": "current process  memoryUsage: {\n  rss: 179027968,\n  heapTotal: 130113536,\n  heapUsed: 107490816,\n  external: 3117374,\n  arrayBuffers: 111271\n} os memoryUsage :12.703033447265625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179845095600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59eef487-820c-4ce3-98cf-e283361358da", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179973476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edcc46fd-8a74-49ef-a822-034d4c7b9982", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179844209200, "endTime": 11179976156700}, "additional": {"logType": "info", "children": [], "durationId": "a0130211-00b2-4649-8dbf-c494db0a8f4b", "parent": "1a91fd18-2853-40f4-a37c-10c1d462d4ac"}}, {"head": {"id": "1154e11b-3fc0-44ff-a131-dd0b7bb68f53", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179976278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e2b454e-9a9b-49b5-bde6-58536d1accd2", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179977205000, "endTime": 11180153187700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d", "logId": "416f9179-4726-4e79-8b29-7e7904b563f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7244235e-c2d0-46ce-a2f7-b992934bc47e", "name": "current process  memoryUsage: {\n  rss: 179056640,\n  heapTotal: 130113536,\n  heapUsed: 107816576,\n  external: 3117500,\n  arrayBuffers: 111412\n} os memoryUsage :12.709453582763672", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179978008100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0622309d-e0a8-4851-bbc6-39a0a8a129f7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180149226100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "416f9179-4726-4e79-8b29-7e7904b563f3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179977205000, "endTime": 11180153187700}, "additional": {"logType": "info", "children": [], "durationId": "5e2b454e-9a9b-49b5-bde6-58536d1accd2", "parent": "1a91fd18-2853-40f4-a37c-10c1d462d4ac"}}, {"head": {"id": "027c2246-d1d1-4509-89e3-073253ecb9e4", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180153341900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cbc613f-c549-4d92-9d86-e612825f20c0", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180154424400, "endTime": 11180312677100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d", "logId": "9d4d6fdb-2213-4835-80b0-6a7436738f36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a978da6-b2ec-4130-8a22-8debedd85863", "name": "current process  memoryUsage: {\n  rss: 179073024,\n  heapTotal: 130113536,\n  heapUsed: 108085992,\n  external: 3117626,\n  arrayBuffers: 111602\n} os memoryUsage :12.695411682128906", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180155387300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117b2582-eaa2-47aa-93cd-887f06e62cfa", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180308824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d4d6fdb-2213-4835-80b0-6a7436738f36", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180154424400, "endTime": 11180312677100}, "additional": {"logType": "info", "children": [], "durationId": "0cbc613f-c549-4d92-9d86-e612825f20c0", "parent": "1a91fd18-2853-40f4-a37c-10c1d462d4ac"}}, {"head": {"id": "2d808d2b-54de-4f85-8962-628ddb37e8ee", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180313079500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92df1a7a-5056-41de-90c0-d037ced3f16b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180315234000, "endTime": 11180519114900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d", "logId": "d6a74a63-ace6-45b5-a43d-7db279687ad8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07a7ed30-96e7-4e93-bc09-48f280e62283", "name": "current process  memoryUsage: {\n  rss: 179081216,\n  heapTotal: 130113536,\n  heapUsed: 108401696,\n  external: 3125944,\n  arrayBuffers: 120923\n} os memoryUsage :12.707351684570312", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180316473300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454d1b71-ea49-44d3-883e-a0667d6eea9a", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180515143900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a74a63-ace6-45b5-a43d-7db279687ad8", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180315234000, "endTime": 11180519114900}, "additional": {"logType": "info", "children": [], "durationId": "92df1a7a-5056-41de-90c0-d037ced3f16b", "parent": "1a91fd18-2853-40f4-a37c-10c1d462d4ac"}}, {"head": {"id": "e2764eb4-398d-4e9d-927d-8f195de64fc6", "name": "entry : default@PreviewCompileResource cost memory 1.2182846069335938", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180520387800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab0a171-ac9e-465e-b742-17e6a453436f", "name": "runTaskFromQueue task cost before running: 1 s 72 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180520588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a91fd18-2853-40f4-a37c-10c1d462d4ac", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179769386600, "endTime": 11180520671500, "totalTime": 751141500}, "additional": {"logType": "info", "children": ["070e9376-dd1a-4026-bfc7-1afc70691ef0", "edcc46fd-8a74-49ef-a822-034d4c7b9982", "416f9179-4726-4e79-8b29-7e7904b563f3", "9d4d6fdb-2213-4835-80b0-6a7436738f36", "d6a74a63-ace6-45b5-a43d-7db279687ad8"], "durationId": "978755f2-f2ed-4443-b33b-e7a2de7a0f3d"}}, {"head": {"id": "3d0e75e6-b6c3-4d2f-a2bf-515b2c343aa1", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525021700, "endTime": 11180525750200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "519fc924-ba43-4982-8570-555c257c9fb9", "logId": "37f3a479-6b51-4a26-a8b8-3cd85ed644e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "519fc924-ba43-4982-8570-555c257c9fb9", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180523575600}, "additional": {"logType": "detail", "children": [], "durationId": "3d0e75e6-b6c3-4d2f-a2bf-515b2c343aa1"}}, {"head": {"id": "e3a9f5ca-5b4e-483c-b326-efea398c9b62", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180524560800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "664b486b-f884-48ad-ba47-948af02<PERSON>fe", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180524736900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9360f0b3-4d69-4e78-a7c9-3ca1ad0cb991", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180524849800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eae02a9-a7a0-456b-8797-b1098173b822", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525036600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4150cda3-41d1-4ee7-8132-7e3c7e7235a0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525199000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e608a3-3b44-467d-98fd-f6388a60b07e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525279900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cdb4938-6949-49aa-aa1a-c08549418040", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525391600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a364bd62-6748-48a8-ab3f-6be6ff01bbfd", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525522400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1bbada-0117-47bc-8d16-bbd7f45f07ee", "name": "runTaskFromQueue task cost before running: 1 s 77 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f3a479-6b51-4a26-a8b8-3cd85ed644e3", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180525021700, "endTime": 11180525750200, "totalTime": 604400}, "additional": {"logType": "info", "children": [], "durationId": "3d0e75e6-b6c3-4d2f-a2bf-515b2c343aa1"}}, {"head": {"id": "389cbed5-67c0-42a1-a800-1824a43c0051", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180530736000, "endTime": 11180538695700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "7b7cb57b-0f2a-444f-b233-a311edf05704", "logId": "4c66fc09-c253-4239-9b1f-6e308c981c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b7cb57b-0f2a-444f-b233-a311edf05704", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180528653700}, "additional": {"logType": "detail", "children": [], "durationId": "389cbed5-67c0-42a1-a800-1824a43c0051"}}, {"head": {"id": "12f192d1-ee93-4a0a-8db6-7f2a3f3dc01b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180529455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05395508-03cb-46a6-95ba-9457971f32af", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180529639100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1f5d17d-dad8-4b52-9a73-d1298b4ad274", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180529722800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9847a22-2007-4526-a863-7ca6a5816dcd", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180530762000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f2c68b5-7ec1-41b0-90f7-07e55b9c8022", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180532968300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e840dff4-d6ff-4e5a-b5b2-26f134562cbb", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180533218900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91dd04d8-e45f-4d94-8e87-4966d8e47faa", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180533371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6da335-9e2d-47a9-9a98-33dfdb26cb47", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180533440500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399a09eb-a70a-4b4d-90e8-9707c3a71f4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180533490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "021ea61a-679b-444c-9882-d487afda5e8f", "name": "entry : default@CopyPreviewProfile cost memory 0.258087158203125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180538448800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fc6c2b3-d28d-4cc1-a6ce-83dbbf2ff1e7", "name": "runTaskFromQueue task cost before running: 1 s 90 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180538615700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c66fc09-c253-4239-9b1f-6e308c981c3f", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180530736000, "endTime": 11180538695700, "totalTime": 7846500}, "additional": {"logType": "info", "children": [], "durationId": "389cbed5-67c0-42a1-a800-1824a43c0051"}}, {"head": {"id": "fcbcf2d4-a107-4edf-b4d4-329414b84701", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543372800, "endTime": 11180543899800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "b0d4e0c0-c3f0-483b-ac60-6a48530de9e4", "logId": "bed4e21e-0d3c-4f99-8801-9c1c24d1a4a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0d4e0c0-c3f0-483b-ac60-6a48530de9e4", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180541434100}, "additional": {"logType": "detail", "children": [], "durationId": "fcbcf2d4-a107-4edf-b4d4-329414b84701"}}, {"head": {"id": "f4dbd15d-0352-4888-8655-e7ced27f0995", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180542016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031fb70a-2f23-46e3-b2ff-49fb77d2d537", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180542210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de9ef97-80e4-4d1a-b157-93d184ed12bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180542330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea7c92dd-e9fb-4e9c-8b13-ca02b82ea72f", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543386200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a67285-c0e4-4cd9-afb4-a3956e64c47c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543513900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e51e68-00de-40af-94c1-822794958a23", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543583900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "130589b8-3ce6-4b30-ada0-614deeecc322", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ac94f11-f1fd-486a-975b-2288ee3f19f0", "name": "entry : default@ReplacePreviewerPage cost memory 0.05261993408203125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543745900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d04a86a-7e32-4709-878c-dc3c445b2e4d", "name": "runTaskFromQueue task cost before running: 1 s 95 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543841700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bed4e21e-0d3c-4f99-8801-9c1c24d1a4a6", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180543372800, "endTime": 11180543899800, "totalTime": 438900}, "additional": {"logType": "info", "children": [], "durationId": "fcbcf2d4-a107-4edf-b4d4-329414b84701"}}, {"head": {"id": "f5073d3a-73a0-421c-be81-06a654168fe6", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180545622800, "endTime": 11180545893500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2a5bbc8a-c155-47e7-a4cd-62b59c7e3a86", "logId": "f67b926d-7805-47a0-b424-fc5fdd77db31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a5bbc8a-c155-47e7-a4cd-62b59c7e3a86", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180545563400}, "additional": {"logType": "detail", "children": [], "durationId": "f5073d3a-73a0-421c-be81-06a654168fe6"}}, {"head": {"id": "7bff92dc-b0ff-4113-ab0d-e88a8709319b", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180545633100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3fc14f-76b2-4881-9799-b0d2f344ac9f", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180545752800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f804f1-0f1f-4874-b1aa-a002a27a70e3", "name": "runTaskFromQueue task cost before running: 1 s 97 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180545837900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f67b926d-7805-47a0-b424-fc5fdd77db31", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180545622800, "endTime": 11180545893500, "totalTime": 192100}, "additional": {"logType": "info", "children": [], "durationId": "f5073d3a-73a0-421c-be81-06a654168fe6"}}, {"head": {"id": "89d27e09-b5c0-4017-b4e7-1c0f16126132", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180549460400, "endTime": 11180552643100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "e95e0cbf-6d68-448b-85bd-240631614fdc", "logId": "5a594dc7-609c-4d89-af05-31ce4aa52dc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e95e0cbf-6d68-448b-85bd-240631614fdc", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180547491300}, "additional": {"logType": "detail", "children": [], "durationId": "89d27e09-b5c0-4017-b4e7-1c0f16126132"}}, {"head": {"id": "cbb4052f-a783-4a30-888f-b66c0225ee5e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180548065600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c657c8a-d66d-47f0-9359-d1d770d6f6ea", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180548213700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a31e11b3-df55-4203-8e94-727f50e6ee69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180548353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0420c36-ac2c-4a53-870c-b4dfdcc05d62", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180549475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c75cbb24-16be-4495-8c04-cb9653b46fce", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180551322900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f3d00c-5eb9-4651-b69e-86f6807b5fdf", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180551447900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b07e601-aff3-453d-a78e-641fda524dd8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180551538700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f20851a-d044-4972-b36d-c6ffb0264a4a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180551592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d29ee4-9875-4c69-9af8-c566f20cd859", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180551639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de46b9fe-064e-4560-806f-a59f4b256be3", "name": "entry : default@PreviewUpdateAssets cost memory 0.15465545654296875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180552473300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf34814a-8c6a-4ac7-816c-905b48359c42", "name": "runTaskFromQueue task cost before running: 1 s 104 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180552582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a594dc7-609c-4d89-af05-31ce4aa52dc6", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180549460400, "endTime": 11180552643100, "totalTime": 3098500}, "additional": {"logType": "info", "children": [], "durationId": "89d27e09-b5c0-4017-b4e7-1c0f16126132"}}, {"head": {"id": "47001ad4-b436-4619-8896-dc149989e051", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180561975800, "endTime": 11193809570000}, "additional": {"children": ["7d8e3bad-19e1-4043-ba5d-85f364bae987"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "57ab741c-ed8b-4790-ae26-fc632002dde7", "logId": "334dd3a7-3e2f-47a7-a8b7-a05f26851de9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57ab741c-ed8b-4790-ae26-fc632002dde7", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180555588400}, "additional": {"logType": "detail", "children": [], "durationId": "47001ad4-b436-4619-8896-dc149989e051"}}, {"head": {"id": "3bc531c7-7cfb-4984-b126-0e9be6783ce3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180556133300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6282011b-6de3-46c1-9ca7-a05c13971585", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180556291100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254a2138-7ea3-4cfa-a11d-26df9e0bfc33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180556385700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46378b4-2fd4-4c3f-9222-c9ffd2bf5b2d", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180561993700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11180589677100, "endTime": 11193806151900}, "additional": {"children": ["d0352f8a-578b-4828-9665-40ba3a658756", "b6276f31-6543-4cf5-a88f-32f2849d8198", "59587698-c2aa-4a97-8fa5-98229313b7f4", "c5cfb63c-72c0-421d-a49e-d5e7d6d17fbc", "4a256306-3099-4472-bbf8-583483e319b4", "68de7aa6-ae06-4df8-b322-1d62d1f007f0", "3a6ab2c8-5129-4f61-9858-9c9016ce88fd", "b18b6c9f-20a5-4744-8768-24c8d86ef31b", "92148856-36b5-443f-af68-4d19199de39d", "cd96126c-4dab-4424-9526-3450ce9927f8", "0f308ee3-3d27-4597-b86f-4bc88663981e", "59d613ea-dd49-4769-8527-01f89d0a11c8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "47001ad4-b436-4619-8896-dc149989e051", "logId": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "949fa2be-1445-4024-8588-01de6420552d", "name": "entry : default@PreviewArkTS cost memory 1.2291793823242188", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180593491600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e6a7b7-58e9-409d-972f-fffd9595af44", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11184515959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0352f8a-578b-4828-9665-40ba3a658756", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11184517714600, "endTime": 11184517750700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "91cb12e3-3b78-4e88-9b6f-e145eb8b71a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91cb12e3-3b78-4e88-9b6f-e145eb8b71a4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11184517714600, "endTime": 11184517750700}, "additional": {"logType": "info", "children": [], "durationId": "d0352f8a-578b-4828-9665-40ba3a658756", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "6fc7540d-38f5-49ac-8cfa-8626f9bddc3e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190177756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6276f31-6543-4cf5-a88f-32f2849d8198", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11190178807600, "endTime": 11190178827600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "312ba2f8-2365-432d-9421-5432a1aa1226"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "312ba2f8-2365-432d-9421-5432a1aa1226", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190178807600, "endTime": 11190178827600}, "additional": {"logType": "info", "children": [], "durationId": "b6276f31-6543-4cf5-a88f-32f2849d8198", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "807eb856-d7fa-4f15-bd52-23785a150cb1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190178900600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59587698-c2aa-4a97-8fa5-98229313b7f4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11190179648200, "endTime": 11190179662100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "655d46ff-1856-4465-9a49-a942099c57ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "655d46ff-1856-4465-9a49-a942099c57ef", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190179648200, "endTime": 11190179662100}, "additional": {"logType": "info", "children": [], "durationId": "59587698-c2aa-4a97-8fa5-98229313b7f4", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "652b0754-5400-40b5-9e3d-b3b12f04023c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190443324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cfb63c-72c0-421d-a49e-d5e7d6d17fbc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11190444545100, "endTime": 11190444568500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "630402b9-f28c-40f2-8a38-00dff60b97a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "630402b9-f28c-40f2-8a38-00dff60b97a8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190444545100, "endTime": 11190444568500}, "additional": {"logType": "info", "children": [], "durationId": "c5cfb63c-72c0-421d-a49e-d5e7d6d17fbc", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "b9b79bf7-9daa-4f07-b1f1-a94c33399d7f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190757616300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a256306-3099-4472-bbf8-583483e319b4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11190758869900, "endTime": 11190758893300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "fce078fc-1f6c-4b7f-ab62-f6779cd2b997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fce078fc-1f6c-4b7f-ab62-f6779cd2b997", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190758869900, "endTime": 11190758893300}, "additional": {"logType": "info", "children": [], "durationId": "4a256306-3099-4472-bbf8-583483e319b4", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "5f70f8e3-9e5f-42a7-8a75-07e81e31311e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190884209600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68de7aa6-ae06-4df8-b322-1d62d1f007f0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11190885746300, "endTime": 11190885774000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "9666331f-b37e-42db-a2b6-4ce27d7086cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9666331f-b37e-42db-a2b6-4ce27d7086cb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190885746300, "endTime": 11190885774000}, "additional": {"logType": "info", "children": [], "durationId": "68de7aa6-ae06-4df8-b322-1d62d1f007f0", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "d578061a-82bc-46c7-8c7c-5bab1c401889", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190997697000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6ab2c8-5129-4f61-9858-9c9016ce88fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11190999441700, "endTime": 11190999467200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "4ea5fcfe-44af-4ef3-9886-6acae5c95c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ea5fcfe-44af-4ef3-9886-6acae5c95c1b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11190999441700, "endTime": 11190999467200}, "additional": {"logType": "info", "children": [], "durationId": "3a6ab2c8-5129-4f61-9858-9c9016ce88fd", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "6aa7b3fe-f201-47d6-aad0-6d84685d8a26", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11191117194000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18b6c9f-20a5-4744-8768-24c8d86ef31b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11191118564800, "endTime": 11191118595100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "fb54da45-648e-43c7-882a-6b1a13ce779c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb54da45-648e-43c7-882a-6b1a13ce779c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11191118564800, "endTime": 11191118595100}, "additional": {"logType": "info", "children": [], "durationId": "b18b6c9f-20a5-4744-8768-24c8d86ef31b", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "09f3baac-a1de-4069-80e2-77f907c37cc1", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193804133700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92148856-36b5-443f-af68-4d19199de39d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11193805700500, "endTime": 11193805747500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "bf8c548e-baf8-4a70-b3b4-d2aaa47cf889"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf8c548e-baf8-4a70-b3b4-d2aaa47cf889", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193805700500, "endTime": 11193805747500}, "additional": {"logType": "info", "children": [], "durationId": "92148856-36b5-443f-af68-4d19199de39d", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11180589677100, "endTime": 11193806151900}, "additional": {"logType": "info", "children": ["91cb12e3-3b78-4e88-9b6f-e145eb8b71a4", "312ba2f8-2365-432d-9421-5432a1aa1226", "655d46ff-1856-4465-9a49-a942099c57ef", "630402b9-f28c-40f2-8a38-00dff60b97a8", "fce078fc-1f6c-4b7f-ab62-f6779cd2b997", "9666331f-b37e-42db-a2b6-4ce27d7086cb", "4ea5fcfe-44af-4ef3-9886-6acae5c95c1b", "fb54da45-648e-43c7-882a-6b1a13ce779c", "bf8c548e-baf8-4a70-b3b4-d2aaa47cf889", "06003cba-3cc5-4bee-8ad6-63839606888f", "837d6d01-d41b-4839-9cfa-78860511f150", "15fdc79e-869b-451e-ad71-e92f49994f2c"], "durationId": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "parent": "334dd3a7-3e2f-47a7-a8b7-a05f26851de9"}}, {"head": {"id": "cd96126c-4dab-4424-9526-3450ce9927f8", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11183258703000, "endTime": 11184484010900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "06003cba-3cc5-4bee-8ad6-63839606888f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06003cba-3cc5-4bee-8ad6-63839606888f", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11183258703000, "endTime": 11184484010900}, "additional": {"logType": "info", "children": [], "durationId": "cd96126c-4dab-4424-9526-3450ce9927f8", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "0f308ee3-3d27-4597-b86f-4bc88663981e", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11184484239800, "endTime": 11184484425100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "837d6d01-d41b-4839-9cfa-78860511f150"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "837d6d01-d41b-4839-9cfa-78860511f150", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11184484239800, "endTime": 11184484425100}, "additional": {"logType": "info", "children": [], "durationId": "0f308ee3-3d27-4597-b86f-4bc88663981e", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "59d613ea-dd49-4769-8527-01f89d0a11c8", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker8", "startTime": 11184484555700, "endTime": 11193804348300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7d8e3bad-19e1-4043-ba5d-85f364bae987", "logId": "15fdc79e-869b-451e-ad71-e92f49994f2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15fdc79e-869b-451e-ad71-e92f49994f2c", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11184484555700, "endTime": 11193804348300}, "additional": {"logType": "info", "children": [], "durationId": "59d613ea-dd49-4769-8527-01f89d0a11c8", "parent": "fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"}}, {"head": {"id": "334dd3a7-3e2f-47a7-a8b7-a05f26851de9", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11180561975800, "endTime": 11193809570000, "totalTime": 13247548000}, "additional": {"logType": "info", "children": ["fd5b7a33-5a57-48e6-8343-2dbf36a9d7b7"], "durationId": "47001ad4-b436-4619-8896-dc149989e051"}}, {"head": {"id": "bc0bea38-05e5-47a2-8f31-896bab18b3cb", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193816182400, "endTime": 11193816561000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2a2ad71a-7467-4ed0-b1db-258b81202265", "logId": "efd1186d-4a68-4a14-be87-8c6198481511"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a2ad71a-7467-4ed0-b1db-258b81202265", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193816113300}, "additional": {"logType": "detail", "children": [], "durationId": "bc0bea38-05e5-47a2-8f31-896bab18b3cb"}}, {"head": {"id": "f48c2918-cd72-4950-b002-8ba2f7d96051", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193816196500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b1c476d-cda2-4ee8-b0ae-8a89c3812443", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193816360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7bc1795-ca65-44a4-a473-6e0bf09629e7", "name": "runTaskFromQueue task cost before running: 14 s 368 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193816490900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efd1186d-4a68-4a14-be87-8c6198481511", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193816182400, "endTime": 11193816561000, "totalTime": 280200}, "additional": {"logType": "info", "children": [], "durationId": "bc0bea38-05e5-47a2-8f31-896bab18b3cb"}}, {"head": {"id": "703a2666-3501-41ac-bbe2-f858878bc6ed", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193827983800, "endTime": 11193828010500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2b56aa4-612a-4154-a88f-7acddb151efd", "logId": "42b6e7c5-39e8-4421-9dd2-38d201a1d2e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42b6e7c5-39e8-4421-9dd2-38d201a1d2e9", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193827983800, "endTime": 11193828010500}, "additional": {"logType": "info", "children": [], "durationId": "703a2666-3501-41ac-bbe2-f858878bc6ed"}}, {"head": {"id": "509524c2-7d8a-4b4d-a231-b021186340be", "name": "BUILD SUCCESSFUL in 14 s 380 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828126100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "244cc981-ea1c-4964-83fe-65157d7c1074", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11179448847300, "endTime": 11193828481100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 45}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "e60a8770-a522-49c6-b87f-058e2fb706f4", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebc57fa3-682b-4330-8219-190afd9aa80f", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828636100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efaf2825-1662-46ad-9fb7-2e43233ad3a6", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828690100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "954acb11-2d06-4aa6-a43a-5b2b46ebbfc2", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828742500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "987762e8-2374-4d27-a80a-72091afd26c1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828792500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27fb7358-8d3e-432f-bba3-b71223425792", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8abd941-08bf-4027-97d5-e5575a304f50", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193828888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc774626-a06b-45bb-a565-4a9286c7fe84", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193829913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "130ecfd7-ef63-4ca7-a7c6-98557351eb8c", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193845572700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b63bd464-049e-4af9-a6d1-aa30be84add0", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193848437800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada816aa-2b83-4578-8bd1-3f04911db8d9", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193848886300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27416aaf-e6e3-474d-8e86-326b97f7a0e4", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193871036000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f91e753-013a-4aa3-9cbb-6f7b14bf2e7e", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:43 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193871781900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f771a4-67df-46a5-98ce-c900a04d3558", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193872123800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1257ed63-96c0-4231-9591-a75724a956a3", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193873535800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8e1696-d148-4abb-93f0-69c5efacc5d3", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193874500000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8186cf6b-7bd0-488a-8e9f-303ef6bdad2f", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193875077800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6b7de68-2baa-48f6-8c7b-f773ff40bba3", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193875413700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b947613f-4c61-483e-b0e1-0bc8c565fd42", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193875760000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dc19cd5-0a49-4efc-9905-4c512f2307e1", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193878797600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7a799b-423a-459a-887e-36925f583f0d", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193879996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b15ac5c6-d80d-40b6-877b-abac8d88bb65", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193880447100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42164e33-a910-4e35-9658-93708912b331", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193896845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbcdfc16-6e4e-4ebe-b998-39fe99dee211", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193898420100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253cb9bf-610e-4005-a344-d7d5c82c867f", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193898809900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd68a9bf-0637-43a3-b6b3-ee315595d921", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193899170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2fd897d-f0e3-4e82-924d-05be38f3b42b", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193900131600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35daad1c-d630-403c-a1a1-121093bd8c0a", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193903837800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29c799a6-aff3-435e-b067-677a018732a6", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193904170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ff0fff8-51f5-41e2-84d3-b1246d37a423", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193904490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab53b54-88f7-4b9d-9e76-8d7056f2a3d5", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193905459000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cfc7c67-493d-4c50-b3b6-07dea07ee5e8", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:31 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11193906105900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}