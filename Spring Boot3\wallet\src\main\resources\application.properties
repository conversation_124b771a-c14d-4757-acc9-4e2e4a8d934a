spring.application.name=E-Wallet
server.port=8091

spring.datasource.url=**************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=123456

mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.type-aliases-package= com.icss.wallet.entity;
mybatis-plus.mapper-locations=classpath:mapper/*.xml

# Token
token.secret=bank-management-secret-key
token.expire-time=******** 