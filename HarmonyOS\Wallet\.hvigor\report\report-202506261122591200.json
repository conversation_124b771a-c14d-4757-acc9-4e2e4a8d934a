{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "82391643-9a0d-4bd7-8425-0c19821f7f1e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838708072200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae88c5f-6e64-4229-8799-8432f8df62b3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838711000600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d799b60-6995-44db-8017-0819509036fd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838711429900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5601607-cff9-490f-8877-88b3380bb2e3", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838712411400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e84e4089-6852-4d7f-a31f-b257ac265d01", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838713011300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7580b58-f260-4f7c-b0e0-8164166b2c1a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838981369600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e625fb-567b-47d3-be91-603e909be99b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838995797800, "endTime": 9839215353300}, "additional": {"children": ["9b50f3e0-220b-4be7-bf17-f7102612d5ae", "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "bc56fdb7-8110-4ecb-bc6e-fe5e6bf3fdd3", "dc36e935-5c05-4a86-b793-a052ac3f76e4", "12cf4d6d-b1ac-4f82-99b8-1243fb58f893", "7c3a340d-04c3-4551-89a5-50b366a4ef38", "1d642f2a-e874-4817-8dcd-46410f259518"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b50f3e0-220b-4be7-bf17-f7102612d5ae", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838995800700, "endTime": 9839015519200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "791faa31-82f3-4fb1-b14c-e0dc1dfe0040"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839015556200, "endTime": 9839214181800}, "additional": {"children": ["baaa5b2e-c9c4-4731-ad93-c7bb91860942", "02cd56da-687d-4575-a09a-7861796c85c4", "d63dca62-0609-45ec-aef2-693957492b1f", "94cccaae-a7b4-40da-bae8-cf0e2fb9450f", "a216beed-427b-4404-86da-dea149606348", "f845226e-f9de-463e-8170-9120535c8760", "87cc19d0-01e0-4b7f-896a-42c705c0175e", "961460ac-0de5-4296-bc32-06d546835788", "d587999c-17b7-4b46-9911-b11d62151c01"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc56fdb7-8110-4ecb-bc6e-fe5e6bf3fdd3", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839214206000, "endTime": 9839215337900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "1c8acb28-555b-491a-aa5a-da34622830e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc36e935-5c05-4a86-b793-a052ac3f76e4", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839215342900, "endTime": 9839215348500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "f7a1a611-8e85-4c47-95f6-0d5284608940"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12cf4d6d-b1ac-4f82-99b8-1243fb58f893", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839001363800, "endTime": 9839001441200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "daedbd3d-e2e6-4c75-b0e7-a1a3714596f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daedbd3d-e2e6-4c75-b0e7-a1a3714596f4", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839001363800, "endTime": 9839001441200}, "additional": {"logType": "info", "children": [], "durationId": "12cf4d6d-b1ac-4f82-99b8-1243fb58f893", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "7c3a340d-04c3-4551-89a5-50b366a4ef38", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839009305100, "endTime": 9839009344600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "6d550b97-fc00-45f5-b366-d97f07da1579"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d550b97-fc00-45f5-b366-d97f07da1579", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839009305100, "endTime": 9839009344600}, "additional": {"logType": "info", "children": [], "durationId": "7c3a340d-04c3-4551-89a5-50b366a4ef38", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "b6c4874f-28d8-4779-bfd9-dd0d115811be", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839009480300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60cf5e33-2852-4268-80e0-392b3b0aadf0", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839015270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "791faa31-82f3-4fb1-b14c-e0dc1dfe0040", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838995800700, "endTime": 9839015519200}, "additional": {"logType": "info", "children": [], "durationId": "9b50f3e0-220b-4be7-bf17-f7102612d5ae", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "baaa5b2e-c9c4-4731-ad93-c7bb91860942", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839025996000, "endTime": 9839026021100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "56797c58-b3c2-4e42-8cb2-eb88cd46792d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02cd56da-687d-4575-a09a-7861796c85c4", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839026052400, "endTime": 9839032712500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "b8908d0c-2f9c-4fbc-94e1-d29473a708cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d63dca62-0609-45ec-aef2-693957492b1f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839032770200, "endTime": 9839139593400}, "additional": {"children": ["2036edeb-2122-4f1d-81f0-88f7bfd3cd2f", "972bab2d-7d27-4025-b094-c922ef8781c6", "a175b2d0-3ad1-48af-824a-1ae22b2f83c2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "0a145faa-eea3-4685-ae57-333e15694c57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94cccaae-a7b4-40da-bae8-cf0e2fb9450f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839139619200, "endTime": 9839172320300}, "additional": {"children": ["3b251fad-a185-4bf0-95a1-41759a36bc12"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "21ac7042-f626-4596-a8a2-56e5eb54529c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a216beed-427b-4404-86da-dea149606348", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839172333800, "endTime": 9839188221000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "2601ee29-83cb-4fcd-9f39-078c107d09bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f845226e-f9de-463e-8170-9120535c8760", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839189595400, "endTime": 9839199442000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "28a82bea-fc1c-4cb3-9730-c8e076087e33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87cc19d0-01e0-4b7f-896a-42c705c0175e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839199503300, "endTime": 9839214039600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "c1825dd8-4f66-41b1-8568-8380f6070350"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "961460ac-0de5-4296-bc32-06d546835788", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839214057100, "endTime": 9839214169500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "f0257f83-21e4-4bcc-b3ed-0ca34a0660d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56797c58-b3c2-4e42-8cb2-eb88cd46792d", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839025996000, "endTime": 9839026021100}, "additional": {"logType": "info", "children": [], "durationId": "baaa5b2e-c9c4-4731-ad93-c7bb91860942", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "b8908d0c-2f9c-4fbc-94e1-d29473a708cc", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839026052400, "endTime": 9839032712500}, "additional": {"logType": "info", "children": [], "durationId": "02cd56da-687d-4575-a09a-7861796c85c4", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "2036edeb-2122-4f1d-81f0-88f7bfd3cd2f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839034014300, "endTime": 9839034067000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d63dca62-0609-45ec-aef2-693957492b1f", "logId": "6a413383-66c0-4b5b-9f1e-dae49d410386"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a413383-66c0-4b5b-9f1e-dae49d410386", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839034014300, "endTime": 9839034067000}, "additional": {"logType": "info", "children": [], "durationId": "2036edeb-2122-4f1d-81f0-88f7bfd3cd2f", "parent": "0a145faa-eea3-4685-ae57-333e15694c57"}}, {"head": {"id": "972bab2d-7d27-4025-b094-c922ef8781c6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839037829500, "endTime": 9839138435000}, "additional": {"children": ["2101c197-5a91-4b04-9a45-e6e479de020d", "d60bce0a-39e3-466e-a0bf-e4adaa929cfb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d63dca62-0609-45ec-aef2-693957492b1f", "logId": "a251d446-0202-41dc-a0f3-f842d31565b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2101c197-5a91-4b04-9a45-e6e479de020d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839037831700, "endTime": 9839046806200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "972bab2d-7d27-4025-b094-c922ef8781c6", "logId": "087faaf1-1873-4dd7-ab22-cdc1d6c60319"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d60bce0a-39e3-466e-a0bf-e4adaa929cfb", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839046847200, "endTime": 9839138421300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "972bab2d-7d27-4025-b094-c922ef8781c6", "logId": "7432de24-6605-4004-9d05-d239801d80a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e9f232d-94fc-4d21-b17f-aa8a4ce7c1e5", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839037849100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4781e1c-2b8e-478f-80ae-5b1806f327fd", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839046564600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "087faaf1-1873-4dd7-ab22-cdc1d6c60319", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839037831700, "endTime": 9839046806200}, "additional": {"logType": "info", "children": [], "durationId": "2101c197-5a91-4b04-9a45-e6e479de020d", "parent": "a251d446-0202-41dc-a0f3-f842d31565b8"}}, {"head": {"id": "1937f398-29bc-4afc-8e3a-a4303f00a250", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839046883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6264b9f-17c4-4c36-9bcd-9f3fea1c47ba", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839060361700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86b13a7b-6dd7-488a-9f7f-2a3724bc3bd2", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839060562600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da2aba29-151c-4623-8439-4ec1565ae402", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839061066100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afbdcc3c-aba1-4be2-8025-cd4d7c1be53c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839061498000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df4511e1-9d59-483e-8325-2748febadfc4", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839064295000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4404d3fa-64ca-4e31-ab85-ed99bd699222", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839070542300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b9f850-cde7-4564-a18d-46433f45cfe9", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839081779100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "887971f0-a967-483a-a1d9-6737497ba75d", "name": "Sdk init in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839113382900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ae0138-6195-44c9-9c16-b5b2376f9e86", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839113581100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 22}, "markType": "other"}}, {"head": {"id": "c19326ce-892c-4ed8-a48f-280278b1fb86", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839113604200}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 22}, "markType": "other"}}, {"head": {"id": "97c64610-0080-43a0-ae90-073416601e19", "name": "Project task initialization takes 23 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839138081600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe42572-9fe1-4b0b-932b-0f1fdd468e6f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839138237800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b233986-29e2-4b44-baba-394c0f74f991", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839138315700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6090931f-d850-41d2-a542-2e43c9145851", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839138372700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7432de24-6605-4004-9d05-d239801d80a8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839046847200, "endTime": 9839138421300}, "additional": {"logType": "info", "children": [], "durationId": "d60bce0a-39e3-466e-a0bf-e4adaa929cfb", "parent": "a251d446-0202-41dc-a0f3-f842d31565b8"}}, {"head": {"id": "a251d446-0202-41dc-a0f3-f842d31565b8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839037829500, "endTime": 9839138435000}, "additional": {"logType": "info", "children": ["087faaf1-1873-4dd7-ab22-cdc1d6c60319", "7432de24-6605-4004-9d05-d239801d80a8"], "durationId": "972bab2d-7d27-4025-b094-c922ef8781c6", "parent": "0a145faa-eea3-4685-ae57-333e15694c57"}}, {"head": {"id": "a175b2d0-3ad1-48af-824a-1ae22b2f83c2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839139290800, "endTime": 9839139557000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d63dca62-0609-45ec-aef2-693957492b1f", "logId": "072e4265-53e8-47d2-80e0-5ea84e09e6bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "072e4265-53e8-47d2-80e0-5ea84e09e6bf", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839139290800, "endTime": 9839139557000}, "additional": {"logType": "info", "children": [], "durationId": "a175b2d0-3ad1-48af-824a-1ae22b2f83c2", "parent": "0a145faa-eea3-4685-ae57-333e15694c57"}}, {"head": {"id": "0a145faa-eea3-4685-ae57-333e15694c57", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839032770200, "endTime": 9839139593400}, "additional": {"logType": "info", "children": ["6a413383-66c0-4b5b-9f1e-dae49d410386", "a251d446-0202-41dc-a0f3-f842d31565b8", "072e4265-53e8-47d2-80e0-5ea84e09e6bf"], "durationId": "d63dca62-0609-45ec-aef2-693957492b1f", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "3b251fad-a185-4bf0-95a1-41759a36bc12", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839140831400, "endTime": 9839172292600}, "additional": {"children": ["ec3b971d-814f-4ef2-9035-1355a4a7cf96", "f28432f8-0051-4c5f-9861-25ccd4b3b6fa", "6d70e483-2af9-46bc-994e-813eb287ee72"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "94cccaae-a7b4-40da-bae8-cf0e2fb9450f", "logId": "e3386b88-78e9-4cb8-912e-5d0e349b787a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec3b971d-814f-4ef2-9035-1355a4a7cf96", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839145410500, "endTime": 9839145452500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b251fad-a185-4bf0-95a1-41759a36bc12", "logId": "a6c03389-14f4-4a81-bed1-abda5573fe70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6c03389-14f4-4a81-bed1-abda5573fe70", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839145410500, "endTime": 9839145452500}, "additional": {"logType": "info", "children": [], "durationId": "ec3b971d-814f-4ef2-9035-1355a4a7cf96", "parent": "e3386b88-78e9-4cb8-912e-5d0e349b787a"}}, {"head": {"id": "f28432f8-0051-4c5f-9861-25ccd4b3b6fa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839147980200, "endTime": 9839170412600}, "additional": {"children": ["7620dac7-4397-461b-a284-e75c0214d3dc", "fd873442-10a9-4f5d-9649-e02b32e77a15"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b251fad-a185-4bf0-95a1-41759a36bc12", "logId": "47090030-30cf-49f7-a556-3880cad9d997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7620dac7-4397-461b-a284-e75c0214d3dc", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839147981500, "endTime": 9839154395600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f28432f8-0051-4c5f-9861-25ccd4b3b6fa", "logId": "2e28a480-a3a0-42d5-b064-8b26abd277f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd873442-10a9-4f5d-9649-e02b32e77a15", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839154432700, "endTime": 9839170389800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f28432f8-0051-4c5f-9861-25ccd4b3b6fa", "logId": "c7b0102a-3a4f-4e0e-bb7f-229f5a8b6234"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d21810e-324e-4f80-8441-4ef5e15f56b0", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839148051600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c39281-c8b8-40bc-92ef-13afe1035444", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839154161500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e28a480-a3a0-42d5-b064-8b26abd277f7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839147981500, "endTime": 9839154395600}, "additional": {"logType": "info", "children": [], "durationId": "7620dac7-4397-461b-a284-e75c0214d3dc", "parent": "47090030-30cf-49f7-a556-3880cad9d997"}}, {"head": {"id": "40da340b-e718-444f-a506-87205906f149", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839154464900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5be5e1e-ce3d-4201-a1bd-6c64ef4bfba2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839164348100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b4951e-834d-44bd-a251-4b632d7a9aa4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839164557200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "741d8155-32c3-4f9b-b520-8a79ac440cff", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839165087600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18131a1c-f65f-4722-8e41-170b20044e89", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839165295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5e1a71d-8350-402a-9a59-9f38de177ed8", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839165382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afe5842-7d30-48f8-8290-6266278028cb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839165444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218f59a8-377f-41f8-ac71-8b1ac4ba3093", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839165509700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d268ab-5023-4830-a5da-4bd667306291", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839169918200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca969f7-6c98-40d2-91b6-b4<PERSON>e90bd6c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839170160600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf6af29-21cb-4cbe-9550-b4d5fb5d6809", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839170257200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6033b661-7f0a-4886-bd49-9b50ab8ae571", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839170327500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b0102a-3a4f-4e0e-bb7f-229f5a8b6234", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839154432700, "endTime": 9839170389800}, "additional": {"logType": "info", "children": [], "durationId": "fd873442-10a9-4f5d-9649-e02b32e77a15", "parent": "47090030-30cf-49f7-a556-3880cad9d997"}}, {"head": {"id": "47090030-30cf-49f7-a556-3880cad9d997", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839147980200, "endTime": 9839170412600}, "additional": {"logType": "info", "children": ["2e28a480-a3a0-42d5-b064-8b26abd277f7", "c7b0102a-3a4f-4e0e-bb7f-229f5a8b6234"], "durationId": "f28432f8-0051-4c5f-9861-25ccd4b3b6fa", "parent": "e3386b88-78e9-4cb8-912e-5d0e349b787a"}}, {"head": {"id": "6d70e483-2af9-46bc-994e-813eb287ee72", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839172224300, "endTime": 9839172257600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b251fad-a185-4bf0-95a1-41759a36bc12", "logId": "1dc767ae-4012-4193-b0fc-eafb870d560e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dc767ae-4012-4193-b0fc-eafb870d560e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839172224300, "endTime": 9839172257600}, "additional": {"logType": "info", "children": [], "durationId": "6d70e483-2af9-46bc-994e-813eb287ee72", "parent": "e3386b88-78e9-4cb8-912e-5d0e349b787a"}}, {"head": {"id": "e3386b88-78e9-4cb8-912e-5d0e349b787a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839140831400, "endTime": 9839172292600}, "additional": {"logType": "info", "children": ["a6c03389-14f4-4a81-bed1-abda5573fe70", "47090030-30cf-49f7-a556-3880cad9d997", "1dc767ae-4012-4193-b0fc-eafb870d560e"], "durationId": "3b251fad-a185-4bf0-95a1-41759a36bc12", "parent": "21ac7042-f626-4596-a8a2-56e5eb54529c"}}, {"head": {"id": "21ac7042-f626-4596-a8a2-56e5eb54529c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839139619200, "endTime": 9839172320300}, "additional": {"logType": "info", "children": ["e3386b88-78e9-4cb8-912e-5d0e349b787a"], "durationId": "94cccaae-a7b4-40da-bae8-cf0e2fb9450f", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "2ee20bc8-db9a-422d-95bc-eae92ffd5e85", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839187579700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbb67b8b-6dfb-41fd-9301-a36e360bdc94", "name": "hvigorfile, resolve hvigorfile dependencies in 16 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839188058800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2601ee29-83cb-4fcd-9f39-078c107d09bc", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839172333800, "endTime": 9839188221000}, "additional": {"logType": "info", "children": [], "durationId": "a216beed-427b-4404-86da-dea149606348", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "d587999c-17b7-4b46-9911-b11d62151c01", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839189302700, "endTime": 9839189574100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "logId": "c39ebdcb-6de0-4220-90ee-5020168f46ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e638e3d-f0e9-4337-b9be-cc15a72c7506", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839189345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39ebdcb-6de0-4220-90ee-5020168f46ff", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839189302700, "endTime": 9839189574100}, "additional": {"logType": "info", "children": [], "durationId": "d587999c-17b7-4b46-9911-b11d62151c01", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "f3e26e69-7722-4524-ba3c-e56645499a1d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839191898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e85a8a-8f31-40a0-b7c8-081f6d84a4a1", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839198574000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28a82bea-fc1c-4cb3-9730-c8e076087e33", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839189595400, "endTime": 9839199442000}, "additional": {"logType": "info", "children": [], "durationId": "f845226e-f9de-463e-8170-9120535c8760", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "23c79e89-b3e8-40b4-858a-d106098a34b9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839199531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f55555f1-8f47-414c-b937-5a0a6972ef1f", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839205259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2576e3c8-c033-4fd7-a2c7-f4b76b8cda85", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839205382400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3f7e9f-2193-46d3-9fb9-1bbae9699a9b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839205631500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36f02341-7a9b-40df-b9c7-8d200df2ffd6", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839211051100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10592580-efbe-4673-a558-1a13a44d16c8", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839211194400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1825dd8-4f66-41b1-8568-8380f6070350", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839199503300, "endTime": 9839214039600}, "additional": {"logType": "info", "children": [], "durationId": "87cc19d0-01e0-4b7f-896a-42c705c0175e", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "cc6e975e-c5ce-45e4-bfba-2b06105364bd", "name": "Configuration phase cost:189 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839214087000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0257f83-21e4-4bcc-b3ed-0ca34a0660d5", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839214057100, "endTime": 9839214169500}, "additional": {"logType": "info", "children": [], "durationId": "961460ac-0de5-4296-bc32-06d546835788", "parent": "ccb6cbbf-e149-4e34-a887-4633f92e91a2"}}, {"head": {"id": "ccb6cbbf-e149-4e34-a887-4633f92e91a2", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839015556200, "endTime": 9839214181800}, "additional": {"logType": "info", "children": ["56797c58-b3c2-4e42-8cb2-eb88cd46792d", "b8908d0c-2f9c-4fbc-94e1-d29473a708cc", "0a145faa-eea3-4685-ae57-333e15694c57", "21ac7042-f626-4596-a8a2-56e5eb54529c", "2601ee29-83cb-4fcd-9f39-078c107d09bc", "28a82bea-fc1c-4cb3-9730-c8e076087e33", "c1825dd8-4f66-41b1-8568-8380f6070350", "f0257f83-21e4-4bcc-b3ed-0ca34a0660d5", "c39ebdcb-6de0-4220-90ee-5020168f46ff"], "durationId": "2b51ced9-2a41-4175-a5a4-d983ab6ad88e", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "1d642f2a-e874-4817-8dcd-46410f259518", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839215315000, "endTime": 9839215327400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1e625fb-567b-47d3-be91-603e909be99b", "logId": "edc0aeb8-4848-4681-b8d9-db626dda8a80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edc0aeb8-4848-4681-b8d9-db626dda8a80", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839215315000, "endTime": 9839215327400}, "additional": {"logType": "info", "children": [], "durationId": "1d642f2a-e874-4817-8dcd-46410f259518", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "1c8acb28-555b-491a-aa5a-da34622830e0", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839214206000, "endTime": 9839215337900}, "additional": {"logType": "info", "children": [], "durationId": "bc56fdb7-8110-4ecb-bc6e-fe5e6bf3fdd3", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "f7a1a611-8e85-4c47-95f6-0d5284608940", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839215342900, "endTime": 9839215348500}, "additional": {"logType": "info", "children": [], "durationId": "dc36e935-5c05-4a86-b793-a052ac3f76e4", "parent": "fb73b43f-96b8-48e3-8ef0-d4e364458262"}}, {"head": {"id": "fb73b43f-96b8-48e3-8ef0-d4e364458262", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838995797800, "endTime": 9839215353300}, "additional": {"logType": "info", "children": ["791faa31-82f3-4fb1-b14c-e0dc1dfe0040", "ccb6cbbf-e149-4e34-a887-4633f92e91a2", "1c8acb28-555b-491a-aa5a-da34622830e0", "f7a1a611-8e85-4c47-95f6-0d5284608940", "daedbd3d-e2e6-4c75-b0e7-a1a3714596f4", "6d550b97-fc00-45f5-b366-d97f07da1579", "edc0aeb8-4848-4681-b8d9-db626dda8a80"], "durationId": "c1e625fb-567b-47d3-be91-603e909be99b"}}, {"head": {"id": "8f849ffe-457a-429e-a71e-781c1a66ff51", "name": "Configuration task cost before running: 227 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839215733300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96757691-9e2f-4e36-8766-24eccf40d887", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839223291700, "endTime": 9839367327600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5' has been changed."], "detailId": "230c3dbe-6d17-4e4e-aac5-45df994a6343", "logId": "6c2b7c81-7551-4c58-baed-3977927e4c37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "230c3dbe-6d17-4e4e-aac5-45df994a6343", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839217811000}, "additional": {"logType": "detail", "children": [], "durationId": "96757691-9e2f-4e36-8766-24eccf40d887"}}, {"head": {"id": "53c432e4-a4b2-4650-a199-90de3db1fd69", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839218789000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ed1407-40fd-4452-b1cf-a015070278ab", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839218900300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc70d5d-3cf6-4abe-9431-7884859667d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839218960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d77a486-4f86-4344-81e3-3ccafee09ab7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839223310800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ad3b1e-3093-4b4c-885b-506ec2eb7eeb", "name": "entry:default@PreBuild is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839231394100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de6369d-d0e0-4185-ab9f-fb21bcd1d9c9", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839231565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df50e04a-0c89-47d8-883c-716db27d67a6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839231753900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "265fadc7-277a-4037-a991-ce50a43afa1c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839231827700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5756fce2-677e-49b6-bc87-9a65660d2207", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839231881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318aaaef-fd64-49cf-8bd1-2ca59792f7aa", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839365445400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c204f129-6c82-4584-81e9-fd8ee781cb23", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\HarmonyOS\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839366252000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2526fb01-9cd2-4633-aec8-50a73785b905", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\HarmonyOS\\\\DevEco Studio\\\\tools\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839366436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af420424-3eaa-4d78-bfc0-3facd24ab313", "name": "entry : default@PreBuild cost memory -3.4201889038085938", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839367100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "652ae6d5-3a51-4d39-86d3-f902795aeb92", "name": "runTaskFromQueue task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839367246700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c2b7c81-7551-4c58-baed-3977927e4c37", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839223291700, "endTime": 9839367327600, "totalTime": 143919100}, "additional": {"logType": "info", "children": [], "durationId": "96757691-9e2f-4e36-8766-24eccf40d887"}}, {"head": {"id": "f623cf3f-52ec-4deb-bcdf-0d343c6103ad", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839373011000, "endTime": 9839388003500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5' has been changed."], "detailId": "5ae92336-290d-4e32-b1f7-ce331dbcd82b", "logId": "439ab7c2-7c1d-4725-a71d-4b2fd560fa38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ae92336-290d-4e32-b1f7-ce331dbcd82b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839371471500}, "additional": {"logType": "detail", "children": [], "durationId": "f623cf3f-52ec-4deb-bcdf-0d343c6103ad"}}, {"head": {"id": "35769a86-da65-4fbe-b2c2-8ee4a46c7879", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839372081200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02fd051e-6aba-4142-a740-c9908fd40857", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839372189000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ff790b-c29c-4335-9cb3-1647f3ff484b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839372250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73cb51a2-076f-4b29-ad89-a21356cd4b97", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839373025300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57c03574-8c71-458c-8575-049f52176b10", "name": "entry:default@MergeProfile is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839374871000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba73308-7158-438b-8b6f-a048867c732e", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839374996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d16fe90-9c6a-412a-9485-28b3eb01b759", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839375104700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a355dea2-5fb3-4b5f-9ede-e95a3b9cbfd3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839375169700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ea5fa0-fe78-491f-9a64-4fb57e6da3e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839375220100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056436b1-2eb8-4d8c-9f9b-0ccfbf949c0b", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839375731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db24aeba-5b32-4148-a332-4b5473ce5d97", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839376009000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300842c3-639c-4c6d-bb2d-1fd2a08a4352", "name": "Change app target API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839376090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e78c6cb-5ad0-4f57-8acd-c94e75f00865", "name": "Change app minimum API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839376146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd83a7be-fa26-4a6d-a577-720f516d35a4", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839376296100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e778e622-817a-450d-ad38-f53919bffed2", "name": "entry : default@MergeProfile cost memory 0.4546966552734375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839387767300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3286aa61-15c2-459e-b4db-510cc9e424a3", "name": "runTaskFromQueue task cost before running: 399 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839387933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "439ab7c2-7c1d-4725-a71d-4b2fd560fa38", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839373011000, "endTime": 9839388003500, "totalTime": 14889500}, "additional": {"logType": "info", "children": [], "durationId": "f623cf3f-52ec-4deb-bcdf-0d343c6103ad"}}, {"head": {"id": "9c5315bd-577a-4a63-94a3-0a2693d2bbed", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839391822800, "endTime": 9839395917200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5' has been changed."], "detailId": "17044006-f30c-4a37-abc4-8569816e70ed", "logId": "fd3ad859-f7b1-486f-bf0f-045b4b321c62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17044006-f30c-4a37-abc4-8569816e70ed", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839389921800}, "additional": {"logType": "detail", "children": [], "durationId": "9c5315bd-577a-4a63-94a3-0a2693d2bbed"}}, {"head": {"id": "b403a238-7c6e-4ec1-8422-eb6b3a927429", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839390449300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ca3e81-eac2-4dc1-ae8d-3b51fe7393d4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839390542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbf41ff-5446-4bdd-bb5a-b316bd45dfab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839390599900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b360c331-9fb7-4054-a0e3-23e246a06933", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839391848100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e1bf53-2999-4756-8cc0-61f6e8a65784", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839393381600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b78a4a3-90bc-4b27-ab35-a85cd2c2f538", "name": "entry:default@CreateBuildProfile is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839394041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d3eaf2-61a0-4904-a382-a134650e1a20", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839394148900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29219ee1-82c1-4737-9f9c-20bb7fd74946", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839394236600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5fd8057-e726-4921-9f4f-3f409e6fab25", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839394294700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e58be2-1a65-49c0-8b5b-9ae0c5cf71c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839394341900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b79196ea-3b24-42b5-bb0c-59bd13bcca0a", "name": "entry : default@CreateBuildProfile cost memory 0.15479278564453125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839395731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "632c0098-8a0b-4b7a-bfc1-c26a8a6f101f", "name": "runTaskFromQueue task cost before running: 407 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839395852400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd3ad859-f7b1-486f-bf0f-045b4b321c62", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839391822800, "endTime": 9839395917200, "totalTime": 4007200}, "additional": {"logType": "info", "children": [], "durationId": "9c5315bd-577a-4a63-94a3-0a2693d2bbed"}}, {"head": {"id": "618df6ed-65a6-42a1-a99e-485568397885", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399009800, "endTime": 9839399648000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d070ed7e-3ed6-41e1-9f4f-e90559c9edb4", "logId": "036719f1-a798-484a-b5e9-e22d09b8a841"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d070ed7e-3ed6-41e1-9f4f-e90559c9edb4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839397614900}, "additional": {"logType": "detail", "children": [], "durationId": "618df6ed-65a6-42a1-a99e-485568397885"}}, {"head": {"id": "0f700a75-7d37-43ca-9d1a-aac52e2a67de", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839398134100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "308ac730-fb98-440d-b879-711c24c4aa3d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839398220400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db07f73-6d29-4503-ae55-29695c5282fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839398274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eba16d2d-f563-47e3-a2f5-890d3462886b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399021700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee73ab6-a643-4c71-8559-1bebf0c9b61d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399134500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4126c94-8c66-46e9-8a8e-a39c99f364d7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399195500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adbb8cde-caeb-4ec5-8cf8-ffb4476e4e2f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b91d59-9105-4ceb-b0a8-5ce1eb3d5b41", "name": "entry : default@PreCheckSyscap cost memory 0.05072021484375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399484700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5523c91-7f60-4363-8616-d86b2c0355ff", "name": "runTaskFromQueue task cost before running: 411 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399584200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "036719f1-a798-484a-b5e9-e22d09b8a841", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839399009800, "endTime": 9839399648000, "totalTime": 552900}, "additional": {"logType": "info", "children": [], "durationId": "618df6ed-65a6-42a1-a99e-485568397885"}}, {"head": {"id": "271eead7-d44d-4c36-81ac-25f4b01da03a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839414001900, "endTime": 9839416698800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json' has been changed."], "detailId": "6ce8c21e-a5a2-4daa-a8b9-6f6d397c8eb1", "logId": "d6a33fcc-9ba2-4f46-9380-415f4caa49f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ce8c21e-a5a2-4daa-a8b9-6f6d397c8eb1", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839401726700}, "additional": {"logType": "detail", "children": [], "durationId": "271eead7-d44d-4c36-81ac-25f4b01da03a"}}, {"head": {"id": "78d422d4-0066-4a96-8902-10b3361c9656", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839402262300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f72d01-5db6-45ae-a70f-7a2649e15f0f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839402367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1f76da4-b629-45bd-8c48-e6aae36edbf6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839402428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ceeb8d7-5c8d-4a8f-b88f-d3190dfe0d52", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839414024700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5a6d57-3f70-43d6-b841-21a5e5664c0a", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839414367500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7604a38f-6e68-46ee-937b-a8cd35791be4", "name": "entry:default@GeneratePkgContextInfo is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839415097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7ce49c-9f88-4aac-8322-82627629fe66", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839415204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a21d9d-e925-4332-8a14-93b2c8d917bf", "name": "entry : default@GeneratePkgContextInfo cost memory 0.08577728271484375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839416461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f130c4-2932-48c8-8448-406d8e5076b2", "name": "runTaskFromQueue task cost before running: 428 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839416611600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a33fcc-9ba2-4f46-9380-415f4caa49f2", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839414001900, "endTime": 9839416698800, "totalTime": 2580400}, "additional": {"logType": "info", "children": [], "durationId": "271eead7-d44d-4c36-81ac-25f4b01da03a"}}, {"head": {"id": "295cd72a-757f-4d52-97c2-1bc9eedfd92d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839420966800, "endTime": 9839675057700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json' has been changed."], "detailId": "573caa83-ad10-47f9-83e8-0a35b22f266f", "logId": "f3c73f6e-b469-4ad4-88bd-5576b2d4fb2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "573caa83-ad10-47f9-83e8-0a35b22f266f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839418702700}, "additional": {"logType": "detail", "children": [], "durationId": "295cd72a-757f-4d52-97c2-1bc9eedfd92d"}}, {"head": {"id": "3be14aa6-7196-4fa9-a212-dd8ba54c86f4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839419217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d391469-62a1-4ae5-b9a9-598d2d1a1c68", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839419307100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f75076ba-7cb1-4a91-b065-79a159b94749", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839419361900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66aabb1-88d8-41d6-a86e-a659f5ba9082", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839421000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a946d0d9-26b5-4bd0-82ae-9b9a6d7ad4b2", "name": "entry:default@ProcessProfile is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839421566400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc44b763-353d-47b8-95f1-30216179db4b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839421664600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00873bde-840e-4d6e-8623-4de78354f7cf", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839421739400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25e9a4c5-5d18-4197-a351-dd1c246d09bc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839421794400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d79d981-7d71-4612-a5de-aeda309e34b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839421839300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23d670e1-b074-4adb-9341-479efda776ef", "name": "********", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839671517700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebc63421-aa0a-47c1-85ca-0950f6175b8a", "name": "entry : default@ProcessProfile cost memory 2.1595535278320312", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839674739100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b95b4f-3a61-48e9-8be4-70782cddeee6", "name": "runTaskFromQueue task cost before running: 686 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839674957400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c73f6e-b469-4ad4-88bd-5576b2d4fb2b", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839420966800, "endTime": 9839675057700, "totalTime": 253946500}, "additional": {"logType": "info", "children": [], "durationId": "295cd72a-757f-4d52-97c2-1bc9eedfd92d"}}, {"head": {"id": "5d55adba-bad4-46e6-9eea-fd92ddf52f00", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839679912600, "endTime": 9839687173200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5' has been changed."], "detailId": "bc2e7f0b-64fb-4131-8c10-8179ce7e7278", "logId": "4eab75eb-5139-44f5-be5e-4ba12772b9c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc2e7f0b-64fb-4131-8c10-8179ce7e7278", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839677405100}, "additional": {"logType": "detail", "children": [], "durationId": "5d55adba-bad4-46e6-9eea-fd92ddf52f00"}}, {"head": {"id": "a81daf78-a62f-4061-a092-50817feeae8f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839677955900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36bbb266-5cc1-4e6e-a779-d9771bd8fbcc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839678060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6cae5e-219f-499e-b5ce-aa8b7300315b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839678123200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7db619a-95a1-4c5c-ae98-7e92fcafb80d", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839679928500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341c31b0-fc84-4543-87f9-43c50002ae44", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839684775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988f3379-43c5-4ef7-b200-55486ebffbfd", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839684941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66bfb254-f1f4-4217-adee-dcfae34279c0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839685046900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6580d499-c6b3-45c3-bef7-afd38cb1bf9d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839685107600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb773ed-21aa-4cca-9eb8-9d8778707efa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839685157000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "805863bc-6693-45a0-8889-2b03a15b8336", "name": "entry : default@ProcessRouterMap cost memory 0.2555084228515625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839686962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21352841-4d41-4925-a47f-6cd176410663", "name": "runTaskFromQueue task cost before running: 699 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839687094300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eab75eb-5139-44f5-be5e-4ba12772b9c6", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839679912600, "endTime": 9839687173200, "totalTime": 7152800}, "additional": {"logType": "info", "children": [], "durationId": "5d55adba-bad4-46e6-9eea-fd92ddf52f00"}}, {"head": {"id": "32ba9c71-6313-4f72-af12-c4239f2d23d5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839695897300, "endTime": 9839700146400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5bc12a07-b4c8-46bf-a9cf-0c0bfb91fdb3", "logId": "c60ba45e-0248-4f62-a9db-775290c8c017"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bc12a07-b4c8-46bf-a9cf-0c0bfb91fdb3", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839690455000}, "additional": {"logType": "detail", "children": [], "durationId": "32ba9c71-6313-4f72-af12-c4239f2d23d5"}}, {"head": {"id": "1e11e6e5-359a-4bc7-b8cc-a51eced19ee6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839690979300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635bdf2f-6e53-4d8d-9e7e-3599942962a6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839691085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639a6b27-79fa-40bc-879a-a30514b24ea4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839691146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cf296f-b034-4f42-913c-5eea79808c29", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839692857100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4268daa-b6bd-4e40-84d8-73d735d2b549", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839698059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "838fdd3a-3265-4a05-8ef5-271a6971d5e7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839698275600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b32bb82-075f-41e0-83bd-b9386a7404b7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839698349700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af451a3-5c4e-4561-8651-6db2b830ad53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839698403900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30136e2-28ee-4a47-9599-c4d69c27d551", "name": "entry : default@PreviewProcessResource cost memory 0.09877777099609375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839698502200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f1fc42f-8b54-4c87-98ab-051df452d1fc", "name": "runTaskFromQueue task cost before running: 712 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839699986000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c60ba45e-0248-4f62-a9db-775290c8c017", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839695897300, "endTime": 9839700146400, "totalTime": 2680300}, "additional": {"logType": "info", "children": [], "durationId": "32ba9c71-6313-4f72-af12-c4239f2d23d5"}}, {"head": {"id": "6b24e62e-4dae-4f80-b5bc-83743e8a3c6d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839709367600, "endTime": 9839738839100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5' has been changed."], "detailId": "91f614a1-0289-4237-8944-017426c55997", "logId": "67968589-b1a8-4c2d-b580-401bce9e398a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91f614a1-0289-4237-8944-017426c55997", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839703836100}, "additional": {"logType": "detail", "children": [], "durationId": "6b24e62e-4dae-4f80-b5bc-83743e8a3c6d"}}, {"head": {"id": "d2b76086-34f0-4b28-a079-e5accbbc3004", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839704376300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb19842-0697-4028-95ea-1020dce3acc2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839704478400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e031ddcd-4ff9-41e7-bce6-4d072e7cd3ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839704536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e315c3-a4ce-4a07-b8e0-0e6d8e4dac39", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839709396200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86686137-c4c5-4850-96e1-b99e4d57f08d", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839731660300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea6934f-df77-4d10-9d92-2635c20910c5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839731815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af60de1-ae03-448f-b523-e9f53b11a218", "name": "entry : default@GenerateLoaderJson cost memory 1.2429428100585938", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839738585400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b470188e-73bd-4558-9379-347a98873746", "name": "runTaskFromQueue task cost before running: 750 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839738760800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67968589-b1a8-4c2d-b580-401bce9e398a", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839709367600, "endTime": 9839738839100, "totalTime": 29369300}, "additional": {"logType": "info", "children": [], "durationId": "6b24e62e-4dae-4f80-b5bc-83743e8a3c6d"}}, {"head": {"id": "4475b904-3765-4c72-ad58-539de7c142e1", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839751532600, "endTime": 9840466526100}, "additional": {"children": ["635d5091-006d-4788-8515-e67ff4df51fa", "a70b2468-dacd-45ed-aa00-dbaaf78ae4ee", "b443a68e-c257-49b0-bbfc-22c7c7fee927", "ac3715f7-029d-4164-ab6b-882901dc0433", "2e3c3f81-6535-4309-bf85-b71c5cd79559"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources' has been changed."], "detailId": "de07be9c-86dd-4ee8-982a-2638ea1fa14f", "logId": "360904e4-7a29-4598-93b9-9f7de8ec65b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de07be9c-86dd-4ee8-982a-2638ea1fa14f", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839747116100}, "additional": {"logType": "detail", "children": [], "durationId": "4475b904-3765-4c72-ad58-539de7c142e1"}}, {"head": {"id": "ec6e533d-d9b6-45e3-9ceb-b0677f1dd53a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839747705800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e676c4-2f0a-4c9e-8c42-01e469db4d56", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839747804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fb967d-fc9b-4688-8d5f-516f8f3a8219", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839747861600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05380d97-b2a3-4ea0-b8a8-6cdfdd772dd2", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839748725800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0991686-d68c-41c6-b131-2c4a05370780", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839751651900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5209ae3b-32a6-4826-aa69-4ff397f02d00", "name": "entry:default@PreviewCompileResource is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839781378500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5c56c5d-8d27-4201-9df5-0ff38bf17827", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839781548900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635d5091-006d-4788-8515-e67ff4df51fa", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839782932500, "endTime": 9839818094700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4475b904-3765-4c72-ad58-539de7c142e1", "logId": "d20e910f-7668-4695-9e69-115a7eef57c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d20e910f-7668-4695-9e69-115a7eef57c8", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839782932500, "endTime": 9839818094700}, "additional": {"logType": "info", "children": [], "durationId": "635d5091-006d-4788-8515-e67ff4df51fa", "parent": "360904e4-7a29-4598-93b9-9f7de8ec65b9"}}, {"head": {"id": "9c313e6f-4210-44ce-af7c-627e96da0a84", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839818740300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70b2468-dacd-45ed-aa00-dbaaf78ae4ee", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839819650500, "endTime": 9839983208600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4475b904-3765-4c72-ad58-539de7c142e1", "logId": "e2f59ef3-2cfa-4f14-9b4d-6059496345ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4627a8d-2cf2-448a-95e6-fbb92dfa8844", "name": "current process  memoryUsage: {\n  rss: 212336640,\n  heapTotal: 161345536,\n  heapUsed: 127775880,\n  external: 3119364,\n  arrayBuffers: 108615\n} os memoryUsage :12.6934814453125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839820628700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f3edb1-d7b5-4a4c-bac8-5df06143a5f7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839980339300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f59ef3-2cfa-4f14-9b4d-6059496345ba", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839819650500, "endTime": 9839983208600}, "additional": {"logType": "info", "children": [], "durationId": "a70b2468-dacd-45ed-aa00-dbaaf78ae4ee", "parent": "360904e4-7a29-4598-93b9-9f7de8ec65b9"}}, {"head": {"id": "f73f0033-618f-4eac-8616-36c1223b7aa5", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839983525800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b443a68e-c257-49b0-bbfc-22c7c7fee927", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839984692500, "endTime": 9840137150500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4475b904-3765-4c72-ad58-539de7c142e1", "logId": "5dc105ab-70da-4c0a-84ab-241ef51c713d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9721b9bd-5c99-4f66-a5b8-11fea6096a92", "name": "current process  memoryUsage: {\n  rss: 212406272,\n  heapTotal: 161345536,\n  heapUsed: 128228136,\n  external: 3119490,\n  arrayBuffers: 108756\n} os memoryUsage :12.718505859375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839985619500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e2372c-0dd2-4008-a506-41045cfbb98c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840134507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc105ab-70da-4c0a-84ab-241ef51c713d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839984692500, "endTime": 9840137150500}, "additional": {"logType": "info", "children": [], "durationId": "b443a68e-c257-49b0-bbfc-22c7c7fee927", "parent": "360904e4-7a29-4598-93b9-9f7de8ec65b9"}}, {"head": {"id": "694b65f1-8193-406e-b041-f9da82775a93", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840137659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3715f7-029d-4164-ab6b-882901dc0433", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840138724900, "endTime": 9840280481800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4475b904-3765-4c72-ad58-539de7c142e1", "logId": "71dabd7a-98f5-4439-b009-0f704885cfb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6025f3ab-6330-45ee-8c82-40359637a556", "name": "current process  memoryUsage: {\n  rss: 212451328,\n  heapTotal: 161345536,\n  heapUsed: 128504056,\n  external: 3127808,\n  arrayBuffers: 117138\n} os memoryUsage :12.710296630859375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840139617800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea150a2a-f68d-406a-ad53-16e14271975c", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840278392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71dabd7a-98f5-4439-b009-0f704885cfb9", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840138724900, "endTime": 9840280481800}, "additional": {"logType": "info", "children": [], "durationId": "ac3715f7-029d-4164-ab6b-882901dc0433", "parent": "360904e4-7a29-4598-93b9-9f7de8ec65b9"}}, {"head": {"id": "becfe899-670c-4c27-82f9-b89b90556d2d", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840281090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3c3f81-6535-4309-bf85-b71c5cd79559", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840282033600, "endTime": 9840464371400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4475b904-3765-4c72-ad58-539de7c142e1", "logId": "8ab94243-839f-44cc-9363-23badca6f3c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "172d43f2-394c-479b-8995-ea1cb96b7040", "name": "current process  memoryUsage: {\n  rss: 212480000,\n  heapTotal: 161345536,\n  heapUsed: 128817504,\n  external: 3127934,\n  arrayBuffers: 118267\n} os memoryUsage :12.714649200439453", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840282940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7aa07e-3c6c-480b-8fae-cce759d43f6e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840461012900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab94243-839f-44cc-9363-23badca6f3c4", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840282033600, "endTime": 9840464371400}, "additional": {"logType": "info", "children": [], "durationId": "2e3c3f81-6535-4309-bf85-b71c5cd79559", "parent": "360904e4-7a29-4598-93b9-9f7de8ec65b9"}}, {"head": {"id": "008105aa-523c-48ae-8d4c-d5965b7be158", "name": "entry : default@PreviewCompileResource cost memory -8.213180541992188", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840466225900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcf0d3d6-51ac-4e3b-9976-d3c7280fb3e8", "name": "runTaskFromQueue task cost before running: 1 s 478 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840466445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360904e4-7a29-4598-93b9-9f7de8ec65b9", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9839751532600, "endTime": 9840466526100, "totalTime": 714859200}, "additional": {"logType": "info", "children": ["d20e910f-7668-4695-9e69-115a7eef57c8", "e2f59ef3-2cfa-4f14-9b4d-6059496345ba", "5dc105ab-70da-4c0a-84ab-241ef51c713d", "71dabd7a-98f5-4439-b009-0f704885cfb9", "8ab94243-839f-44cc-9363-23badca6f3c4"], "durationId": "4475b904-3765-4c72-ad58-539de7c142e1"}}, {"head": {"id": "54dc1a4e-1663-4cf8-84d4-7bc93fc6e52a", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470194300, "endTime": 9840470629700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "be5708a0-14af-4eaa-b4f4-9269f234f3e4", "logId": "7d517b80-fd95-4227-9b98-e18f73285833"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be5708a0-14af-4eaa-b4f4-9269f234f3e4", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840469369400}, "additional": {"logType": "detail", "children": [], "durationId": "54dc1a4e-1663-4cf8-84d4-7bc93fc6e52a"}}, {"head": {"id": "256415ac-da7f-4bd7-9c4f-2b7a0a0d3c82", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840469911700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5625749b-04f5-40bd-8c3a-8ea5759dfc47", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840469995700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425d7ee1-0daa-4877-95e5-860885f0dbd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29506fb4-0169-4846-991a-1c6cf79c11e5", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470205700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce340ca-14c8-4f52-845e-74e35d729602", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470299700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e122678a-9913-4c0f-86a2-0d241e0603ee", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d88eb41-f83f-434f-96a3-1ad880b21dc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470395000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be256d24-8649-4c60-ae99-7463f8fe4c4c", "name": "entry : default@PreviewHookCompileResource cost memory 0.05230712890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470485800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2a90b6-7048-4adb-838f-2de014815c68", "name": "runTaskFromQueue task cost before running: 1 s 482 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d517b80-fd95-4227-9b98-e18f73285833", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840470194300, "endTime": 9840470629700, "totalTime": 357600}, "additional": {"logType": "info", "children": [], "durationId": "54dc1a4e-1663-4cf8-84d4-7bc93fc6e52a"}}, {"head": {"id": "54c79a52-27a4-4dac-9993-42c0d93ca6dd", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840473628200, "endTime": 9840483575700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile' has been changed."], "detailId": "17c7b550-2df7-4779-8d15-b5a6892128ac", "logId": "fa170919-5ea0-4eb6-b980-a6b106c6770f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17c7b550-2df7-4779-8d15-b5a6892128ac", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840472310100}, "additional": {"logType": "detail", "children": [], "durationId": "54c79a52-27a4-4dac-9993-42c0d93ca6dd"}}, {"head": {"id": "09e6011e-870a-4dac-8179-dc460c248496", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840472882700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd48ff1-fa12-4056-a698-b55dcbd32619", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840472973700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1569b172-c435-43d1-8239-e0f7bb0a47be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840473027100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02738a93-2b28-4332-821b-2543fc38fd8d", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840473641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed2f63a-fa1e-4336-855c-d2c88cbde8ca", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840475376400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c56df896-37cb-4d5c-ae84-9e09ef989cb3", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840475595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd34b76-d86d-4cdd-9329-b459b83d34c9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840475743600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db1844d-f8cc-4467-af41-839fb2e867b6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840475802400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be5904a8-b95d-4128-8cd5-bc57eb5cd65b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840475852300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92ae5b6-c1f1-446a-90f8-751094c0e8d3", "name": "entry : default@CopyPreviewProfile cost memory 0.25164031982421875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840483134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcb589d-6d30-4468-84b1-48cc97dc8ba4", "name": "runTaskFromQueue task cost before running: 1 s 495 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840483342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa170919-5ea0-4eb6-b980-a6b106c6770f", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840473628200, "endTime": 9840483575700, "totalTime": 9667800}, "additional": {"logType": "info", "children": [], "durationId": "54c79a52-27a4-4dac-9993-42c0d93ca6dd"}}, {"head": {"id": "5d036a23-d097-44f4-8505-41d82fc9b4cd", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487146700, "endTime": 9840487768100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "ae8b3806-983d-41a4-b3b4-649fa88e8124", "logId": "78dec5c4-561f-4344-add2-320692a7711e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae8b3806-983d-41a4-b3b4-649fa88e8124", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840485729500}, "additional": {"logType": "detail", "children": [], "durationId": "5d036a23-d097-44f4-8505-41d82fc9b4cd"}}, {"head": {"id": "bbcbc3d1-3ea8-4c56-b8a3-a4348d13be1a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840486282300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d72989b1-fb6e-4f92-972f-343900957573", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840486366800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d6072d-e24f-4828-957c-0cd5d457a1b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840486419000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9470aaa-c085-4f3e-b309-407cda2c2ac4", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487157200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b0af30-d23d-43e1-bce1-91bcb7793dcd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9202c42-7448-4432-9fbf-0bbf0675ee20", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487329700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62bb6864-42f2-4f77-bb4e-e403f3bbb21c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487374900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d16184-c223-4949-9226-9e05300b535a", "name": "entry : default@ReplacePreviewerPage cost memory 0.052215576171875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487600600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d1ffa4-3b83-4178-afd1-0e132f65731c", "name": "runTaskFromQueue task cost before running: 1 s 499 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487703800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78dec5c4-561f-4344-add2-320692a7711e", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840487146700, "endTime": 9840487768100, "totalTime": 529300}, "additional": {"logType": "info", "children": [], "durationId": "5d036a23-d097-44f4-8505-41d82fc9b4cd"}}, {"head": {"id": "22a100e2-2899-44c9-bcab-ae4923cd8e37", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840489536700, "endTime": 9840489858400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "934b42a6-bc5f-4144-b086-721f3aef8be2", "logId": "1af46c55-b290-4b93-b102-d960bd550d1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "934b42a6-bc5f-4144-b086-721f3aef8be2", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840489477000}, "additional": {"logType": "detail", "children": [], "durationId": "22a100e2-2899-44c9-bcab-ae4923cd8e37"}}, {"head": {"id": "de9dc570-69ba-442d-afc4-77b51d9a927c", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840489550200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cf715f-4dc0-477e-bf13-55441f2efddf", "name": "entry : buildPreviewerResource cost memory 0.01219940185546875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840489701900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd058f7-879f-4d32-b71c-6230db2aa009", "name": "runTaskFromQueue task cost before running: 1 s 501 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840489799800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af46c55-b290-4b93-b102-d960bd550d1f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840489536700, "endTime": 9840489858400, "totalTime": 239900}, "additional": {"logType": "info", "children": [], "durationId": "22a100e2-2899-44c9-bcab-ae4923cd8e37"}}, {"head": {"id": "131f2aef-820c-4af0-b980-7248617c97fc", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840493562400, "endTime": 9840497614000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "7278b639-7d1d-4a4f-af42-385c9d6f50f9", "logId": "a3e317c7-0dc8-4a28-b3be-86ed6d83c7cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7278b639-7d1d-4a4f-af42-385c9d6f50f9", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840491667700}, "additional": {"logType": "detail", "children": [], "durationId": "131f2aef-820c-4af0-b980-7248617c97fc"}}, {"head": {"id": "e90aa40b-d1fe-4890-9e78-5643735485a8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840492540300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9d7004-2e53-4236-b93e-a7670e7875a7", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840492660100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d399c5d6-b3cd-4123-b044-1ecbbc032013", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840492725200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "929db502-20b1-4429-a4e8-a0445fd180e4", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840493576400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6fb2773-e3e7-47e6-a937-87f440d4ba29", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840495874500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4deb30bf-2be5-4772-8154-c2a11de93b90", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840495998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a867886-af87-407d-b661-3d781bbdd0d3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840496092200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3340a5ad-f7ae-48d7-83a2-611d43b79f87", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840496150700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cabc90d-b981-46b8-84ca-5af495014caf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840496197800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee2a520-874b-41c8-a026-9153047c6fcb", "name": "entry : default@PreviewUpdateAssets cost memory 0.1621246337890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840497435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a87ef59c-6567-4e73-9b67-0c8a4b0dd585", "name": "runTaskFromQueue task cost before running: 1 s 509 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840497551800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3e317c7-0dc8-4a28-b3be-86ed6d83c7cf", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840493562400, "endTime": 9840497614000, "totalTime": 3963800}, "additional": {"logType": "info", "children": [], "durationId": "131f2aef-820c-4af0-b980-7248617c97fc"}}, {"head": {"id": "3e02d1b2-3bf8-4a49-a3f7-3c712907676a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840506733700, "endTime": 9844331715400}, "additional": {"children": ["37650c50-988e-4903-8ea0-0fa2cb34ab1a"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default' has been changed."], "detailId": "2b464e9a-fa2d-49b5-af62-8f5d5ef8df14", "logId": "be84abd3-ec64-4ca1-9efb-d801b91d2ad2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b464e9a-fa2d-49b5-af62-8f5d5ef8df14", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840500563600}, "additional": {"logType": "detail", "children": [], "durationId": "3e02d1b2-3bf8-4a49-a3f7-3c712907676a"}}, {"head": {"id": "a8532910-0cd3-456e-b4c1-fd46ea6079d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840501170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543aaad0-372d-452a-b123-215ce95a1314", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840501286400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16e45efb-404b-45c6-b253-de7893f4e62f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840501350600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6efe1eaf-5ee0-4b39-a3b2-4b02d200688f", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840506747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c5c970-7b72-4349-960c-5ebd8077fdcb", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840525525900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9cac6b8-cc66-43a8-b7a6-6def894ccfa3", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840525721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37650c50-988e-4903-8ea0-0fa2cb34ab1a", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker1", "startTime": 9840544739900, "endTime": 9844331346500}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3e02d1b2-3bf8-4a49-a3f7-3c712907676a", "logId": "56fd4bb3-607d-4b29-8d9b-8ae71b185ce7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6378455e-a15d-4b1a-b013-aacc55e51349", "name": "entry : default@PreviewArkTS cost memory 1.986114501953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840547517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56fd4bb3-607d-4b29-8d9b-8ae71b185ce7", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker1", "startTime": 9840544739900, "endTime": 9844331346500}, "additional": {"logType": "error", "children": [], "durationId": "37650c50-988e-4903-8ea0-0fa2cb34ab1a", "parent": "be84abd3-ec64-4ca1-9efb-d801b91d2ad2"}}, {"head": {"id": "9aaf8eb7-c389-4fc3-a8fd-d2e6e19ff5ee", "name": "default@PreviewArkTS watch work[1] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844331454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be84abd3-ec64-4ca1-9efb-d801b91d2ad2", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9840506733700, "endTime": 9844331715400}, "additional": {"logType": "error", "children": ["56fd4bb3-607d-4b29-8d9b-8ae71b185ce7"], "durationId": "3e02d1b2-3bf8-4a49-a3f7-3c712907676a"}}, {"head": {"id": "d2e548d0-a001-4f2a-8845-882fa7e1d861", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844331977200}, "additional": {"logType": "debug", "children": [], "durationId": "3e02d1b2-3bf8-4a49-a3f7-3c712907676a"}}, {"head": {"id": "90995dec-acfd-42d2-9e7e-44fb7c3bc846", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/pages/Index.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844334762300}, "additional": {"logType": "debug", "children": [], "durationId": "3e02d1b2-3bf8-4a49-a3f7-3c712907676a"}}, {"head": {"id": "572124e7-76ad-449e-bc1c-ec0cfcf2a4c9", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844350197400, "endTime": 9844350521200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1a01e308-14ce-4e60-bb0c-66226b4750a4", "logId": "5bfb2636-13dc-4a10-9f24-ab9a9bfe7fda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bfb2636-13dc-4a10-9f24-ab9a9bfe7fda", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844350197400, "endTime": 9844350521200}, "additional": {"logType": "info", "children": [], "durationId": "572124e7-76ad-449e-bc1c-ec0cfcf2a4c9"}}, {"head": {"id": "4570fa02-81e4-4503-a7a9-2e137ecdec2e", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9838988940300, "endTime": 9844351024500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 23}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "db5516c1-526b-4694-957b-b2cc5fb0e1a3", "name": "BUILD FAILED in 5 s 362 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844351082800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "d5f64b08-bc8a-4898-b31e-8d0b7fac7d8a", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844352351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ca28d3f-fb4d-44ae-a6b3-7ab4c2285694", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844352487800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae0e222-5d4a-498e-b79c-0b0a5cb6f1aa", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844352826300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756d5731-5b47-4fc2-94de-12edb7e4d7bb", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844353142800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd09ffde-e3b4-453d-a8cd-b4f97cc6f8a1", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844353440500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fc8fd46-d82f-4a28-98eb-f004d96fc440", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844353732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d0867b-2b5a-4586-8a00-38c96cd9c4c2", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844354007800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c2bb855-30b5-4d1a-8f54-fb8aed324c49", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844354284400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df9f73c-b340-49e9-9136-cbc2469694a1", "name": "Update task entry:default@PreBuild input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844354544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df24cf1b-fbbb-43cc-a9fb-0d4dbf3f8a26", "name": "Incremental task entry:default@PreBuild post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844355018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a891bdd-e200-4e6f-a102-103daee829d2", "name": "Update task entry:default@MergeProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844355346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18ef245-0078-4581-8da2-c8801f4fbd01", "name": "Update task entry:default@MergeProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844355430200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c8d6ad-896a-4690-96e4-dcb22c8c407d", "name": "Update task entry:default@MergeProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844355707600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "850430e6-2856-4fdc-a8b0-aad60355551c", "name": "Update task entry:default@MergeProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844355984700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2afbb803-5a68-4b69-a070-44697c305d85", "name": "Update task entry:default@MergeProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844356323400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9032f01-13b5-4fe5-9386-021d6cd5b65e", "name": "Incremental task entry:default@MergeProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844356703400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3d335e9-992d-4468-9168-ff0013e46c80", "name": "Update task entry:default@CreateBuildProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\AppScope\\app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844357007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3418c927-1441-41e9-b2f9-693dd9101f5f", "name": "Update task entry:default@CreateBuildProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844357089100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1f9bbe-4861-4aca-928b-2d352e28f867", "name": "Update task entry:default@CreateBuildProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844357434400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff87c8f-29cd-47bc-80c6-0459ca25a4c9", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844357791100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df99c26a-4b0c-4331-9a55-653589ad7e79", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844357883700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11110d52-3761-40b7-b98c-210cae690901", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844358192000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06518e9-264e-4d79-89b2-22fb292f9e67", "name": "Update task entry:default@ProcessProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844358303900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e1a94c-46d2-4663-9685-8f0382fb3220", "name": "Update task entry:default@ProcessProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844358416100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1727d94-907b-426b-8c2e-0d79f15908cc", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844358827200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0df09ed3-59bb-46cf-97a8-59475f504f87", "name": "Update task entry:default@ProcessRouterMap input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844360409700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b762d8-43c1-4354-b4f4-23c5286c8c26", "name": "Update task entry:default@ProcessRouterMap input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844360496800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fdcadef-dc9e-42a5-87ae-2076199c1e8d", "name": "Update task entry:default@ProcessRouterMap input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844360778300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "808c26f0-f4b2-46f2-a624-07aac503d497", "name": "Update task entry:default@ProcessRouterMap input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844361059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751fe4f6-557d-4fff-954f-579cccf05592", "name": "Update task entry:default@ProcessRouterMap output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844361354100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db505fa-4fc1-431c-aa2b-6c8cada2535c", "name": "Update task entry:default@ProcessRouterMap output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844361640000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3ea627-aa55-4a64-a0ea-b2025b5e5e2c", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844361968400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7133eb8-c75a-453b-8859-0452b8c2aebe", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844365542800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d99b402d-3e2e-4824-80c9-097fe5966cec", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844365684200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bb26ed-9e24-434c-a4a5-c32aa532f716", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844366051800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3de2cf7-8ba8-42df-b4ef-a008ca360489", "name": "Update task entry:default@GenerateLoaderJson output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844366380200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045e5304-9b15-4f41-9640-fc4959f070b4", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844366728600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "357a4db7-0360-4a17-859a-7644c42bed8c", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844367470900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63941e66-4725-4314-aaf9-239d3702e307", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844367554800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a433794-7445-4222-8e84-cf788ad9fa08", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resource_str cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844370214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e94db511-c28e-4d6b-a136-c0cc281a1bfc", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844370493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f3c003-725a-4706-b5e0-4f4371a9badf", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844370782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f001bc8d-cd8c-4df2-8323-18cdec247b47", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844388686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eae366d-ba41-4af3-8977-a07d8c04a07a", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:23 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844389458600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f52eb85-2c84-457e-a30e-e352396321fe", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844389770500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6194c257-0125-426a-8505-4ecbac29c41b", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844389909000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40220f8a-be7f-46df-b450-6e176e730bb1", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844391124700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27378ae2-7576-4f27-b553-0e2dbe3a6e1e", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844391857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "993c41ec-e03e-439b-9bbf-064155f11f57", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844392312700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daece271-ce08-4fc3-a1c0-7c9189cd0ad7", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844392703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed5918b-b686-4773-a44f-722f9558806e", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache from map.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844395848400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00284474-22ef-451f-aa74-9dc97c12198a", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844395950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3820f86-2932-4129-93a5-aa2eee5ece3e", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844396238900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "831eb140-a0e8-4eea-a0e2-ecbf6114aaae", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844418047300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "808a7d48-97cc-4939-a77e-cf673d0cdc22", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844419056400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9404cfd-648f-4900-9527-e68379d5bf96", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844419397700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b935422f-d304-446a-8c3b-13c9c0058c80", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844419701200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28456591-9d17-46eb-8449-1751e8fad6bb", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844420491200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d35f08-cfb4-455b-b8b7-69da456f1371", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844424069200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0774acbe-1b15-4dba-b393-9d6756ce1909", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844424477800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1829cb04-c0d0-44c5-9ac3-cc216a80be17", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844424820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a57a199e-315c-4b3a-adf9-98467b47e871", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844425229200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a750a2f-8e18-4acb-a9de-09e88866ff2c", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:33 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9844425591900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}