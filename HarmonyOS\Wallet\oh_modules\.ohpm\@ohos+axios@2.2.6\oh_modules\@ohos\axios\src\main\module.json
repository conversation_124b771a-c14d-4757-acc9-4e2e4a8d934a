{"app": {"bundleName": "com.example.axios_openharmony", "debug": true, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 12, "targetAPIVersion": 12, "apiReleaseType": "Release", "compileSdkVersion": "5.0.0.71", "compileSdkType": "OpenHarmony", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "library", "type": "har", "deviceTypes": ["default", "phone", "tablet", "2in1", "wearable", "car"], "packageName": "@ohos/axios", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}