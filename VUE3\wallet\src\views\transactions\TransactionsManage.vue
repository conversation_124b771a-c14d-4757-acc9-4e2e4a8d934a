<template>
  <div class="transactions-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><DocumentCopy /></el-icon>
            交易记录管理
          </h2>
          <p>管理系统交易记录，包括充值、提现、转账、消费等各类交易</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="exportData" size="large">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>交易列表</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="交易流水号">
          <el-input
            v-model="searchForm.transNo"
            placeholder="请输入交易流水号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择交易类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" :value="null" />
            <el-option label="充值" :value="1" />
            <el-option label="提现" :value="2" />
            <el-option label="转账" :value="3" />
            <el-option label="消费" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 交易记录表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column prop="transId" label="交易ID" width="80" />
        <el-table-column prop="transNo" label="交易流水号" width="180" />
        <el-table-column prop="userId" label="用户ID" width="80" />
        <el-table-column prop="amount" label="交易金额" width="120" align="right">
          <template #default="{ row }">
            <span :class="getAmountClass(row.type)">
              {{ formatAmount(row.amount, row.type) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="交易类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetInfo" label="目标信息" width="150" show-overflow-tooltip />
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { Search, Refresh, DocumentCopy, Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  transNo: '',
  type: null
})

// 获取交易记录列表
const fetchTransactions = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    // 只有交易类型参数可以传给后端API
    if (searchForm.type !== null) {
      params.type = searchForm.type
    }

    const response = await axios.get('http://localhost:8091/transactions', { params })

    if (response.data && response.data.code === 0) {
      let records = response.data.data?.records || []

      // 前端过滤：根据交易流水号筛选
      if (searchForm.transNo) {
        records = records.filter(record =>
          record.transNo && record.transNo.toLowerCase().includes(searchForm.transNo.toLowerCase())
        )
      }

      tableData.value = records
      total.value = records.length // 注意：这里是过滤后的总数，不是真实的分页总数
    } else {
      ElMessage.error('获取交易记录失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取交易记录失败:', error)
    ElMessage.error('获取交易记录失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTransactions()
}

// 重置
const handleReset = () => {
  searchForm.transNo = ''
  searchForm.type = null
  currentPage.value = 1
  fetchTransactions()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchTransactions()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchTransactions()
}

// 格式化金额
const formatAmount = (amount, type) => {
  const prefix = (type === 2 || type === 3 || type === 4) ? '-' : '+'
  return `${prefix}¥${Number(amount).toFixed(2)}`
}

// 获取金额样式类
const getAmountClass = (type) => {
  return (type === 2 || type === 3 || type === 4) ? 'amount-negative' : 'amount-positive'
}

// 获取交易类型名称
const getTypeName = (type) => {
  const typeMap = {
    1: '充值',
    2: '提现',
    3: '转账',
    4: '消费'
  }
  return typeMap[type] || '未知'
}

// 获取交易类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    1: 'success',
    2: 'warning',
    3: 'primary',
    4: 'info'
  }
  return typeMap[type] || ''
}

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    0: '处理中',
    1: '成功',
    2: '失败'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return statusMap[status] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 导出数据
const exportData = () => {
  ElMessage.success('导出功能开发中...')
}

// 初始化
onMounted(() => {
  fetchTransactions()
})
</script>

<style scoped>
.transactions-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.amount-positive {
  color: #67c23a;
  font-weight: bold;
}

.amount-negative {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>