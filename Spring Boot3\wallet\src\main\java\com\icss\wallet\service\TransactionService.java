package com.icss.wallet.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.icss.wallet.entity.Transaction;
import com.icss.wallet.mapper.TransactionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TransactionService {
    @Autowired
    private TransactionMapper transactionMapper;

    public IPage<Transaction> search(String phone, Integer type, int pageNum, int pageSize) {
        Page<Transaction> page = new Page<>(pageNum, pageSize);
        return transactionMapper.searchTransactions(page, phone, type);
    }

    /**
     * Vue前端专用：分页查询交易记录（支持更多筛选条件）
     */
    public IPage<Transaction> searchPage(String phone, Integer type, Integer status, String transNo, String startDate, String endDate, int pageNum, int pageSize) {
        Page<Transaction> page = new Page<>(pageNum, pageSize);
        return transactionMapper.searchTransactionsPage(page, phone, type, status, transNo, startDate, endDate);
    }
    public IPage<Transaction> getByUserId(Long userId, int pageNum, int pageSize) {
        Page<Transaction> page = new Page<>(pageNum, pageSize);
        return transactionMapper.selectPage(page,
                new QueryWrapper<Transaction>()
                        .eq("user_id", userId)
                        .orderByDesc("create_time"));
    }

    /**
     * HarmonyOS前端专用：根据用户ID和交易类型查询交易记录（包含用户信息）
     */
    public IPage<Transaction> getByUserIdWithType(Long userId, int pageNum, int pageSize, Integer type) {
        Page<Transaction> page = new Page<>(pageNum, pageSize);
        return transactionMapper.selectTransactionsWithUserInfo(page, userId, type);
    }

    /**
     * HarmonyOS前端专用：获取用户月度统计数据
     */
    public java.util.Map<String, Object> getMonthlyStats(Long userId, int year, int month, Integer type) {
        return transactionMapper.getMonthlyStats(userId, year, month, type);
    }

    /**
     * Vue前端专用：获取交易统计数据
     */
    public java.util.Map<String, Object> getTransactionStatistics() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();

        // 获取各类型交易数量
        stats.put("totalTransactions", transactionMapper.selectCount(null));
        stats.put("rechargeCount", transactionMapper.selectCount(new QueryWrapper<Transaction>().eq("type", 1)));
        stats.put("withdrawCount", transactionMapper.selectCount(new QueryWrapper<Transaction>().eq("type", 2)));
        stats.put("transferCount", transactionMapper.selectCount(new QueryWrapper<Transaction>().eq("type", 3)));
        stats.put("consumeCount", transactionMapper.selectCount(new QueryWrapper<Transaction>().eq("type", 4)));

        // 获取总交易金额（只计算成功的交易）
        java.math.BigDecimal totalAmount = transactionMapper.getTotalAmount();
        stats.put("totalAmount", totalAmount != null ? totalAmount : java.math.BigDecimal.ZERO);

        return stats;
    }

    /**
     * Vue前端专用：获取总交易金额
     */
    public java.math.BigDecimal getTotalAmount() {
        java.math.BigDecimal amount = transactionMapper.getTotalAmount();
        return amount != null ? amount : java.math.BigDecimal.ZERO;
    }

    /**
     * Vue前端专用：按类型统计交易数量
     */
    public Long getTransactionCount(Integer type) {
        if (type != null) {
            return transactionMapper.selectCount(new QueryWrapper<Transaction>().eq("type", type));
        } else {
            return transactionMapper.selectCount(null);
        }
    }
}