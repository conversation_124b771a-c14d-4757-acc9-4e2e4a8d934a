package com.icss.wallet.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName("transaction")
public class Transaction {
    @TableId(type = IdType.AUTO)
    private Long transId;
    private String transNo;
    private Long userId;
    private BigDecimal amount;
    private Integer type;
    private Integer status;
    private String targetInfo;
    private String remark;
    private Date createTime;
    private Date updateTime;

    // HarmonyOS前端需要的额外字段（不存储在数据库中）
    @TableField(exist = false)
    private BigDecimal balance; // 当前钱包余额

    @TableField(exist = false)
    private String phone; // 用户手机号

    @TableField(exist = false)
    private String accountNumber; // 账户号码（用于转账等场景）

    public Long getTransId() {
        return transId;
    }

    public void setTransId(Long transId) {
        this.transId = transId;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTargetInfo() {
        return targetInfo;
    }

    public void setTargetInfo(String targetInfo) {
        this.targetInfo = targetInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    // HarmonyOS前端需要的字段的getter和setter方法
    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    // 为了兼容HarmonyOS前端，添加一些别名方法
    public Long getId() {
        return this.transId;
    }

    public void setId(Long id) {
        this.transId = id;
    }

    public String getDescription() {
        return this.remark;
    }

    public void setDescription(String description) {
        this.remark = description;
    }
}