<template>
  <div class="cards-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>银行卡管理（管理员）</h2>
      <p>管理系统所有用户的银行卡信息、绑定状态和卡片审核</p>
    </div>

    <!-- 统计面板 -->
    <div class="statistics-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalCards || 0 }}</div>
              <div class="stat-label">总银行卡数</div>
            </div>
            <el-icon class="stat-icon total"><CreditCard /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.boundCards || 0 }}</div>
              <div class="stat-label">已绑定卡片</div>
            </div>
            <el-icon class="stat-icon bound"><Check /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.unboundCards || 0 }}</div>
              <div class="stat-label">未绑定卡片</div>
            </div>
            <el-icon class="stat-icon unbound"><Close /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.defaultCards || 0 }}</div>
              <div class="stat-label">默认卡片</div>
            </div>
            <el-icon class="stat-icon default"><Star /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容卡片 -->
    <el-card>
      <!-- 卡片头部 -->
      <template #header>
        <div class="card-header">
          <span>银行卡列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              添加银行卡
            </el-button>
            <el-button type="info" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户手机号">
            <el-input
              v-model="searchForm.phone"
              placeholder="请输入用户手机号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="银行卡号">
            <el-input
              v-model="searchForm.cardNumber"
              placeholder="请输入银行卡号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="绑定状态">
            <el-select v-model="searchForm.isBound" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="已绑定" :value="1" />
              <el-option label="未绑定" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="cardId" label="卡片ID" width="80" />
        <el-table-column prop="userId" label="用户ID" width="80" />
        <el-table-column prop="phone" label="用户手机号" width="120" />
        <el-table-column prop="cardNumber" label="银行卡号" width="180">
          <template #default="{ row }">
            <span v-if="row.showFullNumber">{{ row.cardNumber }}</span>
            <span v-else>{{ maskCardNumber(row.cardNumber) }}</span>
            <el-button 
              type="text" 
              size="small" 
              @click="toggleCardNumber(row)"
              style="margin-left: 8px"
            >
              <el-icon><View v-if="!row.showFullNumber" /><Hide v-else /></el-icon>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="bankName" label="银行名称" width="120" />
        <el-table-column prop="cardType" label="卡片类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.cardType === 1 ? 'success' : 'info'">
              {{ row.cardType === 1 ? '储蓄卡' : '信用卡' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isBound" label="绑定状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isBound === 1 ? 'success' : 'warning'">
              {{ row.isBound === 1 ? '已绑定' : '未绑定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认卡片" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isDefault === 1" type="danger">
              <el-icon><Star /></el-icon>
              默认
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button 
              :type="row.isBound === 1 ? 'warning' : 'success'" 
              size="small" 
              @click="toggleBind(row)"
            >
              {{ row.isBound === 1 ? '解绑' : '绑定' }}
            </el-button>
            <el-button 
              v-if="row.isBound === 1"
              :type="row.isDefault === 1 ? 'info' : 'primary'" 
              size="small" 
              @click="setDefault(row)"
              :disabled="row.isDefault === 1"
            >
              设为默认
            </el-button>
            <el-button type="danger" size="small" @click="deleteCard(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加银行卡对话框 -->
    <el-dialog v-model="showAddDialog" title="添加银行卡" width="500px">
      <el-form :model="addForm" label-width="100px">
        <el-form-item label="用户ID">
          <el-input-number
            v-model="addForm.userId"
            :min="1"
            style="width: 100%"
            placeholder="请输入用户ID"
          />
        </el-form-item>
        <el-form-item label="银行卡号">
          <el-input
            v-model="addForm.cardNumber"
            placeholder="请输入银行卡号"
            maxlength="19"
          />
        </el-form-item>
        <el-form-item label="银行名称">
          <el-input
            v-model="addForm.bankName"
            placeholder="请输入银行名称"
          />
        </el-form-item>
        <el-form-item label="卡片类型">
          <el-radio-group v-model="addForm.cardType">
            <el-radio :value="1">储蓄卡</el-radio>
            <el-radio :value="2">信用卡</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否绑定">
          <el-switch v-model="addForm.isBound" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAdd">确认添加</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CreditCard, Check, Close, Star, Plus, Refresh, Search, View, Hide
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const showAddDialog = ref(false)

// 统计数据
const statistics = ref({
  totalCards: 0,
  boundCards: 0,
  unboundCards: 0,
  defaultCards: 0
})

// 搜索表单
const searchForm = reactive({
  phone: '',
  cardNumber: '',
  isBound: null
})

// 添加表单
const addForm = reactive({
  userId: null,
  cardNumber: '',
  bankName: '',
  cardType: 1,
  isBound: 1
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await axios.get('/bankCards/admin/statistics')
    if (response.data.code === 0) {
      statistics.value = response.data.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

const fetchCards = async () => {
  try {
    loading.value = true
    const response = await axios.get('/bankCards/admin/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        phone: searchForm.phone,
        cardNumber: searchForm.cardNumber,
        isBound: searchForm.isBound
      }
    })
    
    if (response.data.code === 0) {
      tableData.value = response.data.data.records.map(item => ({
        ...item,
        showFullNumber: false
      }))
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error)
    ElMessage.error('获取银行卡列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchCards()
}

// 重置
const handleReset = () => {
  searchForm.phone = ''
  searchForm.cardNumber = ''
  searchForm.isBound = null
  currentPage.value = 1
  fetchCards()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchCards()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchCards()
}

// 切换卡号显示
const toggleCardNumber = (row) => {
  row.showFullNumber = !row.showFullNumber
}

// 掩码卡号
const maskCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  return cardNumber.replace(/(\d{4})\d{8,12}(\d{4})/, '$1****$2')
}

// 切换绑定状态
const toggleBind = async (row) => {
  const action = row.isBound === 1 ? '解绑' : '绑定'
  try {
    await ElMessageBox.confirm(`确定要${action}该银行卡吗？`, '确认操作')
    
    const url = row.isBound === 1 ? `/bankCards/admin/unbind/${row.cardId}` : `/bankCards/admin/bind/${row.cardId}`
    await axios.post(url)
    
    ElMessage.success(`${action}成功`)
    fetchCards()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('绑定状态切换失败:', error)
      ElMessage.error('绑定状态切换失败')
    }
  }
}

// 设为默认
const setDefault = async (row) => {
  try {
    await axios.put(`/bankCards/admin/${row.cardId}/default`)
    ElMessage.success('设置默认卡片成功')
    fetchCards()
  } catch (error) {
    console.error('设置默认卡片失败:', error)
    ElMessage.error('设置默认卡片失败')
  }
}

// 删除银行卡
const deleteCard = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该银行卡吗？此操作不可恢复！', '确认删除', {
      type: 'warning'
    })
    
    await axios.delete(`/bankCards/admin/${row.cardId}`)
    ElMessage.success('删除成功')
    fetchCards()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除银行卡失败:', error)
      ElMessage.error('删除银行卡失败')
    }
  }
}

// 确认添加
const confirmAdd = async () => {
  if (!addForm.userId || !addForm.cardNumber || !addForm.bankName) {
    ElMessage.warning('请填写完整信息')
    return
  }

  try {
    await axios.post('/bankCards/admin/add', addForm)
    ElMessage.success('添加银行卡成功')
    showAddDialog.value = false
    refreshData()
    
    // 重置表单
    Object.assign(addForm, {
      userId: null,
      cardNumber: '',
      bankName: '',
      cardType: 1,
      isBound: 1
    })
  } catch (error) {
    console.error('添加银行卡失败:', error)
    ElMessage.error('添加银行卡失败')
  }
}

// 刷新数据
const refreshData = () => {
  loadStatistics()
  fetchCards()
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
  fetchCards()
})
</script>

<style scoped>
.cards-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.statistics-panel {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 32px;
  opacity: 0.3;
}

.stat-icon.total {
  color: #409eff;
}

.stat-icon.bound {
  color: #67c23a;
}

.stat-icon.unbound {
  color: #e6a23c;
}

.stat-icon.default {
  color: #f56c6c;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
