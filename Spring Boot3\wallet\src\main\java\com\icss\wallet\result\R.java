package com.icss.wallet.result;


public class R<T> {
    private Integer code; //编码：0成功，1和其它数字为失败
    private String msg; //错误信息
    private T data; //数据

    public static <T> R<T> success(String msg, T object) {
        R<T> r = new R<T>();
        r.data = object;
        r.msg = msg;
        r.code = 0;
        return r;
    }

    public static <T> R<T> success(String msg) {
        R<T> r = new R<T>();
        r.data = null;
        r.msg = msg;
        r.code = 0;
        return r;
    }

    public static <T> R<T> failure(String msg) {
        R<T> r = new R<T>();
        r.data = null;
        r.msg = msg;
        r.code = 1;
        return r;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

}
