<template>
  <div class="receipts-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>收款记录</span>
        </div>
      </template>

      <!-- 筛选表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="交易流水号">
          <el-input
            v-model="searchForm.transNo"
            placeholder="请输入交易流水号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="收款方式">
          <el-select
            v-model="searchForm.receiptType"
            placeholder="请选择收款方式"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="转账收款" value="transfer" />
            <el-option label="充值收款" value="recharge" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 收款记录表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column prop="transId" label="交易ID" width="80" />
        <el-table-column prop="transNo" label="交易流水号" width="180" />
        <el-table-column label="收款类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getReceiptTypeTagType(row.type)">
              {{ getReceiptTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="收款金额" width="120" align="right">
          <template #default="{ row }">
            <span class="amount-positive">
              +¥{{ Number(row.amount).toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="付款人信息" width="200">
          <template #default="{ row }">
            <div class="payer-info">
              <div class="payer-main">{{ getPayerInfo(row) }}</div>
              <div class="payer-detail">{{ getPayerDetail(row) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="收款时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  transNo: '',
  receiptType: ''
})

// 获取收款记录列表
const fetchReceipts = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    const response = await axios.get('http://localhost:8091/transactions', { params })

    if (response.data && response.data.code === 0) {
      let records = response.data.data?.records || []

      // 根据交易流水号筛选
      if (searchForm.transNo) {
        records = records.filter(record =>
          record.transNo && record.transNo.toLowerCase().includes(searchForm.transNo.toLowerCase())
        )
      }

      // 只显示收款类型的记录（充值和转账收入）
      records = records.filter(record => {
        if (searchForm.receiptType === 'transfer') {
          return record.type === 3 // 转账
        } else if (searchForm.receiptType === 'recharge') {
          return record.type === 1 // 充值
        } else {
          return record.type === 1 || record.type === 3 // 充值或转账
        }
      })

      tableData.value = records
      total.value = records.length
    } else {
      ElMessage.error('获取收款记录失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取收款记录失败:', error)
    ElMessage.error('获取收款记录失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchReceipts()
}

// 重置
const handleReset = () => {
  searchForm.transNo = ''
  searchForm.receiptType = ''
  currentPage.value = 1
  fetchReceipts()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchReceipts()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchReceipts()
}

// 获取收款类型名称
const getReceiptTypeName = (type) => {
  const typeMap = {
    1: '充值收款',
    3: '转账收款'
  }
  return typeMap[type] || '其他收款'
}

// 获取收款类型标签类型
const getReceiptTypeTagType = (type) => {
  const typeMap = {
    1: 'success',
    3: 'primary'
  }
  return typeMap[type] || 'info'
}

// 获取付款人信息
const getPayerInfo = (row) => {
  if (row.type === 1) {
    // 充值：显示银行卡信息
    return row.targetInfo || '银行卡充值'
  } else if (row.type === 3) {
    // 转账：显示转账方信息
    return `用户ID: ${row.userId}`
  }
  return '未知付款方'
}

// 获取付款人详情
const getPayerDetail = (row) => {
  if (row.type === 1) {
    return '银行卡充值'
  } else if (row.type === 3) {
    if (row.targetInfo) {
      // 如果是手机号格式
      if (/^1[3-9]\d{9}$/.test(row.targetInfo)) {
        return '手机号转账'
      }
      // 如果是银行卡号格式
      if (/^\d{16,19}$/.test(row.targetInfo)) {
        return '银行卡转账'
      }
    }
    return '转账收款'
  }
  return ''
}

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    0: '处理中',
    1: '成功',
    2: '失败'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return statusMap[status] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 初始化
onMounted(() => {
  fetchReceipts()
})
</script>

<style scoped>
.receipts-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.amount-positive {
  color: #67c23a;
  font-weight: bold;
}

.payer-info {
  line-height: 1.4;
}

.payer-main {
  font-weight: 500;
}

.payer-detail {
  font-size: 12px;
  color: #999;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>