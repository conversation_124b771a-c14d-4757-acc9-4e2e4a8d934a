package com.icss.wallet.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Payment;
import com.icss.wallet.entity.Transaction;
import com.icss.wallet.entity.Wallet;
import com.icss.wallet.entity.BankCard;

import com.icss.wallet.mapper.PaymentMapper;
import com.icss.wallet.mapper.TransactionMapper;
import com.icss.wallet.mapper.WalletMapper;
import com.icss.wallet.mapper.BankCardMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class PaymentService {
    
    @Autowired
    private PaymentMapper paymentMapper;
    
    @Autowired
    private WalletMapper walletMapper;
    
    @Autowired
    private BankCardMapper bankCardMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;
    
    /**
     * 创建支付订单
     */
    public Payment createPayment(Long userId, BigDecimal amount, String merchantName, 
                               String orderNo, String description) {
        Payment payment = new Payment();
        payment.setPaymentNo(generatePaymentNo());
        payment.setUserId(userId);
        payment.setAmount(amount);
        payment.setMerchantName(merchantName);
        payment.setOrderNo(orderNo);
        payment.setDescription(description);
        payment.setStatus(0); // 待支付
        payment.setCreateTime(new Date());
        payment.setUpdateTime(new Date());
        
        paymentMapper.insert(payment);
        return payment;
    }
    
    /**
     * 钱包支付
     */
    @Transactional
    public boolean payWithWallet(String paymentNo, String payPassword) {
        try {
            Payment payment = paymentMapper.findByPaymentNo(paymentNo);
            if (payment == null || payment.getStatus() != 0) {
                return false;
            }
            
            // 验证支付密码（这里简化处理，实际应该验证用户的支付密码）
            
            // 检查钱包余额并扣款
            int result = walletMapper.subtractBalance(payment.getUserId(), payment.getAmount());
            if (result <= 0) {
                return false; // 余额不足
            }
            
            // 更新支付状态
            payment.setStatus(1); // 支付成功
            payment.setPaymentType(1); // 钱包支付
            payment.setPaymentSource("wallet");
            payment.setPayTime(new Date());
            payment.setUpdateTime(new Date());
            paymentMapper.updateById(payment);
            
            // 记录交易
            Transaction transaction = new Transaction();
            transaction.setTransNo(generateTransNo());
            transaction.setUserId(payment.getUserId());
            transaction.setAmount(payment.getAmount());
            transaction.setType(4); // 4-消费
            transaction.setStatus(1); // 1-成功
            transaction.setTargetInfo(payment.getMerchantName());
            transaction.setRemark("钱包支付-" + payment.getDescription());
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            
            transactionMapper.insert(transaction);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 银行卡支付
     */
    @Transactional
    public boolean payWithBankCard(String paymentNo, Long bankCardId, String payPassword) {
        try {
            Payment payment = paymentMapper.findByPaymentNo(paymentNo);
            if (payment == null || payment.getStatus() != 0) {
                return false;
            }
            
            // 验证银行卡
            BankCard bankCard = bankCardMapper.selectById(bankCardId);
            if (bankCard == null || bankCard.getStatus() != 1 || 
                !bankCard.getUserId().equals(payment.getUserId())) {
                return false;
            }
            
            // 验证支付密码（这里简化处理）
            
            // 模拟银行卡扣款（实际应该调用银行接口）
            // 这里假设银行卡支付总是成功
            
            // 更新支付状态
            payment.setStatus(1); // 支付成功
            payment.setPaymentType(2); // 银行卡支付
            payment.setPaymentSource(bankCard.getCardNumber());
            payment.setPayTime(new Date());
            payment.setUpdateTime(new Date());
            paymentMapper.updateById(payment);
            
            // 记录交易
            Transaction transaction = new Transaction();
            transaction.setTransNo(generateTransNo());
            transaction.setUserId(payment.getUserId());
            transaction.setAmount(payment.getAmount());
            transaction.setType(4); // 4-消费
            transaction.setStatus(1); // 1-成功
            transaction.setTargetInfo(payment.getMerchantName());
            transaction.setRemark("银行卡支付-" + payment.getDescription());
            transaction.setCreateTime(new Date());
            transaction.setUpdateTime(new Date());
            
            transactionMapper.insert(transaction);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取用户支付记录
     */
    public List<Payment> getPaymentHistory(Long userId) {
        return paymentMapper.findByUserId(userId);
    }
    
    /**
     * 根据支付单号获取支付信息
     */
    public Payment getPaymentByNo(String paymentNo) {
        return paymentMapper.findByPaymentNo(paymentNo);
    }
    
    /**
     * 生成支付单号
     */
    private String generatePaymentNo() {
        return "PAY" + System.currentTimeMillis();
    }
    
    /**
     * 生成交易流水号
     */
    private String generateTransNo() {
        return "TXN" + System.currentTimeMillis();
    }

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询支付记录（包含用户信息）
     */
    public IPage<Payment> getPaymentsWithUserInfo(int pageNum, int pageSize, String paymentNo, String orderNo, Integer status) {
        Page<Payment> page = new Page<>(pageNum, pageSize);
        return paymentMapper.selectPaymentsWithUserInfo(page, paymentNo, orderNo, status);
    }

    /**
     * 管理员获取支付统计信息
     */
    public java.util.Map<String, Object> getPaymentStatistics() {
        return paymentMapper.getPaymentStatistics();
    }

    /**
     * 管理员处理支付状态
     */
    @Transactional
    public boolean processPayment(Long paymentId, Integer status) {
        Payment payment = paymentMapper.selectById(paymentId);
        if (payment == null) {
            throw new RuntimeException("支付记录不存在");
        }

        payment.setStatus(status);
        payment.setUpdateTime(new Date());
        return paymentMapper.updateById(payment) > 0;
    }

    /**
     * 保存支付记录
     */
    public boolean save(Payment payment) {
        return paymentMapper.insert(payment) > 0;
    }

    /**
     * 管理员修改支付记录
     */
    public boolean updatePayment(Payment payment) {
        if (payment.getPaymentId() == null) {
            throw new RuntimeException("支付记录ID不能为空");
        }

        Payment existingPayment = paymentMapper.selectById(payment.getPaymentId());
        if (existingPayment == null) {
            throw new RuntimeException("支付记录不存在");
        }

        // 添加日志输出
        System.out.println("更新支付记录 - ID: " + payment.getPaymentId());
        System.out.println("更新前状态: " + existingPayment.getStatus());
        System.out.println("更新后状态: " + payment.getStatus());

        int updateCount = paymentMapper.updatePaymentById(payment);
        System.out.println("更新影响行数: " + updateCount);

        return updateCount > 0;
    }

    /**
     * 管理员删除支付记录
     */
    public boolean deletePayment(Long paymentId) {
        if (paymentId == null) {
            throw new RuntimeException("支付记录ID不能为空");
        }

        Payment existingPayment = paymentMapper.selectById(paymentId);
        if (existingPayment == null) {
            throw new RuntimeException("支付记录不存在");
        }

        return paymentMapper.deletePaymentById(paymentId) > 0;
    }
}
