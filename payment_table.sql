/*
Navicat MySQL Data Transfer

Source Server         : MySQL
Source Server Version : 50528
Source Host           : localhost:3306
Source Database       : wallet

Target Server Type    : MYSQL
Target Server Version : 50528
File Encoding         : 65001

Date: 2025-06-25 23:38:33
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `admin_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `role` varchar(20) DEFAULT 'operator',
  `status` int(11) DEFAULT '1',
  `last_login_time` datetime DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  <PERSON><PERSON>AR<PERSON>EY (`admin_id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES ('1', 'admin', '0192023a7bbd73250516f069df18b500', '13682000691', '张建国', 'admin', '1', '2024-01-20 09:15:30', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('2', 'limanager', 'e10adc3949ba59abbe56e057f20f883e', '13695000673', '李雪梅', 'admin', '0', '2024-01-19 14:22:15', '2025-06-20 17:58:08', '2025-06-25 13:51:18');
INSERT INTO `admin` VALUES ('3', 'wangop', '9d34de276eaa015a937f5c90365b1581', '15033697139', '王志强', 'operator', '1', '2024-01-18 16:45:20', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('4', 'chenop', '1581f91b362ed2f90bf892aaaedc75b2', '***********', '陈美丽', 'operator', '1', '2024-01-17 11:30:45', '2025-06-20 17:58:08', '2025-06-20 17:58:08');
INSERT INTO `admin` VALUES ('5', 'zhaoop', '5bfdad151f5a624c3b137cc91686e359', '***********', '赵小明', 'operator', '0', null, '2025-06-20 17:58:08', '2025-06-20 17:58:08');

-- ----------------------------
-- Table structure for bank_account
-- ----------------------------
DROP TABLE IF EXISTS `bank_account`;
CREATE TABLE `bank_account` (
  `account_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户ID',
  `account_number` varchar(30) NOT NULL COMMENT '银行账号',
  `account_type` tinyint(4) NOT NULL COMMENT '账户类型(1-储蓄账户,2-支票账户,3-信用卡账户)',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `branch_name` varchar(100) DEFAULT NULL COMMENT '支行名称',
  `account_holder` varchar(50) NOT NULL COMMENT '账户持有人姓名',
  `phone` varchar(20) NOT NULL COMMENT '绑定手机号',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `available_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `credit_limit` decimal(15,2) DEFAULT '0.00' COMMENT '信用额度(信用卡专用)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-冻结,1-正常,2-销户)',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认账户(0-否,1-是)',
  `open_date` date NOT NULL COMMENT '开户日期',
  `last_transaction_time` datetime DEFAULT NULL COMMENT '最后交易时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `idx_account_number` (`account_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  CONSTRAINT `fk_bank_account_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='银行账户表';

-- ----------------------------
-- Records of bank_account
-- ----------------------------
INSERT INTO `bank_account` VALUES ('1', '1', '62220219850515123450', '1', '中国工商银行', '北京朝阳支行', '刘德华', '***********', 'CNY', '2561179.50', '2580000.50', '0.00', '1', '1', '2020-01-15', '2024-01-20 10:30:00', '2025-06-20 18:06:28', '2025-06-24 14:37:33');
INSERT INTO `bank_account` VALUES ('2', '1', '62258819850515123460', '3', '招商银行', '北京分行', '刘德华', '***********', 'CNY', '0.00', '450000.00', '500000.00', '1', '0', '2020-03-20', '2024-01-19 15:20:00', '2025-06-20 18:06:28', '2025-06-20 18:22:38');
INSERT INTO `bank_account` VALUES ('3', '2', '62270019920320156780', '1', '中国建设银行', '上海浦东支行', '张曼玉', '***********', 'HKD', '1890000.75', '1890000.75', '0.00', '1', '1', '2019-05-10', '2024-01-19 14:15:00', '2025-06-20 18:06:28', '2025-06-20 18:22:41');
INSERT INTO `bank_account` VALUES ('4', '2', '60138219920320156790', '3', '中国银行', '上海分行', '张曼玉', '***********', 'CNY', '26800.00', '270000.00', '300000.00', '1', '0', '2019-08-25', '2024-01-18 16:45:00', '2025-06-20 18:06:28', '2025-06-24 14:37:33');
INSERT INTO `bank_account` VALUES ('5', '1', '62257719850515123470', '2', '浦发银行', '北京海淀支行', '刘德华', '***********', 'CNY', '588720.60', '856000.60', '0.00', '1', '0', '2021-03-15', '2024-01-18 10:45:00', '2025-06-20 18:06:28', '2025-06-25 11:08:47');
INSERT INTO `bank_account` VALUES ('6', '3', '62284819781212345670', '1', '中国农业银行', '深圳福田支行', '周星驰', '***********', 'HKD', '3560000.00', '3560000.00', '0.00', '1', '1', '2018-12-01', '2024-01-18 11:30:00', '2025-06-20 18:06:36', '2025-06-20 18:22:48');
INSERT INTO `bank_account` VALUES ('7', '3', '62226019781212345680', '3', '交通银行', '深圳分行', '周星驰', '***********', 'HKD', '0.00', '720000.00', '800000.00', '1', '0', '2019-02-14', '2024-01-17 09:20:00', '2025-06-20 18:06:36', '2025-06-20 18:22:52');
INSERT INTO `bank_account` VALUES ('8', '3', '62305819781212345690', '2', '平安银行', '深圳南山支行', '周星驰', '***********', 'CNY', '1563300.45', '1234000.45', '0.00', '1', '0', '2020-11-05', '2024-01-16 12:20:00', '2025-06-20 18:06:36', '2025-06-25 11:08:47');
INSERT INTO `bank_account` VALUES ('9', '4', '62170019890615678900', '1', '中信银行', '广州天河支行', '林青霞', '***********', 'CNY', '1678000.25', '0.00', '0.00', '0', '1', '2020-06-18', '2024-01-15 14:30:00', '2025-06-20 18:06:44', '2025-06-20 18:22:56');
INSERT INTO `bank_account` VALUES ('10', '4', '62262019890615678910', '3', '光大银行', '广州分行', '林青霞', '***********', 'CNY', '0.00', '180000.00', '200000.00', '1', '0', '2020-09-10', '2024-01-14 16:45:00', '2025-06-20 18:06:44', '2025-06-20 18:22:57');
INSERT INTO `bank_account` VALUES ('11', '4', '62258019890615678920', '1', '广发银行', '广州越秀支行', '林青霞', '***********', 'CNY', '445000.00', '445000.00', '0.00', '0', '0', '2022-01-10', '2024-01-10 14:15:00', '2025-06-20 18:06:44', '2025-06-25 16:16:24');
INSERT INTO `bank_account` VALUES ('12', '5', '62262219900101987650', '1', '民生银行', '杭州西湖支行', '梁朝伟', '***********', 'HKD', '2987500.80', '2987500.80', '0.00', '1', '1', '2019-01-08', '2024-01-20 09:15:00', '2025-06-20 18:06:51', '2025-06-20 18:23:01');
INSERT INTO `bank_account` VALUES ('13', '5', '62268519900101987660', '3', '兴业银行', '杭州分行', '梁朝伟', '***********', 'USD', '0.00', '540000.00', '600000.00', '1', '0', '2019-04-22', '2024-01-19 13:25:00', '2025-06-20 18:06:51', '2025-06-20 18:23:03');
INSERT INTO `bank_account` VALUES ('14', '2', '62262019920320156800', '2', '华夏银行', '上海静安支行', '张曼玉', '***********', 'USD', '534000.00', '534000.00', '0.00', '1', '0', '2021-07-20', '2024-01-17 15:30:00', '2025-06-20 18:06:51', '2025-06-20 18:23:05');
INSERT INTO `bank_account` VALUES ('15', '5', '62218819900101987670', '1', '中国邮政储蓄银行', '杭州滨江支行', '梁朝伟', '***********', 'CNY', '1050001.90', '1120000.90', '0.00', '1', '0', '2021-09-18', '2024-01-15 16:40:00', '2025-06-20 18:06:51', '2025-06-23 20:59:00');

-- ----------------------------
-- Table structure for bank_card
-- ----------------------------
DROP TABLE IF EXISTS `bank_card`;
CREATE TABLE `bank_card` (
  `card_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '银行卡ID',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户ID',
  `card_number` varchar(20) NOT NULL DEFAULT '',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `card_type` tinyint(4) NOT NULL COMMENT '卡类型(1-借记卡,2-信用卡)',
  `card_holder` varchar(50) NOT NULL COMMENT '持卡人姓名',
  `phone` varchar(20) NOT NULL COMMENT '预留手机号',
  `expiry_date` varchar(10) DEFAULT NULL COMMENT '有效期(信用卡专用)',
  `cvv` varchar(10) DEFAULT NULL COMMENT '安全码(信用卡专用)',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认(0-否,1-是)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-解绑,1-绑定)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`card_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_bank_card_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 COMMENT='银行卡表';

-- ----------------------------
-- Records of bank_card
-- ----------------------------
INSERT INTO `bank_card` VALUES ('1', '1', '6222021985051512345', '工商银行', '1', '刘德华', '***********', null, null, '0', '1', '2025-06-20 17:58:57', '2025-06-24 21:09:04');
INSERT INTO `bank_card` VALUES ('2', '1', '6225881985051512346', '招商银行', '2', '刘德华', '***********', '12/26', '123', '0', '1', '2025-06-20 17:58:57', '2025-06-24 21:09:04');
INSERT INTO `bank_card` VALUES ('3', '2', '6227001992032015678', '建设银行', '1', '张曼玉', '***********', null, null, '1', '1', '2025-06-20 17:58:57', '2025-06-24 11:24:36');
INSERT INTO `bank_card` VALUES ('4', '2', '6013821992032015679', '中国银行', '2', '张曼玉', '***********', '03/27', '456', '0', '0', '2025-06-20 17:58:57', '2025-06-20 17:58:57');
INSERT INTO `bank_card` VALUES ('5', '1', '6222021985051512345', '工商银行', '1', '刘德华', '***********', null, null, '0', '1', '2025-06-20 17:59:12', '2025-06-24 21:09:04');
INSERT INTO `bank_card` VALUES ('6', '1', '6225881985051512346', '招商银行', '2', '刘德华', '***********', '12/26', '123', '0', '1', '2025-06-20 17:59:12', '2025-06-24 21:09:04');
INSERT INTO `bank_card` VALUES ('7', '2', '6227001992032015678', '建设银行', '1', '张曼玉', '***********', null, null, '1', '1', '2025-06-20 17:59:12', '2025-06-24 11:24:42');
INSERT INTO `bank_card` VALUES ('8', '2', '6013821992032015679', '中国银行', '2', '张曼玉', '***********', '03/27', '456', '0', '0', '2025-06-20 17:59:12', '2025-06-20 17:59:12');
INSERT INTO `bank_card` VALUES ('9', '4', '6217001989061567890', '中信银行', '1', '林青霞', '***********', null, null, '1', '0', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('11', '5', '6226221990010198765', '民生银行', '1', '梁朝伟', '***********', null, null, '1', '1', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('12', '5', '6226851990010198766', '兴业银行', '2', '梁朝伟', '***********', '05/28', '654', '0', '1', '2025-06-20 17:59:24', '2025-06-20 17:59:24');
INSERT INTO `bank_card` VALUES ('13', '1', '6225771985051512347', '浦发银行', '1', '刘德华', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-24 21:09:04');
INSERT INTO `bank_card` VALUES ('14', '2', '6226201992032015680', '华夏银行', '1', '张曼玉', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('15', '3', '6230581978121234569', '平安银行', '2', '周星驰', '***********', '09/27', '987', '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('16', '4', '6225801989061567892', '广发银行', '1', '林青霞', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-20 17:59:36');
INSERT INTO `bank_card` VALUES ('17', '5', '6221881990010198767', '邮政储蓄银行', '1', '梁朝伟', '***********', null, null, '0', '0', '2025-06-20 17:59:36', '2025-06-24 11:24:47');
INSERT INTO `bank_card` VALUES ('18', '1', '6225881985051512648', '招商银行', '1', '孙燕姿', '***********', null, null, '1', '1', '2025-06-20 18:11:25', '2025-06-24 21:09:04');
INSERT INTO `bank_card` VALUES ('22', '6', '6225881985051512636', '华夏银行', '2', '周润发', '***********', null, null, '0', '0', '2025-06-25 09:16:38', '2025-06-25 09:16:38');
INSERT INTO `bank_card` VALUES ('23', '6', '6225881985051512637', '工商银行', '1', '周润发', '***********', null, null, '0', '1', '2025-06-25 13:04:21', '2025-06-25 13:04:27');

-- ----------------------------
-- Table structure for payment
-- ----------------------------
DROP TABLE IF EXISTS `payment`;
CREATE TABLE `payment` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `payment_no` varchar(32) NOT NULL COMMENT '支付单号',
  `user_id` bigint(20) NOT NULL COMMENT '支付用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '支付金额',
  `payment_type` tinyint(4) DEFAULT NULL COMMENT '支付方式(1-钱包支付,2-银行卡支付)',
  `payment_source` varchar(50) DEFAULT NULL COMMENT '支付来源(钱包ID或银行卡号)',
  `merchant_name` varchar(100) NOT NULL COMMENT '商户名称',
  `order_no` varchar(50) DEFAULT NULL COMMENT '商户订单号',
  `description` varchar(255) DEFAULT NULL COMMENT '支付描述',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付状态(0-待支付,1-支付成功,2-支付失败,3-已取消)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付完成时间',
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `idx_payment_no` (`payment_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_payment_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8 COMMENT='支付记录表';

-- ----------------------------
-- Records of payment
-- ----------------------------
INSERT INTO `payment` VALUES ('1', 'PAY202401200001', '1', '299.00', '1', 'wallet', '星巴克咖啡', 'SB20240120001', '购买咖啡套餐', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('2', 'PAY202401200002', '1', '1299.00', '2', '6222021985051512345', '苹果官方商店', 'AP20240120001', '购买AirPods Pro', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('3', 'PAY202401190001', '2', '89.00', '1', 'wallet', '美团外卖', 'MT20240119001', '午餐订单', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('4', 'PAY202401190002', '2', '2599.00', '2', '6227001992032015678', '京东商城', 'JD20240119001', '购买手机', '0', '2025-06-23 09:52:36', '2025-06-25 13:35:19', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('5', 'PAY202401180001', '3', '199.00', '1', 'wallet', '滴滴出行', 'DD20240118001', '打车费用', '3', '2025-06-23 09:52:36', '2025-06-25 13:35:22', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('6', 'PAY202401180002', '3', '4999.00', '2', '6228481978121234567', '华为商城', 'HW20240118001', '购买笔记本电脑', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('7', 'PAY202401170001', '4', '59.00', '1', 'wallet', '网易云音乐', 'WY20240117001', 'VIP会员续费', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('8', 'PAY202401170002', '4', '399.00', '2', '6217001989061567890', '淘宝网', 'TB20240117001', '购买服装', '0', '2025-06-23 09:52:36', '2025-06-25 13:09:27', null);
INSERT INTO `payment` VALUES ('9', 'PAY202401160001', '5', '129.00', '1', 'wallet', '腾讯视频', 'TX20240116001', '年度会员', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('10', 'PAY202401160002', '5', '1899.00', '2', '6226221990010198765', '小米商城', 'MI20240116001', '购买智能手表', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('11', 'PAY202401150001', '1', '49.00', '1', 'wallet', '饿了么', 'ELM20240115001', '晚餐订单', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('12', 'PAY202401150002', '2', '799.00', '2', '6227001992032015678', '天猫超市', 'TM20240115001', '日用品采购', '0', '2025-06-23 09:52:36', '2025-06-25 13:28:50', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('13', 'PAY202401140001', '3', '299.00', '1', 'wallet', '爱奇艺', 'IQY20240114001', '黄金VIP年费', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('14', 'PAY202401140002', '4', '1599.00', '2', '6217001989061567890', '苏宁易购', 'SN20240114001', '购买空气净化器', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('15', 'PAY202401130001', '5', '88.00', '1', 'wallet', '哈啰出行', 'HL20240113001', '共享单车月卡', '1', '2025-06-23 09:52:36', '2025-06-23 09:52:36', '2025-06-23 09:52:36');
INSERT INTO `payment` VALUES ('16', 'PAY1750643743867', '1', '200.00', '1', 'wallet', '绝味鸭脖', 'ORDER1750643743778', '钱包支付', '0', '2025-06-23 09:55:43', '2025-06-25 13:31:59', '2025-06-23 09:55:44');
INSERT INTO `payment` VALUES ('17', 'PAY1750643962539', '1', '122.00', '1', 'wallet', '滴滴打车', 'ORDER1750643962535', '钱包支付', '1', '2025-06-23 09:59:22', '2025-06-25 13:32:00', '2025-06-23 09:59:22');
INSERT INTO `payment` VALUES ('18', 'PAY1750644189588', '1', '12.00', '2', '6225881985051512346', '滴滴', 'ORDER1750644189583', '银行卡支付', '2', '2025-06-23 10:03:09', '2025-06-25 13:32:13', '2025-06-23 10:03:09');
INSERT INTO `payment` VALUES ('20', 'TEST1750829427638', '1', '99.99', null, null, '测试商户', 'ORDER1750829427638', '测试支付记录', '0', '2025-06-25 13:30:27', '2025-06-25 13:32:04', null);
INSERT INTO `payment` VALUES ('21', 'TEST1750829427636', '6', '600.00', null, null, '钢笔', 'GB20250309006', '购买钢笔', '0', '2025-06-25 13:32:56', '2025-06-25 13:32:56', null);

-- ----------------------------
-- Table structure for sms_code
-- ----------------------------
DROP TABLE IF EXISTS `sms_code`;
CREATE TABLE `sms_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` tinyint(4) NOT NULL COMMENT '类型(1-注册登录,2-修改密码,3-支付验证)',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone_type` (`phone`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8 COMMENT='短信验证码表';

-- ----------------------------
-- Records of sms_code
-- ----------------------------
INSERT INTO `sms_code` VALUES ('1', '***********', '576312', '1', '2025-06-23 09:07:46', '2025-06-23 09:02:46');
INSERT INTO `sms_code` VALUES ('2', '***********', '467421', '1', '2025-06-23 09:08:27', '2025-06-23 09:03:27');
INSERT INTO `sms_code` VALUES ('3', '***********', '929063', '1', '2025-06-23 09:16:30', '2025-06-23 09:11:30');
INSERT INTO `sms_code` VALUES ('4', '***********', '774528', '1', '2025-06-23 09:37:23', '2025-06-23 09:32:23');
INSERT INTO `sms_code` VALUES ('5', '***********', '048866', '1', '2025-06-23 10:25:11', '2025-06-23 10:20:11');
INSERT INTO `sms_code` VALUES ('6', '***********', '454370', '1', '2025-06-23 10:25:38', '2025-06-23 10:20:38');
INSERT INTO `sms_code` VALUES ('7', '***********', '722647', '1', '2025-06-23 10:33:07', '2025-06-23 10:28:07');
INSERT INTO `sms_code` VALUES ('8', '***********', '569315', '1', '2025-06-23 10:34:23', '2025-06-23 10:29:23');
INSERT INTO `sms_code` VALUES ('9', '***********', '435179', '1', '2025-06-23 10:35:03', '2025-06-23 10:30:03');
INSERT INTO `sms_code` VALUES ('10', '***********', '946659', '1', '2025-06-23 10:36:30', '2025-06-23 10:31:30');
INSERT INTO `sms_code` VALUES ('11', '***********', '758227', '1', '2025-06-23 10:36:58', '2025-06-23 10:31:58');
INSERT INTO `sms_code` VALUES ('12', '***********', '236816', '1', '2025-06-23 10:38:49', '2025-06-23 10:33:49');
INSERT INTO `sms_code` VALUES ('13', '***********', '829709', '1', '2025-06-23 11:27:41', '2025-06-23 11:22:41');
INSERT INTO `sms_code` VALUES ('14', '***********', '297975', '1', '2025-06-23 11:37:01', '2025-06-23 11:32:01');
INSERT INTO `sms_code` VALUES ('15', '***********', '339072', '1', '2025-06-23 13:12:46', '2025-06-23 13:07:46');
INSERT INTO `sms_code` VALUES ('16', '***********', '413647', '1', '2025-06-23 13:13:24', '2025-06-23 13:08:24');
INSERT INTO `sms_code` VALUES ('17', '***********', '745796', '1', '2025-06-23 21:00:34', '2025-06-23 20:55:34');
INSERT INTO `sms_code` VALUES ('18', '***********', '424494', '1', '2025-06-24 09:39:05', '2025-06-24 09:34:05');
INSERT INTO `sms_code` VALUES ('19', '***********', '509698', '1', '2025-06-24 10:15:16', '2025-06-24 10:10:16');
INSERT INTO `sms_code` VALUES ('20', '***********', '214098', '1', '2025-06-24 10:15:39', '2025-06-24 10:10:39');
INSERT INTO `sms_code` VALUES ('21', '***********', '261441', '1', '2025-06-24 10:16:19', '2025-06-24 10:11:19');
INSERT INTO `sms_code` VALUES ('22', '***********', '268576', '1', '2025-06-24 10:26:08', '2025-06-24 10:21:08');
INSERT INTO `sms_code` VALUES ('23', '***********', '304626', '1', '2025-06-24 10:27:31', '2025-06-24 10:22:31');
INSERT INTO `sms_code` VALUES ('24', '***********', '603505', '1', '2025-06-24 20:58:45', '2025-06-24 20:53:45');
INSERT INTO `sms_code` VALUES ('25', '***********', '458629', '1', '2025-06-25 10:46:43', '2025-06-25 10:41:43');

-- ----------------------------
-- Table structure for transaction
-- ----------------------------
DROP TABLE IF EXISTS `transaction`;
CREATE TABLE `transaction` (
  `trans_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '交易ID',
  `trans_no` varchar(32) NOT NULL COMMENT '交易流水号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
  `type` tinyint(4) NOT NULL COMMENT '交易类型(1-充值,2-提现,3-转账,4-消费)',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态(0-处理中,1-成功,2-失败)',
  `target_info` varchar(100) DEFAULT NULL COMMENT '目标信息(银行卡号/手机号等)',
  `remark` varchar(255) DEFAULT NULL COMMENT '交易备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`trans_id`),
  UNIQUE KEY `idx_trans_no` (`trans_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_transaction_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8 COMMENT='交易记录表';

-- ----------------------------
-- Records of transaction
-- ----------------------------
INSERT INTO `transaction` VALUES ('1', 'TXN202401200001', '1', '500000.00', '1', '1', '6222021985051512345', '电影票房分红充值', '2025-06-20 18:06:58', '2025-06-20 18:06:58');
INSERT INTO `transaction` VALUES ('2', 'TXN202401190001', '1', '80000.00', '2', '1', '6225881985051512346', '购买豪车提现', '2025-06-20 18:06:58', '2025-06-20 18:06:58');
INSERT INTO `transaction` VALUES ('3', 'TXN202401180001', '1', '150000.00', '3', '1', '***********', '慈善捐款转账给张曼玉', '2025-06-20 18:06:58', '2025-06-20 18:06:58');
INSERT INTO `transaction` VALUES ('4', 'TXN202401200002', '2', '300000.00', '1', '1', '6227001992032015678', '广告代言费充值', '2025-06-20 18:07:13', '2025-06-20 18:07:13');
INSERT INTO `transaction` VALUES ('5', 'TXN202401190002', '2', '50000.00', '4', '1', '珠宝店', '购买珠宝消费', '2025-06-20 18:07:13', '2025-06-20 18:07:13');
INSERT INTO `transaction` VALUES ('6', 'TXN202401180002', '2', '200000.00', '3', '1', '***********', '投资收益转账给周星驰', '2025-06-20 18:07:13', '2025-06-20 18:07:13');
INSERT INTO `transaction` VALUES ('7', 'TXN202401200003', '3', '800000.00', '1', '1', '6228481978121234567', '电影制作分成充值', '2025-06-20 18:07:18', '2025-06-20 18:07:18');
INSERT INTO `transaction` VALUES ('8', 'TXN202401190003', '3', '120000.00', '2', '1', '6222601978121234568', '购买房产提现', '2025-06-20 18:07:18', '2025-06-20 18:07:18');
INSERT INTO `transaction` VALUES ('9', 'TXN202401180003', '3', '100000.00', '3', '1', '***********', '员工奖金转账给林青霞', '2025-06-20 18:07:18', '2025-06-20 18:07:18');
INSERT INTO `transaction` VALUES ('10', 'TXN202401200004', '4', '280000.00', '1', '2', '6217001989061567890', '演出费用充值失败', '2025-06-20 18:07:24', '2025-06-20 18:07:24');
INSERT INTO `transaction` VALUES ('11', 'TXN202401190004', '4', '45000.00', '2', '2', '6226201989061567891', '账户冻结提现失败', '2025-06-20 18:07:24', '2025-06-20 18:07:24');
INSERT INTO `transaction` VALUES ('12', 'TXN202401180004', '4', '180000.00', '3', '1', '***********', '版权收入转账给梁朝伟', '2025-06-20 18:07:24', '2025-06-20 18:07:24');
INSERT INTO `transaction` VALUES ('13', 'TXN202401200005', '5', '450000.00', '1', '1', '6226221990010198765', '影视投资回报充值', '2025-06-20 18:07:30', '2025-06-20 18:07:30');
INSERT INTO `transaction` VALUES ('14', 'TXN202401190005', '5', '75000.00', '4', '1', '艺术品拍卖行', '艺术品收藏消费', '2025-06-20 18:07:30', '2025-06-20 18:07:30');
INSERT INTO `transaction` VALUES ('15', 'TXN202401180005', '5', '200000.00', '3', '1', '公益基金会', '公益基金转账', '2025-06-20 18:07:30', '2025-06-20 18:07:30');
INSERT INTO `transaction` VALUES ('16', 'T365e430802464da', '5', '9999.00', '3', '1', '62220219850515123450', '转账至账户 62220219850515123450', '2025-06-20 18:23:33', '2025-06-20 18:23:33');
INSERT INTO `transaction` VALUES ('17', 'TXN1750640657705', '1', '200.00', '1', '1', '6222021985051512345', '写字', '2025-06-23 09:04:17', '2025-06-23 09:04:17');
INSERT INTO `transaction` VALUES ('18', 'TXN1750640715263', '1', '0.99', '1', '1', '6225881985051512346', '钱包充值', '2025-06-23 09:05:15', '2025-06-23 09:05:15');
INSERT INTO `transaction` VALUES ('19', 'TXN1750640728188', '1', '100.00', '1', '1', '6222021985051512345', '钱包充值', '2025-06-23 09:05:28', '2025-06-23 09:05:28');
INSERT INTO `transaction` VALUES ('20', 'TXN1750640738304', '1', '300.00', '2', '1', '6222021985051512345', '钱包提现', '2025-06-23 09:05:38', '2025-06-23 09:05:38');
INSERT INTO `transaction` VALUES ('21', 'TXN1750643744275', '1', '200.00', '4', '1', '绝味鸭脖', '钱包支付-钱包支付', '2025-06-23 09:55:44', '2025-06-23 09:55:44');
INSERT INTO `transaction` VALUES ('22', 'TXN1750643962566', '1', '122.00', '4', '1', '滴滴打车', '钱包支付-钱包支付', '2025-06-23 09:59:22', '2025-06-23 09:59:22');
INSERT INTO `transaction` VALUES ('23', 'TXN1750644189621', '1', '12.00', '4', '1', '滴滴', '银行卡支付-银行卡支付', '2025-06-23 10:03:09', '2025-06-23 10:03:09');
INSERT INTO `transaction` VALUES ('24', 'TXN1750665708226', '1', '100.00', '1', '1', '6225881985051512346', '钱包充值', '2025-06-23 16:01:48', '2025-06-23 16:01:48');
INSERT INTO `transaction` VALUES ('25', 'TXN1750665769261', '1', '99.00', '2', '1', '6225881985051512346', '钱包提现', '2025-06-23 16:02:49', '2025-06-23 16:02:49');
INSERT INTO `transaction` VALUES ('26', 'T293b86d9e32c400', '1', '200.00', '3', '1', '60138219920320156790', '转账至账户 60138219920320156790', '2025-06-23 17:17:50', '2025-06-23 17:17:50');
INSERT INTO `transaction` VALUES ('27', 'T7e26b442fb574ef', '1', '20.00', '3', '1', '62257719850515123470', '转账至账户 62257719850515123470', '2025-06-23 17:19:42', '2025-06-23 17:19:42');
INSERT INTO `transaction` VALUES ('28', 'T6c8ab0e05524412', '5', '60000.00', '3', '1', '62305819781212345690', '转账至账户 62305819781212345690', '2025-06-23 20:59:00', '2025-06-23 20:59:00');
INSERT INTO `transaction` VALUES ('29', 'Tbf7ceb12e6c3492', '1', '2000.00', '3', '1', '62257719850515123470', '转账至账户 62257719850515123470', '2025-06-23 21:15:45', '2025-06-23 21:15:45');
INSERT INTO `transaction` VALUES ('30', 'Tdb7e63917cf746a', '1', '26600.00', '3', '1', '60138219920320156790', '转账至账户 60138219920320156790', '2025-06-24 14:37:33', '2025-06-24 14:37:33');
INSERT INTO `transaction` VALUES ('31', 'T7882ef6ce725442', '1', '69300.00', '3', '1', '62305819781212345690', '转账至账户 62305819781212345690', '2025-06-24 15:06:38', '2025-06-24 15:06:38');
INSERT INTO `transaction` VALUES ('32', 'TXN1750770497937', '1', '0.01', '5', '1', null, '管理员调整余额: 凑为整数11', '2025-06-24 21:08:17', '2025-06-24 21:08:17');
INSERT INTO `transaction` VALUES ('33', 'Tdde45d8b2431435', '1', '200000.00', '3', '1', '62305819781212345690', '转账至账户 62305819781212345690', '2025-06-25 11:08:47', '2025-06-25 11:08:47');
INSERT INTO `transaction` VALUES ('34', 'TXN1750820947602', '1', '200.00', '2', '1', '6225881985051512648', '钱包提现', '2025-06-25 11:09:07', '2025-06-25 11:09:07');
INSERT INTO `transaction` VALUES ('35', 'TXN1750821664882', '1', '500.00', '1', '1', '6225881985051512648', '钱包充值', '2025-06-25 11:21:04', '2025-06-25 11:21:04');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `password` varchar(50) NOT NULL COMMENT '密码(明文存储)',
  `pay_password` varchar(50) NOT NULL DEFAULT '123456' COMMENT '支付密码(明文存储)',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-禁用,1-正常)',
  `pay_limit` decimal(15,2) DEFAULT '50000.00' COMMENT '支付限额',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='用户表';

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('1', '***********', 'user123', 'pay123', '刘德华', '******************', null, '1', '100000.00', '2025-06-25 22:07:33', '2025-06-20 17:58:23', '2025-06-25 22:07:33');
INSERT INTO `user` VALUES ('2', '***********', 'user456', 'pay456', '张曼玉', '110108199203201567', null, '1', '80000.00', '2025-06-24 20:11:06', '2025-06-20 17:58:23', '2025-06-24 20:11:06');
INSERT INTO `user` VALUES ('3', '***********', 'user789', 'pay789', '周星驰', '310115197812123456', null, '1', '150000.00', '2024-01-18 12:20:45', '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('4', '***********', 'user321', 'pay321', '林青霞', '320102198906156789', null, '0', '60000.00', '2024-01-15 16:10:20', '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('5', '***********', 'user654', 'pay654', '梁朝伟', '440304199001019876', null, '1', '120000.00', null, '2025-06-20 17:58:23', '2025-06-20 17:58:23');
INSERT INTO `user` VALUES ('6', '***********', 'user890', 'pay890', '周润发', '369700055040608020', null, '1', '60100.00', null, '2025-06-25 09:15:45', '2025-06-25 12:30:02');

-- ----------------------------
-- Table structure for wallet
-- ----------------------------
DROP TABLE IF EXISTS `wallet`;
CREATE TABLE `wallet` (
  `wallet_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '钱包ID',
  `user_id` bigint(20) NOT NULL COMMENT '所属用户ID',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0-冻结,1-正常)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  PRIMARY KEY (`wallet_id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_wallet_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='钱包表';

-- ----------------------------
-- Records of wallet
-- ----------------------------
INSERT INTO `wallet` VALUES ('1', '1', '2579980.50', '1', '2025-06-20 17:58:40', '2025-06-25 11:21:04');
INSERT INTO `wallet` VALUES ('2', '2', '1890000.75', '1', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
INSERT INTO `wallet` VALUES ('3', '3', '3560000.00', '1', '2025-06-20 17:58:40', '2025-06-25 13:10:25');
INSERT INTO `wallet` VALUES ('4', '4', '1678000.25', '0', '2025-06-20 17:58:40', '2025-06-24 21:11:48');
INSERT INTO `wallet` VALUES ('5', '5', '2987500.80', '1', '2025-06-20 17:58:40', '2025-06-20 17:58:40');
DROP TRIGGER IF EXISTS `update_bank_account_time`;
DELIMITER ;;
CREATE TRIGGER `update_bank_account_time` BEFORE UPDATE ON `bank_account` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_bank_card_time`;
DELIMITER ;;
CREATE TRIGGER `update_bank_card_time` BEFORE UPDATE ON `bank_card` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_payment_time`;
DELIMITER ;;
CREATE TRIGGER `update_payment_time` BEFORE UPDATE ON `payment` FOR EACH ROW BEGIN 
    SET NEW.update_time = CURRENT_TIMESTAMP; 
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_user_time`;
DELIMITER ;;
CREATE TRIGGER `update_user_time` BEFORE UPDATE ON `user` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
DROP TRIGGER IF EXISTS `update_wallet_time`;
DELIMITER ;;
CREATE TRIGGER `update_wallet_time` BEFORE UPDATE ON `wallet` FOR EACH ROW BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END
;;
DELIMITER ;
