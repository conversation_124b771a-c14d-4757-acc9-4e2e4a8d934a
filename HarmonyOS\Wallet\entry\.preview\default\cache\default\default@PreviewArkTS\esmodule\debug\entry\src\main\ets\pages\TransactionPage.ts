if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionPage_Params {
    currentBalance?: number;
    transactions?: Transaction[];
    currentTab?: number;
    isLoading?: boolean;
    isRefreshing?: boolean;
    userId?: number;
    pageNum?: number;
    pageSize?: number;
    hasMore?: boolean;
    showBalance?: boolean;
    monthIncome?: number;
    monthExpense?: number;
    totalRecords?: number;
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse, AxiosError } from "@normalized:N&&&@ohos/axios/index&2.2.6";
import { UserStorage } from "@normalized:N&&&entry/src/main/ets/common/UserStorage&";
/**
 * API响应结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
/**
 * 分页响应结构
 */
interface PageResponse<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
}
/**
 * 后台返回的交易记录
 */
interface TransactionResponse {
    id: number;
    userId: number;
    type: number; // 1-充值, 2-提现, 3-转账, 4-消费
    amount: number;
    balance: number;
    description: string;
    createTime: string;
    phone?: string;
    accountNumber?: string;
}
/**
 * 前端使用的交易记录
 */
interface Transaction {
    id: number;
    userId: number;
    type: number;
    amount: number;
    balance: number;
    description: string;
    createTime: string;
    phone?: string;
    accountNumber?: string;
}
/**
 * 钱包信息
 */
interface Wallet {
    id: number;
    userId: number;
    balance: number;
    status: number; // 0-冻结, 1-正常
    createTime: string;
    updateTime: string;
}
/**
 * 转账请求
 */
interface TransferRequest {
    fromAccountNumber: string;
    toAccountNumber: string;
    amount: number;
}
export class TransactionPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentBalance = new ObservedPropertySimplePU(0, this, "currentBalance");
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__currentTab = new ObservedPropertySimplePU(0, this, "currentTab");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isRefreshing = new ObservedPropertySimplePU(false, this, "isRefreshing");
        this.__userId = new ObservedPropertySimplePU(0, this, "userId");
        this.__pageNum = new ObservedPropertySimplePU(1, this, "pageNum");
        this.__pageSize = new ObservedPropertySimplePU(10, this, "pageSize");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__showBalance = new ObservedPropertySimplePU(false, this, "showBalance");
        this.__monthIncome = new ObservedPropertySimplePU(950.00, this, "monthIncome");
        this.__monthExpense = new ObservedPropertySimplePU(389.39, this, "monthExpense");
        this.__totalRecords = new ObservedPropertySimplePU(2, this, "totalRecords");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionPage_Params) {
        if (params.currentBalance !== undefined) {
            this.currentBalance = params.currentBalance;
        }
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.currentTab !== undefined) {
            this.currentTab = params.currentTab;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isRefreshing !== undefined) {
            this.isRefreshing = params.isRefreshing;
        }
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
        if (params.pageNum !== undefined) {
            this.pageNum = params.pageNum;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.showBalance !== undefined) {
            this.showBalance = params.showBalance;
        }
        if (params.monthIncome !== undefined) {
            this.monthIncome = params.monthIncome;
        }
        if (params.monthExpense !== undefined) {
            this.monthExpense = params.monthExpense;
        }
        if (params.totalRecords !== undefined) {
            this.totalRecords = params.totalRecords;
        }
    }
    updateStateVars(params: TransactionPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__currentTab.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isRefreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
        this.__pageNum.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__showBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__monthIncome.purgeDependencyOnElmtId(rmElmtId);
        this.__monthExpense.purgeDependencyOnElmtId(rmElmtId);
        this.__totalRecords.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentBalance.aboutToBeDeleted();
        this.__transactions.aboutToBeDeleted();
        this.__currentTab.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isRefreshing.aboutToBeDeleted();
        this.__userId.aboutToBeDeleted();
        this.__pageNum.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__showBalance.aboutToBeDeleted();
        this.__monthIncome.aboutToBeDeleted();
        this.__monthExpense.aboutToBeDeleted();
        this.__totalRecords.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentBalance: ObservedPropertySimplePU<number>;
    get currentBalance() {
        return this.__currentBalance.get();
    }
    set currentBalance(newValue: number) {
        this.__currentBalance.set(newValue);
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __currentTab: ObservedPropertySimplePU<number>; // 0-全部, 1-充值, 2-提现, 3-转账, 4-消费
    get currentTab() {
        return this.__currentTab.get();
    }
    set currentTab(newValue: number) {
        this.__currentTab.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isRefreshing: ObservedPropertySimplePU<boolean>;
    get isRefreshing() {
        return this.__isRefreshing.get();
    }
    set isRefreshing(newValue: boolean) {
        this.__isRefreshing.set(newValue);
    }
    private __userId: ObservedPropertySimplePU<number>; // 初始化为0，将从存储中获取
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    private __pageNum: ObservedPropertySimplePU<number>;
    get pageNum() {
        return this.__pageNum.get();
    }
    set pageNum(newValue: number) {
        this.__pageNum.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __showBalance: ObservedPropertySimplePU<boolean>;
    get showBalance() {
        return this.__showBalance.get();
    }
    set showBalance(newValue: boolean) {
        this.__showBalance.set(newValue);
    }
    // 统计数据
    private __monthIncome: ObservedPropertySimplePU<number>;
    get monthIncome() {
        return this.__monthIncome.get();
    }
    set monthIncome(newValue: number) {
        this.__monthIncome.set(newValue);
    }
    private __monthExpense: ObservedPropertySimplePU<number>;
    get monthExpense() {
        return this.__monthExpense.get();
    }
    set monthExpense(newValue: number) {
        this.__monthExpense.set(newValue);
    }
    private __totalRecords: ObservedPropertySimplePU<number>;
    get totalRecords() {
        return this.__totalRecords.get();
    }
    set totalRecords(newValue: number) {
        this.__totalRecords.set(newValue);
    }
    async aboutToAppear() {
        await this.loadUserInfo();
        if (this.userId > 0) {
            this.loadBalance();
            this.loadTransactions();
        }
        else {
            // 如果没有用户信息，跳转到登录页
            router.replaceUrl({ url: 'pages/LoginPage' });
        }
    }
    /**
     * 加载用户信息
     */
    async loadUserInfo() {
        try {
            const userInfo = await UserStorage.getCurrentUserInfo();
            if (userInfo) {
                this.userId = userInfo.userId;
                console.log('TransactionPage加载用户信息成功:', userInfo);
            }
            else {
                console.log('TransactionPage未找到用户信息，需要重新登录');
                this.userId = 0;
            }
        }
        catch (error) {
            console.error('TransactionPage加载用户信息失败:', error);
            this.userId = 0;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(127:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f7fa');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        // 余额卡片
        this.buildBalanceCard.bind(this)();
        // 统计信息
        this.buildStatsCard.bind(this)();
        // 标签页切换
        this.buildTabBar.bind(this)();
        // 交易记录列表
        this.buildTransactionList.bind(this)();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionPage.ets(150:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 16, right: 16 });
            Row.backgroundColor('#ffffff');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777260, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransactionPage.ets(151:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(158:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777264, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransactionPage.ets(165:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                this.refreshData();
            });
        }, Image);
        Row.pop();
    }
    buildBalanceCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(180:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.padding(20);
            Column.margin({ left: 16, right: 16, top: 16 });
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionPage.ets(181:7)", "entry");
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('当前余额');
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(182:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.showBalance ? { "id": 16777262, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" } : { "id": 16777263, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransactionPage.ets(186:9)", "entry");
            Image.width(16);
            Image.height(16);
            Image.margin({ left: 8 });
            Image.onClick(() => {
                this.showBalance = !this.showBalance;
            });
        }, Image);
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.showBalance ? `¥${this.currentBalance.toFixed(2)}` : '******');
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(196:7)", "entry");
            Text.fontSize(32);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.letterSpacing(this.showBalance ? 0 : 4);
        }, Text);
        Text.pop();
        Column.pop();
    }
    buildStatsCard(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionPage.ets(212:5)", "entry");
            Row.width('100%');
            Row.backgroundColor('#ffffff');
            Row.borderRadius(12);
            Row.padding(20);
            Row.margin({ left: 16, right: 16, top: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(213:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('本月收入');
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(214:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`+${this.monthIncome.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(219:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#34a853');
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(227:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.End);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('本月支出');
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(228:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`-${this.monthExpense.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(233:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#ea4335');
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    buildTabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionPage.ets(250:5)", "entry");
            Row.width('100%');
            Row.backgroundColor('#ffffff');
            Row.padding({ left: 16, right: 16, top: 16, bottom: 16 });
            Row.margin({ top: 12 });
        }, Row);
        this.buildTabItem.bind(this)('全部', 0);
        this.buildTabItem.bind(this)('充值', 1);
        this.buildTabItem.bind(this)('提现', 2);
        this.buildTabItem.bind(this)('转账', 3);
        this.buildTabItem.bind(this)('消费', 4);
        Row.pop();
    }
    buildTabItem(title: string, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(title);
            Button.debugLine("entry/src/main/ets/pages/TransactionPage.ets(265:5)", "entry");
            Button.type(this.currentTab === index ? ButtonType.Capsule : ButtonType.Normal);
            Button.backgroundColor(this.currentTab === index ? '#4285f4' : 'transparent');
            Button.fontColor(this.currentTab === index ? '#ffffff' : '#666666');
            Button.fontSize(14);
            Button.height(36);
            Button.layoutWeight(1);
            Button.onClick(() => {
                this.currentTab = index;
                this.pageNum = 1;
                this.transactions = [];
                this.loadTransactions();
            });
        }, Button);
        Button.pop();
    }
    buildTransactionList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(282:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#ffffff');
            Column.layoutWeight(1);
            Column.margin({ top: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 记录统计
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionPage.ets(284:7)", "entry");
            // 记录统计
            Row.width('100%');
            // 记录统计
            Row.padding({ left: 16, right: 16, top: 16, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`共 ${this.totalRecords} 条记录`);
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(285:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/TransactionPage.ets(289:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近30天');
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(291:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        // 记录统计
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 交易列表
            if (this.isLoading && this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(300:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/TransactionPage.ets(301:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#4285f4');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(306:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 12 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else if (this.transactions.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(315:9)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777281, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                        Image.debugLine("entry/src/main/ets/pages/TransactionPage.ets(316:11)", "entry");
                        Image.width(80);
                        Image.height(80);
                        Image.opacity(0.5);
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(321:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/TransactionPage.ets(330:9)", "entry");
                        List.width('100%');
                        List.layoutWeight(1);
                        List.padding({ left: 16, right: 16 });
                        List.onReachEnd(() => {
                            if (this.hasMore && !this.isLoading) {
                                this.loadMoreTransactions();
                            }
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const transaction = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.debugLine("entry/src/main/ets/pages/TransactionPage.ets(332:13)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.buildTransactionItem.bind(this)(transaction);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction, (transaction: Transaction) => transaction.id?.toString() || Math.random().toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildTransactionItem(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionPage.ets(355:5)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor('#ffffff');
            Row.borderRadius(8);
            Row.margin({ bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易图标
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/TransactionPage.ets(357:7)", "entry");
            // 交易图标
            Stack.margin({ right: 12 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/TransactionPage.ets(358:9)", "entry");
            Circle.width(40);
            Circle.height(40);
            Circle.fill(this.getTransactionColor(transaction.type));
            Circle.opacity(0.1);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.getTransactionIcon(transaction.type));
            Image.debugLine("entry/src/main/ets/pages/TransactionPage.ets(364:9)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor(this.getTransactionColor(transaction.type));
        }, Image);
        // 交易图标
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionPage.ets(372:7)", "entry");
            // 交易信息
            Column.layoutWeight(1);
            // 交易信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionTitle(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(373:9)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#1a1a1a');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 4 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatDateTime(transaction.createTime));
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(380:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(transaction.description || this.getTransactionTitle(transaction.type));
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(386:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 交易信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 金额
            Text.create(this.formatAmount(transaction.type, transaction.amount));
            Text.debugLine("entry/src/main/ets/pages/TransactionPage.ets(395:7)", "entry");
            // 金额
            Text.fontSize(16);
            // 金额
            Text.fontWeight(FontWeight.Bold);
            // 金额
            Text.fontColor(this.getAmountColor(transaction.type));
        }, Text);
        // 金额
        Text.pop();
        Row.pop();
    }
    // 加载余额
    loadBalance() {
        axios({
            url: `http://localhost:8091/wallet/balance/${this.userId}`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<Wallet>>) => {
            console.log('获取钱包余额结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                this.currentBalance = res.data.data.balance;
            }
            else {
                console.error('获取余额失败:', res.data.msg);
                promptAction.showToast({
                    message: res.data.msg || '获取余额失败',
                    duration: 2000
                });
                // 如果获取失败，保持默认值0
                this.currentBalance = 0;
            }
        }).catch((err: AxiosError) => {
            console.error('获取钱包余额错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
            // 网络错误时保持默认值0
            this.currentBalance = 0;
        });
    }
    // 加载交易记录
    loadTransactions() {
        this.isLoading = true;
        // 根据当前标签页确定type参数
        let typeParam: number | undefined = undefined;
        if (this.currentTab > 0) {
            typeParam = this.currentTab; // 1-充值, 2-提现, 3-转账, 4-消费
        }
        axios({
            url: `http://localhost:8091/transactions/user/${this.userId}`,
            method: 'get',
            params: {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                type: typeParam
            }
        }).then((res: AxiosResponse<ApiResponse<PageResponse<TransactionResponse>>>) => {
            console.log('加载交易记录结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                const pageData = res.data.data;
                const newTransactions = pageData.records?.map((item: TransactionResponse) => this.convertToTransaction(item)) || [];
                if (this.pageNum === 1) {
                    this.transactions = newTransactions;
                }
                else {
                    this.transactions = [...this.transactions, ...newTransactions];
                }
                this.totalRecords = pageData.total || 0;
                this.hasMore = pageData.current < pageData.pages;
            }
            else {
                promptAction.showToast({
                    message: res.data.msg || '加载失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('加载交易记录错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isLoading = false;
            this.isRefreshing = false;
        });
    }
    // 加载更多交易记录
    loadMoreTransactions() {
        if (this.hasMore && !this.isLoading) {
            this.pageNum++;
            this.loadTransactions();
        }
    }
    // 刷新数据
    refreshData() {
        this.isRefreshing = true;
        this.pageNum = 1;
        this.transactions = [];
        this.loadBalance();
        this.loadTransactions();
    }
    // 数据转换：后台数据转换为前端数据
    convertToTransaction(response: TransactionResponse): Transaction {
        return {
            id: response.id,
            userId: response.userId,
            type: response.type,
            amount: response.amount,
            balance: response.balance,
            description: response.description,
            createTime: response.createTime,
            phone: response.phone,
            accountNumber: response.accountNumber
        };
    }
    // 获取交易图标
    getTransactionIcon(type: number): Resource {
        switch (type) {
            case 1: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 充值
            case 2: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 提现
            case 3: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 转账
            case 4: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" }; // 消费
            default: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
        }
    }
    // 获取交易颜色
    getTransactionColor(type: number): string {
        switch (type) {
            case 1: return '#34a853'; // 充值 - 绿色
            case 2: return '#ea4335'; // 提现 - 红色
            case 3: return '#4285f4'; // 转账 - 蓝色
            case 4: return '#ff9800'; // 消费 - 橙色
            default: return '#666666';
        }
    }
    // 获取交易标题
    getTransactionTitle(type: number): string {
        switch (type) {
            case 1: return '充值';
            case 2: return '提现';
            case 3: return '转账';
            case 4: return '消费';
            default: return '交易';
        }
    }
    // 格式化金额
    formatAmount(type: number, amount: number): string {
        const prefix = (type === 1) ? '+' : '-';
        return `${prefix}${Math.abs(amount).toFixed(2)}`;
    }
    // 获取金额颜色
    getAmountColor(type: number): string {
        return (type === 1) ? '#34a853' : '#ea4335';
    }
    // 格式化日期时间
    formatDateTime(dateTimeStr: string): string {
        try {
            const date = new Date(dateTimeStr);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${month}-${day} ${hours}:${minutes}`;
        }
        catch (error) {
            return dateTimeStr;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionPage";
    }
}
registerNamedRoute(() => new TransactionPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/TransactionPage", pageFullPath: "entry/src/main/ets/pages/TransactionPage", integratedHsp: "false", moduleType: "followWithHap" });
