{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreCheckSyscap": 1821100, "PreviewProcessResource": 10378700, "PreviewCompileResource": 1162091900, "PreviewHookCompileResource": 882500, "CopyPreviewProfile": 8254400, "ReplacePreviewerPage": 609100, "buildPreviewerResource": 499900, "PreviewUpdateAssets": 5225400}}, "TOTAL_TIME": 14543770900, "BUILD_ID": "202506261141042090", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750909278740"}}}