package com.icss.wallet.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.icss.wallet.entity.BankAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

@Mapper
public interface BankAccountMapper extends BaseMapper<BankAccount> {
    @Update("UPDATE bank_account SET balance = balance + #{amount} WHERE account_number = #{accountNumber}")
    int increaseBalance(@Param("accountNumber") String accountNumber,
                        @Param("amount") BigDecimal amount);

    @Update("UPDATE bank_account SET balance = balance - #{amount} WHERE account_number = #{accountNumber}")
    int decreaseBalance(@Param("accountNumber") String accountNumber,
                        @Param("amount") BigDecimal amount);
    @Select("SELECT * FROM bank_account WHERE account_number = #{accountNumber}")
    BankAccount selectByAccountNumber(@Param("accountNumber") String accountNumber);

}