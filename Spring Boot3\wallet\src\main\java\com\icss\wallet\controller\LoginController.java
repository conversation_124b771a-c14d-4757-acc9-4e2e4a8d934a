package com.icss.wallet.controller;


import com.icss.wallet.result.R;
import com.icss.wallet.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@CrossOrigin
@RestController
@RequestMapping("/auth1")
public class LoginController {
    @Autowired
    private LoginService authService;

    /**
     * 管理员登录
     */
    @PostMapping("/login1")
    public R login(@RequestParam String username, @RequestParam String password) {
        try {
            String token = authService.login(username, password);
            return R.success("登录成功", token);
        } catch (RuntimeException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("登录失败，请稍后重试");
        }
    }

    /**
     * 管理员注册
     */
    @PostMapping("/register")
    public R register(@RequestParam String username,
                     @RequestParam String password,
                     @RequestParam String phone,
                     @RequestParam String realName,
                     @RequestParam(defaultValue = "operator") String role) {
        try {
            String token = authService.register(username, password, phone, realName, role);
            return R.success("注册成功", token);
        } catch (RuntimeException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("注册失败，请稍后重试");
        }
    }
}