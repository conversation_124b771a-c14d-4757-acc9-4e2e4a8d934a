<template>
  <div class="transactions-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><DocumentCopy /></el-icon>
            交易记录管理
          </h2>
          <p>管理系统交易记录，包括充值、提现、转账、消费等各类交易</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="exportData" size="large">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card total">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><List /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.totalTransactions }}</div>
            <div class="stat-label">总交易</div>
          </div>
        </div>
      </el-card>
      <el-card class="stat-card recharge">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Plus /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.rechargeCount }}</div>
            <div class="stat-label">充值交易</div>
          </div>
        </div>
      </el-card>
      <el-card class="stat-card withdraw">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Minus /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ statistics.withdrawCount }}</div>
            <div class="stat-label">提现交易</div>
          </div>
        </div>
      </el-card>
      <el-card class="stat-card amount">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">¥{{ statistics.totalAmount }}</div>
            <div class="stat-label">总交易额</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 筛选标签 -->
    <div class="filter-tabs">
      <el-button
        :type="activeTab === 'all' ? 'primary' : ''"
        @click="setActiveTab('all')"
        class="filter-tab all-tab"
        :class="{ 'active': activeTab === 'all' }"
      >
        <el-icon><List /></el-icon>
        <span>全部交易</span>
        <span class="tab-count">{{ statistics.totalTransactions }}</span>
      </el-button>
      <el-button
        :type="activeTab === 'recharge' ? 'primary' : ''"
        @click="setActiveTab('recharge')"
        class="filter-tab recharge-tab"
        :class="{ 'active': activeTab === 'recharge' }"
      >
        <el-icon><Plus /></el-icon>
        <span>充值</span>
        <span class="tab-count">{{ statistics.rechargeCount }}</span>
      </el-button>
      <el-button
        :type="activeTab === 'withdraw' ? 'primary' : ''"
        @click="setActiveTab('withdraw')"
        class="filter-tab withdraw-tab"
        :class="{ 'active': activeTab === 'withdraw' }"
      >
        <el-icon><Minus /></el-icon>
        <span>提现</span>
        <span class="tab-count">{{ statistics.withdrawCount }}</span>
      </el-button>
      <el-button
        :type="activeTab === 'transfer' ? 'primary' : ''"
        @click="setActiveTab('transfer')"
        class="filter-tab transfer-tab"
        :class="{ 'active': activeTab === 'transfer' }"
      >
        <el-icon><Switch /></el-icon>
        <span>转账</span>
        <span class="tab-count">{{ statistics.transferCount }}</span>
      </el-button>
      <el-button
        :type="activeTab === 'consume' ? 'primary' : ''"
        @click="setActiveTab('consume')"
        class="filter-tab consume-tab"
        :class="{ 'active': activeTab === 'consume' }"
      >
        <el-icon><ShoppingCart /></el-icon>
        <span>消费</span>
        <span class="tab-count">{{ statistics.consumeCount }}</span>
      </el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="交易流水号">
          <el-input 
            v-model="searchForm.transNo" 
            placeholder="请输入交易流水号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="交易状态">
          <el-select 
            v-model="searchForm.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="成功" :value="1" />
            <el-option label="失败" :value="0" />
            <el-option label="处理中" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 交易记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f8f9fa', color: '#606266' }"
      >
        <el-table-column prop="transNo" label="交易流水号" width="180">
          <template #default="{ row }">
            <div class="trans-no">
              <el-icon class="trans-icon"><DocumentCopy /></el-icon>
              <span>{{ row.transNo }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="交易类型" width="100">
          <template #default="{ row }">
            <div class="type-cell">
              <el-icon class="type-icon" :class="getTypeTag(row.type)">
                <component :is="getTypeIcon(row.type)" />
              </el-icon>
              <el-tag :type="getTypeTag(row.type)" size="small">
                {{ getTypeName(row.type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="交易金额" width="120" sortable>
          <template #default="{ row }">
            <div class="amount-cell" :class="getAmountClass(row.type)">
              <span class="amount-symbol">{{ getAmountSymbol(row.type) }}</span>
              <span class="amount-value">¥{{ row.amount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="userInfo" label="用户信息" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <el-icon class="user-icon"><User /></el-icon>
              <div class="user-details">
                <div class="user-name">{{ row.real_name || '未知用户' }}</div>
                <div class="user-phone">{{ row.phone || '-' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="交易状态" width="100">
          <template #default="{ row }">
            <div class="status-cell">
              <el-icon v-if="row.status === 1" class="status-icon success"><Check /></el-icon>
              <el-icon v-else-if="row.status === 0" class="status-icon danger"><Close /></el-icon>
              <el-icon v-else class="status-icon warning"><Clock /></el-icon>
              <el-tag :type="getStatusTag(row.status)" size="small">
                {{ getStatusName(row.status) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="交易时间" width="180" sortable>
          <template #default="{ row }">
            {{ formatDateTime(row.createTime || row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="交易描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import {
  DocumentCopy, Download, Plus, Minus, Money, List, Switch, ShoppingCart,
  Search, Refresh, User, Check, Close, Clock, View
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const activeTab = ref('all')

// 统计数据
const statistics = ref({
  totalTransactions: 0,
  rechargeCount: 0,
  withdrawCount: 0,
  transferCount: 0,
  consumeCount: 0,
  totalAmount: 0
})

// 搜索表单
const searchForm = reactive({
  transNo: '',
  status: null,
  dateRange: null
})

// 方法
const fetchTransactions = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加搜索条件
    if (searchForm.transNo) {
      params.transNo = searchForm.transNo
    }
    if (searchForm.status !== null) {
      params.status = searchForm.status
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }

    // 添加类型过滤
    if (activeTab.value !== 'all') {
      const typeMap = {
        'recharge': 1,
        'withdraw': 2,
        'transfer': 3,
        'consume': 4
      }
      params.type = typeMap[activeTab.value]
    }

    // 调用后台API获取交易数据
    console.log('🚀 发送API请求:', 'http://localhost:8091/transactions/page', params)
    const response = await axios.get('http://localhost:8091/transactions/page', { params })
    console.log('📥 API响应:', response.data)

    if (response.data && response.data.code === 0) {
      tableData.value = response.data.data?.records || []
      total.value = response.data.data?.total || 0
      console.log('✅ 数据加载成功:', tableData.value.length, '条记录')
      console.log('📊 表格数据:', tableData.value)

      // 加载统计数据
      await loadStatistics()
    } else {
      console.error('❌ API返回错误:', response.data)
      ElMessage.error('获取交易记录失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取交易记录失败:', error)
    ElMessage.error('获取交易记录失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 调用新添加的统计API
    const statsResponse = await axios.get('http://localhost:8091/transactions/statistics')
    if (statsResponse.data && statsResponse.data.code === 0) {
      statistics.value = statsResponse.data.data
    } else {
      // 如果统计API失败，使用备用方案
      await loadStatisticsByType()
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 如果统计API失败，使用备用方案
    await loadStatisticsByType()
  }
}

// 按类型加载统计数据
const loadStatisticsByType = async () => {
  try {
    // 获取各类型交易数量 - 使用新的count API
    const [rechargeRes, withdrawRes, transferRes, consumeRes, totalRes] = await Promise.allSettled([
      axios.get('http://localhost:8091/transactions/count', { params: { type: 1 } }),
      axios.get('http://localhost:8091/transactions/count', { params: { type: 2 } }),
      axios.get('http://localhost:8091/transactions/count', { params: { type: 3 } }),
      axios.get('http://localhost:8091/transactions/count', { params: { type: 4 } }),
      axios.get('http://localhost:8091/transactions/count')
    ])

    statistics.value = {
      rechargeCount: rechargeRes.status === 'fulfilled' ? (rechargeRes.value.data?.data || 0) : 0,
      withdrawCount: withdrawRes.status === 'fulfilled' ? (withdrawRes.value.data?.data || 0) : 0,
      transferCount: transferRes.status === 'fulfilled' ? (transferRes.value.data?.data || 0) : 0,
      consumeCount: consumeRes.status === 'fulfilled' ? (consumeRes.value.data?.data || 0) : 0,
      totalTransactions: totalRes.status === 'fulfilled' ? (totalRes.value.data?.data || 0) : 0,
      totalAmount: 0
    }

    // 尝试获取总交易金额
    try {
      const amountResponse = await axios.get('http://localhost:8091/transactions/totalAmount')
      if (amountResponse.data && amountResponse.data.code === 0) {
        statistics.value.totalAmount = amountResponse.data.data || 0
      }
    } catch (amountError) {
      console.warn('获取总交易金额失败:', amountError)
    }

  } catch (error) {
    console.error('获取分类统计数据失败:', error)
    // 使用默认值
    statistics.value = {
      totalTransactions: 0,
      rechargeCount: 0,
      withdrawCount: 0,
      transferCount: 0,
      consumeCount: 0,
      totalAmount: 0
    }
  }
}

const setActiveTab = (tab) => {
  activeTab.value = tab
  currentPage.value = 1 // 重置到第一页
  fetchTransactions()
}

const exportData = () => {
  ElMessage.success('导出功能开发中...')
}

const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
  fetchTransactions()
}

const handleReset = () => {
  Object.assign(searchForm, {
    transNo: '',
    status: null,
    dateRange: null
  })
  activeTab.value = 'all'
  currentPage.value = 1
  fetchTransactions()
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1 // 重置到第一页
  fetchTransactions()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchTransactions()
}

const viewDetail = (row) => {
  ElMessage.info(`查看交易详情: ${row.transNo}`)
}

// 工具方法
const getTypeName = (type) => {
  const typeMap = {
    1: '充值',
    2: '提现',
    3: '转账',
    4: '消费'
  }
  return typeMap[type] || '未知'
}

const getTypeTag = (type) => {
  const tagMap = {
    1: 'success',
    2: 'warning',
    3: 'info',
    4: 'danger'
  }
  return tagMap[type] || 'info'
}

const getTypeIcon = (type) => {
  const iconMap = {
    1: Plus,
    2: Minus,
    3: Switch,
    4: ShoppingCart
  }
  return iconMap[type] || DocumentCopy
}

const getStatusName = (status) => {
  const statusMap = {
    0: '失败',
    1: '成功',
    2: '处理中'
  }
  return statusMap[status] || '未知'
}

const getStatusTag = (status) => {
  const tagMap = {
    0: 'danger',
    1: 'success',
    2: 'warning'
  }
  return tagMap[status] || 'info'
}

const getAmountSymbol = (type) => {
  return type === 2 || type === 4 ? '-' : '+'
}

const getAmountClass = (type) => {
  return type === 2 || type === 4 ? 'amount-negative' : 'amount-positive'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 组件挂载，开始加载数据...')
  fetchTransactions()
})
</script>

<style scoped>
.transactions-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
 margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 28px;
}

.header-left p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card.recharge .stat-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card.withdraw .stat-icon {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-card.amount .stat-icon {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

/* 筛选标签样式 */
.filter-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  justify-content: center;
}

.filter-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px 24px;
  border-radius: 30px;
  border: 2px solid transparent;
  background: #f8f9fa;
  color: #6c757d;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  flex: 0 0 auto;
  min-width: 120px;
  max-width: 160px;
  min-height: 56px;
}

.filter-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.filter-tab:hover::before {
  left: 100%;
}

.filter-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: #e9ecef;
}

/* 全部交易按钮 */
.filter-tab.all-tab {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.filter-tab.all-tab:hover,
.filter-tab.all-tab.active {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* 充值按钮 */
.filter-tab.recharge-tab {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  border-color: #4facfe;
}

.filter-tab.recharge-tab:hover,
.filter-tab.recharge-tab.active {
  background: linear-gradient(135deg, #3d8bfe, #00d4fe);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(79, 172, 254, 0.4);
}

/* 提现按钮 */
.filter-tab.withdraw-tab {
  background: linear-gradient(135deg, #fa709a, #fee140);
  color: white;
  border-color: #fa709a;
}

.filter-tab.withdraw-tab:hover,
.filter-tab.withdraw-tab.active {
  background: linear-gradient(135deg, #f85d8a, #fed130);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(250, 112, 154, 0.4);
}

/* 转账按钮 */
.filter-tab.transfer-tab {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #2c3e50;
  border-color: #a8edea;
}

.filter-tab.transfer-tab:hover,
.filter-tab.transfer-tab.active {
  background: linear-gradient(135deg, #96e6e2, #fec8d8);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(168, 237, 234, 0.4);
}

/* 消费按钮 */
.filter-tab.consume-tab {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #2c3e50;
  border-color: #ffecd2;
}

.filter-tab.consume-tab:hover,
.filter-tab.consume-tab.active {
  background: linear-gradient(135deg, #ffe4c4, #faa687);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(255, 236, 210, 0.4);
}

/* 激活状态样式 */
.filter-tab.el-button--primary {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-width: 2px;
}

.filter-tab .el-icon {
  font-size: 16px;
  margin-right: 4px;
}

.tab-count {
  margin-left: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #2c3e50;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 700;
  min-width: 24px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 当按钮为primary类型时，调整计数标签样式 */
.filter-tab.el-button--primary .tab-count,
.filter-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.95);
  color: #2c3e50;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮文字样式 */
.filter-tab span:not(.tab-count) {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 按钮图标样式 */
.filter-tab .el-icon {
  font-size: 18px;
  margin-right: 6px;
  transition: transform 0.3s ease;
}

.filter-tab:hover .el-icon,
.filter-tab.active .el-icon {
  transform: scale(1.2) rotate(5deg);
}

/* 按钮激活状态的额外样式 */
.filter-tab.active {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  border-width: 3px;
}

/* 按钮点击动画 */
.filter-tab:active {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-tabs {
    justify-content: center;
    gap: 10px;
  }

  .filter-tab {
    flex: 0 0 auto;
    min-width: 110px;
    max-width: 140px;
    padding: 18px 20px;
    min-height: 52px;
  }
}

@media (max-width: 768px) {
  .filter-tabs {
    gap: 8px;
    padding: 20px 16px;
    justify-content: center;
  }

  .filter-tab {
    padding: 16px 18px;
    font-size: 14px;
    min-width: 100px;
    max-width: 130px;
    flex: 0 0 auto;
    min-height: 48px;
  }

  .filter-tab .el-icon {
    font-size: 16px;
  }

  .tab-count {
    padding: 3px 8px;
    font-size: 11px;
    margin-left: 6px;
  }
}

@media (max-width: 480px) {
  .filter-tabs {
    gap: 10px;
    padding: 18px 12px;
    flex-direction: column;
  }

  .filter-tab {
    padding: 16px 20px;
    font-size: 14px;
    min-width: auto;
    max-width: none;
    flex: none;
    width: 100%;
    min-height: 48px;
  }

  .filter-tab .el-icon {
    font-size: 16px;
  }
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 表格卡片样式 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 表格内容样式 */
.trans-no {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trans-icon {
  color: #409eff;
}

.type-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  font-size: 16px;
}

.amount-cell {
  font-weight: 600;
  font-size: 14px;
}

.amount-positive {
  color: #67c23a;
}

.amount-negative {
  color: #f56c6c;
}

.amount-symbol {
  margin-right: 2px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-icon {
  color: #909399;
  font-size: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-phone {
  font-size: 12px;
  color: #909399;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  font-size: 14px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.danger {
  color: #f56c6c;
}

.status-icon.warning {
  color: #e6a23c;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
