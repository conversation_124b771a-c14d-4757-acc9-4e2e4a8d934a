{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "7ceb37d8-ef2b-4de8-adef-824252984a3f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938728155200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b22ad424-2078-4fe6-9a7c-d2221a89ff33", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938740535300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23550f59-2fe5-433c-9cd1-ea5400b9a923", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938740891100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0f86d3-c9cc-473a-9748-7a797cd211b8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028659197700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028667972200, "endTime": 11028846716000}, "additional": {"children": ["a64bfc96-d1b2-4b8f-a849-602efac57a74", "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "30db621f-ef65-453a-b118-2b75479b4799", "58b1023f-457b-48d4-9c3c-fadec364e67f", "99378ace-09c4-4f9a-bf23-101552f84c41", "af65f579-0506-4664-a61c-8149a52dce88", "d7829abd-7cc6-4b5e-824d-a8d3b4042474"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "737e7731-c7c7-402b-a887-5c97b18170d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a64bfc96-d1b2-4b8f-a849-602efac57a74", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028667974600, "endTime": 11028684540600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "b7743512-1ffa-4031-be2b-7796b12a80e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028684591600, "endTime": 11028845560200}, "additional": {"children": ["a250da8e-9fb4-40a6-9bd0-6fc0a6b1c279", "fb68e12a-9471-428d-beba-a2142182a2e3", "2438871b-adc8-4149-8bd6-7bcd4f33e5ad", "0b3ccc68-4327-4233-8a09-7244cb08eb91", "e657c779-223d-4f0f-b68b-e39cdad62bcc", "468e1dfc-6810-431e-bed9-1deb4310cb29", "fe12ac7b-8149-4504-bf6f-9ee4ea234794", "6688c9d7-e605-4ec5-8ef9-fb3bfd0177ec", "b0869a53-ae09-474c-91c8-6b97485b7f01"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30db621f-ef65-453a-b118-2b75479b4799", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028845585200, "endTime": 11028846695500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "dfa764d7-0148-4992-a3a2-b5267902fe0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58b1023f-457b-48d4-9c3c-fadec364e67f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028846702400, "endTime": 11028846711100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "658476d8-6634-46a8-86fd-97e0eb4571d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99378ace-09c4-4f9a-bf23-101552f84c41", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028671427200, "endTime": 11028671617700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "1957a67a-19b5-4550-92ec-00a6bbf8473b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1957a67a-19b5-4550-92ec-00a6bbf8473b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028671427200, "endTime": 11028671617700}, "additional": {"logType": "info", "children": [], "durationId": "99378ace-09c4-4f9a-bf23-101552f84c41", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "af65f579-0506-4664-a61c-8149a52dce88", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028677356100, "endTime": 11028677377500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "5f4c5bc6-e7e6-4b72-8647-f1e1d6dc3c01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f4c5bc6-e7e6-4b72-8647-f1e1d6dc3c01", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028677356100, "endTime": 11028677377500}, "additional": {"logType": "info", "children": [], "durationId": "af65f579-0506-4664-a61c-8149a52dce88", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "3d0a2174-7f42-4a27-97b9-4a634d4f2c9b", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028677451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77ecd9a0-3f11-4dcc-8a60-1cf71a3a0838", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028684220800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7743512-1ffa-4031-be2b-7796b12a80e1", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028667974600, "endTime": 11028684540600}, "additional": {"logType": "info", "children": [], "durationId": "a64bfc96-d1b2-4b8f-a849-602efac57a74", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "a250da8e-9fb4-40a6-9bd0-6fc0a6b1c279", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028694574600, "endTime": 11028694585900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "457d1a64-2b31-4c82-be74-e823b56d9aad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb68e12a-9471-428d-beba-a2142182a2e3", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028694600700, "endTime": 11028700202000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "10ce635c-2245-4139-aecf-56cdd3894e8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2438871b-adc8-4149-8bd6-7bcd4f33e5ad", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028700228000, "endTime": 11028777318300}, "additional": {"children": ["201f9604-3912-4984-a5c3-9393d160509e", "b766e964-c8b9-43e4-ae49-0fa450db8757", "156e810e-ad2a-4459-a3e3-c08bf5e1625d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "66f13e22-5e63-4a8c-be2c-0abafbd2ad5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b3ccc68-4327-4233-8a09-7244cb08eb91", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028777338400, "endTime": 11028803000000}, "additional": {"children": ["120d6b55-364c-404a-92d2-fd9edc4195f5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "bbe0033d-6de4-41ff-b095-0e933897789c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e657c779-223d-4f0f-b68b-e39cdad62bcc", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028803009600, "endTime": 11028817466100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "dbd1c7b6-c908-4113-9474-2cfe93a9c4d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "468e1dfc-6810-431e-bed9-1deb4310cb29", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028818628300, "endTime": 11028831536300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "f26c42a8-8f2a-4dc2-bb5f-c1d143439802"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe12ac7b-8149-4504-bf6f-9ee4ea234794", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028831578600, "endTime": 11028845392300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "35a02240-45e5-43d7-ae19-89ae6426a659"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6688c9d7-e605-4ec5-8ef9-fb3bfd0177ec", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028845425300, "endTime": 11028845544900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "da047f98-9059-426e-85cc-eeffbc047be8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "457d1a64-2b31-4c82-be74-e823b56d9aad", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028694574600, "endTime": 11028694585900}, "additional": {"logType": "info", "children": [], "durationId": "a250da8e-9fb4-40a6-9bd0-6fc0a6b1c279", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "10ce635c-2245-4139-aecf-56cdd3894e8b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028694600700, "endTime": 11028700202000}, "additional": {"logType": "info", "children": [], "durationId": "fb68e12a-9471-428d-beba-a2142182a2e3", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "201f9604-3912-4984-a5c3-9393d160509e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028701246500, "endTime": 11028701539900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2438871b-adc8-4149-8bd6-7bcd4f33e5ad", "logId": "8360bf58-c063-4145-af17-c71a0d8506c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8360bf58-c063-4145-af17-c71a0d8506c1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028701246500, "endTime": 11028701539900}, "additional": {"logType": "info", "children": [], "durationId": "201f9604-3912-4984-a5c3-9393d160509e", "parent": "66f13e22-5e63-4a8c-be2c-0abafbd2ad5d"}}, {"head": {"id": "b766e964-c8b9-43e4-ae49-0fa450db8757", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028705082200, "endTime": 11028776352300}, "additional": {"children": ["2cded849-7c26-4e34-8e39-1fa2d3c1237d", "f31d6bea-1072-4469-b837-7783ee6184f2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2438871b-adc8-4149-8bd6-7bcd4f33e5ad", "logId": "83b7fc65-6a4b-49bb-a1e5-266a13084bcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cded849-7c26-4e34-8e39-1fa2d3c1237d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028705083300, "endTime": 11028708983000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b766e964-c8b9-43e4-ae49-0fa450db8757", "logId": "0e36038e-319d-4cc4-ab50-dd8a50d3892b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f31d6bea-1072-4469-b837-7783ee6184f2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028709002100, "endTime": 11028776335800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b766e964-c8b9-43e4-ae49-0fa450db8757", "logId": "2c6b9ea0-598b-4d9b-99dd-c4bcbb585e59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12e820b7-50fa-4342-a97f-8c697738cb1a", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028705088500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e3916b3-dc2a-45ca-b554-dee6c1294ac6", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028708845100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e36038e-319d-4cc4-ab50-dd8a50d3892b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028705083300, "endTime": 11028708983000}, "additional": {"logType": "info", "children": [], "durationId": "2cded849-7c26-4e34-8e39-1fa2d3c1237d", "parent": "83b7fc65-6a4b-49bb-a1e5-266a13084bcb"}}, {"head": {"id": "c337550f-b70f-43e1-a5d9-ce4a924fa9fe", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028709015000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de85f3a2-9079-4390-bc8c-f6e06cc9bb9e", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028717788300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d86c6f5-f287-4b28-b507-b78000928346", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028717921200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a49a7fc-687e-44da-8bd8-cb9dfd15a2c5", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028718074400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad369738-87d1-4747-9ff9-951dfea8adfe", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028718174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c32023-a82a-4b43-bdf9-ecfa5629ed29", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028719798900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af990754-5315-4b43-9d3c-a3e0aaddb499", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028723107100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a6b43b-f4e1-4feb-8619-e1037ab492b6", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028734104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41260aaa-1cab-44eb-8fb7-ecd5b6ab12f2", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028755633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c818bbf-9d4c-456e-bac1-8ad4c8277435", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028755815400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 42}, "markType": "other"}}, {"head": {"id": "fe1b473a-9a4c-4ace-a25e-b5235537f6f1", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028755831800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 42}, "markType": "other"}}, {"head": {"id": "6e04dcaa-a0b4-45b3-ae2c-4a92f146ff29", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028775980300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c493c06-a2eb-4e11-9f60-1a0064534695", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028776139200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5c6f448-0d5d-4cd5-ad5d-dbdb5ec9cbf7", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028776226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8dc5b11-4918-470c-8603-14fac3397cf6", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028776285700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c6b9ea0-598b-4d9b-99dd-c4bcbb585e59", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028709002100, "endTime": 11028776335800}, "additional": {"logType": "info", "children": [], "durationId": "f31d6bea-1072-4469-b837-7783ee6184f2", "parent": "83b7fc65-6a4b-49bb-a1e5-266a13084bcb"}}, {"head": {"id": "83b7fc65-6a4b-49bb-a1e5-266a13084bcb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028705082200, "endTime": 11028776352300}, "additional": {"logType": "info", "children": ["0e36038e-319d-4cc4-ab50-dd8a50d3892b", "2c6b9ea0-598b-4d9b-99dd-c4bcbb585e59"], "durationId": "b766e964-c8b9-43e4-ae49-0fa450db8757", "parent": "66f13e22-5e63-4a8c-be2c-0abafbd2ad5d"}}, {"head": {"id": "156e810e-ad2a-4459-a3e3-c08bf5e1625d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028777282400, "endTime": 11028777299300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2438871b-adc8-4149-8bd6-7bcd4f33e5ad", "logId": "95a82a9f-3a83-4040-9c75-b839c38365e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95a82a9f-3a83-4040-9c75-b839c38365e1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028777282400, "endTime": 11028777299300}, "additional": {"logType": "info", "children": [], "durationId": "156e810e-ad2a-4459-a3e3-c08bf5e1625d", "parent": "66f13e22-5e63-4a8c-be2c-0abafbd2ad5d"}}, {"head": {"id": "66f13e22-5e63-4a8c-be2c-0abafbd2ad5d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028700228000, "endTime": 11028777318300}, "additional": {"logType": "info", "children": ["8360bf58-c063-4145-af17-c71a0d8506c1", "83b7fc65-6a4b-49bb-a1e5-266a13084bcb", "95a82a9f-3a83-4040-9c75-b839c38365e1"], "durationId": "2438871b-adc8-4149-8bd6-7bcd4f33e5ad", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "120d6b55-364c-404a-92d2-fd9edc4195f5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028778127100, "endTime": 11028802984800}, "additional": {"children": ["3abce4f9-a195-47f9-b6be-fe64cd51cd3e", "1021756f-894e-47ef-bfd5-f6d901444209", "41b04075-831e-47e8-a990-f7251e3dd469"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b3ccc68-4327-4233-8a09-7244cb08eb91", "logId": "4c3c9f3b-4605-4a95-b8c0-3bfc8d47f704"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3abce4f9-a195-47f9-b6be-fe64cd51cd3e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028783000100, "endTime": 11028783020200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "120d6b55-364c-404a-92d2-fd9edc4195f5", "logId": "48fad698-a03f-4d37-bd4d-a6dd63dd86e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48fad698-a03f-4d37-bd4d-a6dd63dd86e1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028783000100, "endTime": 11028783020200}, "additional": {"logType": "info", "children": [], "durationId": "3abce4f9-a195-47f9-b6be-fe64cd51cd3e", "parent": "4c3c9f3b-4605-4a95-b8c0-3bfc8d47f704"}}, {"head": {"id": "1021756f-894e-47ef-bfd5-f6d901444209", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028784834700, "endTime": 11028801400700}, "additional": {"children": ["f4200cbd-fe97-4156-81a9-f60777c3d300", "4dd96fa3-0316-4be8-a7cf-933e8e769192"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "120d6b55-364c-404a-92d2-fd9edc4195f5", "logId": "ebbbdada-2da5-48bf-8184-53e8a7239a3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4200cbd-fe97-4156-81a9-f60777c3d300", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028784835700, "endTime": 11028787659500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1021756f-894e-47ef-bfd5-f6d901444209", "logId": "2a040475-bf1b-45ba-b34e-2fcb8187b60f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dd96fa3-0316-4be8-a7cf-933e8e769192", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028787674200, "endTime": 11028801384400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1021756f-894e-47ef-bfd5-f6d901444209", "logId": "15ed22c1-7c18-49ee-8327-5d4fcc81c99f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "506bb5bc-3bfe-4bd9-9bd9-ed166f6e6cb5", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028784840800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117bbeba-9ce0-494c-98b3-7c7fc07039d9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028787540000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a040475-bf1b-45ba-b34e-2fcb8187b60f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028784835700, "endTime": 11028787659500}, "additional": {"logType": "info", "children": [], "durationId": "f4200cbd-fe97-4156-81a9-f60777c3d300", "parent": "ebbbdada-2da5-48bf-8184-53e8a7239a3e"}}, {"head": {"id": "4edb808c-83a8-4621-84fa-8ec18ae4a0f1", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028787684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c04db5de-1b3a-43b3-8436-793f31dc32c6", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028795842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14306634-6891-49ca-90d8-1680cf4f8248", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028795975300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92fe1437-ff4c-4a52-a49b-ad7405fd15b9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028796166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba89b95-3d3b-4c36-8c98-bcf1eafa2569", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028796314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47cc3cf5-fe89-49f2-8d5c-6899c675c1fc", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028796379400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e251edc0-c76e-447b-bc46-43bbd0269877", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028796431400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27194565-f1ce-488f-a32b-7217dffb86ac", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028796556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "026e9381-fbf3-4221-a5b5-95d2428418d5", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028800964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69285f4a-f6cb-451b-858c-aacf40c3aad4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028801179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3edf0dd4-f5e7-47b6-9e2c-872e0cdd68e8", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028801276800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0038c1d1-930c-47e1-b8f7-79e4f1d7a3bb", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028801333100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15ed22c1-7c18-49ee-8327-5d4fcc81c99f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028787674200, "endTime": 11028801384400}, "additional": {"logType": "info", "children": [], "durationId": "4dd96fa3-0316-4be8-a7cf-933e8e769192", "parent": "ebbbdada-2da5-48bf-8184-53e8a7239a3e"}}, {"head": {"id": "ebbbdada-2da5-48bf-8184-53e8a7239a3e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028784834700, "endTime": 11028801400700}, "additional": {"logType": "info", "children": ["2a040475-bf1b-45ba-b34e-2fcb8187b60f", "15ed22c1-7c18-49ee-8327-5d4fcc81c99f"], "durationId": "1021756f-894e-47ef-bfd5-f6d901444209", "parent": "4c3c9f3b-4605-4a95-b8c0-3bfc8d47f704"}}, {"head": {"id": "41b04075-831e-47e8-a990-f7251e3dd469", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028802954300, "endTime": 11028802967700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "120d6b55-364c-404a-92d2-fd9edc4195f5", "logId": "e9f73dce-1cd3-4aab-947f-b5acd936a627"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9f73dce-1cd3-4aab-947f-b5acd936a627", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028802954300, "endTime": 11028802967700}, "additional": {"logType": "info", "children": [], "durationId": "41b04075-831e-47e8-a990-f7251e3dd469", "parent": "4c3c9f3b-4605-4a95-b8c0-3bfc8d47f704"}}, {"head": {"id": "4c3c9f3b-4605-4a95-b8c0-3bfc8d47f704", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028778127100, "endTime": 11028802984800}, "additional": {"logType": "info", "children": ["48fad698-a03f-4d37-bd4d-a6dd63dd86e1", "ebbbdada-2da5-48bf-8184-53e8a7239a3e", "e9f73dce-1cd3-4aab-947f-b5acd936a627"], "durationId": "120d6b55-364c-404a-92d2-fd9edc4195f5", "parent": "bbe0033d-6de4-41ff-b095-0e933897789c"}}, {"head": {"id": "bbe0033d-6de4-41ff-b095-0e933897789c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028777338400, "endTime": 11028803000000}, "additional": {"logType": "info", "children": ["4c3c9f3b-4605-4a95-b8c0-3bfc8d47f704"], "durationId": "0b3ccc68-4327-4233-8a09-7244cb08eb91", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "308b2f8b-e262-429c-b52f-bb90308434ea", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028816752300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79578aeb-bb46-40b1-b18a-3bb11d181a14", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028817369300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd1c7b6-c908-4113-9474-2cfe93a9c4d1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028803009600, "endTime": 11028817466100}, "additional": {"logType": "info", "children": [], "durationId": "e657c779-223d-4f0f-b68b-e39cdad62bcc", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "b0869a53-ae09-474c-91c8-6b97485b7f01", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028818378800, "endTime": 11028818607700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "logId": "86f78952-3e74-40b0-95a4-c281584b8131"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e740bc93-49af-4e6a-9642-7710ef1a74d9", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028818412600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f78952-3e74-40b0-95a4-c281584b8131", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028818378800, "endTime": 11028818607700}, "additional": {"logType": "info", "children": [], "durationId": "b0869a53-ae09-474c-91c8-6b97485b7f01", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "cc917593-cec0-4e4e-92bb-6463ba5d3b94", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028820030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa76c86-b3f6-4194-ae47-cc31529f04b5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028830454600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26c42a8-8f2a-4dc2-bb5f-c1d143439802", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028818628300, "endTime": 11028831536300}, "additional": {"logType": "info", "children": [], "durationId": "468e1dfc-6810-431e-bed9-1deb4310cb29", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "08f81831-312b-452f-8d49-ad04e5839831", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028831612900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300f7b8a-7ce8-4d59-8a95-e63b6238ba87", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028836543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17f0855e-c164-4371-ae3f-707cc5221c0f", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028836652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5688fac-fb50-46dc-9408-3bd1cf75cd40", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028836881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed23d557-1bc3-47f1-a9cc-fd8a678e106e", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028842006900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "991a49d5-41de-487e-9a41-7c009a4efff7", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028842148600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35a02240-45e5-43d7-ae19-89ae6426a659", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028831578600, "endTime": 11028845392300}, "additional": {"logType": "info", "children": [], "durationId": "fe12ac7b-8149-4504-bf6f-9ee4ea234794", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "c4b99303-4fb9-4c5d-9886-8707470defe6", "name": "Configuration phase cost:151 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028845453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da047f98-9059-426e-85cc-eeffbc047be8", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028845425300, "endTime": 11028845544900}, "additional": {"logType": "info", "children": [], "durationId": "6688c9d7-e605-4ec5-8ef9-fb3bfd0177ec", "parent": "64a13564-60ea-4fcc-b4aa-896d177e61d7"}}, {"head": {"id": "64a13564-60ea-4fcc-b4aa-896d177e61d7", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028684591600, "endTime": 11028845560200}, "additional": {"logType": "info", "children": ["457d1a64-2b31-4c82-be74-e823b56d9aad", "10ce635c-2245-4139-aecf-56cdd3894e8b", "66f13e22-5e63-4a8c-be2c-0abafbd2ad5d", "bbe0033d-6de4-41ff-b095-0e933897789c", "dbd1c7b6-c908-4113-9474-2cfe93a9c4d1", "f26c42a8-8f2a-4dc2-bb5f-c1d143439802", "35a02240-45e5-43d7-ae19-89ae6426a659", "da047f98-9059-426e-85cc-eeffbc047be8", "86f78952-3e74-40b0-95a4-c281584b8131"], "durationId": "b4e278e3-a1cc-4eb3-9296-1f582ec09741", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "d7829abd-7cc6-4b5e-824d-a8d3b4042474", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028846663100, "endTime": 11028846676000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ccaa907-75f2-4e7a-af8a-6393057c5637", "logId": "912ee4d2-19ce-4a71-a26d-52e27d0965bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "912ee4d2-19ce-4a71-a26d-52e27d0965bd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028846663100, "endTime": 11028846676000}, "additional": {"logType": "info", "children": [], "durationId": "d7829abd-7cc6-4b5e-824d-a8d3b4042474", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "dfa764d7-0148-4992-a3a2-b5267902fe0b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028845585200, "endTime": 11028846695500}, "additional": {"logType": "info", "children": [], "durationId": "30db621f-ef65-453a-b118-2b75479b4799", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "658476d8-6634-46a8-86fd-97e0eb4571d2", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028846702400, "endTime": 11028846711100}, "additional": {"logType": "info", "children": [], "durationId": "58b1023f-457b-48d4-9c3c-fadec364e67f", "parent": "737e7731-c7c7-402b-a887-5c97b18170d4"}}, {"head": {"id": "737e7731-c7c7-402b-a887-5c97b18170d4", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028667972200, "endTime": 11028846716000}, "additional": {"logType": "info", "children": ["b7743512-1ffa-4031-be2b-7796b12a80e1", "64a13564-60ea-4fcc-b4aa-896d177e61d7", "dfa764d7-0148-4992-a3a2-b5267902fe0b", "658476d8-6634-46a8-86fd-97e0eb4571d2", "1957a67a-19b5-4550-92ec-00a6bbf8473b", "5f4c5bc6-e7e6-4b72-8647-f1e1d6dc3c01", "912ee4d2-19ce-4a71-a26d-52e27d0965bd"], "durationId": "7ccaa907-75f2-4e7a-af8a-6393057c5637"}}, {"head": {"id": "f6121d5e-c165-457a-9b66-5e1ba22b347d", "name": "Configuration task cost before running: 184 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028846844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a4d6fe-456d-401b-982e-1b0e9312259f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028851514000, "endTime": 11028860430600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e3993ccd-0dec-42cc-9f17-a3f26e55f60a", "logId": "ccf0eab4-d1b7-46e0-9222-df9322de137c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3993ccd-0dec-42cc-9f17-a3f26e55f60a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028848440600}, "additional": {"logType": "detail", "children": [], "durationId": "20a4d6fe-456d-401b-982e-1b0e9312259f"}}, {"head": {"id": "72b8ecb6-21ed-48ab-81f2-5db228db39d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028848944900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44218382-e484-4f98-8abf-00ac423cb4d3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028849045600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64bdea2-5b48-4b17-84bb-c9a8488ade16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028849096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a7f6f6-4d94-42fa-9b9b-888fc968dc82", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028851524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71fd6350-629e-4b6b-82f1-24b8295f1ae1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028860216300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59cf6add-72aa-4d0b-b452-2ae600a0a9e1", "name": "entry : default@PreBuild cost memory -1.5013580322265625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028860360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf0eab4-d1b7-46e0-9222-df9322de137c", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028851514000, "endTime": 11028860430600}, "additional": {"logType": "info", "children": [], "durationId": "20a4d6fe-456d-401b-982e-1b0e9312259f"}}, {"head": {"id": "5f124519-6a65-4b7a-bac9-085320a95224", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028865052300, "endTime": 11028867896100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d1439fab-9d38-41b7-8e3f-ed855f651dac", "logId": "861d6f59-faf4-4b8f-a6f6-f09746be90fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1439fab-9d38-41b7-8e3f-ed855f651dac", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028863663000}, "additional": {"logType": "detail", "children": [], "durationId": "5f124519-6a65-4b7a-bac9-085320a95224"}}, {"head": {"id": "498eeef3-e8b3-4244-aaaf-babab8f5a1e8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028864132100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d1a4da-0db3-48c9-88e1-d815ef14e37d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028864220200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b66170d-02f5-47eb-9888-0ab50c8cab68", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028864273700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dad14e9a-59a0-464b-81c9-75c99d14d7fa", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028865067400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95dd50c1-4399-421c-a39e-55c0c7202179", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028867726800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85072855-a408-4314-b4b4-fd87c9ce97ef", "name": "entry : default@MergeProfile cost memory 0.13210296630859375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028867831200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "861d6f59-faf4-4b8f-a6f6-f09746be90fa", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028865052300, "endTime": 11028867896100}, "additional": {"logType": "info", "children": [], "durationId": "5f124519-6a65-4b7a-bac9-085320a95224"}}, {"head": {"id": "12de3e9e-a513-4aa5-9c57-fe372dc3413e", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028871051700, "endTime": 11028873211700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "77e84a43-00ba-4533-91ba-c6d1357505a0", "logId": "084ef104-743c-4ece-8cbb-a36e6cf38dc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77e84a43-00ba-4533-91ba-c6d1357505a0", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028869733600}, "additional": {"logType": "detail", "children": [], "durationId": "12de3e9e-a513-4aa5-9c57-fe372dc3413e"}}, {"head": {"id": "573277c4-8db3-4455-8fb8-a8b1838a1a57", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028870240300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbfb6d6d-33c1-46b3-baab-2a581fe579aa", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028870325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31426340-03d2-4a2d-899f-16ad2ec31ce4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028870373000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e6d69d-9c4b-4e9c-968b-8923163c3987", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028871060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbaab84f-d7c0-4375-9bf2-c3c0570cb0b8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028871848100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11dbc8f-9fdb-4ba8-8976-f3a0e32f64a2", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028873013800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8670745-9535-43f7-a83a-ccca48f4f985", "name": "entry : default@CreateBuildProfile cost memory 0.10149383544921875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028873135900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084ef104-743c-4ece-8cbb-a36e6cf38dc0", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028871051700, "endTime": 11028873211700}, "additional": {"logType": "info", "children": [], "durationId": "12de3e9e-a513-4aa5-9c57-fe372dc3413e"}}, {"head": {"id": "7fda1e31-9c58-4206-8d82-26d124ea3b30", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876090200, "endTime": 11028876458100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "edd53365-09e3-42f7-99c0-f2c07eefa2b3", "logId": "ed46bf43-c654-46a4-af96-6f398f2ce28f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edd53365-09e3-42f7-99c0-f2c07eefa2b3", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028874846600}, "additional": {"logType": "detail", "children": [], "durationId": "7fda1e31-9c58-4206-8d82-26d124ea3b30"}}, {"head": {"id": "f866a149-d8ec-4d76-961f-f0c185d1711c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028875302700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d3380c1-a41e-4133-93f4-57717f9f0cd9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028875384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cfb0f56-53b4-48b1-a637-522a8d07c44e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028875431400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3482e0-a024-4c60-96d7-1c61fd258fef", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a105da6f-a952-493b-ba6d-9f053aba10d3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30ffcd94-db31-4c6a-8c0f-692111350713", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876238800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83e0c6a1-5c3f-40c7-aad8-f70bad52fc8e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876277600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564a44ee-2c51-4237-b2d6-bd8e6dce0510", "name": "entry : default@PreCheckSyscap cost memory 0.05040740966796875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07037c4b-5882-451d-8203-e414a21a8da4", "name": "runTaskFromQueue task cost before running: 213 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876409300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed46bf43-c654-46a4-af96-6f398f2ce28f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028876090200, "endTime": 11028876458100, "totalTime": 299000}, "additional": {"logType": "info", "children": [], "durationId": "7fda1e31-9c58-4206-8d82-26d124ea3b30"}}, {"head": {"id": "f48b0e77-e5c4-478d-a741-acf0b10c0090", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028886327600, "endTime": 11028887322600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4e66b176-d2c4-40d3-ba99-ffdb86bb1c5c", "logId": "bb283304-0e96-4c64-9cb1-195997e77ce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e66b176-d2c4-40d3-ba99-ffdb86bb1c5c", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028877903100}, "additional": {"logType": "detail", "children": [], "durationId": "f48b0e77-e5c4-478d-a741-acf0b10c0090"}}, {"head": {"id": "8d9eb67f-a5cf-461a-a0f1-c13c95686bcc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028878358300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364c6cec-7f59-4fa4-97c4-82a99496b1c6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028878450600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24043fb0-07c7-4f0a-9b2b-19a64245add4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028878499000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8445cfc-c108-43fb-a124-867023cfba62", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028886345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72b8afb0-4f8b-4102-9151-d5d74fbf982c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028886570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8c1708-0f77-45ec-a672-6aefb824da19", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028887166100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50af103b-f15b-43b8-a712-4d5ef2560671", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06976318359375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028887261700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb283304-0e96-4c64-9cb1-195997e77ce6", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028886327600, "endTime": 11028887322600}, "additional": {"logType": "info", "children": [], "durationId": "f48b0e77-e5c4-478d-a741-acf0b10c0090"}}, {"head": {"id": "c41a440d-a474-452f-9a65-012d79506165", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028890977200, "endTime": 11028892112300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3413f6c1-757d-4ca3-8d56-53d7c1a6e7cd", "logId": "91a0f47c-2791-4e83-96ec-db1d6bc1d7c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3413f6c1-757d-4ca3-8d56-53d7c1a6e7cd", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028888797100}, "additional": {"logType": "detail", "children": [], "durationId": "c41a440d-a474-452f-9a65-012d79506165"}}, {"head": {"id": "11ad603e-378b-4401-8cb6-2ccd2197ccc8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028889300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "210b8227-539b-4069-b6d0-77dfdcadfbad", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028889424000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f71b97b3-3c3b-4257-98ee-17652d0fa15f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028889512100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00bc1b43-ccd2-4204-baa3-f5073a3601b9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028891014100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a81e28fe-d55f-4b3d-b002-05f9f979ff4d", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028891955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d876fb12-d8b4-430f-9855-fbd65f52c7d9", "name": "entry : default@ProcessProfile cost memory 0.05742645263671875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028892049500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91a0f47c-2791-4e83-96ec-db1d6bc1d7c8", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028890977200, "endTime": 11028892112300}, "additional": {"logType": "info", "children": [], "durationId": "c41a440d-a474-452f-9a65-012d79506165"}}, {"head": {"id": "437bf5d3-eb00-4e40-97f5-1c5d63e23b0e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028895519500, "endTime": 11028901574400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "18deb737-f96e-4e38-b13f-b76097b21454", "logId": "5bdd2bd9-2afe-4c1e-ae44-cb8adc467606"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18deb737-f96e-4e38-b13f-b76097b21454", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028893475700}, "additional": {"logType": "detail", "children": [], "durationId": "437bf5d3-eb00-4e40-97f5-1c5d63e23b0e"}}, {"head": {"id": "8b444edf-5769-409f-8347-0b7fd76b3e39", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028893909900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a33b17-dcfb-48c8-9f94-13c6267ba61c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028893984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df63187-914c-4eae-8e92-e8fed61d8abc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028894031700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb0f4f36-8e75-4d9f-9ad0-d1ef81324c9d", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028895527300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f57088-becd-49e3-b309-2593bb8dc50b", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028901345500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee98089-d433-47a0-bb91-8632f5687d09", "name": "entry : default@ProcessRouterMap cost memory 0.22733306884765625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028901495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bdd2bd9-2afe-4c1e-ae44-cb8adc467606", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028895519500, "endTime": 11028901574400}, "additional": {"logType": "info", "children": [], "durationId": "437bf5d3-eb00-4e40-97f5-1c5d63e23b0e"}}, {"head": {"id": "c5bb7e2d-4afe-41c3-aaaa-3d41dfda4c10", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028910093200, "endTime": 11028914577600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a21da504-45fd-4c65-9788-7f24f816ed2c", "logId": "96134adf-b2ef-4ca0-8226-2b7b47c93cda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a21da504-45fd-4c65-9788-7f24f816ed2c", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028904613700}, "additional": {"logType": "detail", "children": [], "durationId": "c5bb7e2d-4afe-41c3-aaaa-3d41dfda4c10"}}, {"head": {"id": "36dea42a-5d2d-4bcd-b502-023c0b8e1df9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028905106800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5720a92-64fd-4654-888a-5b0ca37ac643", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028905201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6122aeb-6052-4b84-9bd9-381572887745", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028905250200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85348a85-419d-4a67-8c5f-6bcb526b29e0", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028906230200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3c61c2e-e625-4b7f-a8a5-cb04fd1b4f6c", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028911963100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5954ca16-8765-419a-8162-31182b03e88d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028912220100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "702123eb-2885-4a3d-a0da-406c74c2f987", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028912325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72995641-4fe8-4551-aa33-fdae171ead10", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028912402100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b320639-7786-4cc0-ac4b-5f1702daa8b9", "name": "entry : default@PreviewProcessResource cost memory 0.09297943115234375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028912509100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f84d49f-f455-4742-b712-421439c7f4a2", "name": "runTaskFromQueue task cost before running: 251 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028914409800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96134adf-b2ef-4ca0-8226-2b7b47c93cda", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028910093200, "endTime": 11028914577600, "totalTime": 2507100}, "additional": {"logType": "info", "children": [], "durationId": "c5bb7e2d-4afe-41c3-aaaa-3d41dfda4c10"}}, {"head": {"id": "38e3b7e1-3a33-4415-bfb8-991cca827b14", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028926170400, "endTime": 11028947479800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "058737c7-dfc7-4c24-8782-89caf4faba14", "logId": "67a378d1-977f-439f-b3e4-0ef159a87512"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "058737c7-dfc7-4c24-8782-89caf4faba14", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028919273100}, "additional": {"logType": "detail", "children": [], "durationId": "38e3b7e1-3a33-4415-bfb8-991cca827b14"}}, {"head": {"id": "b21d748b-4f1c-417a-b72e-303eccb2faef", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028919945100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f43b02-5790-4a71-bc7e-cf4508f4d636", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028920074300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3dca1bd-51f2-4a9a-9baf-2725a78fdb67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028920133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e4c8857-1d95-4b68-8843-9fa7d7848e93", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028926248200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f8db02-ceb7-42d5-b88a-fe594abf315c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028947202700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777b5380-40db-41d8-b3f2-773643529a70", "name": "entry : default@GenerateLoaderJson cost memory 0.**********320312", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028947359700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a378d1-977f-439f-b3e4-0ef159a87512", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028926170400, "endTime": 11028947479800}, "additional": {"logType": "info", "children": [], "durationId": "38e3b7e1-3a33-4415-bfb8-991cca827b14"}}, {"head": {"id": "cde908cb-a610-4424-8f63-3b366fefabe2", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028959733900, "endTime": 11028996401200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "104d5612-ced5-43ce-8160-090003182b9a", "logId": "d909afff-4411-43c7-a722-ab10806b941c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "104d5612-ced5-43ce-8160-090003182b9a", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028955684200}, "additional": {"logType": "detail", "children": [], "durationId": "cde908cb-a610-4424-8f63-3b366fefabe2"}}, {"head": {"id": "91e640fa-f37e-4c40-aa24-57f3a8b59f76", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028956187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fef38338-26d6-4162-a66c-99c715daf60b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028956301200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6afcb7eb-31c7-41ad-aff8-26630dadd297", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028956364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62343caf-5245-4ba9-980c-6ef8af556eda", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028957274200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a878258-4da8-40d1-ab12-83d2729ae473", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028959764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7417b668-c045-4dad-948b-6de8a74801ff", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028996143500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88284381-2bb6-4431-936e-93cb1d409440", "name": "entry : default@PreviewCompileResource cost memory -0.27579498291015625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028996300700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d909afff-4411-43c7-a722-ab10806b941c", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028959733900, "endTime": 11028996401200}, "additional": {"logType": "info", "children": [], "durationId": "cde908cb-a610-4424-8f63-3b366fefabe2"}}, {"head": {"id": "1c9a6cf7-8d67-4b7e-8a6b-04bfad34f68c", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000050300, "endTime": 11029000424500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f3da5292-9dcf-4310-b8f8-f76cd25132ce", "logId": "1ae84ced-c293-4ff0-9c76-ee65de6a482c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3da5292-9dcf-4310-b8f8-f76cd25132ce", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028999275400}, "additional": {"logType": "detail", "children": [], "durationId": "1c9a6cf7-8d67-4b7e-8a6b-04bfad34f68c"}}, {"head": {"id": "656ed6cf-4f71-4649-a767-a947c8555688", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028999772600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067ab311-13a1-4c75-a5d5-d46f7ee834f6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028999887700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367d3bbc-c9e7-423a-872f-baae7e7aedcb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028999945500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40662dbc-aee8-466c-9799-6209a8aa8d88", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dabdcbc-c884-447a-8a3a-0b649f1b8970", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000156900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d958c9-de9b-4dc0-b8aa-1115225115be", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000201300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95942b6d-c9d0-403a-801b-e594555700eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000237200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc6dd61-f345-4e9b-92dc-97cf0a0dbc4c", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db66750-3e20-436b-9f03-fd22c9159995", "name": "runTaskFromQueue task cost before running: 337 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000376500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae84ced-c293-4ff0-9c76-ee65de6a482c", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029000050300, "endTime": 11029000424500, "totalTime": 303900}, "additional": {"logType": "info", "children": [], "durationId": "1c9a6cf7-8d67-4b7e-8a6b-04bfad34f68c"}}, {"head": {"id": "f5170020-a602-4c61-9a64-b5fe8e9a59cd", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029003162200, "endTime": 11029006136800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "2392ee5a-be14-4a45-b9cf-ba2142949590", "logId": "fc7f1c28-1719-4257-b8f9-18e6e32d9f15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2392ee5a-be14-4a45-b9cf-ba2142949590", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029001967500}, "additional": {"logType": "detail", "children": [], "durationId": "f5170020-a602-4c61-9a64-b5fe8e9a59cd"}}, {"head": {"id": "91baefe5-1a09-4e8c-9d22-94d8768c7682", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029002443700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87f9790-3013-4a59-9a09-e8b43d10cdd0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029002532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e941ae06-6159-4802-a521-4f8b2028f778", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029002582200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c9d3f18-ab3c-4961-8471-a7e4dd5e4fcb", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029003170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ced4968-64a1-4ab8-a6af-4bd494d2af7f", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029005860000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "919fcd50-d582-4e27-8ab8-5da1d69364d8", "name": "entry : default@CopyPreviewProfile cost memory -1.**********398438", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029006049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7f1c28-1719-4257-b8f9-18e6e32d9f15", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029003162200, "endTime": 11029006136800}, "additional": {"logType": "info", "children": [], "durationId": "f5170020-a602-4c61-9a64-b5fe8e9a59cd"}}, {"head": {"id": "0e8f1b50-ec5d-483c-b202-a89051300804", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009434600, "endTime": 11029009836100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7c2ec197-5183-4402-bc7c-31926965999d", "logId": "6c6e1e6a-5c3c-4561-a886-c5f9c3feb019"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c2ec197-5183-4402-bc7c-31926965999d", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029008085000}, "additional": {"logType": "detail", "children": [], "durationId": "0e8f1b50-ec5d-483c-b202-a89051300804"}}, {"head": {"id": "2310fa79-5075-4fe2-be8a-052f3236e4a0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029008558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73957ab9-d246-4059-bb9e-77bbe2734f25", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029008651100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c8b2707-0d9d-48c0-8c03-13edaead0c75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029008704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04c7afbd-f2dc-4e68-85f8-5c4080fcc807", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009442900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13164b47-87b9-4155-93ad-74184ff59eba", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5316f46-131e-44d3-a645-9234fae9d1a8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009591200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9481e9e8-4dcf-4775-855b-151bb52a90ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009630600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45369e0f-8738-470d-8046-bd0b446cc2ca", "name": "entry : default@ReplacePreviewerPage cost memory 0.0514678955078125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009711900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3438f025-f1c4-4460-a0eb-87fb8156f74b", "name": "runTaskFromQueue task cost before running: 347 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009788500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c6e1e6a-5c3c-4561-a886-c5f9c3feb019", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029009434600, "endTime": 11029009836100, "totalTime": 333900}, "additional": {"logType": "info", "children": [], "durationId": "0e8f1b50-ec5d-483c-b202-a89051300804"}}, {"head": {"id": "904e15dd-899a-42ea-8203-0e3f15f9b44f", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029011395000, "endTime": 11029011647400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "10dd4b3d-33e6-4cf7-920b-b5e943e983ef", "logId": "8696419a-91d1-451c-b951-88a7d372a919"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10dd4b3d-33e6-4cf7-920b-b5e943e983ef", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029011344500}, "additional": {"logType": "detail", "children": [], "durationId": "904e15dd-899a-42ea-8203-0e3f15f9b44f"}}, {"head": {"id": "fa0848eb-14ef-4ce3-8b7d-767cdad1cd75", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029011403000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4381a190-cf45-43e7-9984-dbf232a96b0c", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029011512600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "097599b2-d61e-475b-8d0b-c06c2b3c913b", "name": "runTaskFromQueue task cost before running: 349 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029011594800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8696419a-91d1-451c-b951-88a7d372a919", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029011395000, "endTime": 11029011647400, "totalTime": 177800}, "additional": {"logType": "info", "children": [], "durationId": "904e15dd-899a-42ea-8203-0e3f15f9b44f"}}, {"head": {"id": "044f361b-6a41-4a36-a9b4-c45e4229b905", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029014392200, "endTime": 11029016787900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "407bd175-66a8-4eba-bd37-28dd2b57a580", "logId": "814ad71a-4654-436c-805a-0abb68e030cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "407bd175-66a8-4eba-bd37-28dd2b57a580", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029013078000}, "additional": {"logType": "detail", "children": [], "durationId": "044f361b-6a41-4a36-a9b4-c45e4229b905"}}, {"head": {"id": "bc277a22-ea47-4f4a-b9cc-c769cebe8c0b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029013534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beeaa8cf-9be4-4c0b-b2ed-6078f9a32a88", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029013623800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2b9a40c-62bf-4ef4-83c7-326da8013355", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029013672100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f504fcd5-47e2-4b4b-a5ce-1ddca4d11dd2", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029014403200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88094dd2-2d4d-4925-9e61-9016e8ac11de", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029016607900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1351a95f-5e09-4576-a5a1-11b7ef6c4483", "name": "entry : default@PreviewUpdateAssets cost memory 0.1068115234375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029016721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "814ad71a-4654-436c-805a-0abb68e030cd", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029014392200, "endTime": 11029016787900}, "additional": {"logType": "info", "children": [], "durationId": "044f361b-6a41-4a36-a9b4-c45e4229b905"}}, {"head": {"id": "33b71020-3036-46e0-bfb9-a75eece9bf86", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029024041500, "endTime": 11041395968600}, "additional": {"children": ["4a53594c-ad1c-417f-b7c6-858c5f26703b"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "591bc07c-683d-4bfa-b7c5-c25665bcb581", "logId": "15f99538-daab-4811-a45e-c9f0896a7162"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "591bc07c-683d-4bfa-b7c5-c25665bcb581", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029018788200}, "additional": {"logType": "detail", "children": [], "durationId": "33b71020-3036-46e0-bfb9-a75eece9bf86"}}, {"head": {"id": "c50a0298-d36c-4187-b885-64dc8a67e9f3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029019242400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a331ac9b-d39d-435c-91cc-d84a140b21c5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029019331100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8520ea25-f0a7-4eb8-beb0-d1a313600fb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029019381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b82397-8fe3-4808-9c4f-ad072776e16a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029024056000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker7", "startTime": 11029048159100, "endTime": 11041393669500}, "additional": {"children": ["c68576a7-ff46-4596-9dfc-1ecb9dcfa612", "614ecd46-30aa-4e7e-b255-8feab6eae9b5", "5d7bf899-723d-4e90-9885-4711921698a2", "af05fbc1-8dd0-4c9b-aa2f-9a867130a57b", "5d4dd8c7-3fa2-421c-b7b8-a09710cebb27", "b609daa5-33b9-41ea-9c58-6a38a53e7d8d", "803fd34a-7441-4a11-931f-8aa7f726309a", "cd54732b-254e-4e08-8b36-c807bf6bb806", "a04fa5fd-4bba-4e7f-a7c3-726ad9f5a714", "17122a8c-87a4-441b-b47d-7821d2919b2c", "d300d89b-e5e7-403e-b928-b1020d984a11", "6b90cd8e-af2c-4768-a0af-1f5cf0f20e33", "d2497324-3e16-41ff-af77-2b63415b1233"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "33b71020-3036-46e0-bfb9-a75eece9bf86", "logId": "3e03e125-4334-4e9d-b817-c930cccf681c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a35ff869-58b8-43f3-948e-1fa3420c7c97", "name": "entry : default@PreviewArkTS cost memory -0.6185989379882812", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029050733600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d730ff-ef99-4032-a4d8-0e8293210f2c", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11032801459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68576a7-ff46-4596-9dfc-1ecb9dcfa612", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11032802619500, "endTime": 11032802639000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "4b4a075e-b1cd-4239-bd4c-70d068c2e039"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b4a075e-b1cd-4239-bd4c-70d068c2e039", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11032802619500, "endTime": 11032802639000}, "additional": {"logType": "info", "children": [], "durationId": "c68576a7-ff46-4596-9dfc-1ecb9dcfa612", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "1195198e-84db-49cc-a256-3dde6921f7c6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038154782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "614ecd46-30aa-4e7e-b255-8feab6eae9b5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038156264900, "endTime": 11038156289800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "8b8c82b0-2230-41c2-8d64-3fd133c7305e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b8c82b0-2230-41c2-8d64-3fd133c7305e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038156264900, "endTime": 11038156289800}, "additional": {"logType": "info", "children": [], "durationId": "614ecd46-30aa-4e7e-b255-8feab6eae9b5", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "0aabbf99-8f7c-442f-b5d0-0165e6fadceb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038156397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7bf899-723d-4e90-9885-4711921698a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038157593000, "endTime": 11038157612000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "b2177597-984e-4de8-b254-dc87d6a8d8d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2177597-984e-4de8-b254-dc87d6a8d8d7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038157593000, "endTime": 11038157612000}, "additional": {"logType": "info", "children": [], "durationId": "5d7bf899-723d-4e90-9885-4711921698a2", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "d0fbda19-6c69-4aa8-8990-ad22cdf8436c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038157721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af05fbc1-8dd0-4c9b-aa2f-9a867130a57b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038158854900, "endTime": 11038158874600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "2fc9f7e8-c51d-41f3-9082-fc3fd9ba32e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fc9f7e8-c51d-41f3-9082-fc3fd9ba32e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038158854900, "endTime": 11038158874600}, "additional": {"logType": "info", "children": [], "durationId": "af05fbc1-8dd0-4c9b-aa2f-9a867130a57b", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "e5e03206-4afd-4504-a168-19caa111d6f7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038158972900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4dd8c7-3fa2-421c-b7b8-a09710cebb27", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038160001600, "endTime": 11038160022600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "6dd358a4-9091-4a5c-8596-68125e59844e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dd358a4-9091-4a5c-8596-68125e59844e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038160001600, "endTime": 11038160022600}, "additional": {"logType": "info", "children": [], "durationId": "5d4dd8c7-3fa2-421c-b7b8-a09710cebb27", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "af84a777-a526-48f4-a792-edab9988560c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038160113700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b609daa5-33b9-41ea-9c58-6a38a53e7d8d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038160967300, "endTime": 11038160983300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "4b0cdd5f-f7b4-4f91-ac7d-db46ee5cf6fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b0cdd5f-f7b4-4f91-ac7d-db46ee5cf6fc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038160967300, "endTime": 11038160983300}, "additional": {"logType": "info", "children": [], "durationId": "b609daa5-33b9-41ea-9c58-6a38a53e7d8d", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "6dd939c7-b40a-49fc-bd61-fa95eadd2bee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038161057000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803fd34a-7441-4a11-931f-8aa7f726309a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038161839400, "endTime": 11038161858400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "6b37f9d3-b720-44c4-8f80-fc570200a020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b37f9d3-b720-44c4-8f80-fc570200a020", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038161839400, "endTime": 11038161858400}, "additional": {"logType": "info", "children": [], "durationId": "803fd34a-7441-4a11-931f-8aa7f726309a", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "a54b7cd1-10c2-4559-a50e-721001c6828e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038441956500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd54732b-254e-4e08-8b36-c807bf6bb806", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038443358300, "endTime": 11038443379700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "eb5e5511-bf68-4915-8bc4-e7d1f0a8694f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb5e5511-bf68-4915-8bc4-e7d1f0a8694f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038443358300, "endTime": 11038443379700}, "additional": {"logType": "info", "children": [], "durationId": "cd54732b-254e-4e08-8b36-c807bf6bb806", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "1d41321f-d8bc-4f21-908f-c9f35f083252", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038693334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04fa5fd-4bba-4e7f-a7c3-726ad9f5a714", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038694658300, "endTime": 11038694686300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "a979c7e0-79c0-480f-9761-bb9db88e509e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a979c7e0-79c0-480f-9761-bb9db88e509e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038694658300, "endTime": 11038694686300}, "additional": {"logType": "info", "children": [], "durationId": "a04fa5fd-4bba-4e7f-a7c3-726ad9f5a714", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "d7bea97e-e22c-4ead-a502-79385f426978", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038825317900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17122a8c-87a4-441b-b47d-7821d2919b2c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038826422300, "endTime": 11038826446200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "75a883b1-ef1e-4a24-8ee4-7b2dc4b63b18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75a883b1-ef1e-4a24-8ee4-7b2dc4b63b18", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038826422300, "endTime": 11038826446200}, "additional": {"logType": "info", "children": [], "durationId": "17122a8c-87a4-441b-b47d-7821d2919b2c", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "7d196d96-83b6-42fa-b572-2bbf33dc0490", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038923716600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d300d89b-e5e7-403e-b928-b1020d984a11", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038924687500, "endTime": 11038924707800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "28b58a7c-8571-46af-bf97-d54db403889b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28b58a7c-8571-46af-bf97-d54db403889b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11038924687500, "endTime": 11038924707800}, "additional": {"logType": "info", "children": [], "durationId": "d300d89b-e5e7-403e-b928-b1020d984a11", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "d3513782-8bbc-4601-bf3c-c9563f33e1f8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11039002336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b90cd8e-af2c-4768-a0af-1f5cf0f20e33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11039003375000, "endTime": 11039003392400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "ff2d35bb-6906-4731-8cb3-7aaf02930219"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff2d35bb-6906-4731-8cb3-7aaf02930219", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11039003375000, "endTime": 11039003392400}, "additional": {"logType": "info", "children": [], "durationId": "6b90cd8e-af2c-4768-a0af-1f5cf0f20e33", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "da4eb952-9d9b-47e7-b3ba-4c5f0da9e453", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041392505600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2497324-3e16-41ff-af77-2b63415b1233", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041393584400, "endTime": 11041393602300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "logId": "7c50f9a5-eb75-4911-be97-d35bd1ac8b8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c50f9a5-eb75-4911-be97-d35bd1ac8b8b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041393584400, "endTime": 11041393602300}, "additional": {"logType": "info", "children": [], "durationId": "d2497324-3e16-41ff-af77-2b63415b1233", "parent": "3e03e125-4334-4e9d-b817-c930cccf681c"}}, {"head": {"id": "3e03e125-4334-4e9d-b817-c930cccf681c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker7", "startTime": 11029048159100, "endTime": 11041393669500}, "additional": {"logType": "error", "children": ["4b4a075e-b1cd-4239-bd4c-70d068c2e039", "8b8c82b0-2230-41c2-8d64-3fd133c7305e", "b2177597-984e-4de8-b254-dc87d6a8d8d7", "2fc9f7e8-c51d-41f3-9082-fc3fd9ba32e8", "6dd358a4-9091-4a5c-8596-68125e59844e", "4b0cdd5f-f7b4-4f91-ac7d-db46ee5cf6fc", "6b37f9d3-b720-44c4-8f80-fc570200a020", "eb5e5511-bf68-4915-8bc4-e7d1f0a8694f", "a979c7e0-79c0-480f-9761-bb9db88e509e", "75a883b1-ef1e-4a24-8ee4-7b2dc4b63b18", "28b58a7c-8571-46af-bf97-d54db403889b", "ff2d35bb-6906-4731-8cb3-7aaf02930219", "7c50f9a5-eb75-4911-be97-d35bd1ac8b8b"], "durationId": "4a53594c-ad1c-417f-b7c6-858c5f26703b", "parent": "15f99538-daab-4811-a45e-c9f0896a7162"}}, {"head": {"id": "01bda690-9045-4aa1-8db1-85a676490b83", "name": "default@PreviewArkTS watch work[7] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041395767800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15f99538-daab-4811-a45e-c9f0896a7162", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11029024041500, "endTime": 11041395968600}, "additional": {"logType": "error", "children": ["3e03e125-4334-4e9d-b817-c930cccf681c"], "durationId": "33b71020-3036-46e0-bfb9-a75eece9bf86"}}, {"head": {"id": "ba3deb10-f2ea-46b6-9bb2-f79aa72dfd2c", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041396181700}, "additional": {"logType": "debug", "children": [], "durationId": "33b71020-3036-46e0-bfb9-a75eece9bf86"}}, {"head": {"id": "69a6f8de-bda2-434a-8c1e-d64e59c8a161", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:79:26\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:97:25\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:115:25\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:133:28\n Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown)\n\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041397058500}, "additional": {"logType": "debug", "children": [], "durationId": "33b71020-3036-46e0-bfb9-a75eece9bf86"}}, {"head": {"id": "06c3cb35-4a12-4769-8d31-800e83e0b2b3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041406944000, "endTime": 11041406994700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c289e63-0f12-4fdc-a4fd-82a50877b91c", "logId": "6a143774-23e2-431a-a4f4-a384752172e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a143774-23e2-431a-a4f4-a384752172e4", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041406944000, "endTime": 11041406994700}, "additional": {"logType": "info", "children": [], "durationId": "06c3cb35-4a12-4769-8d31-800e83e0b2b3"}}, {"head": {"id": "badcd94d-8243-4567-9361-1c63b61a4f96", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11028663570400, "endTime": 11041407097300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 43}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "837ef127-4606-47f5-a186-af2368202ee3", "name": "BUILD FAILED in 12 s 744 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407123300}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "302d9368-f983-4659-8a2c-856f85fef8c1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1825e99c-9cd4-4355-944c-bf4f0ce037a6", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407327100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3eb8c17-ceb6-433c-b949-5767668ad924", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407368000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16b9d32-47d7-440e-b0c6-2974631b62e5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407423400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c80918-8a12-4353-91ae-9d7efd7df919", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407462100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eba95ca7-29c6-42a7-a5f1-9d382bd849b3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407498100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "978ba4f1-282b-46ba-b998-689d947cbed2", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a46af8aa-33f3-4b19-9264-728fae85fd4d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407565200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc998b36-72f7-469d-969c-02f387d12e67", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37721ead-ae2d-4f4a-9d59-1b5e66c847bc", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041407627100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f26f04-ea7f-4399-b9b6-b0ae7dcd229c", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041410383600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c92146-a6bb-4220-bc03-d9a1ef6f017e", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041411128900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cf69c97-182e-4cb2-81d9-fd090f2c841e", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041411392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45c12a4-d65b-4e14-b41a-5e14e897e86b", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041425819700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2680c585-2597-4fad-962e-5216c9fd26a2", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041426817400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f1a57a-a2ba-4d5b-8412-690509baf251", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041427115700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4712b009-1d1f-42d1-908d-a4b0ebe80c2e", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041427392400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b2c52aa-53a0-4606-b20b-43c172497cc9", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041428107600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c000bd68-b4c2-4a2f-a031-eb7c4ae77cfb", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041431741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efd0a50d-7a0e-45bb-9f3c-b2840e70b797", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041432219400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "644c55c7-c48c-4c26-8786-94d94592f75c", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041432677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a71416-833d-4e8b-9ba5-c91c622cf08e", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041433038900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea69874-9768-4b96-b7be-ddbe0a2cecef", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 11041433362200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}