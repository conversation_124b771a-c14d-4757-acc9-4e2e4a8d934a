import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';

/**
 * API响应结构
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 后台返回的银行卡数据结构
 */
interface BankCardResponse {
  cardId: number;
  userId: number;
  bankName: string;
  cardNumber: string;
  cardType: number; // 1-储蓄卡, 2-信用卡
  status: number; // 0-未绑定, 1-已绑定
  isDefault: number; // 0-非默认, 1-默认
  cardHolder: string;
  phone: string;
  expiryDate?: string;
  cvv?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 前端使用的银行卡信息
 */
interface BankCard {
  id: number;
  userId: number;
  bankName: string;
  cardNumber: string;
  cardType: string;
  status: number; // 0-未绑定, 1-已绑定
  isDefault: number; // 0-非默认, 1-默认
  holderName: string;
  phoneNumber: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 添加银行卡请求
 */
interface AddCardRequest {
  userId: number;
  cardNumber: string;
  bankName: string;
  cardType: number; // 1-储蓄卡, 2-信用卡
  cardHolder: string;
  phone: string;
}

@Entry
@Component
export struct BankCardPage {
  @State cards: BankCard[] = [];
  @State currentTab: number = 0; // 0-所有银行卡, 1-已绑定银行卡
  @State showAddDialog: boolean = false;
  @State isLoading: boolean = false;
  @State userId: number = 1;

  // 添加银行卡表单
  @State newCardNumber: string = '';
  @State newHolderName: string = '';
  @State newPhoneNumber: string = '';
  @State newBankName: string = '';
  @State newCardType: string = '储蓄卡';
  @State isAdding: boolean = false;

  aboutToAppear() {
    this.loadCards();
  }

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 标签页切换
      this.buildTabBar()

      // 添加银行卡按钮
      this.buildAddButton()

      // 银行卡列表
      this.buildCardList()

      // 添加银行卡弹窗
      if (this.showAddDialog) {
        this.buildAddDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f8f9fa')
  }

  @Builder
  buildHeader() {
    Row() {
      Image($r('app.media.back'))
        .width(24)
        .height(24)
        .onClick(() => {
          router.back();
        })

      Text('银行卡管理')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 去重按钮
      Image($r('app.media.refresh'))
        .width(24)
        .height(24)
        .onClick(() => {
          this.removeDuplicatesManually();
        })
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor('#ffffff')
    .shadow({
      radius: 4,
      color: '#1a000000',
      offsetX: 0,
      offsetY: 2
    })
  }

  @Builder
  buildTabBar() {
    Row() {
      Button('所有银行卡')
        .type(this.currentTab === 0 ? ButtonType.Capsule : ButtonType.Normal)
        .backgroundColor(this.currentTab === 0 ? '#4285f4' : '#f8f9fa')
        .fontColor(this.currentTab === 0 ? '#ffffff' : '#666666')
        .fontSize(14)
        .height(40)
        .layoutWeight(1)
        .margin({ right: 8 })
        .onClick(() => {
          this.currentTab = 0;
          this.loadCards();
        })

      Button('已绑定银行卡')
        .type(this.currentTab === 1 ? ButtonType.Capsule : ButtonType.Normal)
        .backgroundColor(this.currentTab === 1 ? '#4285f4' : '#f8f9fa')
        .fontColor(this.currentTab === 1 ? '#ffffff' : '#666666')
        .fontSize(14)
        .height(40)
        .layoutWeight(1)
        .margin({ left: 8 })
        .onClick(() => {
          this.currentTab = 1;
          this.loadBoundCards();
        })
    }
    .width('100%')
    .padding(16)
  }

  @Builder
  buildAddButton() {
    Button() {
      Row() {
        Image($r('app.media.add'))
          .width(20)
          .height(20)
          .fillColor('#ffffff')
          .margin({ right: 8 })

        Text('添加银行卡')
          .fontSize(16)
          .fontColor('#ffffff')
          .fontWeight(FontWeight.Medium)
      }
    }
    .width('calc(100% - 32vp)')
    .height(48)
    .backgroundColor('#4285f4')
    .borderRadius(24)
    .margin({ left: 16, right: 16, bottom: 16 })
    .shadow({
      radius: 8,
      color: '#334285f4',
      offsetX: 0,
      offsetY: 4
    })
    .onClick(() => {
      this.showAddDialog = true;
    })
  }

  @Builder
  buildCardList() {
    if (this.isLoading) {
      Column() {
        LoadingProgress()
          .width(40)
          .height(40)
          .color('#4285f4')

        Text('加载中...')
          .fontSize(14)
          .fontColor('#999999')
          .margin({ top: 12 })
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
    } else if (this.cards.length === 0) {
      Column() {
        Image($r('app.media.icbc'))
          .width(80)
          .height(80)
          .opacity(0.5)

        Text('暂无银行卡')
          .fontSize(16)
          .fontColor('#999999')
          .margin({ top: 16 })

        Text('点击上方按钮添加银行卡')
          .fontSize(14)
          .fontColor('#cccccc')
          .margin({ top: 8 })
      }
      .width('100%')
      .height(200)
      .justifyContent(FlexAlign.Center)
    } else {
      List({ space: 12 }) {
        ForEach(this.cards, (card: BankCard) => {
          ListItem() {
            this.buildCardItem(card)
          }
        }, (card: BankCard) => card.id.toString())
      }
      .width('100%')
      .layoutWeight(1)
      .padding({ left: 16, right: 16 })
    }
  }

  @Builder
  buildCardItem(card: BankCard) {
    Column() {
      // 银行卡头部信息
      Row() {
        // 银行图标
        Stack() {
          Circle()
            .width(48)
            .height(48)
            .fill('#ffffff')
            .shadow({
              radius: 4,
              color: '#1a000000',
              offsetX: 0,
              offsetY: 2
            })

          Image(this.getBankIcon(card.bankName))
            .width(32)
            .height(32)
        }
        .margin({ right: 16 })

        Column() {
          Text(card.bankName)
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .alignSelf(ItemAlign.Start)

          Text(`**** **** **** ${card.cardNumber.slice(-4)}`)
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 4 })
            .alignSelf(ItemAlign.Start)

          Text(card.cardType)
            .fontSize(12)
            .fontColor('#999999')
            .margin({ top: 2 })
            .alignSelf(ItemAlign.Start)
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        // 状态标签
        Column() {
          if (card.status === 1) {
            Text('已绑定')
              .fontSize(12)
              .fontColor('#ffffff')
              .backgroundColor('#34a853')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
              .margin({ bottom: 6 })
          } else {
            Text('未绑定')
              .fontSize(12)
              .fontColor('#ffffff')
              .backgroundColor('#ea4335')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
              .margin({ bottom: 6 })
          }

          if (card.isDefault === 1) {
            Text('默认')
              .fontSize(12)
              .fontColor('#ffffff')
              .backgroundColor('#ff9800')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
          }
        }
        .alignItems(HorizontalAlign.End)
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 操作按钮
      this.buildCardActions(card)
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(16)
    .padding(20)
    .shadow({
      radius: 8,
      color: '#1a000000',
      offsetX: 0,
      offsetY: 2
    })
  }

  @Builder
  buildCardActions(card: BankCard) {
    Column() {
      // 第一行操作按钮
      Row() {
        if (card.status === 1) {
          // 已绑定状态
          Button('解除绑定')
            .type(ButtonType.Capsule)
            .backgroundColor('#fff5f5')
            .fontColor('#ea4335')
            .fontSize(12)
            .height(32)
            .layoutWeight(1)
            .margin({ right: 6 })
            .onClick(() => {
              this.unbindCard(card.id);
            })

          if (card.isDefault === 0) {
            Button('设为默认')
              .type(ButtonType.Capsule)
              .backgroundColor('#e3f2fd')
              .fontColor('#4285f4')
              .fontSize(12)
              .height(32)
              .layoutWeight(1)
              .margin({ left: 6 })
              .onClick(() => {
                this.setDefaultCard(card.id);
              })
          }
        } else {
          // 未绑定状态
          Button('绑定银行卡')
            .type(ButtonType.Capsule)
            .backgroundColor('#4285f4')
            .fontColor('#ffffff')
            .fontSize(12)
            .height(32)
            .layoutWeight(1)
            .onClick(() => {
              this.bindCard(card.id);
            })
        }
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)

      // 第二行操作按钮
      Row() {
        Button('查看详情')
          .type(ButtonType.Normal)
          .backgroundColor('#f8f9fa')
          .fontColor('#666666')
          .fontSize(12)
          .height(32)
          .layoutWeight(1)
          .margin({ right: 6 })
          .onClick(() => {
            this.viewCardDetail(card.id);
          })

        Button('删除')
          .type(ButtonType.Normal)
          .backgroundColor('#fff5f5')
          .fontColor('#ea4335')
          .fontSize(12)
          .height(32)
          .layoutWeight(1)
          .margin({ left: 6 })
          .onClick(() => {
            this.deleteCard(card.id);
          })
      }
      .width('100%')
      .margin({ top: 8 })
      .justifyContent(FlexAlign.SpaceBetween)
    }
    .width('100%')
  }

  @Builder
  buildAddDialog() {
    Stack() {
      // 背景遮罩
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0,0,0,0.5)')
        .onClick(() => {
          this.showAddDialog = false;
          this.clearForm();
        })

      // 弹窗内容
      Column() {
        // 弹窗标题
        Row() {
          Text('添加银行卡')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .layoutWeight(1)

          Image($r('app.media.eye_close'))
            .width(24)
            .height(24)
            .onClick(() => {
              this.showAddDialog = false;
              this.clearForm();
            })
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 表单内容
        Column() {
          // 银行卡号
          Column() {
            Text('银行卡号')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入16-19位银行卡号' })
              .type(InputType.Number)
              .maxLength(19)
              .onChange((value: string) => {
                this.newCardNumber = value;
              })
              .backgroundColor('#f8f9fa')
              .borderRadius(8)
              .height(48)
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)
          .margin({ bottom: 16 })

          // 持卡人姓名
          Column() {
            Text('持卡人姓名')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入持卡人姓名' })
              .onChange((value: string) => {
                this.newHolderName = value;
              })
              .backgroundColor('#f8f9fa')
              .borderRadius(8)
              .height(48)
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)
          .margin({ bottom: 16 })

          // 开户银行
          Column() {
            Text('开户银行')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入开户银行' })
              .onChange((value: string) => {
                this.newBankName = value;
              })
              .backgroundColor('#f8f9fa')
              .borderRadius(8)
              .height(48)
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)
          .margin({ bottom: 16 })

          // 预留手机号
          Column() {
            Text('预留手机号')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            TextInput({ placeholder: '请输入银行预留手机号' })
              .type(InputType.PhoneNumber)
              .maxLength(11)
              .onChange((value: string) => {
                this.newPhoneNumber = value;
              })
              .backgroundColor('#f8f9fa')
              .borderRadius(8)
              .height(48)
          }
          .width('100%')
          .alignItems(HorizontalAlign.Start)
          .margin({ bottom: 24 })
        }
        .width('100%')

        // 操作按钮
        Row() {
          Button('取消')
            .type(ButtonType.Normal)
            .backgroundColor('#f8f9fa')
            .fontColor('#666666')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              this.showAddDialog = false;
              this.clearForm();
            })

          Button(this.isAdding ? '添加中...' : '确认添加')
            .type(ButtonType.Capsule)
            .backgroundColor('#4285f4')
            .fontColor('#ffffff')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ left: 8 })
            .enabled(!this.isAdding)
            .onClick(() => {
              this.addCard();
            })
        }
        .width('100%')
      }
      .width('calc(100% - 48vp)')
      .backgroundColor('#ffffff')
      .borderRadius(16)
      .padding(24)
      .shadow({
        radius: 16,
        color: '#********',
        offsetX: 0,
        offsetY: 8
      })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  // 加载银行卡列表
  loadCards() {
    this.isLoading = true;
    const url = this.currentTab === 0
      ? `http://localhost:8091/bankCards/user/${this.userId}`
      : `http://localhost:8091/bankCards/bound/user/${this.userId}`;

    axios({
      url: url,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<BankCardResponse[]>>) => {
      console.log('加载银行卡结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        // 转换数据并去重
        const convertedCards = res.data.data.map((item: BankCardResponse) => this.convertToBankCard(item));
        this.cards = this.removeDuplicateCards(convertedCards);

        console.log(`原始银行卡数量: ${res.data.data.length}, 去重后数量: ${this.cards.length}`);
      } else {
        promptAction.showToast({
          message: res.data.msg || '加载失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('加载银行卡错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isLoading = false;
    });
  }

  // 加载已绑定银行卡
  loadBoundCards() {
    this.isLoading = true;

    axios({
      url: `http://localhost:8091/bankCards/bound/user/${this.userId}`,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<BankCardResponse[]>>) => {
      console.log('加载已绑定银行卡结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        // 转换数据并去重
        const convertedCards = res.data.data.map((item: BankCardResponse) => this.convertToBankCard(item));
        this.cards = this.removeDuplicateCards(convertedCards);

        console.log(`原始已绑定银行卡数量: ${res.data.data.length}, 去重后数量: ${this.cards.length}`);
      } else {
        promptAction.showToast({
          message: res.data.msg || '加载失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('加载已绑定银行卡错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isLoading = false;
    });
  }

  // 添加银行卡
  addCard() {
    // 表单验证
    if (!this.newCardNumber || this.newCardNumber.length < 16) {
      promptAction.showToast({
        message: '请输入正确的银行卡号',
        duration: 2000
      });
      return;
    }

    if (!this.newHolderName || this.newHolderName.length < 2) {
      promptAction.showToast({
        message: '请输入正确的持卡人姓名',
        duration: 2000
      });
      return;
    }

    if (!this.newBankName) {
      promptAction.showToast({
        message: '请输入开户银行',
        duration: 2000
      });
      return;
    }

    if (!this.newPhoneNumber || this.newPhoneNumber.length !== 11) {
      promptAction.showToast({
        message: '请输入正确的手机号',
        duration: 2000
      });
      return;
    }

    // 检查是否已存在相同卡号的银行卡
    const existingCard = this.cards.find(card => card.cardNumber === this.newCardNumber);
    if (existingCard) {
      promptAction.showToast({
        message: `该银行卡已存在 (${existingCard.bankName})`,
        duration: 2000
      });
      return;
    }

    this.isAdding = true;

    const requestData: AddCardRequest = {
      userId: this.userId,
      cardNumber: this.newCardNumber,
      bankName: this.newBankName,
      cardType: this.newCardType === '储蓄卡' ? 1 : 2,
      cardHolder: this.newHolderName,
      phone: this.newPhoneNumber
    };

    axios({
      url: 'http://localhost:8091/bankCards/add',
      method: 'post',
      data: requestData
    }).then((res: AxiosResponse<ApiResponse<BankCardResponse>>) => {
      console.log('添加银行卡结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        this.showAddDialog = false;
        this.clearForm();
        this.loadCards();
      }
    }).catch((err: AxiosError) => {
      console.error('添加银行卡错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isAdding = false;
    });
  }

  // 绑定银行卡
  bindCard(cardId: number) {
    axios({
      url: `http://localhost:8091/bankCards/bind/${cardId}`,
      method: 'post'
    }).then((res: AxiosResponse<ApiResponse<null>>) => {
      console.log('绑定银行卡结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        // 绑定成功后刷新数据
        if (this.currentTab === 0) {
          // 在"所有银行卡"选项卡，重新加载所有银行卡
          this.loadCards();
        } else {
          // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
          this.loadBoundCards();
        }
      }
    }).catch((err: AxiosError) => {
      console.error('绑定银行卡错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    });
  }

  // 解除绑定银行卡
  unbindCard(cardId: number) {
    promptAction.showDialog({
      title: '确认解绑',
      message: '确定要解绑这张银行卡吗？',
      buttons: [
        { text: '取消', color: '#666666' },
        { text: '确定', color: '#ea4335' }
      ]
    }).then(result => {
      if (result.index === 1) {
        axios({
          url: `http://localhost:8091/bankCards/unbind/${cardId}`,
          method: 'post'
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
          console.log('解绑银行卡结果:', JSON.stringify(res.data));

          promptAction.showToast({
            message: res.data.msg || '操作完成',
            duration: 2000
          });

          if (res.data.code === 0) {
            // 解绑成功后刷新数据
            if (this.currentTab === 0) {
              // 在"所有银行卡"选项卡，重新加载所有银行卡
              this.loadCards();
            } else {
              // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
              this.loadBoundCards();
            }
          }
        }).catch((err: AxiosError) => {
          console.error('解绑银行卡错误:', err.message);
          promptAction.showToast({
            message: '网络错误，请重试',
            duration: 2000
          });
        });
      }
    });
  }

  // 设置默认银行卡
  setDefaultCard(cardId: number) {
    axios({
      url: `http://localhost:8091/bankCards/${cardId}/default`,
      method: 'put'
    }).then((res: AxiosResponse<ApiResponse<null>>) => {
      console.log('设置默认银行卡结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        // 设置默认卡成功后刷新数据
        if (this.currentTab === 0) {
          // 在"所有银行卡"选项卡，重新加载所有银行卡
          this.loadCards();
        } else {
          // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
          this.loadBoundCards();
        }
      }
    }).catch((err: AxiosError) => {
      console.error('设置默认银行卡错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    });
  }

  // 删除银行卡
  deleteCard(cardId: number) {
    promptAction.showDialog({
      title: '确认删除',
      message: '确定要删除这张银行卡吗？删除后无法恢复。',
      buttons: [
        { text: '取消', color: '#666666' },
        { text: '确定', color: '#ea4335' }
      ]
    }).then(result => {
      if (result.index === 1) {
        axios({
          url: `http://localhost:8091/bankCards/${cardId}`,
          method: 'delete'
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
          console.log('删除银行卡结果:', JSON.stringify(res.data));

          promptAction.showToast({
            message: res.data.msg || '操作完成',
            duration: 2000
          });

          if (res.data.code === 0) {
            // 删除成功后刷新数据
            if (this.currentTab === 0) {
              // 在"所有银行卡"选项卡，重新加载所有银行卡
              this.loadCards();
            } else {
              // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
              this.loadBoundCards();
            }
          }
        }).catch((err: AxiosError) => {
          console.error('删除银行卡错误:', err.message);
          promptAction.showToast({
            message: '网络错误，请重试',
            duration: 2000
          });
        });
      }
    });
  }

  // 查看银行卡详情
  viewCardDetail(cardId: number) {
    // 找到对应的银行卡数据
    const card = this.cards.find(c => c.id === cardId);
    if (!card) {
      promptAction.showToast({
        message: '银行卡信息不存在',
        duration: 2000
      });
      return;
    }

    // 跳转到银行卡详情页面，传递完整的银行卡数据
    router.pushUrl({
      url: 'pages/CardDetailPage',
      params: {
        cardId: card.id,
        bankName: card.bankName,
        cardNumber: card.cardNumber,
        cardType: card.cardType === '储蓄卡' ? 1 : 2,
        cardHolder: card.holderName,
        phone: card.phoneNumber,
        status: card.status,
        isDefault: card.isDefault,
        createTime: card.createTime
      }
    }).then(() => {
      console.log('跳转到银行卡详情页面成功, cardId:', cardId);
    }).catch((err: Error) => {
      console.error('跳转到银行卡详情页面失败:', err.message);
      promptAction.showToast({
        message: '页面跳转失败',
        duration: 2000
      });
    });
  }

  // 数据转换：后台数据转换为前端数据
  convertToBankCard(response: BankCardResponse): BankCard {
    return {
      id: response.cardId,
      userId: response.userId,
      bankName: response.bankName,
      cardNumber: response.cardNumber,
      cardType: response.cardType === 1 ? '储蓄卡' : '信用卡',
      status: response.status,
      isDefault: response.isDefault,
      holderName: response.cardHolder,
      phoneNumber: response.phone,
      createTime: response.createTime,
      updateTime: response.updateTime
    };
  }

  // 去除重复银行卡
  removeDuplicateCards(cards: BankCard[]): BankCard[] {
    const cardMap = new Map<string, BankCard>();

    for (const card of cards) {
      const cardNumber = card.cardNumber;
      const existingCard = cardMap.get(cardNumber);

      if (!existingCard) {
        // 如果没有重复，直接添加
        cardMap.set(cardNumber, card);
        console.log(`添加银行卡: ${card.bankName} **** ${cardNumber.slice(-4)} (状态: ${card.status === 1 ? '已绑定' : '未绑定'})`);
      } else {
        // 如果有重复，选择优先级更高的银行卡
        let shouldReplace = false;
        let reason = '';

        // 优先级规则：
        // 1. 已绑定的银行卡优先于未绑定的
        if (card.status === 1 && existingCard.status === 0) {
          shouldReplace = true;
          reason = '已绑定优先于未绑定';
        }
        // 2. 如果状态相同，默认银行卡优先于非默认
        else if (card.status === existingCard.status && card.isDefault === 1 && existingCard.isDefault === 0) {
          shouldReplace = true;
          reason = '默认卡优先于非默认卡';
        }
        // 3. 如果状态和默认设置都相同，选择ID更大的（更新的）
        else if (card.status === existingCard.status && card.isDefault === existingCard.isDefault && card.id > existingCard.id) {
          shouldReplace = true;
          reason = '选择更新的记录';
        }

        if (shouldReplace) {
          cardMap.set(cardNumber, card);
          console.log(`替换重复银行卡: ${card.bankName} **** ${cardNumber.slice(-4)} (${reason})`);
        } else {
          console.log(`保留原银行卡: ${existingCard.bankName} **** ${cardNumber.slice(-4)}, 丢弃重复项`);
        }
      }
    }

    const uniqueCards = Array.from(cardMap.values());
    console.log(`去重完成: 原始数量 ${cards.length}, 去重后数量 ${uniqueCards.length}`);

    return uniqueCards;
  }

  // 手动去重操作
  removeDuplicatesManually() {
    promptAction.showDialog({
      title: '去重确认',
      message: '是否要删除重复的银行卡？\n\n去重规则：\n• 已绑定优先于未绑定\n• 默认卡优先于非默认卡\n• 保留最新的记录',
      buttons: [
        { text: '取消', color: '#666666' },
        { text: '确定', color: '#4285f4' }
      ]
    }).then(result => {
      if (result.index === 1) {
        const originalCount = this.cards.length;
        this.cards = this.removeDuplicateCards(this.cards);
        const removedCount = originalCount - this.cards.length;

        if (removedCount > 0) {
          promptAction.showToast({
            message: `已删除 ${removedCount} 张重复银行卡`,
            duration: 2000
          });
        } else {
          promptAction.showToast({
            message: '没有发现重复的银行卡',
            duration: 2000
          });
        }
      }
    });
  }

  // 清空表单
  clearForm() {
    this.newCardNumber = '';
    this.newHolderName = '';
    this.newPhoneNumber = '';
    this.newBankName = '';
    this.newCardType = '储蓄卡';
  }

  // 获取银行图标
  getBankIcon(bankName: string): Resource {
    switch (bankName) {
      case '中国银行': return $r('app.media.boc');
      case '建设银行': return $r('app.media.ccb');
      case '工商银行': return $r('app.media.icbc');
      case '农业银行': return $r('app.media.abc');
      case '招商银行': return $r('app.media.cmb');
      case '交通银行': return $r('app.media.bcm');
      case '中信银行': return $r('app.media.citic');
      case '光大银行': return $r('app.media.ceb');
      case '华夏银行': return $r('app.media.hxb');
      case '民生银行': return $r('app.media.cmbc');
      case '兴业银行': return $r('app.media.cib');
      case '浦发银行': return $r('app.media.spdb');
      default:
        return $r('app.media.bank');
    }
  }
}
