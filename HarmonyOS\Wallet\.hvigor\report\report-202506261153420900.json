{"version": "2.0", "ppid": 13468, "events": [{"head": {"id": "b6494733-ba3a-45df-87ea-5a3e34f00dbf", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562698144300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcede7b9-b5a8-4805-8f2a-3c86731dc15b", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11663086666300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb77e3a-7d9e-494d-89d0-27f5a618e64f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11663087788900, "endTime": 11663087814200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "93b51861-43f4-46df-ad70-ffbebeee0876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93b51861-43f4-46df-ad70-ffbebeee0876", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11663087788900, "endTime": 11663087814200}, "additional": {"logType": "info", "children": [], "durationId": "fdb77e3a-7d9e-494d-89d0-27f5a618e64f"}}, {"head": {"id": "fbeded31-c69a-4d95-b60c-16bd98756d5a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664420080400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee939b96-4b34-4204-8e49-09b04bd2eacb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664421787800, "endTime": 11664421823500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "2f691a9e-ba4a-44e7-b0b2-952bdf55f9ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f691a9e-ba4a-44e7-b0b2-952bdf55f9ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664421787800, "endTime": 11664421823500}, "additional": {"logType": "info", "children": [], "durationId": "ee939b96-4b34-4204-8e49-09b04bd2eacb"}}, {"head": {"id": "005ef3f9-0ea2-4cb2-b9bc-94614e5b5c07", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664421949800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc3215c-30d5-443a-868f-bb0a18db2a1f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664423054500, "endTime": 11664423091200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "7d0a1421-9821-4e50-bd9a-4da8ffa8f324"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d0a1421-9821-4e50-bd9a-4da8ffa8f324", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664423054500, "endTime": 11664423091200}, "additional": {"logType": "info", "children": [], "durationId": "4fc3215c-30d5-443a-868f-bb0a18db2a1f"}}, {"head": {"id": "120f5384-f156-4c9c-bc7b-d7ddba8ca987", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664893960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4252dad3-ca9c-4e56-99a4-4e33eca39f9f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664895042900, "endTime": 11664895064900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "881e3e1f-5437-442d-b5f8-f4469bfd2f00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "881e3e1f-5437-442d-b5f8-f4469bfd2f00", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664895042900, "endTime": 11664895064900}, "additional": {"logType": "info", "children": [], "durationId": "4252dad3-ca9c-4e56-99a4-4e33eca39f9f"}}, {"head": {"id": "8dde4a39-e6dc-4522-a71f-377bdd3f863a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664895157900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df9976a5-8af9-49e3-ba18-a2815a57d3d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664895957900, "endTime": 11664895975800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "2d9e065d-4c2a-41c6-b8cc-bd32a7f8bcda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d9e065d-4c2a-41c6-b8cc-bd32a7f8bcda", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664895957900, "endTime": 11664895975800}, "additional": {"logType": "info", "children": [], "durationId": "df9976a5-8af9-49e3-ba18-a2815a57d3d6"}}, {"head": {"id": "92ea4a15-6b4c-43a3-9ecb-e9b5e71f8cee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664896046700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ef387a-8fad-4ec6-a67e-4a5148f93737", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664897060600, "endTime": 11664897097900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "400a74e5-c86d-49ad-a527-ae9a3a9c3d34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "400a74e5-c86d-49ad-a527-ae9a3a9c3d34", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664897060600, "endTime": 11664897097900}, "additional": {"logType": "info", "children": [], "durationId": "f9ef387a-8fad-4ec6-a67e-4a5148f93737"}}, {"head": {"id": "75091adc-a3e9-49b6-8ff9-d6c13c32f83d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664897238300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84848057-2a67-492e-aecf-3658d90a4fbc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664898188300, "endTime": 11664898214000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "ab2d40a1-12a5-4b10-9bb9-e65cc5920152"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab2d40a1-12a5-4b10-9bb9-e65cc5920152", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664898188300, "endTime": 11664898214000}, "additional": {"logType": "info", "children": [], "durationId": "84848057-2a67-492e-aecf-3658d90a4fbc"}}, {"head": {"id": "2995d6d5-d248-4117-a7ec-2f58d7046b08", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664898455800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79fd75fd-bfbc-434d-a4c6-f5dc73d86c3c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664899253200, "endTime": 11664899268700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "315fa917-2ed3-4b16-a6df-f3a2692b9fae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "315fa917-2ed3-4b16-a6df-f3a2692b9fae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664899253200, "endTime": 11664899268700}, "additional": {"logType": "info", "children": [], "durationId": "79fd75fd-bfbc-434d-a4c6-f5dc73d86c3c"}}, {"head": {"id": "eaaa631f-506c-4d69-a304-d5ff7cbe15fc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664899345900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40176392-7c56-4b92-bf1a-1a825717a782", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664900095300, "endTime": 11664900109200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "164dbe2b-a611-4f4f-9ab1-6579e4b32e1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "164dbe2b-a611-4f4f-9ab1-6579e4b32e1a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664900095300, "endTime": 11664900109200}, "additional": {"logType": "info", "children": [], "durationId": "40176392-7c56-4b92-bf1a-1a825717a782"}}, {"head": {"id": "a72ea9fe-4eba-4009-8f68-90860b892d01", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664900177200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3426d754-dbfe-4433-beb8-eda268ebac07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664900894500, "endTime": 11664900906700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "46bb2dd1-938c-4c3d-af1a-88e394f1c7ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46bb2dd1-938c-4c3d-af1a-88e394f1c7ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664900894500, "endTime": 11664900906700}, "additional": {"logType": "info", "children": [], "durationId": "3426d754-dbfe-4433-beb8-eda268ebac07"}}, {"head": {"id": "62a0e29b-814b-421e-91df-6841046969cd", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664900965100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552a60c2-8a67-4270-a684-368c040df326", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664901673800, "endTime": 11664901685500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "efac202d-0d4b-4bfb-bc24-a0e1b3f859d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efac202d-0d4b-4bfb-bc24-a0e1b3f859d9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11664901673800, "endTime": 11664901685500}, "additional": {"logType": "info", "children": [], "durationId": "552a60c2-8a67-4270-a684-368c040df326"}}, {"head": {"id": "54d2670a-1e64-4cde-82ce-4dd02fe98b3a", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11680276807500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dd5a605-b004-4d96-8b19-7453557a23c7", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11680277259500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f72f9de0-9aad-4127-bd3f-ac8d730a62ca", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681954476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681961366200, "endTime": 11682109247600}, "additional": {"children": ["ab67fc36-bc8d-43d1-ab8f-22160b2c1d8c", "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "b429925b-1b87-470e-be76-61b4121d8ab7", "21b00a43-b8aa-4a77-9e28-509e919964c3", "8623bf56-7e81-4d76-9a9c-d37f073cb4b9", "3bbf0a3c-638e-4f19-a940-b6be1a754986", "7f389117-a4e3-4ae1-80df-0a7b3689f91c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "2d04ab94-6bb0-4225-826d-585c93554470"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab67fc36-bc8d-43d1-ab8f-22160b2c1d8c", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681961367400, "endTime": 11681972637200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "4f0157e8-4856-4735-bc44-4513f110e953"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681972652400, "endTime": 11682108143100}, "additional": {"children": ["fc74ff9f-7264-4796-bc1e-6ad160e79a57", "d30df8ef-6aed-4857-94bb-4637179a9969", "e4dab619-c0a2-48f3-b20a-469c5533acb7", "19fe4d07-02c5-4774-89d5-d036b8d68579", "cdae285a-5ac1-419a-9a6b-c8bfc7a0988e", "a7a0dc61-e24c-48a8-b0b8-3a335842a52a", "f5634558-f61d-4681-9122-e35ba84b7a20", "5015bb9c-938f-42c1-b459-827816837081", "fdca9631-648d-4e85-b998-f2cd5b9ce6ca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "9b131384-e6bc-4144-a8b7-97f26f126a64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b429925b-1b87-470e-be76-61b4121d8ab7", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682108168000, "endTime": 11682109232700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "979f5ee0-509f-4362-921d-b49ada49f403"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21b00a43-b8aa-4a77-9e28-509e919964c3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682109237700, "endTime": 11682109243300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "28b46aba-9e3d-455a-bda9-8a1c452aa4ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8623bf56-7e81-4d76-9a9c-d37f073cb4b9", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681964058900, "endTime": 11681964085100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "e51f3dc5-dd85-4756-b1ce-0cdb8b097237"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e51f3dc5-dd85-4756-b1ce-0cdb8b097237", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681964058900, "endTime": 11681964085100}, "additional": {"logType": "info", "children": [], "durationId": "8623bf56-7e81-4d76-9a9c-d37f073cb4b9", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "3bbf0a3c-638e-4f19-a940-b6be1a754986", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681968998400, "endTime": 11681969010700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "559b55b4-df08-405a-a350-b423d08e60b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "559b55b4-df08-405a-a350-b423d08e60b7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681968998400, "endTime": 11681969010700}, "additional": {"logType": "info", "children": [], "durationId": "3bbf0a3c-638e-4f19-a940-b6be1a754986", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "d6a80385-6efc-454d-9ef7-738450ccd2d6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681969059800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b868202b-5a82-4d8f-bd0e-c709b61991bd", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681972532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0157e8-4856-4735-bc44-4513f110e953", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681961367400, "endTime": 11681972637200}, "additional": {"logType": "info", "children": [], "durationId": "ab67fc36-bc8d-43d1-ab8f-22160b2c1d8c", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "fc74ff9f-7264-4796-bc1e-6ad160e79a57", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681977796800, "endTime": 11681977806300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "70b3933c-caa1-4ac7-b053-0b5dea643163"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d30df8ef-6aed-4857-94bb-4637179a9969", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681977820100, "endTime": 11681981274300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "d8a47591-10a8-48f1-abd8-b80fb1aa6d2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4dab619-c0a2-48f3-b20a-469c5533acb7", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681981290800, "endTime": 11682051278200}, "additional": {"children": ["83748c73-11d0-41e3-bf09-afc8a823b758", "ad5f7789-f3da-4489-8edd-28820ccceb49", "5f6b1832-a5cd-491c-9980-2f785133cfe3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "d68f150d-1616-41ff-b042-aaffdb228a0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19fe4d07-02c5-4774-89d5-d036b8d68579", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682051292500, "endTime": 11682071763700}, "additional": {"children": ["6a935e34-d88a-444a-855d-4a63e9488f23"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "eb7aa08b-3a31-4985-939c-a658f03cda96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdae285a-5ac1-419a-9a6b-c8bfc7a0988e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682071771200, "endTime": 11682085167200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "8451f165-b5bd-4954-9844-5c44df5b70ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7a0dc61-e24c-48a8-b0b8-3a335842a52a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682086261600, "endTime": 11682094554200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "4fa56ae4-873b-4d51-a420-7cd3fc5f4561"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5634558-f61d-4681-9122-e35ba84b7a20", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682094579700, "endTime": 11682108019800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "5eb4cb01-4d75-47e9-9159-628317aecb95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5015bb9c-938f-42c1-b459-827816837081", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682108037200, "endTime": 11682108131400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "5bc04a53-dd83-4a31-b25f-fda1604c0090"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70b3933c-caa1-4ac7-b053-0b5dea643163", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681977796800, "endTime": 11681977806300}, "additional": {"logType": "info", "children": [], "durationId": "fc74ff9f-7264-4796-bc1e-6ad160e79a57", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "d8a47591-10a8-48f1-abd8-b80fb1aa6d2d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681977820100, "endTime": 11681981274300}, "additional": {"logType": "info", "children": [], "durationId": "d30df8ef-6aed-4857-94bb-4637179a9969", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "83748c73-11d0-41e3-bf09-afc8a823b758", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681981869700, "endTime": 11681981886600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4dab619-c0a2-48f3-b20a-469c5533acb7", "logId": "4f9eee59-07d2-4691-aff4-5f9a0bef14d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f9eee59-07d2-4691-aff4-5f9a0bef14d3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681981869700, "endTime": 11681981886600}, "additional": {"logType": "info", "children": [], "durationId": "83748c73-11d0-41e3-bf09-afc8a823b758", "parent": "d68f150d-1616-41ff-b042-aaffdb228a0c"}}, {"head": {"id": "ad5f7789-f3da-4489-8edd-28820ccceb49", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681984215900, "endTime": 11682050620000}, "additional": {"children": ["82ecebd3-1675-48e3-995b-6bc2991b4d2c", "0e1599ed-8ded-412e-b066-360fdb0d9863"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4dab619-c0a2-48f3-b20a-469c5533acb7", "logId": "a13bd3bc-6f19-49f0-b944-232f5f8809e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82ecebd3-1675-48e3-995b-6bc2991b4d2c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681984217000, "endTime": 11681989476200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad5f7789-f3da-4489-8edd-28820ccceb49", "logId": "e8785be1-e988-4030-a097-8dca622529cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e1599ed-8ded-412e-b066-360fdb0d9863", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681989497400, "endTime": 11682050607300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad5f7789-f3da-4489-8edd-28820ccceb49", "logId": "1e804079-ef8b-4ddb-b845-c4626e79ffd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a7cbd2c-631b-448f-abae-21ff09b62d29", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681984224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d18932-5d0d-4693-9533-f68b6d334c15", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681989364900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8785be1-e988-4030-a097-8dca622529cd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681984217000, "endTime": 11681989476200}, "additional": {"logType": "info", "children": [], "durationId": "82ecebd3-1675-48e3-995b-6bc2991b4d2c", "parent": "a13bd3bc-6f19-49f0-b944-232f5f8809e0"}}, {"head": {"id": "4a0540eb-e851-488e-b828-2da1bc405736", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681989511900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9cdc38-5b4c-4b65-977a-3714c29d4c0e", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681995305100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4e5d7c-73b4-49e9-b18c-6dfe428ab313", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681995404600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d603c061-5684-46e9-af5b-fbea119fd7bb", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681995524200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4271c97-b1a5-4170-8f2a-483cbcc16cfe", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681995720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "797edac2-601c-454b-be37-c7e9128ca6e2", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681997698500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcafbec6-715f-4a79-ae33-c8315c7f8ce4", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682000821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cec31d4-6a7c-4087-9d6a-6d3270699b0c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682009518900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b8c81d8-28ec-47b3-8825-75e551b22a0d", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682030885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97029929-c0d6-489a-82c7-ba44bc8f3869", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682031104600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 53}, "markType": "other"}}, {"head": {"id": "8a0dd3d6-a650-4206-bcc2-3728d4163b9f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682031122600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 53}, "markType": "other"}}, {"head": {"id": "7e4f7e70-23a4-43dc-9488-b1c4c21e8504", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682050322200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8f0bbce-00a8-4e7a-8573-b3cf6f5d8a50", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682050449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21b05cfa-e3b2-4978-a95e-d967b8b89375", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682050512600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c13c64a4-14ed-4335-ae1d-fd88faa9dc78", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682050563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e804079-ef8b-4ddb-b845-c4626e79ffd4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681989497400, "endTime": 11682050607300}, "additional": {"logType": "info", "children": [], "durationId": "0e1599ed-8ded-412e-b066-360fdb0d9863", "parent": "a13bd3bc-6f19-49f0-b944-232f5f8809e0"}}, {"head": {"id": "a13bd3bc-6f19-49f0-b944-232f5f8809e0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681984215900, "endTime": 11682050620000}, "additional": {"logType": "info", "children": ["e8785be1-e988-4030-a097-8dca622529cd", "1e804079-ef8b-4ddb-b845-c4626e79ffd4"], "durationId": "ad5f7789-f3da-4489-8edd-28820ccceb49", "parent": "d68f150d-1616-41ff-b042-aaffdb228a0c"}}, {"head": {"id": "5f6b1832-a5cd-491c-9980-2f785133cfe3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682051251500, "endTime": 11682051264500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e4dab619-c0a2-48f3-b20a-469c5533acb7", "logId": "801353e7-3e63-4cb1-82eb-03e1e1b01c32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "801353e7-3e63-4cb1-82eb-03e1e1b01c32", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682051251500, "endTime": 11682051264500}, "additional": {"logType": "info", "children": [], "durationId": "5f6b1832-a5cd-491c-9980-2f785133cfe3", "parent": "d68f150d-1616-41ff-b042-aaffdb228a0c"}}, {"head": {"id": "d68f150d-1616-41ff-b042-aaffdb228a0c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681981290800, "endTime": 11682051278200}, "additional": {"logType": "info", "children": ["4f9eee59-07d2-4691-aff4-5f9a0bef14d3", "a13bd3bc-6f19-49f0-b944-232f5f8809e0", "801353e7-3e63-4cb1-82eb-03e1e1b01c32"], "durationId": "e4dab619-c0a2-48f3-b20a-469c5533acb7", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "6a935e34-d88a-444a-855d-4a63e9488f23", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682051858200, "endTime": 11682071750500}, "additional": {"children": ["9385b16c-93ff-460f-bcc5-2385fa2dcd12", "90639f31-b07e-4f97-bc63-b94f1b7acfe9", "8a23ad49-ecf7-45e4-9d59-3747b2197506"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19fe4d07-02c5-4774-89d5-d036b8d68579", "logId": "be9e7d79-81ac-4e11-aa9a-10f446439571"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9385b16c-93ff-460f-bcc5-2385fa2dcd12", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682055145700, "endTime": 11682055156900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a935e34-d88a-444a-855d-4a63e9488f23", "logId": "0f9d93e3-f1c3-401d-9c1a-511a2a2eba5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f9d93e3-f1c3-401d-9c1a-511a2a2eba5b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682055145700, "endTime": 11682055156900}, "additional": {"logType": "info", "children": [], "durationId": "9385b16c-93ff-460f-bcc5-2385fa2dcd12", "parent": "be9e7d79-81ac-4e11-aa9a-10f446439571"}}, {"head": {"id": "90639f31-b07e-4f97-bc63-b94f1b7acfe9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682056878400, "endTime": 11682070542800}, "additional": {"children": ["f203ece1-a917-43ff-bb67-fbc687d7cf3f", "91c487e4-41fd-47d4-bddd-f631cba4fb60"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a935e34-d88a-444a-855d-4a63e9488f23", "logId": "edc385bd-d546-44c9-918a-472d563fd5f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f203ece1-a917-43ff-bb67-fbc687d7cf3f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682056879700, "endTime": 11682060351800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90639f31-b07e-4f97-bc63-b94f1b7acfe9", "logId": "570e00d6-7dc1-4c4f-8703-40f320feae5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91c487e4-41fd-47d4-bddd-f631cba4fb60", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682060366300, "endTime": 11682070529200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90639f31-b07e-4f97-bc63-b94f1b7acfe9", "logId": "cf28d7be-03b1-4869-9445-a43f3a556066"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45f52278-8574-451c-93ba-0a420fc405a4", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682056884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f29588f5-15b1-410d-81f5-d6cc5c9e0917", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682060253900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "570e00d6-7dc1-4c4f-8703-40f320feae5c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682056879700, "endTime": 11682060351800}, "additional": {"logType": "info", "children": [], "durationId": "f203ece1-a917-43ff-bb67-fbc687d7cf3f", "parent": "edc385bd-d546-44c9-918a-472d563fd5f4"}}, {"head": {"id": "e527ea24-5ede-4342-914f-049127c2de81", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682060383500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8d87c0a-6092-4cd9-b369-0d18e722e37c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682066692200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62a80184-76eb-4f26-9822-e36e6a2ae236", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682066822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5569ac0a-1c61-4279-ab8f-8ac4948c3219", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682067034900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "630d050a-bbf4-4ec0-a145-1a0df5bb3c30", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682067179400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34002aad-e6f4-412e-b29c-ced61c232da9", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682067242900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25c0af6-c7b4-4959-9d35-973e7d67b479", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682067290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3847860b-0d71-4ac2-bda0-be4ee8f42b71", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682067341500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee6c4254-1795-40fc-8f1e-33f576bf823c", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682069766300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639093f4-f401-4451-96bc-e39ad3f08b37", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682070355400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6d82d4-c7ba-47d7-99d1-a5fd7f86114b", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682070436000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c7785af-917b-4e5f-b7f2-bd5c7ef7c268", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682070484900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf28d7be-03b1-4869-9445-a43f3a556066", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682060366300, "endTime": 11682070529200}, "additional": {"logType": "info", "children": [], "durationId": "91c487e4-41fd-47d4-bddd-f631cba4fb60", "parent": "edc385bd-d546-44c9-918a-472d563fd5f4"}}, {"head": {"id": "edc385bd-d546-44c9-918a-472d563fd5f4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682056878400, "endTime": 11682070542800}, "additional": {"logType": "info", "children": ["570e00d6-7dc1-4c4f-8703-40f320feae5c", "cf28d7be-03b1-4869-9445-a43f3a556066"], "durationId": "90639f31-b07e-4f97-bc63-b94f1b7acfe9", "parent": "be9e7d79-81ac-4e11-aa9a-10f446439571"}}, {"head": {"id": "8a23ad49-ecf7-45e4-9d59-3747b2197506", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682071723900, "endTime": 11682071735700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a935e34-d88a-444a-855d-4a63e9488f23", "logId": "a251d6c0-3b24-4df3-b42a-40b6b58a40be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a251d6c0-3b24-4df3-b42a-40b6b58a40be", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682071723900, "endTime": 11682071735700}, "additional": {"logType": "info", "children": [], "durationId": "8a23ad49-ecf7-45e4-9d59-3747b2197506", "parent": "be9e7d79-81ac-4e11-aa9a-10f446439571"}}, {"head": {"id": "be9e7d79-81ac-4e11-aa9a-10f446439571", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682051858200, "endTime": 11682071750500}, "additional": {"logType": "info", "children": ["0f9d93e3-f1c3-401d-9c1a-511a2a2eba5b", "edc385bd-d546-44c9-918a-472d563fd5f4", "a251d6c0-3b24-4df3-b42a-40b6b58a40be"], "durationId": "6a935e34-d88a-444a-855d-4a63e9488f23", "parent": "eb7aa08b-3a31-4985-939c-a658f03cda96"}}, {"head": {"id": "eb7aa08b-3a31-4985-939c-a658f03cda96", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682051292500, "endTime": 11682071763700}, "additional": {"logType": "info", "children": ["be9e7d79-81ac-4e11-aa9a-10f446439571"], "durationId": "19fe4d07-02c5-4774-89d5-d036b8d68579", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "55084970-e7d4-4a82-823c-70f8229a4180", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682084566700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08485089-f0be-42ef-a94a-971d2d7ea5a9", "name": "hvigorfile, resolve hvigorfile dependencies in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682085019700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8451f165-b5bd-4954-9844-5c44df5b70ae", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682071771200, "endTime": 11682085167200}, "additional": {"logType": "info", "children": [], "durationId": "cdae285a-5ac1-419a-9a6b-c8bfc7a0988e", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "fdca9631-648d-4e85-b998-f2cd5b9ce6ca", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682086023000, "endTime": 11682086244400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "logId": "bba56da4-dc09-4d9b-9d6a-029e2b99827d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ca5aee7-b953-481c-8503-f67772041835", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682086051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bba56da4-dc09-4d9b-9d6a-029e2b99827d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682086023000, "endTime": 11682086244400}, "additional": {"logType": "info", "children": [], "durationId": "fdca9631-648d-4e85-b998-f2cd5b9ce6ca", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "c9e3d17b-09f0-4955-bb4e-001a744c843d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682087802800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde63457-c60e-464b-8109-d88f7beb887f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682093788300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa56ae4-873b-4d51-a420-7cd3fc5f4561", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682086261600, "endTime": 11682094554200}, "additional": {"logType": "info", "children": [], "durationId": "a7a0dc61-e24c-48a8-b0b8-3a335842a52a", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "a1910388-e32f-40c3-8a0b-27afe8636502", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682094597500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3363bc57-42a6-431b-adb6-3124bc769733", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682099860100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e58cb422-a553-4760-80c6-57861e9db275", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682100008700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5828a362-7b25-459f-842d-ab647d853e9d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682100414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1380ff01-f6bb-4700-b9e0-49543e7b98f8", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682105415200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e5ddc5-8535-44f4-b200-b596db72866c", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682105509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eb4cb01-4d75-47e9-9159-628317aecb95", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682094579700, "endTime": 11682108019800}, "additional": {"logType": "info", "children": [], "durationId": "f5634558-f61d-4681-9122-e35ba84b7a20", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "890c1743-e1af-41b2-aaaf-003aacdfbc5d", "name": "Configuration phase cost:131 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682108058500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bc04a53-dd83-4a31-b25f-fda1604c0090", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682108037200, "endTime": 11682108131400}, "additional": {"logType": "info", "children": [], "durationId": "5015bb9c-938f-42c1-b459-827816837081", "parent": "9b131384-e6bc-4144-a8b7-97f26f126a64"}}, {"head": {"id": "9b131384-e6bc-4144-a8b7-97f26f126a64", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681972652400, "endTime": 11682108143100}, "additional": {"logType": "info", "children": ["70b3933c-caa1-4ac7-b053-0b5dea643163", "d8a47591-10a8-48f1-abd8-b80fb1aa6d2d", "d68f150d-1616-41ff-b042-aaffdb228a0c", "eb7aa08b-3a31-4985-939c-a658f03cda96", "8451f165-b5bd-4954-9844-5c44df5b70ae", "4fa56ae4-873b-4d51-a420-7cd3fc5f4561", "5eb4cb01-4d75-47e9-9159-628317aecb95", "5bc04a53-dd83-4a31-b25f-fda1604c0090", "bba56da4-dc09-4d9b-9d6a-029e2b99827d"], "durationId": "7a85866f-4af6-4cff-bd24-b2a1b55e7b97", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "7f389117-a4e3-4ae1-80df-0a7b3689f91c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682109211000, "endTime": 11682109223300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b", "logId": "e82e8377-a764-4070-bc4b-9aee344914c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e82e8377-a764-4070-bc4b-9aee344914c4", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682109211000, "endTime": 11682109223300}, "additional": {"logType": "info", "children": [], "durationId": "7f389117-a4e3-4ae1-80df-0a7b3689f91c", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "979f5ee0-509f-4362-921d-b49ada49f403", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682108168000, "endTime": 11682109232700}, "additional": {"logType": "info", "children": [], "durationId": "b429925b-1b87-470e-be76-61b4121d8ab7", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "28b46aba-9e3d-455a-bda9-8a1c452aa4ac", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682109237700, "endTime": 11682109243300}, "additional": {"logType": "info", "children": [], "durationId": "21b00a43-b8aa-4a77-9e28-509e919964c3", "parent": "2d04ab94-6bb0-4225-826d-585c93554470"}}, {"head": {"id": "2d04ab94-6bb0-4225-826d-585c93554470", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681961366200, "endTime": 11682109247600}, "additional": {"logType": "info", "children": ["4f0157e8-4856-4735-bc44-4513f110e953", "9b131384-e6bc-4144-a8b7-97f26f126a64", "979f5ee0-509f-4362-921d-b49ada49f403", "28b46aba-9e3d-455a-bda9-8a1c452aa4ac", "e51f3dc5-dd85-4756-b1ce-0cdb8b097237", "559b55b4-df08-405a-a350-b423d08e60b7", "e82e8377-a764-4070-bc4b-9aee344914c4"], "durationId": "a8e6bfdd-8f89-4f7d-96ba-ec51322f3a1b"}}, {"head": {"id": "8c3808a1-7703-42f0-8595-2bd7d47c9bed", "name": "Configuration task cost before running: 151 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682109358600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914926ae-a9a0-49f1-ab11-aeae00865bc4", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682113671600, "endTime": 11682121129900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "db5d29e8-dcea-4070-bfa2-3c11335e4498", "logId": "088ced29-548a-427e-bb94-128bb6c4496a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db5d29e8-dcea-4070-bfa2-3c11335e4498", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682110633500}, "additional": {"logType": "detail", "children": [], "durationId": "914926ae-a9a0-49f1-ab11-aeae00865bc4"}}, {"head": {"id": "c3512543-123f-4889-b043-707737e6adff", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682111082900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ccdd4e-150c-4009-a31d-bad974ca544d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682111159600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "581be403-54a4-4148-9805-8b4dfb666f1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682111211400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f51c30-d6cb-4d52-b4a4-e67c1b26dc92", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682113682000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71e82e8-bf1f-4b17-9334-1de75d76fecd", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682120934100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349b3210-d5fb-4e7d-8da8-14569ce38103", "name": "entry : default@PreBuild cost memory 0.2841644287109375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682121054500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "088ced29-548a-427e-bb94-128bb6c4496a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682113671600, "endTime": 11682121129900}, "additional": {"logType": "info", "children": [], "durationId": "914926ae-a9a0-49f1-ab11-aeae00865bc4"}}, {"head": {"id": "c9951d66-75dc-4fbd-83d2-95abdb1b6c36", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682125638500, "endTime": 11682128640200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b119cde3-860f-4261-8f99-b0afbc7ecaa7", "logId": "6c7116da-2d2b-4872-a985-7eaa695f827f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b119cde3-860f-4261-8f99-b0afbc7ecaa7", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682124425800}, "additional": {"logType": "detail", "children": [], "durationId": "c9951d66-75dc-4fbd-83d2-95abdb1b6c36"}}, {"head": {"id": "b7ef3916-a59a-4ca4-8fd0-d9e44dac54e5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682124869500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8632a6bb-584c-4a1c-afab-69e47f2969c1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682124956300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd3d7dac-c6d2-4a19-9b42-768f3a6ac685", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682125013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70835453-8a0e-4a6f-9876-68bb04377988", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682125649000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a086ec8-954d-4526-9bd5-e0c6fcd58107", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682128442900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d76510c8-e644-4b1e-af03-dfa1cc6c02fb", "name": "entry : default@MergeProfile cost memory 0.13440704345703125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682128562500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7116da-2d2b-4872-a985-7eaa695f827f", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682125638500, "endTime": 11682128640200}, "additional": {"logType": "info", "children": [], "durationId": "c9951d66-75dc-4fbd-83d2-95abdb1b6c36"}}, {"head": {"id": "17092ed7-893b-4d24-981a-fea97e04b5bc", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682131628200, "endTime": 11682133971700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "976188ff-2874-4a06-8c66-9bf351ed784b", "logId": "8dec920e-32b9-4b9c-9dd4-bdba656d280f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "976188ff-2874-4a06-8c66-9bf351ed784b", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682130324100}, "additional": {"logType": "detail", "children": [], "durationId": "17092ed7-893b-4d24-981a-fea97e04b5bc"}}, {"head": {"id": "20e9435f-8a95-4429-b01f-e97f7cca5044", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682130782100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7699d7ba-2afb-492e-aba4-440e6d47d9ff", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682130866700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03306dac-cf95-4684-b81e-30de6ce9495e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682130920100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b51b4c-e25b-45d4-9c38-52d929ed1879", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682131637100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a19206-d1ab-4aab-97c3-2e1b97965ddb", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682132482000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e0887ab-dbb4-4176-a4b4-6f2cc2d41336", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682133810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00152818-fd09-4f6a-a97c-a6ff9892b78e", "name": "entry : default@CreateBuildProfile cost memory 0.1013031005859375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682133905100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dec920e-32b9-4b9c-9dd4-bdba656d280f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682131628200, "endTime": 11682133971700}, "additional": {"logType": "info", "children": [], "durationId": "17092ed7-893b-4d24-981a-fea97e04b5bc"}}, {"head": {"id": "6080f06d-580b-46ae-913f-167f6e0242b8", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137149200, "endTime": 11682137586200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "5bb3ef9a-819a-4441-9c99-7bc514d939ff", "logId": "57d2715c-9572-494a-823e-53fa72a5e3df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bb3ef9a-819a-4441-9c99-7bc514d939ff", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682135318000}, "additional": {"logType": "detail", "children": [], "durationId": "6080f06d-580b-46ae-913f-167f6e0242b8"}}, {"head": {"id": "5103bfd0-64a8-4c08-95fe-72ae75c4f74e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682136283500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab497fd7-702a-4222-a42f-d44d6410ab45", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682136375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b516038d-9b8e-4a77-b3b5-b124495336a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682136433500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d11b5b-778e-4ffa-9032-8848081931c5", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d00a976-78fb-427b-ae77-f357203e29c1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137274400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29cee70-3e9e-4483-92a6-cc202c607468", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137329900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6caa7927-713c-486d-8c3f-6e158bc46f20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137378400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edf33bed-b5a7-417a-894f-d24a1922e797", "name": "entry : default@PreCheckSyscap cost memory 0.05072784423828125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137457200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95812b47-7cdb-435e-8242-7631a6defb3b", "name": "runTaskFromQueue task cost before running: 179 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137531600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d2715c-9572-494a-823e-53fa72a5e3df", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682137149200, "endTime": 11682137586200, "totalTime": 364400}, "additional": {"logType": "info", "children": [], "durationId": "6080f06d-580b-46ae-913f-167f6e0242b8"}}, {"head": {"id": "842092a1-01be-4fa7-97c0-9b622a061d03", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682147346400, "endTime": 11682148537400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6ae1a3d6-c76b-4248-a4ae-47dec9e8af97", "logId": "e4c6b0af-beeb-4053-a73c-29475008db5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ae1a3d6-c76b-4248-a4ae-47dec9e8af97", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682138986300}, "additional": {"logType": "detail", "children": [], "durationId": "842092a1-01be-4fa7-97c0-9b622a061d03"}}, {"head": {"id": "2443601d-506f-474f-8822-4d21289fd12b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682139456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cc4ec8-3d11-4dee-bfb4-9f7bf27513a4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682139542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "738cffac-833e-4baf-a082-f825c1c4aab8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682139597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "251c06aa-32d2-4830-9c5c-e3863c1bc685", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682147366000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4720dcd0-5864-477f-b5c0-a553413790d5", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682147728300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2470a2f-514f-4678-93a8-5ff1a6c58149", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682148365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2766eb2f-b5e6-42ee-baca-cfd6172ab655", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0708160400390625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682148467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c6b0af-beeb-4053-a73c-29475008db5b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682147346400, "endTime": 11682148537400}, "additional": {"logType": "info", "children": [], "durationId": "842092a1-01be-4fa7-97c0-9b622a061d03"}}, {"head": {"id": "ce11a25b-e964-456e-b2d0-77161d46e893", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682151668200, "endTime": 11682152722800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "937111a8-ac8b-4caa-adc9-8d262b465571", "logId": "ddf95fa1-0053-44e0-9c90-b339b552c800"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "937111a8-ac8b-4caa-adc9-8d262b465571", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682150024300}, "additional": {"logType": "detail", "children": [], "durationId": "ce11a25b-e964-456e-b2d0-77161d46e893"}}, {"head": {"id": "bfb754c9-f6ca-4186-9f70-b55576f5e2d6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682150490900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0c8e33-f0d0-4c8b-85f1-09482f6255b3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682150572000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6796df9a-8b90-49cc-bc21-d4f37e0c347e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682150623400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199c5692-f66c-4603-acdd-a9d4e7db738c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682151678100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f919f9-5050-4e25-bac0-aed7a46fa795", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682152567300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e71211b0-0401-4958-b135-7c9fba000a93", "name": "entry : default@ProcessProfile cost memory 0.0587615966796875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682152658100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf95fa1-0053-44e0-9c90-b339b552c800", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682151668200, "endTime": 11682152722800}, "additional": {"logType": "info", "children": [], "durationId": "ce11a25b-e964-456e-b2d0-77161d46e893"}}, {"head": {"id": "802982bf-02d7-4d51-a3b5-4fd164510e8a", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682156249600, "endTime": 11682162001300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6136f1fe-6f5b-4d60-95fe-6e6cc95a84fd", "logId": "c3977d0b-ac46-4685-9998-76a0059e454c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6136f1fe-6f5b-4d60-95fe-6e6cc95a84fd", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682154111100}, "additional": {"logType": "detail", "children": [], "durationId": "802982bf-02d7-4d51-a3b5-4fd164510e8a"}}, {"head": {"id": "acb9a044-0133-423e-966f-3c0ee22f640f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682154563200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54587c17-7101-4780-807d-a867892a8e08", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682154647100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b06051-f720-42e6-ad07-07b7fa7adb03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682154699800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a39c58da-9a77-40ec-80c6-19d8413913aa", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682156260700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd1b5e02-2280-46a5-994b-2f8f529f98f2", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682161782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2797261-042b-46fb-8f8c-b7e501c684e9", "name": "entry : default@ProcessRouterMap cost memory 0.21776580810546875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682161917100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3977d0b-ac46-4685-9998-76a0059e454c", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682156249600, "endTime": 11682162001300}, "additional": {"logType": "info", "children": [], "durationId": "802982bf-02d7-4d51-a3b5-4fd164510e8a"}}, {"head": {"id": "3372b07e-9161-4e5b-99cd-b2a79dd4f72c", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682169148300, "endTime": 11682172092500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3ffb242d-81d4-4fd7-ab24-6b76713620bc", "logId": "0bf5fd58-c83f-4b42-9779-af72846d2467"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ffb242d-81d4-4fd7-ab24-6b76713620bc", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682165266800}, "additional": {"logType": "detail", "children": [], "durationId": "3372b07e-9161-4e5b-99cd-b2a79dd4f72c"}}, {"head": {"id": "0b56a3fd-1aa3-4882-aa6d-4af8dd256ce9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682165748600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fc1e9a8-813f-43ac-913c-7b1582e5a489", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682165837200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad67949f-4261-4ef5-88b2-8a2edca1397a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682165895900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560de747-6f0e-4c58-bb0b-7a57ce656fbc", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682166786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663dcdae-af73-4df8-87c0-b0cf10c6670c", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682170347000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9302618c-ecab-4104-a7a7-c38d05f695e0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682170516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78231091-8e09-4e0b-bcbc-c76b7c99886f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682170582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224fd7d7-b636-4295-85e0-32ae3fb1f030", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682170629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79aa877f-f74b-49d5-ae5e-683637fde001", "name": "entry : default@PreviewProcessResource cost memory 0.0896759033203125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682170718800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc931db5-f7f7-4424-aefe-97f4f90302bf", "name": "runTaskFromQueue task cost before running: 214 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682172004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf5fd58-c83f-4b42-9779-af72846d2467", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682169148300, "endTime": 11682172092500, "totalTime": 1628100}, "additional": {"logType": "info", "children": [], "durationId": "3372b07e-9161-4e5b-99cd-b2a79dd4f72c"}}, {"head": {"id": "419b6e9e-adab-4b5a-8ac2-e2b60b890d13", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682178831900, "endTime": 11682198569100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8626080d-d132-4158-b177-d23d371a333c", "logId": "2c912d71-42e7-433c-a8e1-c4d8a30d5e0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8626080d-d132-4158-b177-d23d371a333c", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682174706900}, "additional": {"logType": "detail", "children": [], "durationId": "419b6e9e-adab-4b5a-8ac2-e2b60b890d13"}}, {"head": {"id": "5ba85307-77a5-4b36-b9b3-7251cf5ef6ac", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682175179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d94f5a-8dd0-4e29-8eb9-e0a910dce3e3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682175263400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27338acc-f6ca-487c-bef4-cd8ed51c7fc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682175314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3aadae9-f506-4580-b6f1-bbee1468a3c2", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682178849900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e885c872-073f-4e65-9cea-1e1fd512d38e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682198216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2631a1ec-0388-4543-bf15-e2f7c1017c9f", "name": "entry : default@GenerateLoaderJson cost memory 0.8418807983398438", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682198458500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c912d71-42e7-433c-a8e1-c4d8a30d5e0e", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682178831900, "endTime": 11682198569100}, "additional": {"logType": "info", "children": [], "durationId": "419b6e9e-adab-4b5a-8ac2-e2b60b890d13"}}, {"head": {"id": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682210183300, "endTime": 11682829154500}, "additional": {"children": ["19841df1-02ee-4df9-a9be-30535e796d72", "7caa53ee-75f2-40c2-816a-fea759c68e4f", "fa9b30c3-ca08-4dd3-9c13-67fc0c43d4f0", "fcfbc6e8-bd87-443f-9e27-70d31368f4eb", "a3bd93a4-f3d1-4d8f-81ac-6231211a0758"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "67c4f7af-b7f6-42f5-850d-788ed8abf4ed", "logId": "af66629e-e9dd-46cb-9b9b-8332b7e6c673"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67c4f7af-b7f6-42f5-850d-788ed8abf4ed", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682206176200}, "additional": {"logType": "detail", "children": [], "durationId": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa"}}, {"head": {"id": "4a792f52-3a6d-4c43-8cff-989c5f5b24d0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682206615600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be5d6004-d8c4-46f0-b29c-e7d421fef17f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682206700400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b0f113-fa58-4386-bb28-19a4151aca8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682206751400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ac9a26-bfa1-48d5-b519-5b4dd6197b8b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682207693600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "082a71ee-da47-4387-9c0f-0fbc8ebe0ae9", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682210210500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6745d670-4a98-40ce-9fd5-d12e674f50ca", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682247542200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23ed9e9-c732-4ba7-ae35-39c9eee3145d", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 37 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682247702100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19841df1-02ee-4df9-a9be-30535e796d72", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682248820000, "endTime": 11682279682500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa", "logId": "f98084c4-8c0a-4804-af57-10c5f0225cf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f98084c4-8c0a-4804-af57-10c5f0225cf5", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682248820000, "endTime": 11682279682500}, "additional": {"logType": "info", "children": [], "durationId": "19841df1-02ee-4df9-a9be-30535e796d72", "parent": "af66629e-e9dd-46cb-9b9b-8332b7e6c673"}}, {"head": {"id": "5aace71d-e973-4a4c-93aa-0ff24139ffc6", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682279947800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7caa53ee-75f2-40c2-816a-fea759c68e4f", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682280832100, "endTime": 11682405572500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa", "logId": "358c5c3e-b2af-41c2-89a3-de65dc7f64ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f44e9b91-4947-40f8-9227-37ade5add027", "name": "current process  memoryUsage: {\n  rss: 173809664,\n  heapTotal: 120393728,\n  heapUsed: 101772360,\n  external: 3084149,\n  arrayBuffers: 78016\n} os memoryUsage :11.153438568115234", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682281689200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f6c7cd-8a80-4b86-a9af-6ccffe79c863", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682403212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "358c5c3e-b2af-41c2-89a3-de65dc7f64ff", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682280832100, "endTime": 11682405572500}, "additional": {"logType": "info", "children": [], "durationId": "7caa53ee-75f2-40c2-816a-fea759c68e4f", "parent": "af66629e-e9dd-46cb-9b9b-8332b7e6c673"}}, {"head": {"id": "f6a59b33-11e6-4f59-a4f2-1137ca7dd12f", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682405799000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa9b30c3-ca08-4dd3-9c13-67fc0c43d4f0", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682407190700, "endTime": 11682547814400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa", "logId": "b3940d01-0d4a-4fe4-a005-9a253851c884"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17f14146-0e7c-4999-916f-a6dfcd0cc74b", "name": "current process  memoryUsage: {\n  rss: 173826048,\n  heapTotal: 120393728,\n  heapUsed: 102077384,\n  external: 3084275,\n  arrayBuffers: 78157\n} os memoryUsage :11.15850830078125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682408414500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d321aeb1-fd08-4420-9a41-8a0cc052caa2", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682545752500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3940d01-0d4a-4fe4-a005-9a253851c884", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682407190700, "endTime": 11682547814400}, "additional": {"logType": "info", "children": [], "durationId": "fa9b30c3-ca08-4dd3-9c13-67fc0c43d4f0", "parent": "af66629e-e9dd-46cb-9b9b-8332b7e6c673"}}, {"head": {"id": "3e817c00-e882-4083-ba05-e5734284f03c", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682547997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcfbc6e8-bd87-443f-9e27-70d31368f4eb", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682549016300, "endTime": 11682667579900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa", "logId": "38269fa1-c4c3-47c9-b2f7-a737eca1b72e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31bf6b9b-6ccd-4962-a21e-1fc270b4a87d", "name": "current process  memoryUsage: {\n  rss: 173838336,\n  heapTotal: 120393728,\n  heapUsed: 102364632,\n  external: 3092593,\n  arrayBuffers: 86539\n} os memoryUsage :11.164730072021484", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682549904600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1de611-3400-4551-9aa2-f3e32ccf0bb2", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682665448400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38269fa1-c4c3-47c9-b2f7-a737eca1b72e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682549016300, "endTime": 11682667579900}, "additional": {"logType": "info", "children": [], "durationId": "fcfbc6e8-bd87-443f-9e27-70d31368f4eb", "parent": "af66629e-e9dd-46cb-9b9b-8332b7e6c673"}}, {"head": {"id": "c43e6a66-55be-418f-8c16-9193612da8d5", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682667818600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bd93a4-f3d1-4d8f-81ac-6231211a0758", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682668933900, "endTime": 11682827746700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa", "logId": "692b99fa-21ab-4f4d-837b-569386b88426"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbfd3a57-8d94-46de-b096-1982e4edfcd1", "name": "current process  memoryUsage: {\n  rss: 173842432,\n  heapTotal: 120393728,\n  heapUsed: 102683616,\n  external: 3092719,\n  arrayBuffers: 87584\n} os memoryUsage :11.172325134277344", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682670228900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ba00a4-a9ae-405d-9fd1-84aa107617a1", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682824792700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "692b99fa-21ab-4f4d-837b-569386b88426", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682668933900, "endTime": 11682827746700}, "additional": {"logType": "info", "children": [], "durationId": "a3bd93a4-f3d1-4d8f-81ac-6231211a0758", "parent": "af66629e-e9dd-46cb-9b9b-8332b7e6c673"}}, {"head": {"id": "7e99ffbd-b110-4c59-a3b2-eda57dbf5ea6", "name": "entry : default@PreviewCompileResource cost memory -9.411323547363281", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682828843300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bafecd07-e04a-4510-aefb-549f7dfc02d0", "name": "runTaskFromQueue task cost before running: 871 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682829050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af66629e-e9dd-46cb-9b9b-8332b7e6c673", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682210183300, "endTime": 11682829154500, "totalTime": 618801000}, "additional": {"logType": "info", "children": ["f98084c4-8c0a-4804-af57-10c5f0225cf5", "358c5c3e-b2af-41c2-89a3-de65dc7f64ff", "b3940d01-0d4a-4fe4-a005-9a253851c884", "38269fa1-c4c3-47c9-b2f7-a737eca1b72e", "692b99fa-21ab-4f4d-837b-569386b88426"], "durationId": "520ea7bd-b07a-4898-99c4-b301b3bf5dfa"}}, {"head": {"id": "e7199917-d162-45eb-9edf-891efde610d2", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832431200, "endTime": 11682832856000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "cfa43f34-c8b5-4f26-965f-36b341d549a3", "logId": "3940a68b-743e-4baf-a7c0-e678ae502f5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfa43f34-c8b5-4f26-965f-36b341d549a3", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682831676600}, "additional": {"logType": "detail", "children": [], "durationId": "e7199917-d162-45eb-9edf-891efde610d2"}}, {"head": {"id": "38777e2d-c2fd-4d2e-987d-eaf737500ef8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832190300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50d24cfa-4b00-4236-9b3a-d27f03644579", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832283400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1439f7-214c-4c83-a23f-78ca4067cbae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832343700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad67b4b-e4eb-472a-9514-5bb896747fe4", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832439500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da3922b7-64ea-4e0a-9919-22f6aeb8f426", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832552200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a756879a-02b9-4d7f-9034-13ff2cdd2c38", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344d930d-6b0b-4bd6-b020-a10e3f831508", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832647100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c59c5233-7955-413b-be6f-306ad3cc875f", "name": "entry : default@PreviewHookCompileResource cost memory 0.052398681640625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac0ec1f-383f-46e9-a423-3b6232d34b96", "name": "runTaskFromQueue task cost before running: 875 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3940a68b-743e-4baf-a7c0-e678ae502f5b", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682832431200, "endTime": 11682832856000, "totalTime": 346400}, "additional": {"logType": "info", "children": [], "durationId": "e7199917-d162-45eb-9edf-891efde610d2"}}, {"head": {"id": "181816d8-5dbd-46ca-b44a-85c04cd8a9f6", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682835852500, "endTime": 11682843846000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "a92f6183-8bb2-48bc-91a9-c957ff131824", "logId": "171f6a86-4d46-4a7b-9253-1cdc17a530d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a92f6183-8bb2-48bc-91a9-c957ff131824", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682834435100}, "additional": {"logType": "detail", "children": [], "durationId": "181816d8-5dbd-46ca-b44a-85c04cd8a9f6"}}, {"head": {"id": "7f5dad04-70b3-4979-ae39-8880fdb6a003", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682834997800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b780e3b-6c5e-4f89-9478-1f291521de86", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682835120800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7a3bef-df3f-4c8e-a364-18126d8cd014", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682835209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "373e1fd2-9368-464e-bc02-bf28e0a79904", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682835864700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65b7f4ff-3566-49b1-b88c-99f870a46209", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682837753100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fce938a-96d5-4c4e-8b71-24b12ec4ae80", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682837959300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca09c13c-c5b5-4f37-bd5d-95bd8c1529a7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682838118300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed4bc79-3f77-433a-941a-43a6c78c8374", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682838222900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f126fb7e-e979-4d14-affc-a7eedc2e909b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682838293300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20d57de-3d0e-4b29-820f-5782a4f12e89", "name": "entry : default@CopyPreviewProfile cost memory -1.4832916259765625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682843589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f4eb35-3d23-469d-aeab-647a3a91ae75", "name": "runTaskFromQueue task cost before running: 886 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682843754100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "171f6a86-4d46-4a7b-9253-1cdc17a530d8", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682835852500, "endTime": 11682843846000, "totalTime": 7871400}, "additional": {"logType": "info", "children": [], "durationId": "181816d8-5dbd-46ca-b44a-85c04cd8a9f6"}}, {"head": {"id": "a3648b09-3cae-4b43-9101-cf5b46d2021a", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847294000, "endTime": 11682847742500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "99a6d7cf-2338-47f7-8d1a-54514558f800", "logId": "4f3a1c52-b616-4dc6-bc94-4c0bb034caa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99a6d7cf-2338-47f7-8d1a-54514558f800", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682845887800}, "additional": {"logType": "detail", "children": [], "durationId": "a3648b09-3cae-4b43-9101-cf5b46d2021a"}}, {"head": {"id": "be9cde72-9e0b-4fe4-8bef-357ceee6a63c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682846427800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c273c4-bb45-46e1-9ab2-e06bc4346ef6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682846523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c27eff-92fa-4eae-9dcb-e5caade351dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682846577100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0239647-da68-44cf-92ac-ff1cb1d13e36", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847303700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5b32fc-6f84-400f-be5b-d872fa6fe986", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847417000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3e6922-dbf0-45c9-a523-4edb2f1495b6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847469900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0962c71c-434d-41b8-9973-18b2779978b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847512400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f077a54-e880-46ea-8a95-e58dd22c5954", "name": "entry : default@ReplacePreviewerPage cost memory 0.05181121826171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847601100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973b5719-af72-46a2-b387-133ed1edcf9a", "name": "runTaskFromQueue task cost before running: 890 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847689800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3a1c52-b616-4dc6-bc94-4c0bb034caa2", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682847294000, "endTime": 11682847742500, "totalTime": 374600}, "additional": {"logType": "info", "children": [], "durationId": "a3648b09-3cae-4b43-9101-cf5b46d2021a"}}, {"head": {"id": "6dacbdfd-7039-4594-84c3-13a4ad2ac0fb", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682849247400, "endTime": 11682849485400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "884e3169-f6b9-4e7c-bf9a-bb2b082e397f", "logId": "f9530608-2e1f-4f06-969c-a3edf867c83a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "884e3169-f6b9-4e7c-bf9a-bb2b082e397f", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682849196900}, "additional": {"logType": "detail", "children": [], "durationId": "6dacbdfd-7039-4594-84c3-13a4ad2ac0fb"}}, {"head": {"id": "841b5df9-b9c9-4aea-8d69-dec99994bf7d", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682849254800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf70e87e-07ca-4a7a-9122-74d66dac42d1", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682849354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7ada36-a7ac-42bd-a108-2c49d00387c1", "name": "runTaskFromQueue task cost before running: 891 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682849432000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9530608-2e1f-4f06-969c-a3edf867c83a", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682849247400, "endTime": 11682849485400, "totalTime": 164800}, "additional": {"logType": "info", "children": [], "durationId": "6dacbdfd-7039-4594-84c3-13a4ad2ac0fb"}}, {"head": {"id": "54a0aa76-d94e-47f4-94ee-13f17d51d4d5", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682852378000, "endTime": 11682855568700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "7e29e820-568c-4d77-847f-6062c7286cc7", "logId": "ce0cec09-76b8-4370-8070-b8162e3a4fca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e29e820-568c-4d77-847f-6062c7286cc7", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682850897100}, "additional": {"logType": "detail", "children": [], "durationId": "54a0aa76-d94e-47f4-94ee-13f17d51d4d5"}}, {"head": {"id": "c353a64d-b6ce-497c-9b4b-e0c5c0a810bc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682851400900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "797c35af-e246-4eed-ade8-6c911ffbc1c8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682851493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dea7c73-c0b9-4d70-ae76-f591d02d4cff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682851548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47462056-5ad8-4ae5-8805-92d83a40be1f", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682852388100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28a6cec6-755d-4ae0-81a9-346ff611225c", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682854198100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4140bd71-086f-4f40-a8f7-d35a4d5d6c0b", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682854329700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93188d50-a64b-4002-9c73-59aa8a0861f5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682854424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bfb46ef-bcd2-4193-8b09-e00c456d89b3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682854490200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bec74e6-f346-4bf2-9e0c-98b8fa342fe4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682854536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb27df3-3bd6-42d7-81ba-7c8fce4cf796", "name": "entry : default@PreviewUpdateAssets cost memory 0.15358734130859375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682855405800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9831572b-ee7c-4d17-89df-ffcd3858f826", "name": "runTaskFromQueue task cost before running: 897 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682855509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0cec09-76b8-4370-8070-b8162e3a4fca", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682852378000, "endTime": 11682855568700, "totalTime": 3110400}, "additional": {"logType": "info", "children": [], "durationId": "54a0aa76-d94e-47f4-94ee-13f17d51d4d5"}}, {"head": {"id": "d13eab68-02b8-4a69-b0f3-4ee81efd814d", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682863216800, "endTime": 11692790023900}, "additional": {"children": ["f933c544-8daf-4222-ac51-bef951866a57"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "12f612cc-57c9-4f4d-9a5c-c77a064dd404", "logId": "9ea3e88f-5ece-41a4-88a7-a28916cb9b9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12f612cc-57c9-4f4d-9a5c-c77a064dd404", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682857746300}, "additional": {"logType": "detail", "children": [], "durationId": "d13eab68-02b8-4a69-b0f3-4ee81efd814d"}}, {"head": {"id": "42bce913-5bc1-48af-bf43-2adebb5db317", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682858244000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b1d395-ecf9-49d0-b5df-faa705089e8a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682858334400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "599f19ab-ad75-4a53-9682-b023c06b10d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682858385600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300c4b33-ddb9-4945-8a3f-c208d34aae56", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682863231700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f79d809c-44f5-4c89-8b69-725d9081d820", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682897722900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61b5fe3-1520-4bdd-9b7e-e0cb14657c43", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682897908700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f933c544-8daf-4222-ac51-bef951866a57", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11682912492200, "endTime": 11692786866900}, "additional": {"children": ["d904b4df-dd7c-40d2-9107-751bac8a52d6", "dfb23853-8525-436f-9e42-e5e4080b93f4", "03765933-39d5-4569-9997-0d2870c82d20", "9dbac637-20ce-4a94-8596-a5ed93d0c182", "79e6816a-c6de-4aa7-aa9d-de4c65c958a9", "948f6d8e-8537-485e-9794-988821e2264e", "e3f36db9-d659-418f-8f58-e951e0007b6a", "baaca8af-0153-49ee-b35c-ff2c72e49ac7", "cc9b87f1-923e-4844-8916-d167296fe388", "976fb6db-011e-4db1-82b0-162dbbe20b0b", "f95bd538-3050-4678-b8b1-8b6ddac18c39", "97f9b7ab-b6c2-4a33-a99d-2d47dcbd3f8f", "1829cc08-1f44-4fa1-9d77-05176f06517f", "b52afa16-c696-492b-aea8-e647e820b0ba", "545b31c5-7229-4c4b-837c-f9031850eb75"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d13eab68-02b8-4a69-b0f3-4ee81efd814d", "logId": "c982e64e-8d40-440f-82a9-64a87e7c180b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bad94016-1dc5-492a-9c49-5118a54b3e39", "name": "entry : default@PreviewArkTS cost memory 0.6046295166015625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682915306300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0484311-5145-4506-b3a1-7c21c27e698d", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11686537341800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d904b4df-dd7c-40d2-9107-751bac8a52d6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11686538464800, "endTime": 11686538487400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "acc40e90-1d90-4fe0-a59f-c828eb60b557"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acc40e90-1d90-4fe0-a59f-c828eb60b557", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11686538464800, "endTime": 11686538487400}, "additional": {"logType": "info", "children": [], "durationId": "d904b4df-dd7c-40d2-9107-751bac8a52d6", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "0195285d-e9c6-4fda-9ed8-ead989463ee7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11691006605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb23853-8525-436f-9e42-e5e4080b93f4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11691007749900, "endTime": 11691007773600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "f61c53c5-86bc-420f-bdc3-e734769f2489"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f61c53c5-86bc-420f-bdc3-e734769f2489", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11691007749900, "endTime": 11691007773600}, "additional": {"logType": "info", "children": [], "durationId": "dfb23853-8525-436f-9e42-e5e4080b93f4", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "baddf194-a185-4db4-93db-55d3df2746da", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11691007873200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03765933-39d5-4569-9997-0d2870c82d20", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11691008715400, "endTime": 11691008736700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "026526e9-00cd-4246-807a-9bdf806e3bcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "026526e9-00cd-4246-807a-9bdf806e3bcd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11691008715400, "endTime": 11691008736700}, "additional": {"logType": "info", "children": [], "durationId": "03765933-39d5-4569-9997-0d2870c82d20", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "feba0e25-db51-4656-b2bf-17425<PERSON><PERSON><PERSON><PERSON>", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692778396000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dbac637-20ce-4a94-8596-a5ed93d0c182", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692779542200, "endTime": 11692779562200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "618bea07-11c3-4a1e-a277-3446eaea3b70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "618bea07-11c3-4a1e-a277-3446eaea3b70", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692779542200, "endTime": 11692779562200}, "additional": {"logType": "info", "children": [], "durationId": "9dbac637-20ce-4a94-8596-a5ed93d0c182", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "9e19da04-f22c-4241-bca5-cbfba7f216e0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692779634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e6816a-c6de-4aa7-aa9d-de4c65c958a9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692780378400, "endTime": 11692780393400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "9ec70127-2b55-4437-8575-954355873933"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ec70127-2b55-4437-8575-954355873933", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692780378400, "endTime": 11692780393400}, "additional": {"logType": "info", "children": [], "durationId": "79e6816a-c6de-4aa7-aa9d-de4c65c958a9", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "a4b10142-a2a9-4293-b787-2261606560c3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692780457200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "948f6d8e-8537-485e-9794-988821e2264e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692781181000, "endTime": 11692781196000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "013ac6c2-44a1-475a-8d5e-060b5905841f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "013ac6c2-44a1-475a-8d5e-060b5905841f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692781181000, "endTime": 11692781196000}, "additional": {"logType": "info", "children": [], "durationId": "948f6d8e-8537-485e-9794-988821e2264e", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "e98e10a3-feae-4ee6-8182-21c4642b0391", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692781255100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3f36db9-d659-418f-8f58-e951e0007b6a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692781995400, "endTime": 11692782011600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "74c91519-67fc-48a9-9ee7-a5905338c0a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74c91519-67fc-48a9-9ee7-a5905338c0a3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692781995400, "endTime": 11692782011600}, "additional": {"logType": "info", "children": [], "durationId": "e3f36db9-d659-418f-8f58-e951e0007b6a", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "13e6b611-ef78-48cf-b585-78879ae42ba1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692782078100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baaca8af-0153-49ee-b35c-ff2c72e49ac7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692782818800, "endTime": 11692782836500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "06348c38-3649-4db7-8961-6e513d674c99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06348c38-3649-4db7-8961-6e513d674c99", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692782818800, "endTime": 11692782836500}, "additional": {"logType": "info", "children": [], "durationId": "baaca8af-0153-49ee-b35c-ff2c72e49ac7", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "515a47db-fa30-4fcf-82c5-a3fca031a504", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692782916800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9b87f1-923e-4844-8916-d167296fe388", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692784088800, "endTime": 11692784113300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "1339f45a-f830-4c5f-94fe-228d7b98df22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1339f45a-f830-4c5f-94fe-228d7b98df22", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692784088800, "endTime": 11692784113300}, "additional": {"logType": "info", "children": [], "durationId": "cc9b87f1-923e-4844-8916-d167296fe388", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "c23f88b5-31f8-4ad6-bc73-9b8dd39831a5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692784205400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976fb6db-011e-4db1-82b0-162dbbe20b0b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692785413900, "endTime": 11692785453700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "74175a60-711b-402f-9ad5-9a917dc093fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74175a60-711b-402f-9ad5-9a917dc093fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692785413900, "endTime": 11692785453700}, "additional": {"logType": "info", "children": [], "durationId": "976fb6db-011e-4db1-82b0-162dbbe20b0b", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "50be3206-2369-4c17-8ec8-19508d5a083d", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692785568000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95bd538-3050-4678-b8b1-8b6ddac18c39", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11692786688300, "endTime": 11692786713900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "5ec4ce71-76c2-459a-8850-48acd94e31f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ec4ce71-76c2-459a-8850-48acd94e31f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692786688300, "endTime": 11692786713900}, "additional": {"logType": "info", "children": [], "durationId": "f95bd538-3050-4678-b8b1-8b6ddac18c39", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "c982e64e-8d40-440f-82a9-64a87e7c180b", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11682912492200, "endTime": 11692786866900}, "additional": {"logType": "info", "children": ["acc40e90-1d90-4fe0-a59f-c828eb60b557", "f61c53c5-86bc-420f-bdc3-e734769f2489", "026526e9-00cd-4246-807a-9bdf806e3bcd", "618bea07-11c3-4a1e-a277-3446eaea3b70", "9ec70127-2b55-4437-8575-954355873933", "013ac6c2-44a1-475a-8d5e-060b5905841f", "74c91519-67fc-48a9-9ee7-a5905338c0a3", "06348c38-3649-4db7-8961-6e513d674c99", "1339f45a-f830-4c5f-94fe-228d7b98df22", "74175a60-711b-402f-9ad5-9a917dc093fd", "5ec4ce71-76c2-459a-8850-48acd94e31f1", "3733e0ae-ec0d-4e50-89f3-e21ff7b5df81", "5b5b5516-5e08-4b5d-9377-d47bd3783491", "f4165d44-9e2c-4b99-94c0-93d654cd7e09", "9760de33-ca24-485c-8572-b5850d87ecaa"], "durationId": "f933c544-8daf-4222-ac51-bef951866a57", "parent": "9ea3e88f-5ece-41a4-88a7-a28916cb9b9a"}}, {"head": {"id": "97f9b7ab-b6c2-4a33-a99d-2d47dcbd3f8f", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11685344379500, "endTime": 11686437791600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "3733e0ae-ec0d-4e50-89f3-e21ff7b5df81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3733e0ae-ec0d-4e50-89f3-e21ff7b5df81", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11685344379500, "endTime": 11686437791600}, "additional": {"logType": "info", "children": [], "durationId": "97f9b7ab-b6c2-4a33-a99d-2d47dcbd3f8f", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "1829cc08-1f44-4fa1-9d77-05176f06517f", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11686437999000, "endTime": 11686507530600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "5b5b5516-5e08-4b5d-9377-d47bd3783491"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b5b5516-5e08-4b5d-9377-d47bd3783491", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11686437999000, "endTime": 11686507530600}, "additional": {"logType": "info", "children": [], "durationId": "1829cc08-1f44-4fa1-9d77-05176f06517f", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "b52afa16-c696-492b-aea8-e647e820b0ba", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11686507661900, "endTime": 11686507978500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "f4165d44-9e2c-4b99-94c0-93d654cd7e09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4165d44-9e2c-4b99-94c0-93d654cd7e09", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11686507661900, "endTime": 11686507978500}, "additional": {"logType": "info", "children": [], "durationId": "b52afa16-c696-492b-aea8-e647e820b0ba", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "545b31c5-7229-4c4b-837c-f9031850eb75", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker2", "startTime": 11686508067100, "endTime": 11692778527700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f933c544-8daf-4222-ac51-bef951866a57", "logId": "9760de33-ca24-485c-8572-b5850d87ecaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9760de33-ca24-485c-8572-b5850d87ecaa", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11686508067100, "endTime": 11692778527700}, "additional": {"logType": "info", "children": [], "durationId": "545b31c5-7229-4c4b-837c-f9031850eb75", "parent": "c982e64e-8d40-440f-82a9-64a87e7c180b"}}, {"head": {"id": "9ea3e88f-5ece-41a4-88a7-a28916cb9b9a", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11682863216800, "endTime": 11692790023900, "totalTime": 9926794400}, "additional": {"logType": "info", "children": ["c982e64e-8d40-440f-82a9-64a87e7c180b"], "durationId": "d13eab68-02b8-4a69-b0f3-4ee81efd814d"}}, {"head": {"id": "89664f50-98e8-4554-bd0d-073746dc52d3", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692795981100, "endTime": 11692796329000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c57b157a-e36f-437b-b76a-3ddb870cf659", "logId": "d369d058-9f15-4957-ba53-8b58f2a98b8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c57b157a-e36f-437b-b76a-3ddb870cf659", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692795919000}, "additional": {"logType": "detail", "children": [], "durationId": "89664f50-98e8-4554-bd0d-073746dc52d3"}}, {"head": {"id": "38ec464c-7d1c-4c2b-b761-e20f627830c0", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692795996900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe70bb26-ad09-4db4-87b8-66bbbdd4b18f", "name": "entry : PreviewBuild cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692796155000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea381419-d479-4cd7-a987-8b7e772b3d10", "name": "runTaskFromQueue task cost before running: 10 s 838 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692796257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d369d058-9f15-4957-ba53-8b58f2a98b8e", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692795981100, "endTime": 11692796329000, "totalTime": 249700}, "additional": {"logType": "info", "children": [], "durationId": "89664f50-98e8-4554-bd0d-073746dc52d3"}}, {"head": {"id": "8864499d-8b40-450a-aa4d-d5bb0efd0b52", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810159400, "endTime": 11692810191400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f11cb1f7-261b-439d-a2f2-e6cf2d4453a9", "logId": "3fdee040-ef5d-4d7a-baef-b8e218742d3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fdee040-ef5d-4d7a-baef-b8e218742d3b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810159400, "endTime": 11692810191400}, "additional": {"logType": "info", "children": [], "durationId": "8864499d-8b40-450a-aa4d-d5bb0efd0b52"}}, {"head": {"id": "b6dd4723-51b4-4098-996b-63275a2f4420", "name": "BUILD SUCCESSFUL in 10 s 852 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810255200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "910f3adc-b1e5-48e7-b90c-6b59b5748ebf", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11681958523900, "endTime": 11692810599300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 53}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "a320c47e-b634-41f8-bca5-fc3c43<PERSON>eef", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810633900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9e31600-4bc5-4fe5-89c9-96eba0d56a0e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810728000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b13477-768e-4877-86a2-ce391cc74234", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e2a3d30-5346-48d8-9b24-69b77e195b14", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810885900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e07f1ec7-ed31-4822-8b1a-ed648099e16a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692810966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54267686-5468-4548-a1a3-bc25953e46bf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692811051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "128f83cf-48bc-475c-bc2e-342e46024d61", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692811161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c75c60b-5978-4d13-b6a3-b04064c44f91", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692812610500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b993b1f-4081-42a6-8eed-da9a5c5e220a", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692829977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fdc2f96-b39f-4c35-953f-b9646e4f3cdd", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692833510100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4abde760-4fb0-4180-a89b-cff0765e6497", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692833985100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f103f8-ff11-4606-9bfd-2cb0776e3ab1", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692855206700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53c4b223-380a-44ce-ba6f-4bec5a202e3f", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:45 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692855911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50268777-55c2-4029-87c7-f6d65baef771", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692856167300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cb9a167-d748-4fc7-82dd-4c6db785a6f8", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692856945200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c965a3-e00f-47ce-818d-e70a13880d41", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692857768500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c6cdf8-b2ce-494b-ad6a-aaddcfad0dfe", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692858181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e18be8-155d-4da4-aa15-306a03e23da7", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692858484800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d55bc21c-e4d5-4bb9-a453-07c4c423a48c", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692858807900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54ec52e-706d-424e-91ef-4be8fe64c8d0", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692862410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ed3cece-472c-402c-bfd4-6c3c649c5d5d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692863323000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea65dfb-3af0-463f-a3a0-2fa40affbd69", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692863653900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ab38d2-5cd5-4101-b794-652ab1e65ef5", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692879434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe5a8c9-b4e4-4415-8268-884685bf6865", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692880438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b641b6cc-2d7d-4b0b-bf1b-48bfac86dc9d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692880537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58320847-bf89-4f3c-adee-aa0fb837221d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692880809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "014e6e0a-3fde-4f10-8571-5b801f979b70", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692881619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c04d0046-0cbe-4ad6-bbd3-bf9ade454448", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692885533300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ef0338f-aefa-460a-8ee0-c594cbeb8a5d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692885846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3905ec-4790-4b4b-93ae-4560d73e07b7", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692886156800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f4e00e-d025-4d32-a5dc-23d9fa021257", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692886486000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1066be6d-07d5-4606-b06a-41fb4b19f7a5", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:28 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11692886811400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}