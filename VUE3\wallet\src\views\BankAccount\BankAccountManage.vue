<template>
  <div class="bank-account-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><CreditCard /></el-icon>
            银行账户管理
          </h2>
          <p>管理用户银行账户信息，包括查看、添加、编辑和删除操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleAdd" size="large">
            <el-icon><Plus /></el-icon>
            添加账户
          </el-button>
        </div>
      </div>
    </div>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>账户列表</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="正常" :value="1" />
            <el-option label="冻结" :value="0" />
            <el-option label="销户" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="accountId" label="账户ID" width="80" />
        <el-table-column prop="accountNumber" label="银行账号" width="180" />
        <el-table-column prop="bankName" label="银行名称" width="120" />
        <el-table-column prop="accountType" label="账户类型" width="100">
          <template #default="scope">
            {{ getAccountTypeName(scope.row.accountType) }}
          </template>
        </el-table-column>
        <el-table-column prop="accountHolder" label="账户持有人" width="120" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="balance" label="账户余额" width="120">
          <template #default="scope">
            {{ scope.row.currency }} {{ scope.row.balance }}
          </template>
        </el-table-column>
        <el-table-column prop="availableBalance" label="可用余额" width="120">
          <template #default="scope">
            {{ scope.row.currency }} {{ scope.row.availableBalance }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认账户" width="90">
          <template #default="scope">
            <el-tag :type="scope.row.isDefault === 1 ? 'success' : 'info'">
              {{ scope.row.isDefault === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="openDate" label="开户日期" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.accountId)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="formRules" 
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="用户ID" prop="userId">
          <el-input 
            v-model="form.userId" 
            placeholder="请输入用户ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="银行账号" prop="accountNumber">
          <el-input 
            v-model="form.accountNumber" 
            placeholder="请输入银行账号"
            clearable
            @input="formatAccountNumberInput"
          />
        </el-form-item>
        <el-form-item label="账户类型" prop="accountType">
          <el-select 
            v-model="form.accountType" 
            placeholder="请选择账户类型"
            style="width: 100%"
          >
            <el-option label="储蓄账户" :value="1" />
            <el-option label="支票账户" :value="2" />
            <el-option label="信用卡账户" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="银行名称" prop="bankName">
          <el-input 
            v-model="form.bankName" 
            placeholder="请输入银行名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="支行名称" prop="branchName">
          <el-input 
            v-model="form.branchName" 
            placeholder="请输入支行名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="账户持有人" prop="accountHolder">
          <el-input 
            v-model="form.accountHolder" 
            placeholder="请输入账户持有人姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input 
            v-model="form.phone" 
            placeholder="请输入手机号"
            clearable
            maxlength="11"
          />
        </el-form-item>
        <el-form-item label="币种" prop="currency">
          <el-select 
            v-model="form.currency" 
            placeholder="请选择币种"
            style="width: 100%"
          >
            <el-option label="人民币" value="CNY" />
            <el-option label="美元" value="USD" />
            <el-option label="欧元" value="EUR" />
          </el-select>
        </el-form-item>
        <el-form-item label="账户余额" prop="balance">
          <el-input-number 
            v-model="form.balance" 
            :precision="2" 
            :step="0.1" 
            :min="0"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="可用余额" prop="availableBalance">
          <el-input-number 
            v-model="form.availableBalance" 
            :precision="2" 
            :step="0.1" 
            :min="0"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item 
          label="信用额度" 
          prop="creditLimit" 
          v-if="form.accountType === 3"
        >
          <el-input-number 
            v-model="form.creditLimit" 
            :precision="2" 
            :step="0.1" 
            :min="0"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select 
            v-model="form.status" 
            placeholder="请选择状态"
            style="width: 100%"
          >
            <el-option label="正常" :value="1" />
            <el-option label="冻结" :value="0" />
            <el-option label="销户" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="默认账户" prop="isDefault">
          <el-switch 
            v-model="form.isDefault" 
            :active-value="1" 
            :inactive-value="0" 
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="开户日期" prop="openDate">
          <el-date-picker 
            v-model="form.openDate" 
            type="date" 
            placeholder="选择日期"
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CreditCard, Plus } from '@element-plus/icons-vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = ref({
  phone: '',
  status: null
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref(null)
const form = ref({
  accountId: null,
  userId: '',
  accountNumber: '',
  accountType: 1,
  bankName: '',
  branchName: '',
  accountHolder: '',
  phone: '',
  currency: 'CNY',
  balance: 0,
  availableBalance: 0,
  creditLimit: 0,
  status: 1,
  isDefault: 0,
  openDate: new Date()
})

// 更规范的表单验证规则
const formRules = {
  userId: [
    { required: true, message: '请输入用户ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '用户ID必须为数字', trigger: 'blur' }
  ],
  accountNumber: [
    { required: true, message: '请输入银行账号', trigger: 'blur' },
    { pattern: /^[\d\s]+$/, message: '银行账号只能包含数字和空格', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        const digitsOnly = value.replace(/\s/g, '')
        if (digitsOnly.length < 12 || digitsOnly.length > 19) {
          callback(new Error('银行账号长度应在12-19位之间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  accountType: [
    { required: true, message: '请选择账户类型', trigger: 'change' }
  ],
  bankName: [
    { required: true, message: '请输入银行名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ],
  branchName: [
    { min: 2, max: 100, message: '长度在2到100个字符之间', trigger: 'blur' }
  ],
  accountHolder: [
    { required: true, message: '请输入账户持有人姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  currency: [
    { required: true, message: '请选择币种', trigger: 'change' }
  ],
  balance: [
    { required: true, message: '请输入账户余额', trigger: 'blur' },
    { type: 'number', min: 0, message: '余额不能为负数', trigger: 'blur' }
  ],
  availableBalance: [
    { required: true, message: '请输入可用余额', trigger: 'blur' },
    { type: 'number', min: 0, message: '可用余额不能为负数', trigger: 'blur' }
  ],
  creditLimit: [
    { type: 'number', min: 0, message: '信用额度不能为负数', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择账户状态', trigger: 'change' }
  ],
  openDate: [
    { required: true, message: '请选择开户日期', trigger: 'change' }
  ]
}

// 格式化银行卡号显示 (每4位加空格)
const formatBankAccount = (accountNumber) => {
  if (!accountNumber) return ''
  const digitsOnly = accountNumber.replace(/\s/g, '')
  return digitsOnly.replace(/(\d{4})(?=\d)/g, '$1 ')
}

// 格式化手机号显示 (3-4-4格式)
const formatPhoneNumber = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
}

// 格式化货币显示 (千分位分隔)
const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return '0.00'
  return Number(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 处理银行卡号输入时的格式化
const formatAccountNumberInput = () => {
  // 先移除所有空格
  let value = form.value.accountNumber.replace(/\s/g, '')
  // 每4位添加一个空格
  value = value.replace(/(\d{4})(?=\d)/g, '$1 ')
  form.value.accountNumber = value
}

// 获取账户类型名称
const getAccountTypeName = (type) => {
  const types = {
    1: '储蓄账户',
    2: '支票账户',
    3: '信用卡账户'
  }
  return types[type] || '未知'
}

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    0: '冻结',
    1: '正常',
    2: '销户'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    0: 'warning',
    1: 'success',
    2: 'danger'
  }
  return typeMap[status] || ''
}

// 加载数据
const fetchBankAccounts = async () => {
  try {
    loading.value = true
    const response = await axios.get('http://localhost:8091/bankAccounts/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        phone: searchForm.value.phone,
        status: searchForm.value.status
      }
    })
    if (response.data && response.data.code === 0) {
      tableData.value = response.data.data?.records || []
      total.value = response.data.data?.total || 0
    } else {
      ElMessage.error('获取银行账户列表失败: ' + (response.data?.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取银行账户列表失败:', error)
    ElMessage.error('获取银行账户列表失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}


// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchBankAccounts()
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    phone: '',
    status: null
  }
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchBankAccounts()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchBankAccounts()
}

// 添加账户
const handleAdd = () => {
  dialogTitle.value = '添加银行账户'
  form.value = {
    accountId: null,
    userId: '',
    accountNumber: '',
    accountType: 1,
    bankName: '',
    branchName: '',
    accountHolder: '',
    phone: '',
    currency: 'CNY',
    balance: 0,
    availableBalance: 0,
    creditLimit: 0,
    status: 1,
    isDefault: 0,
    openDate: new Date()
  }
  dialogVisible.value = true
}

// 编辑账户
const handleEdit = (row) => {
  dialogTitle.value = '编辑银行账户'
  form.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()

    if (form.value.accountId) {
      // 更新
      const response = await axios.put('http://localhost:8091/bankAccounts/update', form.value)
      if (response.data && response.data.code === 0) {
        ElMessage.success('更新成功')
        dialogVisible.value = false
        fetchBankAccounts()
      } else {
        ElMessage.error('更新失败: ' + (response.data?.msg || '未知错误'))
      }
    } else {
      // 新增
      const response = await axios.post('http://localhost:8091/bankAccounts/add', form.value)
      if (response.data && response.data.code === 0) {
        ElMessage.success('添加成功')
        dialogVisible.value = false
        fetchBankAccounts()
      } else {
        ElMessage.error('添加失败: ' + (response.data?.msg || '未知错误'))
      }
    }
  } catch (error) {
    if (error.response) {
      console.error('操作失败:', error)
      ElMessage.error('操作失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
    } else if (!error.message.includes('validate')) {
      console.error('网络错误:', error)
      ElMessage.error('网络错误，请稍后重试')
    }
  }
}

// 删除账户
const handleDelete = (accountId) => {
  ElMessageBox.confirm('确定要删除该银行账户吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const response = await axios.delete(`http://localhost:8091/bankAccounts/${accountId}`)
        if (response.data && response.data.code === 0) {
          ElMessage.success('删除成功')
          fetchBankAccounts()
        } else {
          ElMessage.error('删除失败: ' + (response.data?.msg || '未知错误'))
        }
      } catch (error) {
        console.error('删除银行账户失败:', error)
        ElMessage.error('删除银行账户失败: ' + (error.response?.data?.msg || error.response?.data?.message || error.message))
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 初始化加载数据
onMounted(() => {
  fetchBankAccounts()
})
</script>

<style scoped>
.bank-account-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>