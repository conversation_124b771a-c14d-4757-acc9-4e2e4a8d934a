{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "e33718b2-428f-4da7-bb79-6a2869a5b126", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176495556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b0aedd-c10b-4a62-a1f0-d2f57914d3d1", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176508339300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac223cca-5e2f-4edf-9e3f-eb0b0d22cfc9", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10176508550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a9027bb-2c32-48e6-8c8f-ab4efc8b977a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694981722900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc9b751-3588-40e1-9074-c28e795f0899", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694988210400, "endTime": 10695145943200}, "additional": {"children": ["f2bd87c0-88e4-480e-9d31-dca232b8e18f", "16057eb4-acca-4921-8aee-5eab933ea5ce", "21e73f63-c716-4d95-bc27-8f337d55aa13", "7c9b3c85-0ed4-47ea-a402-ed81efe6edd9", "f938ff0c-063f-4c54-908e-a23beac0bdac", "34c4791a-d220-47f5-8496-c9a5423730d6", "6bade6b7-36aa-41e9-bbe6-4de4c93d4e75"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2bd87c0-88e4-480e-9d31-dca232b8e18f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694988212100, "endTime": 10695001839800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "d4f4c9cb-068d-4efe-a372-dbff6d569bd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16057eb4-acca-4921-8aee-5eab933ea5ce", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695001871500, "endTime": 10695144414500}, "additional": {"children": ["614a44ef-66ff-45d0-becd-4e0edd66852a", "4d7cdf97-9362-4946-ad52-f8284dc57acc", "a3b937b8-2867-4334-999d-b92e14f400b4", "fed1bf67-62b6-4377-8091-aa072a826fa8", "ce1785a1-531a-4383-9ab9-9b50bbcd1fc7", "f0e540d2-135e-4222-aafa-db2b4d536786", "33cc3faa-3898-4319-b062-29b38ebbee87", "354c6f26-bafb-4376-8e8d-849debd963e7", "a163be35-57f1-42f1-93f0-4888daf797d8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "4eba7728-4ebf-4256-9956-ce1d13550112"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21e73f63-c716-4d95-bc27-8f337d55aa13", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695144441100, "endTime": 10695145927200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "d01b9fbc-c8b1-46b5-8527-5c38ecd71edc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c9b3c85-0ed4-47ea-a402-ed81efe6edd9", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695145932900, "endTime": 10695145939300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "0415ba21-9e6e-44f9-bda7-85daf9b0cf2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f938ff0c-063f-4c54-908e-a23beac0bdac", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694991825100, "endTime": 10694991864600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "7f836852-0bd5-4df4-a90c-21753144bf31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f836852-0bd5-4df4-a90c-21753144bf31", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694991825100, "endTime": 10694991864600}, "additional": {"logType": "info", "children": [], "durationId": "f938ff0c-063f-4c54-908e-a23beac0bdac", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "34c4791a-d220-47f5-8496-c9a5423730d6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694996354200, "endTime": 10694996374800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "a7ab6319-242e-445a-b142-979607c0e4ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7ab6319-242e-445a-b142-979607c0e4ef", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694996354200, "endTime": 10694996374800}, "additional": {"logType": "info", "children": [], "durationId": "34c4791a-d220-47f5-8496-c9a5423730d6", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "2851aa3e-4298-4d43-9dcf-54ebe6f1fb14", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694996429000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e778d6b-cc63-4729-ac42-51bc97e58860", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695001660600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f4c9cb-068d-4efe-a372-dbff6d569bd0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694988212100, "endTime": 10695001839800}, "additional": {"logType": "info", "children": [], "durationId": "f2bd87c0-88e4-480e-9d31-dca232b8e18f", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "614a44ef-66ff-45d0-becd-4e0edd66852a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695007946100, "endTime": 10695007967500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "4536fdfc-c20f-4f66-8462-02bc77ba53c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d7cdf97-9362-4946-ad52-f8284dc57acc", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695007995700, "endTime": 10695015155400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "99ca336b-7b1a-41bd-911c-64bb3333910a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3b937b8-2867-4334-999d-b92e14f400b4", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695015179800, "endTime": 10695087965800}, "additional": {"children": ["6e277d7c-989b-4b8e-9e9b-6726bb1c6e07", "282dc9fb-4454-4eca-93f8-0cc16613ca93", "e6631e33-da87-46d1-8b1c-4589e1a42669"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "c97bf715-24a7-4d38-aa64-9685290e64f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fed1bf67-62b6-4377-8091-aa072a826fa8", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087979000, "endTime": 10695108141400}, "additional": {"children": ["73897410-3a03-4871-8d06-daf938b0cf11"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "a3aa450e-e00d-4d69-a129-6d1c6027cbd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce1785a1-531a-4383-9ab9-9b50bbcd1fc7", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695108152300, "endTime": 10695121068600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "00044df1-2324-42ef-b3ab-2d7abf31305b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0e540d2-135e-4222-aafa-db2b4d536786", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695122727400, "endTime": 10695131523900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "9d0fd84a-674a-4c44-92bf-096be5b81c49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33cc3faa-3898-4319-b062-29b38ebbee87", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695131548600, "endTime": 10695144267200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "06bbdbea-f358-4348-9ae1-16329694ff07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "354c6f26-bafb-4376-8e8d-849debd963e7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695144304400, "endTime": 10695144402000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "f0419dec-9b9b-4f7a-ad32-9b30734e3077"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4536fdfc-c20f-4f66-8462-02bc77ba53c0", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695007946100, "endTime": 10695007967500}, "additional": {"logType": "info", "children": [], "durationId": "614a44ef-66ff-45d0-becd-4e0edd66852a", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "99ca336b-7b1a-41bd-911c-64bb3333910a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695007995700, "endTime": 10695015155400}, "additional": {"logType": "info", "children": [], "durationId": "4d7cdf97-9362-4946-ad52-f8284dc57acc", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "6e277d7c-989b-4b8e-9e9b-6726bb1c6e07", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695016265600, "endTime": 10695016303700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3b937b8-2867-4334-999d-b92e14f400b4", "logId": "19959a48-39d5-4229-9efa-de48e4dfc98d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19959a48-39d5-4229-9efa-de48e4dfc98d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695016265600, "endTime": 10695016303700}, "additional": {"logType": "info", "children": [], "durationId": "6e277d7c-989b-4b8e-9e9b-6726bb1c6e07", "parent": "c97bf715-24a7-4d38-aa64-9685290e64f1"}}, {"head": {"id": "282dc9fb-4454-4eca-93f8-0cc16613ca93", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695018703600, "endTime": 10695087378600}, "additional": {"children": ["f10de53f-8c8b-42c0-a246-b46275477621", "8ce654a9-88e6-49e3-a9ee-b7b3ced5e9b8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3b937b8-2867-4334-999d-b92e14f400b4", "logId": "9b8867fd-6f97-406f-9590-190a6197e599"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f10de53f-8c8b-42c0-a246-b46275477621", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695018705500, "endTime": 10695023845800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "282dc9fb-4454-4eca-93f8-0cc16613ca93", "logId": "b3779630-d50b-4fa9-893b-a76f710e298a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ce654a9-88e6-49e3-a9ee-b7b3ced5e9b8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695023870000, "endTime": 10695087366800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "282dc9fb-4454-4eca-93f8-0cc16613ca93", "logId": "b8efc7c0-2797-417c-aabf-0d83180009a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64700f3b-efbf-4364-b751-f0039aeb031a", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695018711700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41fb4ea-0845-406a-92b7-bff1811eac60", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695023712400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3779630-d50b-4fa9-893b-a76f710e298a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695018705500, "endTime": 10695023845800}, "additional": {"logType": "info", "children": [], "durationId": "f10de53f-8c8b-42c0-a246-b46275477621", "parent": "9b8867fd-6f97-406f-9590-190a6197e599"}}, {"head": {"id": "a31fc1b1-d06d-43cb-93bb-93cc2d781c73", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695023884300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152a654f-07af-43c9-bf5f-4a6c8f42186e", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695030410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df60661a-2797-43d3-ac00-9fa27d7228e7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695030526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e18a361-9cfd-4b3c-a2b0-9b67ed2475a1", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695030660600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff69a3a-c078-4c78-9004-cc356213a7e0", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695030762800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3526b98a-40fd-4175-bf93-aee5f660b06b", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695033183900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e2082d-deb3-441c-9fd6-7140feaec049", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695036987600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47f90c41-544b-463d-a240-9978a5943253", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695046526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7176f359-5b49-4a3d-944c-433ceca136ff", "name": "Sdk init in 31 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695068347200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84077744-d382-4fc1-bbfb-1ef791bd098c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695068486800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 37}, "markType": "other"}}, {"head": {"id": "58be8769-2e2a-41c0-b172-13174d2cfbec", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695068501600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 37}, "markType": "other"}}, {"head": {"id": "c386245f-a37b-49c1-a834-9bc6055e6ba6", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087100500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7cc9913-22b7-48c6-854e-4fc5bc72955f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087216100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4e3135-39b2-4ee7-9d7a-c83069a6c689", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087280800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f82518ef-2ef5-4007-afc8-5db1992fb944", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8efc7c0-2797-417c-aabf-0d83180009a0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695023870000, "endTime": 10695087366800}, "additional": {"logType": "info", "children": [], "durationId": "8ce654a9-88e6-49e3-a9ee-b7b3ced5e9b8", "parent": "9b8867fd-6f97-406f-9590-190a6197e599"}}, {"head": {"id": "9b8867fd-6f97-406f-9590-190a6197e599", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695018703600, "endTime": 10695087378600}, "additional": {"logType": "info", "children": ["b3779630-d50b-4fa9-893b-a76f710e298a", "b8efc7c0-2797-417c-aabf-0d83180009a0"], "durationId": "282dc9fb-4454-4eca-93f8-0cc16613ca93", "parent": "c97bf715-24a7-4d38-aa64-9685290e64f1"}}, {"head": {"id": "e6631e33-da87-46d1-8b1c-4589e1a42669", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087939300, "endTime": 10695087951500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3b937b8-2867-4334-999d-b92e14f400b4", "logId": "9cb6c206-22ae-4f4b-b863-8936b2c21897"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cb6c206-22ae-4f4b-b863-8936b2c21897", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087939300, "endTime": 10695087951500}, "additional": {"logType": "info", "children": [], "durationId": "e6631e33-da87-46d1-8b1c-4589e1a42669", "parent": "c97bf715-24a7-4d38-aa64-9685290e64f1"}}, {"head": {"id": "c97bf715-24a7-4d38-aa64-9685290e64f1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695015179800, "endTime": 10695087965800}, "additional": {"logType": "info", "children": ["19959a48-39d5-4229-9efa-de48e4dfc98d", "9b8867fd-6f97-406f-9590-190a6197e599", "9cb6c206-22ae-4f4b-b863-8936b2c21897"], "durationId": "a3b937b8-2867-4334-999d-b92e14f400b4", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "73897410-3a03-4871-8d06-daf938b0cf11", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695088536300, "endTime": 10695108127400}, "additional": {"children": ["2f3eeb3b-f649-4ae3-8435-174c738ccf78", "d560dd83-293d-42c1-b381-55e6a6a0b639", "8f056dc8-5a3e-4ea0-872a-1d63a614cc39"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fed1bf67-62b6-4377-8091-aa072a826fa8", "logId": "a727763e-554a-416c-8875-b8d8c6d4ff93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f3eeb3b-f649-4ae3-8435-174c738ccf78", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695091349200, "endTime": 10695091362800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73897410-3a03-4871-8d06-daf938b0cf11", "logId": "4d287859-13ee-4df5-ad63-e422dc31da79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d287859-13ee-4df5-ad63-e422dc31da79", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695091349200, "endTime": 10695091362800}, "additional": {"logType": "info", "children": [], "durationId": "2f3eeb3b-f649-4ae3-8435-174c738ccf78", "parent": "a727763e-554a-416c-8875-b8d8c6d4ff93"}}, {"head": {"id": "d560dd83-293d-42c1-b381-55e6a6a0b639", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695093690400, "endTime": 10695106921100}, "additional": {"children": ["33966f61-5232-4f13-a77a-2306b1f394b0", "7eaf1071-7c8d-4747-8e86-a4a4838519e6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73897410-3a03-4871-8d06-daf938b0cf11", "logId": "f7603fcb-d993-44cc-b9d3-bca3f1cdc628"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33966f61-5232-4f13-a77a-2306b1f394b0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695093692100, "endTime": 10695097111200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d560dd83-293d-42c1-b381-55e6a6a0b639", "logId": "da0922b0-ece9-4681-9b95-cb7cf05efa64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7eaf1071-7c8d-4747-8e86-a4a4838519e6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695097127200, "endTime": 10695106910400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d560dd83-293d-42c1-b381-55e6a6a0b639", "logId": "9f0945e8-2749-496c-9a2e-79f311929166"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44dab9f8-c3fb-48dc-b026-8063a557719b", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695093697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253595d7-848b-4a6b-a9ae-81eba5782007", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695097008600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0922b0-ece9-4681-9b95-cb7cf05efa64", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695093692100, "endTime": 10695097111200}, "additional": {"logType": "info", "children": [], "durationId": "33966f61-5232-4f13-a77a-2306b1f394b0", "parent": "f7603fcb-d993-44cc-b9d3-bca3f1cdc628"}}, {"head": {"id": "2689d89f-674e-498e-9413-f42319127b58", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695097138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7269f39b-799e-4fbe-8b60-db484ba9d82b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695103539300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23d37e70-4f46-4852-a754-e11de1d9713f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695103670900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f95bf6-c725-470e-b73d-a5f7d46be3a3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695103863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09db8ce-abd2-4f6f-ba84-fec8360b1604", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695103994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d3566c-70e2-4374-9b1a-6113560157e5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695104051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e938008-68a4-4c5a-8209-5e6d1fe0c80a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695104095600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d8e2b7-bf79-4630-ba3c-c0ea3c6ad869", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695104146400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72e6f4cd-cf39-4d85-a623-60112614e5d8", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695106645700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78a4b0fb-ab30-4c49-80e3-2bb6762b5f03", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695106758500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f3c761-813b-4284-b6fd-dc570ff8a6fd", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695106823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3516a70-904f-40ea-96d0-ac8d42a1a31e", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695106868800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f0945e8-2749-496c-9a2e-79f311929166", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695097127200, "endTime": 10695106910400}, "additional": {"logType": "info", "children": [], "durationId": "7eaf1071-7c8d-4747-8e86-a4a4838519e6", "parent": "f7603fcb-d993-44cc-b9d3-bca3f1cdc628"}}, {"head": {"id": "f7603fcb-d993-44cc-b9d3-bca3f1cdc628", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695093690400, "endTime": 10695106921100}, "additional": {"logType": "info", "children": ["da0922b0-ece9-4681-9b95-cb7cf05efa64", "9f0945e8-2749-496c-9a2e-79f311929166"], "durationId": "d560dd83-293d-42c1-b381-55e6a6a0b639", "parent": "a727763e-554a-416c-8875-b8d8c6d4ff93"}}, {"head": {"id": "8f056dc8-5a3e-4ea0-872a-1d63a614cc39", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695108095200, "endTime": 10695108107900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73897410-3a03-4871-8d06-daf938b0cf11", "logId": "e510cf19-9797-4d4c-9fbf-f965d55172bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e510cf19-9797-4d4c-9fbf-f965d55172bd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695108095200, "endTime": 10695108107900}, "additional": {"logType": "info", "children": [], "durationId": "8f056dc8-5a3e-4ea0-872a-1d63a614cc39", "parent": "a727763e-554a-416c-8875-b8d8c6d4ff93"}}, {"head": {"id": "a727763e-554a-416c-8875-b8d8c6d4ff93", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695088536300, "endTime": 10695108127400}, "additional": {"logType": "info", "children": ["4d287859-13ee-4df5-ad63-e422dc31da79", "f7603fcb-d993-44cc-b9d3-bca3f1cdc628", "e510cf19-9797-4d4c-9fbf-f965d55172bd"], "durationId": "73897410-3a03-4871-8d06-daf938b0cf11", "parent": "a3aa450e-e00d-4d69-a129-6d1c6027cbd7"}}, {"head": {"id": "a3aa450e-e00d-4d69-a129-6d1c6027cbd7", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695087979000, "endTime": 10695108141400}, "additional": {"logType": "info", "children": ["a727763e-554a-416c-8875-b8d8c6d4ff93"], "durationId": "fed1bf67-62b6-4377-8091-aa072a826fa8", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "702d2790-8c28-466b-8ad1-67fe78e67555", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695120514800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2731070e-4a0a-4720-9818-b9e6006b0a7b", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695120916800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00044df1-2324-42ef-b3ab-2d7abf31305b", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695108152300, "endTime": 10695121068600}, "additional": {"logType": "info", "children": [], "durationId": "ce1785a1-531a-4383-9ab9-9b50bbcd1fc7", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "a163be35-57f1-42f1-93f0-4888daf797d8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695122302400, "endTime": 10695122689800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "16057eb4-acca-4921-8aee-5eab933ea5ce", "logId": "c3f2d61e-c462-4381-9aa5-4c18a361ee7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06bc9dc5-ca89-40bb-ab82-98640fd5d927", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695122365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f2d61e-c462-4381-9aa5-4c18a361ee7c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695122302400, "endTime": 10695122689800}, "additional": {"logType": "info", "children": [], "durationId": "a163be35-57f1-42f1-93f0-4888daf797d8", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "9b39a90b-ecc2-45f8-a695-244b2f6e4ac5", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695124401300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "511f99d2-0e3c-4b0c-a44f-f7484ac98fb9", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695130747300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d0fd84a-674a-4c44-92bf-096be5b81c49", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695122727400, "endTime": 10695131523900}, "additional": {"logType": "info", "children": [], "durationId": "f0e540d2-135e-4222-aafa-db2b4d536786", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "e5777c00-63d0-4bf7-85cd-d822117fc161", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695131568400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172ecf5c-e9d4-4088-8ed9-b063e9840ec1", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695136539000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45aa79b0-7f23-419b-bec2-60f94401e2e5", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695136649900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b942d76-1ead-4685-ad5b-54340f54ed8b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695136945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fde82e36-7de4-4230-8c01-08ae8db7f243", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695141504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5351a396-d184-48d5-8f7a-7de6254bc27f", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695141607500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bbdbea-f358-4348-9ae1-16329694ff07", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695131548600, "endTime": 10695144267200}, "additional": {"logType": "info", "children": [], "durationId": "33cc3faa-3898-4319-b062-29b38ebbee87", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "1a3f0e12-abac-4432-ac76-86c2eb71f193", "name": "Configuration phase cost:137 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695144328100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0419dec-9b9b-4f7a-ad32-9b30734e3077", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695144304400, "endTime": 10695144402000}, "additional": {"logType": "info", "children": [], "durationId": "354c6f26-bafb-4376-8e8d-849debd963e7", "parent": "4eba7728-4ebf-4256-9956-ce1d13550112"}}, {"head": {"id": "4eba7728-4ebf-4256-9956-ce1d13550112", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695001871500, "endTime": 10695144414500}, "additional": {"logType": "info", "children": ["4536fdfc-c20f-4f66-8462-02bc77ba53c0", "99ca336b-7b1a-41bd-911c-64bb3333910a", "c97bf715-24a7-4d38-aa64-9685290e64f1", "a3aa450e-e00d-4d69-a129-6d1c6027cbd7", "00044df1-2324-42ef-b3ab-2d7abf31305b", "9d0fd84a-674a-4c44-92bf-096be5b81c49", "06bbdbea-f358-4348-9ae1-16329694ff07", "f0419dec-9b9b-4f7a-ad32-9b30734e3077", "c3f2d61e-c462-4381-9aa5-4c18a361ee7c"], "durationId": "16057eb4-acca-4921-8aee-5eab933ea5ce", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "6bade6b7-36aa-41e9-bbe6-4de4c93d4e75", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695145904200, "endTime": 10695145917400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6cc9b751-3588-40e1-9074-c28e795f0899", "logId": "af188a49-889b-4161-8710-0bd51f91195e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af188a49-889b-4161-8710-0bd51f91195e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695145904200, "endTime": 10695145917400}, "additional": {"logType": "info", "children": [], "durationId": "6bade6b7-36aa-41e9-bbe6-4de4c93d4e75", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "d01b9fbc-c8b1-46b5-8527-5c38ecd71edc", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695144441100, "endTime": 10695145927200}, "additional": {"logType": "info", "children": [], "durationId": "21e73f63-c716-4d95-bc27-8f337d55aa13", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "0415ba21-9e6e-44f9-bda7-85daf9b0cf2a", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695145932900, "endTime": 10695145939300}, "additional": {"logType": "info", "children": [], "durationId": "7c9b3c85-0ed4-47ea-a402-ed81efe6edd9", "parent": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf"}}, {"head": {"id": "5b7622b6-0a7d-4164-af55-a6ab97ecfbbf", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694988210400, "endTime": 10695145943200}, "additional": {"logType": "info", "children": ["d4f4c9cb-068d-4efe-a372-dbff6d569bd0", "4eba7728-4ebf-4256-9956-ce1d13550112", "d01b9fbc-c8b1-46b5-8527-5c38ecd71edc", "0415ba21-9e6e-44f9-bda7-85daf9b0cf2a", "7f836852-0bd5-4df4-a90c-21753144bf31", "a7ab6319-242e-445a-b142-979607c0e4ef", "af188a49-889b-4161-8710-0bd51f91195e"], "durationId": "6cc9b751-3588-40e1-9074-c28e795f0899"}}, {"head": {"id": "342a24dd-39f1-40c4-8272-c1dd68f46b64", "name": "Configuration task cost before running: 161 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695146161400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84374e30-07ae-49bb-8c0f-7b53815ddc04", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695150395000, "endTime": 10695158304500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9581f8c2-6860-4a16-b229-0cbdda5500e1", "logId": "19382dca-8ce5-4dcc-a1ca-bd2b7d665c78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9581f8c2-6860-4a16-b229-0cbdda5500e1", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695147404500}, "additional": {"logType": "detail", "children": [], "durationId": "84374e30-07ae-49bb-8c0f-7b53815ddc04"}}, {"head": {"id": "bc8c3bc5-fab9-475a-ae0e-dea12867574c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695147840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c80b91-f713-404d-b6ed-73da223be2e9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695147914400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab9b9c0-5792-4ae7-87b3-716ae2af1383", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695147960600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45641154-0062-4401-96ed-86fc3e75b3a7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695150409200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650f11aa-7d11-41db-a1c2-83c494487199", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695158058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776d0d63-623c-4069-8efd-af425d435e7b", "name": "entry : default@PreBuild cost memory 0.2952728271484375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695158216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19382dca-8ce5-4dcc-a1ca-bd2b7d665c78", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695150395000, "endTime": 10695158304500}, "additional": {"logType": "info", "children": [], "durationId": "84374e30-07ae-49bb-8c0f-7b53815ddc04"}}, {"head": {"id": "5770c8b8-72c1-42bb-9f90-4898569dfc06", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695162867400, "endTime": 10695165565000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3f7cf695-72ec-41bd-836e-bb54c6532d06", "logId": "286bb64e-cfb9-45a9-abbe-6027752ccd34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f7cf695-72ec-41bd-836e-bb54c6532d06", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695161693400}, "additional": {"logType": "detail", "children": [], "durationId": "5770c8b8-72c1-42bb-9f90-4898569dfc06"}}, {"head": {"id": "4d52a09d-dfa6-4ae8-a47a-5586db83aacd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695162144500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3788913f-9336-4850-a919-4b8a1a278909", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695162223300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b956c0-4d0c-4fbd-a76f-a2f0b148fb0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695162269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f09895e-9400-4c11-80ac-974022cddda2", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695162879800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca3e7a8e-8798-4cd4-90a7-3925a66c7ffc", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695165417000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa81e521-17ee-4659-b7f4-9dbd7b997952", "name": "entry : default@MergeProfile cost memory 0.13233184814453125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695165503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "286bb64e-cfb9-45a9-abbe-6027752ccd34", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695162867400, "endTime": 10695165565000}, "additional": {"logType": "info", "children": [], "durationId": "5770c8b8-72c1-42bb-9f90-4898569dfc06"}}, {"head": {"id": "0156e967-a4c4-4d9b-8a91-8a3244d671e7", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695168559500, "endTime": 10695170659500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e2ed3487-52ba-4617-9d8e-ac043ea9df63", "logId": "9d525837-edc2-4398-b1e9-52b9de0c4ac8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2ed3487-52ba-4617-9d8e-ac043ea9df63", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695167197800}, "additional": {"logType": "detail", "children": [], "durationId": "0156e967-a4c4-4d9b-8a91-8a3244d671e7"}}, {"head": {"id": "33bc2e6a-ce5d-4342-ab3e-dc027052b8e1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695167682000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e897f1b3-bc17-4da9-9c62-dd184d5ccf51", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695167791700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8be284a-e2fc-43ae-a87e-f8931d00aeb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695167847200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d467863a-8e70-496f-aad9-9ea271ef4a3e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695168569600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a149646-42fe-4cc7-9ebb-a2620e79f23c", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695169414300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af0adc9f-7a1b-449b-a5f4-ff4a1aefadba", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695170516100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "578cb4b7-32fc-407a-95d4-08663ce76c31", "name": "entry : default@CreateBuildProfile cost memory 0.10052490234375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695170600300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d525837-edc2-4398-b1e9-52b9de0c4ac8", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695168559500, "endTime": 10695170659500}, "additional": {"logType": "info", "children": [], "durationId": "0156e967-a4c4-4d9b-8a91-8a3244d671e7"}}, {"head": {"id": "9934ede9-3390-4966-ae94-2541d38cb45e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173126300, "endTime": 10695173986700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7c57d10b-7fe8-457a-adaa-b7e1e22870d7", "logId": "e3ff3641-f2b5-4e22-843e-8317dd650050"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c57d10b-7fe8-457a-adaa-b7e1e22870d7", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695171958000}, "additional": {"logType": "detail", "children": [], "durationId": "9934ede9-3390-4966-ae94-2541d38cb45e"}}, {"head": {"id": "d5206a69-6559-47ab-ad3c-a97d97ad2fa2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695172383500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ded3998-bf11-4be1-94cc-03e8e034712a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695172458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ff6cd0-b955-4c93-984b-a5a357e10365", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695172503400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f54c4b8a-5032-4530-bcda-5ab0533a597f", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173134700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9abae710-e445-4a5f-a9a1-c4d158146fc5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3002385f-e8a8-438f-bb86-c14684465acc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173277900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4fe77b9-7114-469c-9c29-5da79754a8e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f76a66-9f2d-4bed-b4a1-269b37f5c6d0", "name": "entry : default@PreCheckSyscap cost memory 0.05040740966796875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173384000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe7117c-150b-4ca4-9dab-08036289feaf", "name": "runTaskFromQueue task cost before running: 189 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173452200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3ff3641-f2b5-4e22-843e-8317dd650050", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695173126300, "endTime": 10695173986700, "totalTime": 307800}, "additional": {"logType": "info", "children": [], "durationId": "9934ede9-3390-4966-ae94-2541d38cb45e"}}, {"head": {"id": "40833081-6083-4f1a-abd0-34ccbeb9829b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695183229900, "endTime": 10695184455300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9922c96d-a20b-4a6e-92db-5ead8ec0b8b0", "logId": "b2ee9e61-7467-4c73-bca0-1871f5596ca4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9922c96d-a20b-4a6e-92db-5ead8ec0b8b0", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695175553700}, "additional": {"logType": "detail", "children": [], "durationId": "40833081-6083-4f1a-abd0-34ccbeb9829b"}}, {"head": {"id": "d9bab7a5-21d4-4883-8485-cfbb3f082151", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695176048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2b24eb2-9085-4eb1-9946-b3b5641a059d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695176145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5359c014-1b8c-4e05-a74d-3fe512129003", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695176195100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8e0491-47b2-4a34-84f8-ae4288ed771e", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695183246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d932ea0-5330-45d1-aaf7-42c43765bdcc", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695183623600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211edd9a-f4a2-4e27-9205-dd9cd42b4527", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695184230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4fe5d0-2acf-4da3-9146-a7e116f59aa4", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06978607177734375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695184331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2ee9e61-7467-4c73-bca0-1871f5596ca4", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695183229900, "endTime": 10695184455300}, "additional": {"logType": "info", "children": [], "durationId": "40833081-6083-4f1a-abd0-34ccbeb9829b"}}, {"head": {"id": "1f140deb-6e0f-47aa-8b6a-10ae11b106db", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695187785500, "endTime": 10695188856800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2b58c733-c647-4562-9fd0-c8b6609ece57", "logId": "22c35619-2473-4154-bf2c-6b34442f7a06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b58c733-c647-4562-9fd0-c8b6609ece57", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695186148200}, "additional": {"logType": "detail", "children": [], "durationId": "1f140deb-6e0f-47aa-8b6a-10ae11b106db"}}, {"head": {"id": "132de8c5-8cdb-41a9-b2ca-07b34f348e79", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695186610400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e1ed11-c6d5-41c7-bf2f-6e8951eb4fd2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695186693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e7f2b34-5300-45bf-be03-5c8adef78ba5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695186741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86fb2b98-4299-4935-a5dd-84f38e76f773", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695187794400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f8c62d-4101-4182-a67a-633c49cf5399", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695188707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dea3728-c894-45f9-9575-3ee9043e80a6", "name": "entry : default@ProcessProfile cost memory 0.057098388671875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695188793700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c35619-2473-4154-bf2c-6b34442f7a06", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695187785500, "endTime": 10695188856800}, "additional": {"logType": "info", "children": [], "durationId": "1f140deb-6e0f-47aa-8b6a-10ae11b106db"}}, {"head": {"id": "606209ab-72aa-473b-8647-540a48523692", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695192366100, "endTime": 10695197970800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "99cb0a5f-c4b3-4833-b5b5-ce88a145c2a9", "logId": "601271e6-0a72-4db9-92ae-03e430a0884b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99cb0a5f-c4b3-4833-b5b5-ce88a145c2a9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695190273100}, "additional": {"logType": "detail", "children": [], "durationId": "606209ab-72aa-473b-8647-540a48523692"}}, {"head": {"id": "68b8d994-5e44-48fc-ab56-750c59887a6f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695190717100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd70684-6a98-47b4-bf4b-0b01c5607784", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695190794000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111ddec7-b881-4a09-941a-3a6b1215b1c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695190839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08e153ce-ed72-4ad6-bc2a-346abaf7881a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695192376800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d13567-b3fe-4d21-a245-2db1e123a047", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695197769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8cfde3-6c1a-475e-8d0b-ef1d81caccb9", "name": "entry : default@ProcessRouterMap cost memory 0.22821807861328125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695197896600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "601271e6-0a72-4db9-92ae-03e430a0884b", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695192366100, "endTime": 10695197970800}, "additional": {"logType": "info", "children": [], "durationId": "606209ab-72aa-473b-8647-540a48523692"}}, {"head": {"id": "9ccfb231-8903-462c-99ac-923ede232ec0", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695205389000, "endTime": 10695208098100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "68dd1f65-6db0-4c23-82dd-aad08555dccb", "logId": "14794179-3d89-4616-bd5b-104381a844a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68dd1f65-6db0-4c23-82dd-aad08555dccb", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695200802400}, "additional": {"logType": "detail", "children": [], "durationId": "9ccfb231-8903-462c-99ac-923ede232ec0"}}, {"head": {"id": "f17236c9-7380-42c3-a81f-e1e192164465", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695201276500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63bfc5ef-7bb7-4b70-bdf4-18775aeaa588", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695201368000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e9536c-f4c8-4123-bafe-5a0a624b0a8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695201418500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6286683c-8788-4fec-b83a-aefe8f42ca1f", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695202246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f6bcbdb-3b88-4835-adac-7cb81ab61aa1", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695206532100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb0c77a-fcf4-4c7c-8b8f-4adfceaa97a7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695206680700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef549c15-5b2d-4cba-a9f1-14efbc9d10a8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695206739000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7228be2f-10af-4b10-bb8b-345bf723407f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695206781700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211db607-7678-461f-bdfd-4eb43a8d645c", "name": "entry : default@PreviewProcessResource cost memory 0.093048095703125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695206847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ad0165-5f22-4f0c-82f3-91d6d14af83e", "name": "runTaskFromQueue task cost before running: 223 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695208014900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14794179-3d89-4616-bd5b-104381a844a1", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695205389000, "endTime": 10695208098100, "totalTime": 1517200}, "additional": {"logType": "info", "children": [], "durationId": "9ccfb231-8903-462c-99ac-923ede232ec0"}}, {"head": {"id": "018ddf58-730a-4d7c-8038-cb6dab974ffc", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695214493700, "endTime": 10695233896900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "21c889b6-108d-4ca3-b519-73439326ccc5", "logId": "10d4b2b0-90f8-40de-b886-f731963862ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21c889b6-108d-4ca3-b519-73439326ccc5", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695210618700}, "additional": {"logType": "detail", "children": [], "durationId": "018ddf58-730a-4d7c-8038-cb6dab974ffc"}}, {"head": {"id": "33af2639-f05e-493f-8f66-00c02895a216", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695211095000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0b63f9-fe42-49dc-95da-acd8e312ee32", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695211188200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0aed698-2930-4459-9819-a22b001808c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695211237900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e10fc7e-d9e9-40a6-8d98-f5cf798505b8", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695214509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "505aafa3-8f48-4639-b55c-78e4606e8d4f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695233668700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef1a133a-f02e-41d5-9572-7ef1fb77c4f7", "name": "entry : default@GenerateLoaderJson cost memory 0.**********820312", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695233821900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10d4b2b0-90f8-40de-b886-f731963862ce", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695214493700, "endTime": 10695233896900}, "additional": {"logType": "info", "children": [], "durationId": "018ddf58-730a-4d7c-8038-cb6dab974ffc"}}, {"head": {"id": "c9095f0c-f5ff-40b7-a88a-b7bf823cb8ff", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695244939500, "endTime": 10695281004100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "503d6361-59a6-41f3-b2b4-6a24756acb23", "logId": "eeb85e1e-e864-4a16-9508-2b83eb1ea647"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "503d6361-59a6-41f3-b2b4-6a24756acb23", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695241086300}, "additional": {"logType": "detail", "children": [], "durationId": "c9095f0c-f5ff-40b7-a88a-b7bf823cb8ff"}}, {"head": {"id": "fe42a14f-4c6a-4895-9d09-7f954450bd96", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695241546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ca795f-e3a0-42a8-8fcb-069b2779780d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695241634800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a225b27d-820e-40c0-8813-6e9c344372bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695241684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707bf67e-f4b5-48c5-969a-82dea7624a8b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695242547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac2f1ae5-0e25-4ec3-bdaf-36168c94bc86", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695244972300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079e0cde-2960-448e-a1f1-10087576a367", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695280756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f25427c5-ef11-4c1a-b3ba-f2d774c127a3", "name": "entry : default@PreviewCompileResource cost memory 1.4839935302734375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695280903500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb85e1e-e864-4a16-9508-2b83eb1ea647", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695244939500, "endTime": 10695281004100}, "additional": {"logType": "info", "children": [], "durationId": "c9095f0c-f5ff-40b7-a88a-b7bf823cb8ff"}}, {"head": {"id": "a0559f65-4df9-48ad-b511-1cd51820d6b9", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284324200, "endTime": 10695284661300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "aed55887-e2bb-4bb2-9420-1469eac2a3c0", "logId": "cf5dd011-842f-420a-b730-8b1db5942340"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aed55887-e2bb-4bb2-9420-1469eac2a3c0", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695283579600}, "additional": {"logType": "detail", "children": [], "durationId": "a0559f65-4df9-48ad-b511-1cd51820d6b9"}}, {"head": {"id": "5c028466-1468-488e-a1bd-e5e0fee49ddf", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f4de82-810d-42f5-b3ec-da1ceb2b5d46", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284195900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a0e4f0-5949-49fe-926d-12af7aab3da6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284249200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3765ba-f085-43fc-843d-aa30ed9f95e9", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284332400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c931576-fdbf-4455-a672-a93665949e63", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284411600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85eddd93-4091-4b11-82d2-df243fbe80d8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284452400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b2619e-b519-4ebe-9cee-7afd3b87feae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284489500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9431811f-3a35-4cae-a4ef-01b1b8e636a7", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ca56a0-6b2b-48ed-add1-2bb4641edc74", "name": "runTaskFromQueue task cost before running: 300 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf5dd011-842f-420a-b730-8b1db5942340", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695284324200, "endTime": 10695284661300, "totalTime": 269400}, "additional": {"logType": "info", "children": [], "durationId": "a0559f65-4df9-48ad-b511-1cd51820d6b9"}}, {"head": {"id": "cc69d59f-5a6e-4c80-b0e7-76f2b39566fc", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695287097700, "endTime": 10695289213900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c487cdc6-332b-4689-bcff-9c39fa85e8ef", "logId": "7bc9f19e-04a2-447c-a3f7-0d1dbedc8b0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c487cdc6-332b-4689-bcff-9c39fa85e8ef", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695286025500}, "additional": {"logType": "detail", "children": [], "durationId": "cc69d59f-5a6e-4c80-b0e7-76f2b39566fc"}}, {"head": {"id": "8e77a964-f5a6-415f-b7c7-a3c3c6bfdec4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695286443200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a690a0-5447-41ea-8b2a-f4f7df4c4fd0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695286516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72793627-94d7-4fe6-a70f-50bfd4fee8dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695286561500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716b5e54-dda1-40ee-a6ab-d2ea24716f3b", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695287105700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae408b3-4c7b-46df-80f8-a5b84d394159", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695289049400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "533e1441-4bca-4316-b1d6-1b9fa5b2ac8f", "name": "entry : default@CopyPreviewProfile cost memory 0.12810516357421875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695289157400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc9f19e-04a2-447c-a3f7-0d1dbedc8b0f", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695287097700, "endTime": 10695289213900}, "additional": {"logType": "info", "children": [], "durationId": "cc69d59f-5a6e-4c80-b0e7-76f2b39566fc"}}, {"head": {"id": "fc9d80e2-0523-4d72-9963-e4b2b3928289", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291822300, "endTime": 10695292228400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "9d315572-86f3-4e6b-999d-ff632ad6d950", "logId": "d82ebc6c-2985-469c-8281-2c6a794265a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d315572-86f3-4e6b-999d-ff632ad6d950", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695290611900}, "additional": {"logType": "detail", "children": [], "durationId": "fc9d80e2-0523-4d72-9963-e4b2b3928289"}}, {"head": {"id": "2d75a043-353d-4a5b-a71e-d1fd3732509e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bb0bbf-b14f-4403-9730-c67bae75dc24", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291124500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d84f53a-a3cf-4ea7-9a50-ce32fd1de513", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291171300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354136dd-50fa-43b1-9098-dda2125ef56e", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72309254-57b3-4099-b553-522051c4cf3d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d36f885-98fd-45ca-a799-b6179183969e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291987400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d84056ec-d557-402f-ad0f-2dc9f0f2148e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695292025600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3590ad41-754e-4252-85c0-91cf4eae13d1", "name": "entry : default@ReplacePreviewerPage cost memory 0.0514678955078125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695292102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae3b5243-d179-4131-b548-367a3085ca5f", "name": "runTaskFromQueue task cost before running: 308 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695292177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82ebc6c-2985-469c-8281-2c6a794265a9", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695291822300, "endTime": 10695292228400, "totalTime": 333900}, "additional": {"logType": "info", "children": [], "durationId": "fc9d80e2-0523-4d72-9963-e4b2b3928289"}}, {"head": {"id": "a871d410-b58e-4c60-a55a-1dcf8558bb81", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695293646500, "endTime": 10695293866300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "50456546-c22f-4ffa-8c39-8937becf4458", "logId": "39228948-e2b5-40be-afff-fd92b98c8d2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50456546-c22f-4ffa-8c39-8937becf4458", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695293595700}, "additional": {"logType": "detail", "children": [], "durationId": "a871d410-b58e-4c60-a55a-1dcf8558bb81"}}, {"head": {"id": "e2db2774-d61e-4167-aa0a-b0c628158b26", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695293653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9413b899-dd93-4c0f-9606-818fc9538e00", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695293747800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "274c6b33-1e5b-4cdc-b775-48e13004badf", "name": "runTaskFromQueue task cost before running: 309 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695293819000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39228948-e2b5-40be-afff-fd92b98c8d2a", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695293646500, "endTime": 10695293866300, "totalTime": 155300}, "additional": {"logType": "info", "children": [], "durationId": "a871d410-b58e-4c60-a55a-1dcf8558bb81"}}, {"head": {"id": "c41f0ba5-971b-4cd0-8126-5cbd96207625", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695296315000, "endTime": 10695298421500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e43b1c40-0c71-43cb-b90d-1c0fc5ecc9c6", "logId": "59131d38-dc0a-4d1a-ad70-c13fb54c051a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e43b1c40-0c71-43cb-b90d-1c0fc5ecc9c6", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695295172800}, "additional": {"logType": "detail", "children": [], "durationId": "c41f0ba5-971b-4cd0-8126-5cbd96207625"}}, {"head": {"id": "151d9ec6-4d59-41ae-a906-fe090a42b531", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695295593300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b93c9213-956b-4994-b5a0-2f2428e1ef13", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695295665200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4cd39c-2cc6-4f1d-907b-d0e096fcfaaf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695295709300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97858eec-0098-48c6-8225-3896728134f7", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695296323900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "814b493d-0076-481c-991e-c4c4dc61a287", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695298284800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47eca97c-9afd-4d32-8d6a-f9d1e9f11378", "name": "entry : default@PreviewUpdateAssets cost memory 0.11650848388671875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695298366500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59131d38-dc0a-4d1a-ad70-c13fb54c051a", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695296315000, "endTime": 10695298421500}, "additional": {"logType": "info", "children": [], "durationId": "c41f0ba5-971b-4cd0-8126-5cbd96207625"}}, {"head": {"id": "72d81758-225f-4cb7-859f-d343f7cb141c", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695305999000, "endTime": 10707236118100}, "additional": {"children": ["ef474155-388a-4ba8-92d9-4cabd504537b"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6ddae483-140d-4c94-b309-5694ba779fa6", "logId": "41c7eef0-6d27-46a8-92e0-5412bda15397"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ddae483-140d-4c94-b309-5694ba779fa6", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695300367500}, "additional": {"logType": "detail", "children": [], "durationId": "72d81758-225f-4cb7-859f-d343f7cb141c"}}, {"head": {"id": "59b43c2b-8841-4245-8379-88531645c764", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695300890800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827c61b2-57a9-4ec3-9ec9-71b5684b892b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695301001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64976fd4-03f1-4119-9aab-624ea649e7e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695301051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712e888a-7c80-42ba-82af-eafd05ee1ad8", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695306009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef474155-388a-4ba8-92d9-4cabd504537b", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker5", "startTime": 10695327767800, "endTime": 10707235905700}, "additional": {"children": ["b7b75e30-3586-4a7d-8f44-52e48798c12a", "6d5178f8-3583-4a86-8214-ab77d0f52db6", "7987d438-16ca-45c4-a2b5-3314d92d9def", "0ed49b3b-e664-4e5d-a0ad-d32bc1fa8f97", "dd402529-ad5c-4270-8851-b1063b72c7bd", "cc004ce5-928a-4cef-90bf-273ca770b1eb", "b5df68ab-8053-4f30-97f5-934537a9472c", "81d2d111-229b-452d-8d63-86bac44bea52", "2009831f-5982-4d52-b49e-fd537cce0361", "593c027b-08c5-451d-983b-84a2ccd957cf", "be589ce9-cd49-473f-90b1-4a6aa273a5e1", "cf4a9ca4-eec3-44dd-8a50-ad8486af7373", "47364f97-4933-4398-bb78-961436c274ac", "56dd6279-642a-4c18-bd4c-34096853bfec", "85aac4b4-25b0-4819-ab83-5f91a37491ae"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "72d81758-225f-4cb7-859f-d343f7cb141c", "logId": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "321173a1-4b8b-40c6-8589-865e7a94a4ab", "name": "entry : default@PreviewArkTS cost memory 1.1580352783203125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695329673300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ece8cac-d46b-4f08-b2b8-96fd57bfee16", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10698836358100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b75e30-3586-4a7d-8f44-52e48798c12a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10698837512400, "endTime": 10698837531000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "01296cdd-7b07-4af4-af70-fc2b532908bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01296cdd-7b07-4af4-af70-fc2b532908bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10698837512400, "endTime": 10698837531000}, "additional": {"logType": "info", "children": [], "durationId": "b7b75e30-3586-4a7d-8f44-52e48798c12a", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "01488775-c9a9-49cd-9b9e-bfb847c3a6eb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704250295500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d5178f8-3583-4a86-8214-ab77d0f52db6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704251249800, "endTime": 10704251268400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "8d6c81fa-bca8-4712-b4cf-232398fad9c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d6c81fa-bca8-4712-b4cf-232398fad9c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704251249800, "endTime": 10704251268400}, "additional": {"logType": "info", "children": [], "durationId": "6d5178f8-3583-4a86-8214-ab77d0f52db6", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "4164b6cb-3c2f-4603-833d-ec441eeab7a8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704251342400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7987d438-16ca-45c4-a2b5-3314d92d9def", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704252053900, "endTime": 10704252066400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "71df5d74-06b2-40b7-9ff6-989c6ba9e490"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71df5d74-06b2-40b7-9ff6-989c6ba9e490", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704252053900, "endTime": 10704252066400}, "additional": {"logType": "info", "children": [], "durationId": "7987d438-16ca-45c4-a2b5-3314d92d9def", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "92eb681a-5386-42ed-81b0-70846e004ac2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704252125700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed49b3b-e664-4e5d-a0ad-d32bc1fa8f97", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704252809600, "endTime": 10704252822000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "1a177a3e-793d-4ad2-8f88-2260c5efaf35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a177a3e-793d-4ad2-8f88-2260c5efaf35", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704252809600, "endTime": 10704252822000}, "additional": {"logType": "info", "children": [], "durationId": "0ed49b3b-e664-4e5d-a0ad-d32bc1fa8f97", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "60e5490b-e936-4e42-a64b-bcd23b32e422", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704252881600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd402529-ad5c-4270-8851-b1063b72c7bd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704253608900, "endTime": 10704253624200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "6f5d53f2-d8fd-44c3-962c-497484ac2c2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f5d53f2-d8fd-44c3-962c-497484ac2c2f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704253608900, "endTime": 10704253624200}, "additional": {"logType": "info", "children": [], "durationId": "dd402529-ad5c-4270-8851-b1063b72c7bd", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "3c90601d-b6bc-4cef-a544-ef09c9153462", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704253691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc004ce5-928a-4cef-90bf-273ca770b1eb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704254359500, "endTime": 10704254371700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "90a39f5e-ed49-4962-a741-8e2b9dfc9e33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90a39f5e-ed49-4962-a741-8e2b9dfc9e33", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704254359500, "endTime": 10704254371700}, "additional": {"logType": "info", "children": [], "durationId": "cc004ce5-928a-4cef-90bf-273ca770b1eb", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "c620fe08-e3f6-40fe-a32a-f431103b7c24", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704254431000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5df68ab-8053-4f30-97f5-934537a9472c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704255106100, "endTime": 10704255117800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "2120fbc9-04d9-4f30-a1b8-90260e329fc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2120fbc9-04d9-4f30-a1b8-90260e329fc8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704255106100, "endTime": 10704255117800}, "additional": {"logType": "info", "children": [], "durationId": "b5df68ab-8053-4f30-97f5-934537a9472c", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "04d6778c-bca6-4727-9ef1-cdf7318b4f75", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704255175700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d2d111-229b-452d-8d63-86bac44bea52", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704255830600, "endTime": 10704255842600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "5ef89554-9d86-4661-bd34-837c8e2b94ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ef89554-9d86-4661-bd34-837c8e2b94ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704255830600, "endTime": 10704255842600}, "additional": {"logType": "info", "children": [], "durationId": "81d2d111-229b-452d-8d63-86bac44bea52", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "ede216f0-8853-475f-9037-4ad5296dab3f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704255898900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2009831f-5982-4d52-b49e-fd537cce0361", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704256576100, "endTime": 10704256593800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "3a5135df-4d04-4997-84a5-49e4c9e38083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a5135df-4d04-4997-84a5-49e4c9e38083", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704256576100, "endTime": 10704256593800}, "additional": {"logType": "info", "children": [], "durationId": "2009831f-5982-4d52-b49e-fd537cce0361", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "adcbf2d0-1875-4258-8cc9-a8fb89470445", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704444461200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "593c027b-08c5-451d-983b-84a2ccd957cf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704445603800, "endTime": 10704445630000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "7c69ee78-5e0a-4013-ab41-a08018640e13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c69ee78-5e0a-4013-ab41-a08018640e13", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704445603800, "endTime": 10704445630000}, "additional": {"logType": "info", "children": [], "durationId": "593c027b-08c5-451d-983b-84a2ccd957cf", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "e40a39c9-1638-43f0-9dc4-bd347f17dbca", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704582954200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be589ce9-cd49-473f-90b1-4a6aa273a5e1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704584564700, "endTime": 10704584588800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "5577f66e-0279-4e44-9fc9-6adba9a55f19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5577f66e-0279-4e44-9fc9-6adba9a55f19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704584564700, "endTime": 10704584588800}, "additional": {"logType": "info", "children": [], "durationId": "be589ce9-cd49-473f-90b1-4a6aa273a5e1", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "a340a11c-031b-42ca-836c-e5d6c000b7b1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704717546100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf4a9ca4-eec3-44dd-8a50-ad8486af7373", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704718865000, "endTime": 10704718885400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "95e6403f-9745-4f68-98a2-1ae56c2b53d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95e6403f-9745-4f68-98a2-1ae56c2b53d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704718865000, "endTime": 10704718885400}, "additional": {"logType": "info", "children": [], "durationId": "cf4a9ca4-eec3-44dd-8a50-ad8486af7373", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "a15d5185-63f3-434e-820e-330f1c7d5bc5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704763706400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47364f97-4933-4398-bb78-961436c274ac", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704764793600, "endTime": 10704764813400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "37392602-d8f0-4dee-a1d0-c064f83936dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37392602-d8f0-4dee-a1d0-c064f83936dd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704764793600, "endTime": 10704764813400}, "additional": {"logType": "info", "children": [], "durationId": "47364f97-4933-4398-bb78-961436c274ac", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "b512ceb7-abe8-410e-8bbf-dbfe911cf9c8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704855316800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56dd6279-642a-4c18-bd4c-34096853bfec", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704856388700, "endTime": 10704856409000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "ab4637fc-7386-48ce-830b-d669148ae7ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab4637fc-7386-48ce-830b-d669148ae7ba", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10704856388700, "endTime": 10704856409000}, "additional": {"logType": "info", "children": [], "durationId": "56dd6279-642a-4c18-bd4c-34096853bfec", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "2842edd4-683b-4629-a58d-a99b1b148c18", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707234840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85aac4b4-25b0-4819-ab83-5f91a37491ae", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707235817000, "endTime": 10707235835600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ef474155-388a-4ba8-92d9-4cabd504537b", "logId": "4df889ab-5679-4588-ab36-36a5cc166313"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4df889ab-5679-4588-ab36-36a5cc166313", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707235817000, "endTime": 10707235835600}, "additional": {"logType": "info", "children": [], "durationId": "85aac4b4-25b0-4819-ab83-5f91a37491ae", "parent": "bd2c1bf7-0061-40eb-9045-c995f2641aa2"}}, {"head": {"id": "bd2c1bf7-0061-40eb-9045-c995f2641aa2", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker5", "startTime": 10695327767800, "endTime": 10707235905700}, "additional": {"logType": "error", "children": ["01296cdd-7b07-4af4-af70-fc2b532908bd", "8d6c81fa-bca8-4712-b4cf-232398fad9c0", "71df5d74-06b2-40b7-9ff6-989c6ba9e490", "1a177a3e-793d-4ad2-8f88-2260c5efaf35", "6f5d53f2-d8fd-44c3-962c-497484ac2c2f", "90a39f5e-ed49-4962-a741-8e2b9dfc9e33", "2120fbc9-04d9-4f30-a1b8-90260e329fc8", "5ef89554-9d86-4661-bd34-837c8e2b94ed", "3a5135df-4d04-4997-84a5-49e4c9e38083", "7c69ee78-5e0a-4013-ab41-a08018640e13", "5577f66e-0279-4e44-9fc9-6adba9a55f19", "95e6403f-9745-4f68-98a2-1ae56c2b53d3", "37392602-d8f0-4dee-a1d0-c064f83936dd", "ab4637fc-7386-48ce-830b-d669148ae7ba", "4df889ab-5679-4588-ab36-36a5cc166313"], "durationId": "ef474155-388a-4ba8-92d9-4cabd504537b", "parent": "41c7eef0-6d27-46a8-92e0-5412bda15397"}}, {"head": {"id": "2d024fe3-6dd4-48d3-bbf0-ba3978d5fce8", "name": "default@PreviewArkTS watch work[5] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707235974000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41c7eef0-6d27-46a8-92e0-5412bda15397", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10695305999000, "endTime": 10707236118100}, "additional": {"logType": "error", "children": ["bd2c1bf7-0061-40eb-9045-c995f2641aa2"], "durationId": "72d81758-225f-4cb7-859f-d343f7cb141c"}}, {"head": {"id": "b217a0f9-eb34-44c1-8c9f-fca5f9a0a40a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707236221300}, "additional": {"logType": "debug", "children": [], "durationId": "72d81758-225f-4cb7-859f-d343f7cb141c"}}, {"head": {"id": "dbbeda3e-af13-402a-aa55-1453a7f01da6", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:78:24\n Conversion of type 'void & Promise<ValueType>' to type 'number | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:96:23\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:114:23\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:132:26\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:151:24\n Conversion of type 'void & Promise<ValueType>' to type 'number | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:152:23\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707243083900}, "additional": {"logType": "debug", "children": [], "durationId": "72d81758-225f-4cb7-859f-d343f7cb141c"}}, {"head": {"id": "d3597688-bc30-42d4-aa1d-d987467c8329", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707252649400, "endTime": 10707252710600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f0f5ec5f-4a0b-413b-be67-cb195482cfd1", "logId": "6036818a-9f7d-4000-b53f-e6e60ea3d36f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6036818a-9f7d-4000-b53f-e6e60ea3d36f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707252649400, "endTime": 10707252710600}, "additional": {"logType": "info", "children": [], "durationId": "d3597688-bc30-42d4-aa1d-d987467c8329"}}, {"head": {"id": "9887fc1b-c679-444b-b905-47e5056a817d", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10694985148100, "endTime": 10707252869700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 37}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "4673ed2c-9d58-458d-9366-ddeea50b55fa", "name": "BUILD FAILED in 12 s 268 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707252900100}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "a804d76a-dd26-45dd-ae6d-8a9e8688e66f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253229500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba76d37f-bc02-490e-8990-9e2bad0af7d7", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253295900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f59323-ed9d-4623-a481-a9a4ea768336", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f340016-45a4-4d68-bc5c-386483b971c8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253392300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26242b1d-1f38-41c0-b922-49d96f8b646f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489374fe-3b22-410d-b1e0-48ef75b981c7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253479200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c804bd27-8b67-4687-bcc5-c21b903f2e97", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253521100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0272d6a3-75b0-4d62-abb7-3290dc3d04d6", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454e5d64-9567-4c80-8aee-5c6ca0177c47", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f5ea179-4b48-4d90-a9ca-3677c759f50d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707253647600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6d3c6be-4056-440c-8049-b2dbc67b67a0", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707256688600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd26912-8bfd-447a-bec0-be3854881546", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707257656600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677ce04a-9531-4fd5-9dfc-4c38e33624a0", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707257972700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ff9e3a-0826-4fde-a255-2655a26dec46", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707273075900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc6946f7-9a8d-45c1-a699-d968f373c3c5", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707274825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ffdf09e-9694-429f-9245-8c8fad687c76", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707275179900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bdc5db7-c348-4a17-9d4b-d574b73f0d3c", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707275487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c4569b9-bdd6-4d0c-8954-94923531e7aa", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707276286900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd152732-bd3d-4b7b-b93f-10096c5e246a", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707279921900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cc34b5-8d63-48d8-aa9f-d66c753087fb", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707280226100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65640106-0a0f-49b1-949c-73301e6b55d6", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707280502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "108db00d-99ca-4673-acf1-d26d8459d2c4", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707280816700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c901f34d-7384-4927-85f5-8aef2f9a741d", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:28 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707281147800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}