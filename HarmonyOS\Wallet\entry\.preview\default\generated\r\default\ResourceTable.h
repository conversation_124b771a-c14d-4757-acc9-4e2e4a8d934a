/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RESOURCE_TABLE_H
#define RESOURCE_TABLE_H

#include<stdint.h>

namespace OHOS {
const int32_t STRING_ENTRYABILITY_DESC = 0x01000006;
const int32_t STRING_ENTRYABILITY_LABEL = 0x01000007;
const int32_t STRING_APP_NAME = 0x01000001;
const int32_t STRING_INTERNET_PERMISSION_REASON = 0x01000036;
const int32_t STRING_MODULE_DESC = 0x01000008;
const int32_t STRING_PAGE_SHOW = 0x01000037;
const int32_t COLOR_START_WINDOW_BACKGROUND = 0x0100000b;
const int32_t FLOAT_PAGE_TEXT_FONT_SIZE = 0x01000009;
const int32_t MEDIA_ABC = 0x01000039;
const int32_t MEDIA_ABOUT = 0x01000032;
const int32_t MEDIA_ADD = 0x01000024;
const int32_t MEDIA_ARROW = 0x01000035;
const int32_t MEDIA_BACK = 0x0100002c;
const int32_t MEDIA_BACKGROUND = 0x01000003;
const int32_t MEDIA_BANK = 0x01000028;
const int32_t MEDIA_BANNER1 = 0x0100001a;
const int32_t MEDIA_BANNER2 = 0x01000019;
const int32_t MEDIA_BANNER3 = 0x0100001b;
const int32_t MEDIA_BCM = 0x0100003a;
const int32_t MEDIA_BOC = 0x01000025;
const int32_t MEDIA_CARD = 0x0100000e;
const int32_t MEDIA_CARD_SELECTED = 0x01000015;
const int32_t MEDIA_CCB = 0x01000026;
const int32_t MEDIA_CEB = 0x0100003c;
const int32_t MEDIA_CIB = 0x01000040;
const int32_t MEDIA_CITIC = 0x0100003b;
const int32_t MEDIA_CMB = 0x01000038;
const int32_t MEDIA_CMBC = 0x0100003e;
const int32_t MEDIA_EDIT = 0x01000033;
const int32_t MEDIA_EMPTY = 0x01000041;
const int32_t MEDIA_EYE_CLOSE = 0x0100002f;
const int32_t MEDIA_EYE_OPEN = 0x0100002e;
const int32_t MEDIA_FOREGROUND = 0x01000002;
const int32_t MEDIA_HOME = 0x01000016;
const int32_t MEDIA_HOME_SELECTED = 0x01000012;
const int32_t MEDIA_HXB = 0x0100003f;
const int32_t MEDIA_IC_BILL = 0x0100002b;
const int32_t MEDIA_IC_CARD = 0x01000029;
const int32_t MEDIA_IC_TRANSFER = 0x0100002a;
const int32_t MEDIA_ICBC = 0x01000027;
const int32_t MEDIA_ICON_AVATAR = 0x0100002d;
const int32_t MEDIA_LAYERED_IMAGE = 0x01000000;
const int32_t MEDIA_LOGO = 0x0100000a;
const int32_t MEDIA_PASSWORD = 0x01000031;
const int32_t MEDIA_PHONE = 0x0100001c;
const int32_t MEDIA_PROFILE = 0x0100000d;
const int32_t MEDIA_PROFILE_SELECTED = 0x0100000f;
const int32_t MEDIA_REFRESH = 0x01000030;
const int32_t MEDIA_SCAN = 0x0100001d;
const int32_t MEDIA_SEARCH = 0x01000018;
const int32_t MEDIA_SERVICE = 0x01000017;
const int32_t MEDIA_SERVICE2 = 0x01000020;
const int32_t MEDIA_SERVICE4 = 0x01000021;
const int32_t MEDIA_SERVICE5 = 0x01000022;
const int32_t MEDIA_SERVICE6 = 0x0100001f;
const int32_t MEDIA_SETTING = 0x01000034;
const int32_t MEDIA_SPDB = 0x0100003d;
const int32_t MEDIA_STARTICON = 0x0100000c;
const int32_t MEDIA_TRANSACTION = 0x01000011;
const int32_t MEDIA_TRANSACTION_SELECTED = 0x01000010;
const int32_t MEDIA_TRANSFER = 0x0100001e;
const int32_t MEDIA_WALLET = 0x01000013;
const int32_t MEDIA_WALLET_SELECTED = 0x01000014;
const int32_t MEDIA_WITHDRAW = 0x01000023;
const int32_t PROFILE_BACKUP_CONFIG = 0x01000005;
const int32_t PROFILE_MAIN_PAGES = 0x01000004;
}
#endif