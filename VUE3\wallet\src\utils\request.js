import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: 'http://localhost:8091',
  timeout: 30000, // 增加超时时间到30秒
  withCredentials: false, // 明确设置不发送凭证
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    console.log('🚀 发送请求:', {
      method: config.method?.toUpperCase(),
      url: config.baseURL + config.url,
      params: config.params,
      data: config.data,
      headers: config.headers
    })

    // 如果是POST请求且使用params，确保Content-Type正确
    if (config.method === 'post' && config.params && !config.data) {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      console.log('📝 设置Content-Type为application/x-www-form-urlencoded')
    }

    // 确保URL格式正确
    if (config.url && !config.url.startsWith('/')) {
      config.url = '/' + config.url
    }

    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('❌ 请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    console.log('✅ 收到响应:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    })
    return response
  },
  error => {
    // 对响应错误做点什么
    console.error('❌ 响应错误详情:', {
      message: error.message,
      code: error.code,
      config: error.config,
      request: error.request,
      response: error.response
    })

    if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      const { status, data, statusText } = error.response
      console.error(`🔴 服务器错误 ${status}: ${statusText}`, data)

      switch (status) {
        case 400:
          ElMessage.error(data?.msg || '请求参数错误')
          break
        case 401:
          ElMessage.error('未授权，请重新登录')
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.msg || `请求失败 (${status})`)
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('🔴 网络错误 - 无响应:', {
        readyState: error.request.readyState,
        status: error.request.status,
        statusText: error.request.statusText,
        responseURL: error.request.responseURL
      })

      if (error.code === 'ERR_NETWORK') {
        ElMessage.error('网络连接失败！请检查：\n1. 后端服务是否启动\n2. 端口8091是否正确\n3. 防火墙设置')
      } else if (error.code === 'ECONNABORTED') {
        ElMessage.error('请求超时，请稍后重试')
      } else {
        ElMessage.error('网络连接失败，请检查网络设置')
      }
    } else {
      // 发送请求时出了点问题
      console.error('🔴 请求配置错误:', error.message)
      ElMessage.error('请求配置错误: ' + error.message)
    }

    return Promise.reject(error)
  }
)

export default request
