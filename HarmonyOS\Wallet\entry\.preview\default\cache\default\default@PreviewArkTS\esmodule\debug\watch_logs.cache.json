[{"time": "2025-06-26T04:06:20.573Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/WalletPage.ets:64:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/WalletPage.ets"}, {"time": "2025-06-26T04:06:20.828Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/HomePage.ets:59:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/HomePage.ets"}, {"time": "2025-06-26T04:06:20.945Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/TransactionPage.ets:75:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/TransactionPage.ets"}, {"time": "2025-06-26T04:06:21.036Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankCardPage.ets:62:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankCardPage.ets"}, {"time": "2025-06-26T04:06:21.353Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankAccountTransferPage.ets:58:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankAccountTransferPage.ets"}, {"time": "2025-06-26T04:06:21.468Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/ChangePasswordPage.ets:13:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/ChangePasswordPage.ets"}, {"time": "2025-06-26T04:12:26.586Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/SettingsPage.ets:49:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/SettingsPage.ets"}]