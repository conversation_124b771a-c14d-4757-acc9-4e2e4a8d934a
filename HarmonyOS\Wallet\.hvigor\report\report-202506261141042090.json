{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "7c151de2-11fa-4bd6-b376-24e2e3257665", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707302572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70972e4-0d9b-4cd4-b6af-94c00ee9246d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707311601900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae48293-4bda-4b54-99e7-16cd884dac68", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10707311923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5475afae-7fe5-4158-8a6e-b37b6bcbafe8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924072021900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924085229700, "endTime": 10924514670300}, "additional": {"children": ["30ad4079-5772-46a6-bf24-6ef8c37b7a69", "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "eb2cecfe-16ec-43c7-9512-ae7b3ca09b6f", "cfa6f393-d143-423b-97c9-3d2ba61bc29c", "004033b7-7265-4329-b7f7-cf704fd2a23a", "9d623965-36d0-4750-b943-e4652ee0ed89", "0b159d56-3565-4eda-85a3-baa541ffd1fe"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30ad4079-5772-46a6-bf24-6ef8c37b7a69", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924085233900, "endTime": 10924139744100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "889688bf-7879-4b36-aae0-1b3e024e8c9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924139783300, "endTime": 10924511967900}, "additional": {"children": ["129fd498-9c38-491a-bced-51be9d3ceb67", "dad67c4b-1b02-4410-81e2-e6f8dc027c9a", "b6d400a9-a73d-49bc-8629-86a10992cec7", "8eaabd43-6447-47d0-a9c8-0f68fe19338e", "d476848b-7fd6-4cd5-8f5f-624cd79fd393", "865f0eb8-dc84-447f-832a-815d40badbc7", "d7f4f7d1-b828-48f6-adf5-3e9a4dc127ca", "87526e19-2666-41cf-b5e9-bbd776c32dad", "34f494aa-828d-4392-ad36-34a556527d4d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "7b1940ff-6265-411e-ac1b-c61b5700f557"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb2cecfe-16ec-43c7-9512-ae7b3ca09b6f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924512033800, "endTime": 10924514629600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "154677c1-0930-4a86-b18a-2425208adad0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfa6f393-d143-423b-97c9-3d2ba61bc29c", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924514639200, "endTime": 10924514659000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "197ce10c-51e7-4445-8591-b8d59ad09342"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "004033b7-7265-4329-b7f7-cf704fd2a23a", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924091667500, "endTime": 10924091815700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "8d1862cb-d3e8-484c-a65a-5939dd94c902"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d1862cb-d3e8-484c-a65a-5939dd94c902", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924091667500, "endTime": 10924091815700}, "additional": {"logType": "info", "children": [], "durationId": "004033b7-7265-4329-b7f7-cf704fd2a23a", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "9d623965-36d0-4750-b943-e4652ee0ed89", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924121958200, "endTime": 10924122119400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "270d6eb8-79d2-4b4f-8193-8ce9b29649d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "270d6eb8-79d2-4b4f-8193-8ce9b29649d6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924121958200, "endTime": 10924122119400}, "additional": {"logType": "info", "children": [], "durationId": "9d623965-36d0-4750-b943-e4652ee0ed89", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "fb83f690-2077-4be7-b0ec-8c5663103baa", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924122576700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7426a09-eda7-406d-9c2a-7117b7506295", "name": "Cache service initialization finished in 17 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924139423700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "889688bf-7879-4b36-aae0-1b3e024e8c9c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924085233900, "endTime": 10924139744100}, "additional": {"logType": "info", "children": [], "durationId": "30ad4079-5772-46a6-bf24-6ef8c37b7a69", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "129fd498-9c38-491a-bced-51be9d3ceb67", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924153504900, "endTime": 10924153539700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "a8c230b2-bd21-47f0-83af-bab8de4e167f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dad67c4b-1b02-4410-81e2-e6f8dc027c9a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924153594200, "endTime": 10924175685300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "38618ec2-9a97-4eea-a78c-c3ad405c35e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6d400a9-a73d-49bc-8629-86a10992cec7", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924175726100, "endTime": 10924387139400}, "additional": {"children": ["cb9556be-9555-40d6-8bfe-7c09e8537942", "28b345ea-cdb7-4fe4-8423-baa488321d85", "d0f434d2-b608-4ba2-95c8-1dd1459e89cf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "c199941f-e5fd-49e2-888a-67d9b5ffbc8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8eaabd43-6447-47d0-a9c8-0f68fe19338e", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924387172900, "endTime": 10924443744700}, "additional": {"children": ["e7a48b1d-378d-4ad1-85bf-884daa73445f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "cad20a81-4bef-49ab-9bd1-466ffe929971"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d476848b-7fd6-4cd5-8f5f-624cd79fd393", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924443754600, "endTime": 10924471736700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "6ecaa332-a072-43eb-92ca-c70ecc54b010"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "865f0eb8-dc84-447f-832a-815d40badbc7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924473687500, "endTime": 10924488684600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "8015c8ce-aff0-47cf-9fed-1666297ba7d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7f4f7d1-b828-48f6-adf5-3e9a4dc127ca", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924488732100, "endTime": 10924511484400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "73f769f2-1605-478a-85a2-60d7faa18743"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87526e19-2666-41cf-b5e9-bbd776c32dad", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924511578800, "endTime": 10924511921000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "01ee9c5b-205c-42f5-b56e-8ca82693471e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8c230b2-bd21-47f0-83af-bab8de4e167f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924153504900, "endTime": 10924153539700}, "additional": {"logType": "info", "children": [], "durationId": "129fd498-9c38-491a-bced-51be9d3ceb67", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "38618ec2-9a97-4eea-a78c-c3ad405c35e2", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924153594200, "endTime": 10924175685300}, "additional": {"logType": "info", "children": [], "durationId": "dad67c4b-1b02-4410-81e2-e6f8dc027c9a", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "cb9556be-9555-40d6-8bfe-7c09e8537942", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924177044700, "endTime": 10924177084300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6d400a9-a73d-49bc-8629-86a10992cec7", "logId": "5e29dc19-2009-4eae-83c3-749de7b264a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e29dc19-2009-4eae-83c3-749de7b264a0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924177044700, "endTime": 10924177084300}, "additional": {"logType": "info", "children": [], "durationId": "cb9556be-9555-40d6-8bfe-7c09e8537942", "parent": "c199941f-e5fd-49e2-888a-67d9b5ffbc8d"}}, {"head": {"id": "28b345ea-cdb7-4fe4-8423-baa488321d85", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924184157700, "endTime": 10924385255600}, "additional": {"children": ["2e2c08cc-42b7-42cc-abac-f63c89534844", "3933803b-d5a1-4da3-8fba-a43fa2b1ee94"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6d400a9-a73d-49bc-8629-86a10992cec7", "logId": "e897e21f-58eb-409b-846d-20bac2ab5cc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e2c08cc-42b7-42cc-abac-f63c89534844", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924184171200, "endTime": 10924205265000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28b345ea-cdb7-4fe4-8423-baa488321d85", "logId": "b50e8860-0184-48a6-bfec-eba01e3b3119"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3933803b-d5a1-4da3-8fba-a43fa2b1ee94", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924205342000, "endTime": 10924385227700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28b345ea-cdb7-4fe4-8423-baa488321d85", "logId": "df0a0584-7d55-470f-9daa-1270dc64b3bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c114258-5500-4fc6-8134-9691cc1a46fc", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924184236000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5afeb6-4743-41a8-8bce-562a551e3b63", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924204862400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50e8860-0184-48a6-bfec-eba01e3b3119", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924184171200, "endTime": 10924205265000}, "additional": {"logType": "info", "children": [], "durationId": "2e2c08cc-42b7-42cc-abac-f63c89534844", "parent": "e897e21f-58eb-409b-846d-20bac2ab5cc6"}}, {"head": {"id": "c94ebc53-77ea-46d8-a1fe-9e4555298332", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924205386500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e72416b-9f39-4079-9848-6bd666c9d4e2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924224355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef29210d-a270-420b-bf61-b4c2500c45bc", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924224718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdad0132-ea6a-4c8f-8000-05a22f896c04", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924225031900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5cf1a0-5525-487f-a194-edf468faff38", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924225347400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763b97c4-b0ca-46fb-b0e8-1cb77d4db733", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924228267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30abba9-9f10-47fb-a3a0-7a2ade1aae44", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924241877300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcef15ee-8573-4d1c-8f4f-f43cc6da6613", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924260213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38424af-1651-4248-8032-78ad0b0ad48f", "name": "Sdk init in 94 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924338216800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a3ffa2-69aa-4e41-9b21-2ae20a74c94f", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924338700000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 41}, "markType": "other"}}, {"head": {"id": "ed723040-6df5-4ff3-a871-b73aafe7bd42", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924338743600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 41}, "markType": "other"}}, {"head": {"id": "367ccf34-1fff-455e-aa25-84fe3a4a4653", "name": "Project task initialization takes 43 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924384121300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f771b6-e613-4484-992c-343b2fbb2386", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924384704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317e0b4d-ceec-4695-8ef3-3f25c97e0976", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924384925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90e91b2e-59d4-45d3-b349-dc4fbecaee7c", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924385091600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0a0584-7d55-470f-9daa-1270dc64b3bc", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924205342000, "endTime": 10924385227700}, "additional": {"logType": "info", "children": [], "durationId": "3933803b-d5a1-4da3-8fba-a43fa2b1ee94", "parent": "e897e21f-58eb-409b-846d-20bac2ab5cc6"}}, {"head": {"id": "e897e21f-58eb-409b-846d-20bac2ab5cc6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924184157700, "endTime": 10924385255600}, "additional": {"logType": "info", "children": ["b50e8860-0184-48a6-bfec-eba01e3b3119", "df0a0584-7d55-470f-9daa-1270dc64b3bc"], "durationId": "28b345ea-cdb7-4fe4-8423-baa488321d85", "parent": "c199941f-e5fd-49e2-888a-67d9b5ffbc8d"}}, {"head": {"id": "d0f434d2-b608-4ba2-95c8-1dd1459e89cf", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924387050800, "endTime": 10924387085200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6d400a9-a73d-49bc-8629-86a10992cec7", "logId": "61fae4bf-3d97-487a-af23-4d0314295423"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61fae4bf-3d97-487a-af23-4d0314295423", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924387050800, "endTime": 10924387085200}, "additional": {"logType": "info", "children": [], "durationId": "d0f434d2-b608-4ba2-95c8-1dd1459e89cf", "parent": "c199941f-e5fd-49e2-888a-67d9b5ffbc8d"}}, {"head": {"id": "c199941f-e5fd-49e2-888a-67d9b5ffbc8d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924175726100, "endTime": 10924387139400}, "additional": {"logType": "info", "children": ["5e29dc19-2009-4eae-83c3-749de7b264a0", "e897e21f-58eb-409b-846d-20bac2ab5cc6", "61fae4bf-3d97-487a-af23-4d0314295423"], "durationId": "b6d400a9-a73d-49bc-8629-86a10992cec7", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "e7a48b1d-378d-4ad1-85bf-884daa73445f", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924390456300, "endTime": 10924443727100}, "additional": {"children": ["b2c3b4af-f326-4b72-985d-029c9a8c1160", "3e0973fd-dff4-4746-a74b-13e70f465244", "e56813f4-4be9-4591-a702-de77027f1f7d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8eaabd43-6447-47d0-a9c8-0f68fe19338e", "logId": "3dc3b200-a12b-42e0-a528-5b89e3ac5e8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2c3b4af-f326-4b72-985d-029c9a8c1160", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924398463900, "endTime": 10924398499400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a48b1d-378d-4ad1-85bf-884daa73445f", "logId": "f794d2ca-83c6-4b0e-b527-6bf35bca4a51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f794d2ca-83c6-4b0e-b527-6bf35bca4a51", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924398463900, "endTime": 10924398499400}, "additional": {"logType": "info", "children": [], "durationId": "b2c3b4af-f326-4b72-985d-029c9a8c1160", "parent": "3dc3b200-a12b-42e0-a528-5b89e3ac5e8d"}}, {"head": {"id": "3e0973fd-dff4-4746-a74b-13e70f465244", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924403096200, "endTime": 10924441527100}, "additional": {"children": ["5dfd6a9e-dca6-4176-a9ef-9283da77cec9", "7d9cc23a-4086-49b5-a70a-6c8acbf33139"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a48b1d-378d-4ad1-85bf-884daa73445f", "logId": "9a3aa25c-965e-44e4-980e-97ca59f3890c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dfd6a9e-dca6-4176-a9ef-9283da77cec9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924403101100, "endTime": 10924417200500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e0973fd-dff4-4746-a74b-13e70f465244", "logId": "210f8dad-f5c0-4b19-bca9-54e683b70e00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d9cc23a-4086-49b5-a70a-6c8acbf33139", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924417274300, "endTime": 10924441496700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e0973fd-dff4-4746-a74b-13e70f465244", "logId": "4a3bf76b-8803-4983-ae55-7c35758f62f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "994d291f-9937-4af9-b11c-3f7b4c640b87", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924403116500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5260481e-991e-4a45-b188-c110866a68fc", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924415743500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "210f8dad-f5c0-4b19-bca9-54e683b70e00", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924403101100, "endTime": 10924417200500}, "additional": {"logType": "info", "children": [], "durationId": "5dfd6a9e-dca6-4176-a9ef-9283da77cec9", "parent": "9a3aa25c-965e-44e4-980e-97ca59f3890c"}}, {"head": {"id": "4843fbfc-1c29-4f81-803b-be1242672237", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924417316500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81725ecb-6a56-4a0b-b315-40aa5596a755", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924433074600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dbd79ac-bb7f-4210-80b2-8c9898e826e8", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924433285600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ecff9d-5a2a-474c-a612-7c3f38d6010c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924433803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9798794c-cab6-4f4e-8643-ab1bb2fda971", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924434209300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de24034-7708-4cc2-8cab-cb8aa21a090a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924434372900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b32fdd8-357f-4857-abcb-6e916772f359", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924434487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "444e2589-4440-4bfb-a323-1f711e398f4c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924434613800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5a34fd-082d-4580-a0f8-2bf2d781aa54", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924440841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f6dd50-9ab1-4232-b1c6-7bcf92de81af", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924441133200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b732cd64-32e7-4427-8a31-40a07da4a8f3", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924441285300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a9200a9-f6ae-4b25-a034-60ce19763688", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924441392000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3bf76b-8803-4983-ae55-7c35758f62f2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924417274300, "endTime": 10924441496700}, "additional": {"logType": "info", "children": [], "durationId": "7d9cc23a-4086-49b5-a70a-6c8acbf33139", "parent": "9a3aa25c-965e-44e4-980e-97ca59f3890c"}}, {"head": {"id": "9a3aa25c-965e-44e4-980e-97ca59f3890c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924403096200, "endTime": 10924441527100}, "additional": {"logType": "info", "children": ["210f8dad-f5c0-4b19-bca9-54e683b70e00", "4a3bf76b-8803-4983-ae55-7c35758f62f2"], "durationId": "3e0973fd-dff4-4746-a74b-13e70f465244", "parent": "3dc3b200-a12b-42e0-a528-5b89e3ac5e8d"}}, {"head": {"id": "e56813f4-4be9-4591-a702-de77027f1f7d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924443688000, "endTime": 10924443703500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a48b1d-378d-4ad1-85bf-884daa73445f", "logId": "7651f98b-0b8b-4bf4-8385-6b4c1f6ee36c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7651f98b-0b8b-4bf4-8385-6b4c1f6ee36c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924443688000, "endTime": 10924443703500}, "additional": {"logType": "info", "children": [], "durationId": "e56813f4-4be9-4591-a702-de77027f1f7d", "parent": "3dc3b200-a12b-42e0-a528-5b89e3ac5e8d"}}, {"head": {"id": "3dc3b200-a12b-42e0-a528-5b89e3ac5e8d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924390456300, "endTime": 10924443727100}, "additional": {"logType": "info", "children": ["f794d2ca-83c6-4b0e-b527-6bf35bca4a51", "9a3aa25c-965e-44e4-980e-97ca59f3890c", "7651f98b-0b8b-4bf4-8385-6b4c1f6ee36c"], "durationId": "e7a48b1d-378d-4ad1-85bf-884daa73445f", "parent": "cad20a81-4bef-49ab-9bd1-466ffe929971"}}, {"head": {"id": "cad20a81-4bef-49ab-9bd1-466ffe929971", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924387172900, "endTime": 10924443744700}, "additional": {"logType": "info", "children": ["3dc3b200-a12b-42e0-a528-5b89e3ac5e8d"], "durationId": "8eaabd43-6447-47d0-a9c8-0f68fe19338e", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "6dc7836f-c5fa-4171-a52b-e513b497b91d", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924470598600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcc098c-5cd9-492d-97c7-6cdb607734ba", "name": "hvigorfile, resolve hvigorfile dependencies in 28 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924471534800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ecaa332-a072-43eb-92ca-c70ecc54b010", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924443754600, "endTime": 10924471736700}, "additional": {"logType": "info", "children": [], "durationId": "d476848b-7fd6-4cd5-8f5f-624cd79fd393", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "34f494aa-828d-4392-ad36-34a556527d4d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924473166300, "endTime": 10924473655700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "logId": "97baf2d4-a665-4774-a2c6-66db37a6510f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "884a568f-a660-4dac-a258-141809539667", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924473240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97baf2d4-a665-4774-a2c6-66db37a6510f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924473166300, "endTime": 10924473655700}, "additional": {"logType": "info", "children": [], "durationId": "34f494aa-828d-4392-ad36-34a556527d4d", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "b33645d3-2916-4373-a924-1e2ff7a3de4a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924475853300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97625824-4d5e-4963-9ad0-311a6bcd1dcb", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924487248700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8015c8ce-aff0-47cf-9fed-1666297ba7d0", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924473687500, "endTime": 10924488684600}, "additional": {"logType": "info", "children": [], "durationId": "865f0eb8-dc84-447f-832a-815d40badbc7", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "61474dc2-161e-4f18-9396-b2c43ab65d36", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924488765700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "456feabe-28cb-4994-a7ea-0f7d4de7c44e", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924498300200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eab682c8-426b-4aad-b8ef-a448c74a6a6a", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924498581100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4676c60-65fd-43cd-9da0-935aca18d882", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924499015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0fd4a66-8439-4b6d-8974-2d6d676e9566", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924506367600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ddb4ef7-ef92-41bb-a08c-59f9fea79940", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924506570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f769f2-1605-478a-85a2-60d7faa18743", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924488732100, "endTime": 10924511484400}, "additional": {"logType": "info", "children": [], "durationId": "d7f4f7d1-b828-48f6-adf5-3e9a4dc127ca", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "04382796-1a40-47b9-9e2f-4bf4908c92f2", "name": "Configuration phase cost:359 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924511679500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ee9c5b-205c-42f5-b56e-8ca82693471e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924511578800, "endTime": 10924511921000}, "additional": {"logType": "info", "children": [], "durationId": "87526e19-2666-41cf-b5e9-bbd776c32dad", "parent": "7b1940ff-6265-411e-ac1b-c61b5700f557"}}, {"head": {"id": "7b1940ff-6265-411e-ac1b-c61b5700f557", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924139783300, "endTime": 10924511967900}, "additional": {"logType": "info", "children": ["a8c230b2-bd21-47f0-83af-bab8de4e167f", "38618ec2-9a97-4eea-a78c-c3ad405c35e2", "c199941f-e5fd-49e2-888a-67d9b5ffbc8d", "cad20a81-4bef-49ab-9bd1-466ffe929971", "6ecaa332-a072-43eb-92ca-c70ecc54b010", "8015c8ce-aff0-47cf-9fed-1666297ba7d0", "73f769f2-1605-478a-85a2-60d7faa18743", "01ee9c5b-205c-42f5-b56e-8ca82693471e", "97baf2d4-a665-4774-a2c6-66db37a6510f"], "durationId": "0b8c57a0-4abc-48b6-b3af-60e6bffa4966", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "0b159d56-3565-4eda-85a3-baa541ffd1fe", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924514583800, "endTime": 10924514607700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46f22cd2-47b9-4284-9a7d-25010b2d5101", "logId": "de3b0340-6225-4ba3-9fad-a8ce2a1cf36a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de3b0340-6225-4ba3-9fad-a8ce2a1cf36a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924514583800, "endTime": 10924514607700}, "additional": {"logType": "info", "children": [], "durationId": "0b159d56-3565-4eda-85a3-baa541ffd1fe", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "154677c1-0930-4a86-b18a-2425208adad0", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924512033800, "endTime": 10924514629600}, "additional": {"logType": "info", "children": [], "durationId": "eb2cecfe-16ec-43c7-9512-ae7b3ca09b6f", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "197ce10c-51e7-4445-8591-b8d59ad09342", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924514639200, "endTime": 10924514659000}, "additional": {"logType": "info", "children": [], "durationId": "cfa6f393-d143-423b-97c9-3d2ba61bc29c", "parent": "a4308452-3679-4be4-9a0a-3cb7c7fa874a"}}, {"head": {"id": "a4308452-3679-4be4-9a0a-3cb7c7fa874a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924085229700, "endTime": 10924514670300}, "additional": {"logType": "info", "children": ["889688bf-7879-4b36-aae0-1b3e024e8c9c", "7b1940ff-6265-411e-ac1b-c61b5700f557", "154677c1-0930-4a86-b18a-2425208adad0", "197ce10c-51e7-4445-8591-b8d59ad09342", "8d1862cb-d3e8-484c-a65a-5939dd94c902", "270d6eb8-79d2-4b4f-8193-8ce9b29649d6", "de3b0340-6225-4ba3-9fad-a8ce2a1cf36a"], "durationId": "46f22cd2-47b9-4284-9a7d-25010b2d5101"}}, {"head": {"id": "b2ab00e6-da02-43d5-b4c1-e95d14ae9fe2", "name": "Configuration task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924514994200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b6c5be-10c0-46a5-8b78-9767fbd9ff40", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924524070000, "endTime": 10924537199400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "10f00bce-1343-44f4-b5a5-b84c760a34e0", "logId": "8e6edfa2-0c9b-4c06-b472-7ab62d314308"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10f00bce-1343-44f4-b5a5-b84c760a34e0", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924517948300}, "additional": {"logType": "detail", "children": [], "durationId": "e1b6c5be-10c0-46a5-8b78-9767fbd9ff40"}}, {"head": {"id": "ae962941-9852-42ed-84fc-24c36ccf4174", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924519045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4693387-4fdc-48fe-b363-fbe0e3f71ad9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924519340800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d4bc16-ba89-49d6-84c7-1ffd978c1a7f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924519510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b96f3cf-a5fa-412f-ab81-c8b62874f9c9", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924524105400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd8e388-d928-4864-b0cd-f3e9ae30ca20", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924536660900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f28f59-07d2-4cf1-8534-54a426f5755d", "name": "entry : default@PreBuild cost memory 0.3032684326171875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924537025400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e6edfa2-0c9b-4c06-b472-7ab62d314308", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924524070000, "endTime": 10924537199400}, "additional": {"logType": "info", "children": [], "durationId": "e1b6c5be-10c0-46a5-8b78-9767fbd9ff40"}}, {"head": {"id": "a7f12cec-b009-4bb6-a10c-0e568ecb4851", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924546317100, "endTime": 10924552470900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9475217e-3460-4d89-9cbd-bc92408b2afa", "logId": "33369ad3-a1c3-4d02-8a09-bef99ae67ebb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9475217e-3460-4d89-9cbd-bc92408b2afa", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924543752000}, "additional": {"logType": "detail", "children": [], "durationId": "a7f12cec-b009-4bb6-a10c-0e568ecb4851"}}, {"head": {"id": "411f7eaf-0c0d-4b87-ae1e-a484369ddd4a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924544748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57b39a7-55db-4d96-9739-d3edc951b2d0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924545014100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab682d5a-a5e0-4d58-b4ce-d0235b5c6694", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924545129200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5439ef3-76f5-47a5-b1a6-bd0029f08375", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924546345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae18e8bd-b57c-4425-a419-e2ac6a6eed76", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924551951300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a5b851b-0f8b-4af4-856a-f5ad60816fb2", "name": "entry : default@MergeProfile cost memory -1.5878753662109375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924552294300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33369ad3-a1c3-4d02-8a09-bef99ae67ebb", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924546317100, "endTime": 10924552470900}, "additional": {"logType": "info", "children": [], "durationId": "a7f12cec-b009-4bb6-a10c-0e568ecb4851"}}, {"head": {"id": "9fc20b0b-eba1-467d-badf-8192cfe952bc", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924558966100, "endTime": 10924563445000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "886fdb9c-bc71-4d46-ae78-999a3e15aee4", "logId": "63970901-f5b0-4f86-b7d8-c1515c42d93c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "886fdb9c-bc71-4d46-ae78-999a3e15aee4", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924556080000}, "additional": {"logType": "detail", "children": [], "durationId": "9fc20b0b-eba1-467d-badf-8192cfe952bc"}}, {"head": {"id": "c9a5d453-038a-4621-b03f-69006f5db313", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924557007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01444800-19e3-4f39-84e6-6a229a61a04a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924557178300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4642e0-ba49-48a6-8357-1e2e3c501f7f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924557300700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51345522-8734-4d4a-9b60-42c5af88c9e2", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924559019000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e358321-970c-4ab1-ae43-b1b5ede0f238", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924560762300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f9df75-841a-495b-b2b8-d2bce8100d04", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924562912800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09cf219-aef1-411c-8e92-a50d5b781c76", "name": "entry : default@CreateBuildProfile cost memory 0.10643768310546875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924563157500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63970901-f5b0-4f86-b7d8-c1515c42d93c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924558966100, "endTime": 10924563445000}, "additional": {"logType": "info", "children": [], "durationId": "9fc20b0b-eba1-467d-badf-8192cfe952bc"}}, {"head": {"id": "8fe1dba8-4a41-45ab-a453-a4b3ee3daa09", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924574595600, "endTime": 10924576366900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0c508236-ceba-4370-be8e-c232448260fe", "logId": "b0325f87-460c-4a38-889b-0acff9ce43be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c508236-ceba-4370-be8e-c232448260fe", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924567088500}, "additional": {"logType": "detail", "children": [], "durationId": "8fe1dba8-4a41-45ab-a453-a4b3ee3daa09"}}, {"head": {"id": "739490f2-4e6c-4f53-8492-9f262880e8c6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924567953300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fac22f2-cfa2-470b-b36c-05fe073ac2e2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924569234800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e930107d-7b54-4afe-b57f-5a69169b3a4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924569498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbdf1d81-70c5-4e77-9c86-bdce40e19ecc", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924574650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "471db73d-79e0-4ad0-8399-3cc703f494ed", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924575118600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd759460-8070-42a3-81c1-db182aa3ac67", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924575499600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d43437-e635-4f7a-b08a-72fd8966d27e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924575664500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59d96c21-63fc-4017-a5d4-78d441d41d8c", "name": "entry : default@PreCheckSyscap cost memory 0.05040740966796875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924575865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678040e9-1b15-43cc-b867-2a2bcda680cd", "name": "runTaskFromQueue task cost before running: 498 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924576224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0325f87-460c-4a38-889b-0acff9ce43be", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924574595600, "endTime": 10924576366900, "totalTime": 1558900}, "additional": {"logType": "info", "children": [], "durationId": "8fe1dba8-4a41-45ab-a453-a4b3ee3daa09"}}, {"head": {"id": "7cfd9ce2-2e89-4910-b18f-08e5cc327912", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924637166800, "endTime": 10924646845700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f0abb559-b10c-41e8-a3d2-05eae74ad091", "logId": "174c9e3b-59a5-453d-8ca7-5e5d0db9baee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0abb559-b10c-41e8-a3d2-05eae74ad091", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924580941600}, "additional": {"logType": "detail", "children": [], "durationId": "7cfd9ce2-2e89-4910-b18f-08e5cc327912"}}, {"head": {"id": "114b4ad8-0d3f-4ce2-89ac-61ba9e552628", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924581772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa97322a-bf78-42cd-9551-a02ecb18f74d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924581951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07786672-79f7-41a4-b9e4-f6514acc7830", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924582064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce05224c-999a-4448-b5fd-4c66ce4fa954", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924637291900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37ab65a-990e-4eb1-b3c3-b39df7311d33", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924642747700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449ac80f-6162-4dad-8f9f-c85409a381f2", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924645817600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f896918f-4954-457b-8ab4-783d09a7cf99", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07268524169921875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924646370400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174c9e3b-59a5-453d-8ca7-5e5d0db9baee", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924637166800, "endTime": 10924646845700}, "additional": {"logType": "info", "children": [], "durationId": "7cfd9ce2-2e89-4910-b18f-08e5cc327912"}}, {"head": {"id": "304c6ddc-0e62-4ac7-9b80-af4194e87b88", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924662352300, "endTime": 10924668040700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6ce874c1-4ce8-461f-bc7d-96cf9ae11d30", "logId": "909b9b0c-a1dc-469a-9dde-9bb8f335503d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ce874c1-4ce8-461f-bc7d-96cf9ae11d30", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924652519900}, "additional": {"logType": "detail", "children": [], "durationId": "304c6ddc-0e62-4ac7-9b80-af4194e87b88"}}, {"head": {"id": "faae16ac-936b-45ed-a32c-c36edc28433d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924655811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c84402-5803-4422-b65f-fccb68d9e183", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924657454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b4fe68-384e-43cf-a446-1710cd679a62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924657859400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb591f8-719c-4575-884a-b608f42f51c7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924662508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6e6639-da54-4dce-94ca-bf23fb80ea87", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924664715600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8de5cf4b-12a3-416a-830e-bf4f19a78fea", "name": "entry : default@ProcessProfile cost memory 0.0580596923828125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924667360100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "909b9b0c-a1dc-469a-9dde-9bb8f335503d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924662352300, "endTime": 10924668040700}, "additional": {"logType": "info", "children": [], "durationId": "304c6ddc-0e62-4ac7-9b80-af4194e87b88"}}, {"head": {"id": "ff19fdca-d8f9-401f-b3fe-73dce7a19fda", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924678189100, "endTime": 10924707778900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dfa00d43-90dc-4ae8-90be-8f4f52e67129", "logId": "c66838b7-39cd-4642-8f0a-1741ce7ad859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfa00d43-90dc-4ae8-90be-8f4f52e67129", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924672764600}, "additional": {"logType": "detail", "children": [], "durationId": "ff19fdca-d8f9-401f-b3fe-73dce7a19fda"}}, {"head": {"id": "80eb66ac-3ae4-47b7-b60b-0c06e520d889", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924673686000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beecdb9a-68c3-404a-be5a-0c21c5ca0a05", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924673984900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c385464a-34d3-40e7-88da-dbab1f3af538", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924674327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc761a2a-dbc7-43d0-8e07-ca13adb5d845", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924678282500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f9717db-6ddf-4d18-8080-ca710f76e812", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924706290100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "725928e1-150b-4881-970c-7c9f6af1769c", "name": "entry : default@ProcessRouterMap cost memory 0.23029327392578125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924707245000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66838b7-39cd-4642-8f0a-1741ce7ad859", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924678189100, "endTime": 10924707778900}, "additional": {"logType": "info", "children": [], "durationId": "ff19fdca-d8f9-401f-b3fe-73dce7a19fda"}}, {"head": {"id": "4f68d7e8-95dd-47ba-abce-f6d411215d9e", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924731189300, "endTime": 10924738772400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bc0ee0fc-a78d-43e6-8954-089f78d0d759", "logId": "9b424106-6e0f-4c5a-8552-4f6c0b46e8e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc0ee0fc-a78d-43e6-8954-089f78d0d759", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924720789500}, "additional": {"logType": "detail", "children": [], "durationId": "4f68d7e8-95dd-47ba-abce-f6d411215d9e"}}, {"head": {"id": "df952731-e954-4703-b894-6da8ec8829ca", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924722024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0420f1f-52e3-431f-aba5-653c22e62c36", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924722447100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8bc4112-347d-4afb-a1bd-76807098f49b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924722650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ebcaff1-09c4-4619-a5a3-52839ca8ff45", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924725164800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0393fd8-13c2-4989-9a50-5469c85570f3", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924733620800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa84517-94f7-4a7f-b75c-05cb3fdfb594", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924734576900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423729b3-27dc-489a-ba7f-c3ef0c45e0da", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924734887300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4381f902-1d67-41e1-9106-ec90d2561c57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924735068000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e14ab468-239d-4dc8-9165-39ccbe0d4c4f", "name": "entry : default@PreviewProcessResource cost memory 0.09305572509765625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924735347700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7858b3b-0c0e-4ea8-9469-c082826cd532", "name": "runTaskFromQueue task cost before running: 660 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924738428100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b424106-6e0f-4c5a-8552-4f6c0b46e8e7", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924731189300, "endTime": 10924738772400, "totalTime": 4359800}, "additional": {"logType": "info", "children": [], "durationId": "4f68d7e8-95dd-47ba-abce-f6d411215d9e"}}, {"head": {"id": "08a09566-1275-4eca-90cf-678090ed5326", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924779984700, "endTime": 10924828418000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "985c4391-2543-4d08-a694-de682d7a9c30", "logId": "076db356-193c-41de-b957-d51482a990d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "985c4391-2543-4d08-a694-de682d7a9c30", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924761789100}, "additional": {"logType": "detail", "children": [], "durationId": "08a09566-1275-4eca-90cf-678090ed5326"}}, {"head": {"id": "b3d12947-491b-4e20-ab01-a3eee946d7d2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924764465600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75a5a8b-3a4a-461b-a349-12bc3b51fb75", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924767324600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "053b8b1e-c1ea-4c89-a476-8cd32976a6b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924768198200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c572697-658f-489e-9410-0cf8fcd2f6b3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924780087600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eecb5d1-0b6a-43a8-84b0-4895803f3f5c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924827762100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f3ad08b-ed42-434e-8340-d62a567930bd", "name": "entry : default@GenerateLoaderJson cost memory -0.7761383056640625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924828218700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "076db356-193c-41de-b957-d51482a990d8", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924779984700, "endTime": 10924828418000}, "additional": {"logType": "info", "children": [], "durationId": "08a09566-1275-4eca-90cf-678090ed5326"}}, {"head": {"id": "63aea689-509e-4973-89f1-3970fa730d0e", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924853681700, "endTime": 10926014522700}, "additional": {"children": ["fddab8ff-9a16-4fa4-8862-dce3f347b46c", "bd1b55e4-b636-4647-a723-527f7abcc9eb", "2bf616f2-ae15-404b-9a12-0fb9d5e073dd", "d3ddd117-973a-40a2-948e-b2d76365957a", "a51f3edf-51f5-4ef7-bf3a-6db717395401"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "1dd3d270-87b4-4a07-baa6-edf578f91a83", "logId": "f48ca75d-2c3a-49a1-949e-f338416f3861"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dd3d270-87b4-4a07-baa6-edf578f91a83", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924843480400}, "additional": {"logType": "detail", "children": [], "durationId": "63aea689-509e-4973-89f1-3970fa730d0e"}}, {"head": {"id": "c7d9e366-58d4-4420-894e-3f955237c963", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924845707900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99ec35b7-69ad-4115-8b66-5f0b884a055c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924846189400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90168539-e9c8-41fa-9f5c-3ca4d65bfacd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924846392900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb3023d-73d1-41f0-bef1-628a85e7ea57", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924847881000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb5f0ab-994c-4b28-89f5-cdd6789e3797", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924853782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad6db41-ed97-44ad-8e50-80bf1a8a11b2", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924918817600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1324fe33-07d7-44d9-ba88-6d9a6e2c02d8", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 64 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924919143600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fddab8ff-9a16-4fa4-8862-dce3f347b46c", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924921023800, "endTime": 10924959574100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63aea689-509e-4973-89f1-3970fa730d0e", "logId": "7a647595-d7ae-4c76-9dfa-0341e7d84734"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a647595-d7ae-4c76-9dfa-0341e7d84734", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924921023800, "endTime": 10924959574100}, "additional": {"logType": "info", "children": [], "durationId": "fddab8ff-9a16-4fa4-8862-dce3f347b46c", "parent": "f48ca75d-2c3a-49a1-949e-f338416f3861"}}, {"head": {"id": "8ec4f2e7-2f4b-4952-b6f8-4fca773018ee", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924960678900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd1b55e4-b636-4647-a723-527f7abcc9eb", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924963363900, "endTime": 10925231638400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63aea689-509e-4973-89f1-3970fa730d0e", "logId": "4d5764c5-752e-4650-b1db-d2fdadc2804b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2696b6d-6c32-4561-83b4-1aa4a1eb9e45", "name": "current process  memoryUsage: {\n  rss: 176160768,\n  heapTotal: 122511360,\n  heapUsed: 110460720,\n  external: 3125566,\n  arrayBuffers: 119463\n} os memoryUsage :12.781425476074219", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924965156700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0ccf13-d81a-40f8-92b5-350076b73681", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925227317000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5764c5-752e-4650-b1db-d2fdadc2804b", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924963363900, "endTime": 10925231638400}, "additional": {"logType": "info", "children": [], "durationId": "bd1b55e4-b636-4647-a723-527f7abcc9eb", "parent": "f48ca75d-2c3a-49a1-949e-f338416f3861"}}, {"head": {"id": "3b703f5a-2d2b-42bd-ad0e-dd0b6f6d22d4", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925231918900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bf616f2-ae15-404b-9a12-0fb9d5e073dd", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925233851300, "endTime": 10925495936200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63aea689-509e-4973-89f1-3970fa730d0e", "logId": "2f0081b6-c3aa-4c91-9637-6b405977c13a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92c9acdb-9927-40d4-9044-d4a073f78b3a", "name": "current process  memoryUsage: {\n  rss: 176189440,\n  heapTotal: 122511360,\n  heapUsed: 110751000,\n  external: 3125692,\n  arrayBuffers: 119604\n} os memoryUsage :12.719249725341797", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925235512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06291e36-c119-4114-94cd-0e443d554888", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925491556800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0081b6-c3aa-4c91-9637-6b405977c13a", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925233851300, "endTime": 10925495936200}, "additional": {"logType": "info", "children": [], "durationId": "2bf616f2-ae15-404b-9a12-0fb9d5e073dd", "parent": "f48ca75d-2c3a-49a1-949e-f338416f3861"}}, {"head": {"id": "fdfa7711-fd4e-4634-bb9c-8ecc8cad9591", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925496233700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ddd117-973a-40a2-948e-b2d76365957a", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925498106000, "endTime": 10925739294200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63aea689-509e-4973-89f1-3970fa730d0e", "logId": "9967f3c9-6ef7-454d-a2e7-3e904ff26539"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63c3bf51-3160-4393-be92-1e966da381e0", "name": "current process  memoryUsage: {\n  rss: 176201728,\n  heapTotal: 122511360,\n  heapUsed: 109151424,\n  external: 3116078,\n  arrayBuffers: 110054\n} os memoryUsage :12.764141082763672", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925499529000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4933599d-edc7-4452-b9cd-c0bb04da43a1", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925735240000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9967f3c9-6ef7-454d-a2e7-3e904ff26539", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925498106000, "endTime": 10925739294200}, "additional": {"logType": "info", "children": [], "durationId": "d3ddd117-973a-40a2-948e-b2d76365957a", "parent": "f48ca75d-2c3a-49a1-949e-f338416f3861"}}, {"head": {"id": "f512b2c1-7743-4cb8-879e-e1b943988662", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925740081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51f3edf-51f5-4ef7-bf3a-6db717395401", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925741963000, "endTime": 10926012413300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63aea689-509e-4973-89f1-3970fa730d0e", "logId": "a86bcd7f-20b4-455a-9675-ba3ce0cf1261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2357df8a-3895-4989-a356-54260d50692b", "name": "current process  memoryUsage: {\n  rss: 176300032,\n  heapTotal: 122511360,\n  heapUsed: 109465888,\n  external: 3116204,\n  arrayBuffers: 111183\n} os memoryUsage :12.7255859375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925743738900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fd775ed-c498-408e-8375-72b7e8132f5b", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926006656200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a86bcd7f-20b4-455a-9675-ba3ce0cf1261", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10925741963000, "endTime": 10926012413300}, "additional": {"logType": "info", "children": [], "durationId": "a51f3edf-51f5-4ef7-bf3a-6db717395401", "parent": "f48ca75d-2c3a-49a1-949e-f338416f3861"}}, {"head": {"id": "ea1b0f71-6083-4485-9081-768e85358dff", "name": "entry : default@PreviewCompileResource cost memory -0.5929183959960938", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926014036700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c59c4f-79c0-4aa5-b001-72f04c3a0c55", "name": "runTaskFromQueue task cost before running: 1 s 936 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926014376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f48ca75d-2c3a-49a1-949e-f338416f3861", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924853681700, "endTime": 10926014522700, "totalTime": 1160598800}, "additional": {"logType": "info", "children": ["7a647595-d7ae-4c76-9dfa-0341e7d84734", "4d5764c5-752e-4650-b1db-d2fdadc2804b", "2f0081b6-c3aa-4c91-9637-6b405977c13a", "9967f3c9-6ef7-454d-a2e7-3e904ff26539", "a86bcd7f-20b4-455a-9675-ba3ce0cf1261"], "durationId": "63aea689-509e-4973-89f1-3970fa730d0e"}}, {"head": {"id": "3133382c-1810-470a-94ad-a2868e1f9038", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926019853800, "endTime": 10926020704900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "275aaf28-0317-4504-84ce-030dfc08fa5b", "logId": "648bbb14-7c5d-4646-993c-ab56221c1261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "275aaf28-0317-4504-84ce-030dfc08fa5b", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926018655200}, "additional": {"logType": "detail", "children": [], "durationId": "3133382c-1810-470a-94ad-a2868e1f9038"}}, {"head": {"id": "1f5fc68e-2eee-4ba8-ab95-d5688f6bf2b4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926019344700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a5af31-e84e-4491-803a-7cfe6b3c521f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926019530300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98732dbb-a8e1-4b55-b745-81f85e717bb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926019643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f17f9367-17c4-48a0-9af1-7f1b0ac89b81", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926019879500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b4a14f-7cb1-406f-872e-56697565c2ff", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926020063000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "186d0acf-571a-4ddc-8ea8-cf6da2c373c4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926020167000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e30fec-a591-4d5d-b5f4-3a2f21740ef1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926020262700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2102e025-30ce-42fb-b09d-52e682e18a57", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926020415700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1519fef-2403-47e2-ac16-1fb15712344d", "name": "runTaskFromQueue task cost before running: 1 s 942 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926020591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "648bbb14-7c5d-4646-993c-ab56221c1261", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926019853800, "endTime": 10926020704900, "totalTime": 690500}, "additional": {"logType": "info", "children": [], "durationId": "3133382c-1810-470a-94ad-a2868e1f9038"}}, {"head": {"id": "bde32620-e834-4a7b-b885-1370fc3f3b27", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926025570500, "endTime": 10926033662900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "b03c463e-e97e-49a4-941b-820819ce3458", "logId": "6f3a6850-5618-49c5-82ed-c291c5f16403"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b03c463e-e97e-49a4-941b-820819ce3458", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926023448600}, "additional": {"logType": "detail", "children": [], "durationId": "bde32620-e834-4a7b-b885-1370fc3f3b27"}}, {"head": {"id": "c6f72cac-f810-41e3-992e-a1e5c6ad8643", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926024226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26a11cdb-8fc1-40c1-9c63-f32e5f38c6d6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926024417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8534a984-7060-49b3-bf6b-00f58db162bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926024543700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d34b2477-5ba8-4b9a-91e8-efce1cf51e6e", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926025592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bae0769a-b6bf-4c49-be2d-ff2e3327fc67", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926027062600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a11e01-7a6f-432c-857b-a5b2fd80c869", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926027212800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aefc35c2-1744-4cb4-abd2-3d86f7a81dfa", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926027312400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005406af-01ea-4d61-b84b-f20f11113c9e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926027370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "782ddd57-ca4a-4bf6-984b-37dd96551c66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926027417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f9d0d6a-c0b6-4d40-b26e-a26ad05eb30b", "name": "entry : default@CopyPreviewProfile cost memory 0.2547760009765625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926033318800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded33bdd-07c8-4c73-8c80-f02bf41a7d87", "name": "runTaskFromQueue task cost before running: 1 s 955 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926033550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f3a6850-5618-49c5-82ed-c291c5f16403", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926025570500, "endTime": 10926033662900, "totalTime": 7941800}, "additional": {"logType": "info", "children": [], "durationId": "bde32620-e834-4a7b-b885-1370fc3f3b27"}}, {"head": {"id": "03d6b33d-5b35-4d57-92b2-21f7348de59e", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926037876200, "endTime": 10926038464100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "a8aa6c72-9387-44a6-a1f0-fb7858d9a9fe", "logId": "5a4ef8ad-a359-4014-9f20-e93a8ff1285b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8aa6c72-9387-44a6-a1f0-fb7858d9a9fe", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926036174100}, "additional": {"logType": "detail", "children": [], "durationId": "03d6b33d-5b35-4d57-92b2-21f7348de59e"}}, {"head": {"id": "7e446d92-59e9-4398-97c9-af2933037d55", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926036857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52dbbf4c-f7dc-4d43-8ae9-c667acb47db3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926036999200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9a1ba93-5e0c-499a-96ab-5a0bc2805c2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926037067400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58c49e3-6191-4d19-8827-be0618c7b8e0", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926037889500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d7f199-0d5b-411f-a8e5-c935654c4cdf", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926038033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a46826d-a394-4b1c-a430-2e00a7e7ce4f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926038100100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d45aac21-f79f-40d2-817f-bf9f3d433acc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926038171400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1b457f-d55b-4524-8936-271512d81e0b", "name": "entry : default@ReplacePreviewerPage cost memory 0.05347442626953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926038289100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a57222-c82d-4e0f-975e-010f2a787924", "name": "runTaskFromQueue task cost before running: 1 s 960 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926038394800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a4ef8ad-a359-4014-9f20-e93a8ff1285b", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926037876200, "endTime": 10926038464100, "totalTime": 482700}, "additional": {"logType": "info", "children": [], "durationId": "03d6b33d-5b35-4d57-92b2-21f7348de59e"}}, {"head": {"id": "669934af-9fbd-4916-9651-8b3a2f70a3e9", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926041461700, "endTime": 10926041941300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "bccd2a7a-52bb-4868-a39f-acc780c7f383", "logId": "e4459763-620c-4b8b-be32-11d65c124820"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bccd2a7a-52bb-4868-a39f-acc780c7f383", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926040560300}, "additional": {"logType": "detail", "children": [], "durationId": "669934af-9fbd-4916-9651-8b3a2f70a3e9"}}, {"head": {"id": "41a546ea-8f00-4aff-a747-b0981ba06db8", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926041481300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da61e40-7837-4565-9bc3-3eea2087520c", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926041679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c86053-839d-4440-a823-b7bc48ed5d0e", "name": "runTaskFromQueue task cost before running: 1 s 963 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926041821700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4459763-620c-4b8b-be32-11d65c124820", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926041461700, "endTime": 10926041941300, "totalTime": 326000}, "additional": {"logType": "info", "children": [], "durationId": "669934af-9fbd-4916-9651-8b3a2f70a3e9"}}, {"head": {"id": "b95c0435-6c09-4b7f-bb03-64ce5eeaab31", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926045689800, "endTime": 10926050013300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "60c0b4ce-272b-43cc-a5a8-c70fefae5370", "logId": "6a186031-ef46-46ac-9bfb-d43f084b0f08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60c0b4ce-272b-43cc-a5a8-c70fefae5370", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926044110700}, "additional": {"logType": "detail", "children": [], "durationId": "b95c0435-6c09-4b7f-bb03-64ce5eeaab31"}}, {"head": {"id": "9c749c76-8e4a-4ab4-a5a2-47a66acaef69", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926044703300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b30cd5-397f-4338-8925-e1431a1ebbef", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926044825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5cc0f4f-9b4b-49e5-83b9-b755d8ca952f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926044895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23446b9a-5a1e-441f-a02b-08a9c73bf3ba", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926045701000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8308f1-e554-4350-86fd-2f1c8d9a8bbe", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926047795200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c42b063a-91aa-42e3-844c-0c3141242dd8", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926047908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90936179-69ba-49bf-bc24-cb173b077a7a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926047994800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b8f529-98e6-4822-a664-cc302e62e0d4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926048050400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342c8a35-2d6c-4191-b20d-9630744aef34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926048097400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4fd3b7-df92-4130-82a4-00e064ec365b", "name": "entry : default@PreviewUpdateAssets cost memory 0.149993896484375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926049632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9dcb51d-3a13-4235-ae14-a49983a32cc8", "name": "runTaskFromQueue task cost before running: 1 s 971 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926049872100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a186031-ef46-46ac-9bfb-d43f084b0f08", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926045689800, "endTime": 10926050013300, "totalTime": 4117700}, "additional": {"logType": "info", "children": [], "durationId": "b95c0435-6c09-4b7f-bb03-64ce5eeaab31"}}, {"head": {"id": "ff1c1560-a54c-4af7-972c-9141c24ca850", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926064702800, "endTime": 10938608779400}, "additional": {"children": ["fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ae4ffd4d-4458-46bf-ae11-e3cc4ee17ee3", "logId": "ab2510a2-903b-4141-b4f0-64a73747b7b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae4ffd4d-4458-46bf-ae11-e3cc4ee17ee3", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926054294600}, "additional": {"logType": "detail", "children": [], "durationId": "ff1c1560-a54c-4af7-972c-9141c24ca850"}}, {"head": {"id": "0b95890b-2bc1-49fc-ac74-7bfe58c7d867", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926055262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3e34fb-7ffe-448e-94c6-a05b747b5cba", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926055503000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e39d2b8-d9f8-4de2-afd8-de99afe08750", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926055623000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32a594e0-8a25-4158-b86d-6204a4fc7966", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926064732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker6", "startTime": 10926104611400, "endTime": 10938608610300}, "additional": {"children": ["80e2c640-167c-46ef-a23b-6ab58be34b70", "7893dd17-24dd-475a-8374-add1e4b18e72", "a74302d2-b285-4d52-a5c0-cb70e08b132a", "ebeb2c92-cf51-49a5-8da4-50c3190a443f", "e1095ae0-35c8-4504-b4e2-bb4af7937df5", "8bec708e-96b2-466f-94bd-4586e5e669e4", "ca0dcec3-d5d9-463c-bf54-9c4884b1f2e7", "b0a96a41-b7f8-4e80-ad04-e24dfea67634", "9a7c22fd-a705-45cd-956f-5f7e9d5370d1", "56aeb1bb-431a-4efe-b254-71e90f7fb415", "5bab89c3-3fe0-44f4-90aa-888d96c496d0", "b043ac30-919f-4abe-b00d-5bd15448ae19", "ac24dda4-ffc9-45f9-8b05-f06eb05b90a2"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ff1c1560-a54c-4af7-972c-9141c24ca850", "logId": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b18623e7-b75d-4e39-9d0e-1c00e223a9f9", "name": "entry : default@PreviewArkTS cost memory -0.47154998779296875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926107634900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cee7876-9066-4b8a-857a-e76e39253775", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10930017413400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e2c640-167c-46ef-a23b-6ab58be34b70", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10930018474000, "endTime": 10930018490500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "51207e91-9394-4338-84ef-c4b6990f8045"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51207e91-9394-4338-84ef-c4b6990f8045", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10930018474000, "endTime": 10930018490500}, "additional": {"logType": "info", "children": [], "durationId": "80e2c640-167c-46ef-a23b-6ab58be34b70", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "d859e555-00ee-472f-97c8-f638c1c8452f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935363091500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7893dd17-24dd-475a-8374-add1e4b18e72", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935363980600, "endTime": 10935363998900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "672b9f54-8fb4-498e-94da-d99fcb8327f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "672b9f54-8fb4-498e-94da-d99fcb8327f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935363980600, "endTime": 10935363998900}, "additional": {"logType": "info", "children": [], "durationId": "7893dd17-24dd-475a-8374-add1e4b18e72", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "7ba2882a-e456-4973-87a9-130f060cef58", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935364067400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74302d2-b285-4d52-a5c0-cb70e08b132a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935364722600, "endTime": 10935364733400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "49152442-2d71-4f62-be2b-bdaaac2df40a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49152442-2d71-4f62-be2b-bdaaac2df40a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935364722600, "endTime": 10935364733400}, "additional": {"logType": "info", "children": [], "durationId": "a74302d2-b285-4d52-a5c0-cb70e08b132a", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "b23163dc-2619-4ccc-b525-140f6b96bf51", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935364786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebeb2c92-cf51-49a5-8da4-50c3190a443f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935365416800, "endTime": 10935365427900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "149e8696-99f6-4271-a3c1-748f36b53e41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "149e8696-99f6-4271-a3c1-748f36b53e41", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935365416800, "endTime": 10935365427900}, "additional": {"logType": "info", "children": [], "durationId": "ebeb2c92-cf51-49a5-8da4-50c3190a443f", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "a238ef98-c9fe-4d50-a494-70def6c8c14a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935365480800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1095ae0-35c8-4504-b4e2-bb4af7937df5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935366166200, "endTime": 10935366180400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "420e4748-ece7-4901-b374-7c0361e8531d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "420e4748-ece7-4901-b374-7c0361e8531d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935366166200, "endTime": 10935366180400}, "additional": {"logType": "info", "children": [], "durationId": "e1095ae0-35c8-4504-b4e2-bb4af7937df5", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "24f4f57f-c5dd-429a-b97e-654f731727b6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935366278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bec708e-96b2-466f-94bd-4586e5e669e4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935367105300, "endTime": 10935367122600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "e99a6a00-9aa9-4c13-875b-56307ccf20b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e99a6a00-9aa9-4c13-875b-56307ccf20b6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935367105300, "endTime": 10935367122600}, "additional": {"logType": "info", "children": [], "durationId": "8bec708e-96b2-466f-94bd-4586e5e669e4", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "133932f1-e68d-4f30-aad0-965e912fa93b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935367198100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0dcec3-d5d9-463c-bf54-9c4884b1f2e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935367927500, "endTime": 10935367942000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "e1eb9b0e-9ac9-4452-86b9-218186e9c64f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1eb9b0e-9ac9-4452-86b9-218186e9c64f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935367927500, "endTime": 10935367942000}, "additional": {"logType": "info", "children": [], "durationId": "ca0dcec3-d5d9-463c-bf54-9c4884b1f2e7", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "4ec79edd-2922-44e5-b585-f9f2744c440e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935618140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a96a41-b7f8-4e80-ad04-e24dfea67634", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935619731100, "endTime": 10935619758700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "309a431b-90cb-4408-ac8a-b84b99023ef5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "309a431b-90cb-4408-ac8a-b84b99023ef5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935619731100, "endTime": 10935619758700}, "additional": {"logType": "info", "children": [], "durationId": "b0a96a41-b7f8-4e80-ad04-e24dfea67634", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "b14b46bb-fe0a-4c79-85a4-532b9afd9f62", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935917557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a7c22fd-a705-45cd-956f-5f7e9d5370d1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935918775100, "endTime": 10935918798700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "4fae790e-701e-4908-bb3c-124734f65dd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fae790e-701e-4908-bb3c-124734f65dd0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10935918775100, "endTime": 10935918798700}, "additional": {"logType": "info", "children": [], "durationId": "9a7c22fd-a705-45cd-956f-5f7e9d5370d1", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "1c1727f9-7c84-48e0-92b8-99ac636d6a22", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936034376800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56aeb1bb-431a-4efe-b254-71e90f7fb415", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936035564700, "endTime": 10936035585600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "f4d7a220-0d71-4f42-bb76-09bc15c9b432"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4d7a220-0d71-4f42-bb76-09bc15c9b432", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936035564700, "endTime": 10936035585600}, "additional": {"logType": "info", "children": [], "durationId": "56aeb1bb-431a-4efe-b254-71e90f7fb415", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "13966979-fd52-4b58-8f15-35e8dc98056c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936151182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bab89c3-3fe0-44f4-90aa-888d96c496d0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936152255500, "endTime": 10936152274300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "426d9078-8ec1-4068-9651-34ef252cd1d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "426d9078-8ec1-4068-9651-34ef252cd1d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936152255500, "endTime": 10936152274300}, "additional": {"logType": "info", "children": [], "durationId": "5bab89c3-3fe0-44f4-90aa-888d96c496d0", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "549d7464-16cb-4f9f-83ab-3e85be3b32da", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936206697300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b043ac30-919f-4abe-b00d-5bd15448ae19", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936207876600, "endTime": 10936207902800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "1ba30601-53e1-471b-9efb-8c5419a7ea05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ba30601-53e1-471b-9efb-8c5419a7ea05", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10936207876600, "endTime": 10936207902800}, "additional": {"logType": "info", "children": [], "durationId": "b043ac30-919f-4abe-b00d-5bd15448ae19", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "69f411cc-01bc-446a-a43d-c1c10d1f4fb9", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938607390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac24dda4-ffc9-45f9-8b05-f06eb05b90a2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938608526400, "endTime": 10938608545300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "logId": "617e2cba-6241-4f59-b4b0-c51dea33f7ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "617e2cba-6241-4f59-b4b0-c51dea33f7ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938608526400, "endTime": 10938608545300}, "additional": {"logType": "info", "children": [], "durationId": "ac24dda4-ffc9-45f9-8b05-f06eb05b90a2", "parent": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a"}}, {"head": {"id": "6146b2b6-c42e-44b8-ba1f-bea68f25c33a", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker6", "startTime": 10926104611400, "endTime": 10938608610300}, "additional": {"logType": "error", "children": ["51207e91-9394-4338-84ef-c4b6990f8045", "672b9f54-8fb4-498e-94da-d99fcb8327f8", "49152442-2d71-4f62-be2b-bdaaac2df40a", "149e8696-99f6-4271-a3c1-748f36b53e41", "420e4748-ece7-4901-b374-7c0361e8531d", "e99a6a00-9aa9-4c13-875b-56307ccf20b6", "e1eb9b0e-9ac9-4452-86b9-218186e9c64f", "309a431b-90cb-4408-ac8a-b84b99023ef5", "4fae790e-701e-4908-bb3c-124734f65dd0", "f4d7a220-0d71-4f42-bb76-09bc15c9b432", "426d9078-8ec1-4068-9651-34ef252cd1d3", "1ba30601-53e1-471b-9efb-8c5419a7ea05", "617e2cba-6241-4f59-b4b0-c51dea33f7ad"], "durationId": "fa0988f0-cdfc-4fca-b42d-1e2d7cf3d3bf", "parent": "ab2510a2-903b-4141-b4f0-64a73747b7b7"}}, {"head": {"id": "a75f5fa1-ddec-4ae2-928d-c315decf2b90", "name": "default@PreviewArkTS watch work[6] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938608661600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab2510a2-903b-4141-b4f0-64a73747b7b7", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10926064702800, "endTime": 10938608779400}, "additional": {"logType": "error", "children": ["6146b2b6-c42e-44b8-ba1f-bea68f25c33a"], "durationId": "ff1c1560-a54c-4af7-972c-9141c24ca850"}}, {"head": {"id": "d74efcd5-f158-49c4-8b54-3eb68354eb58", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938608872100}, "additional": {"logType": "debug", "children": [], "durationId": "ff1c1560-a54c-4af7-972c-9141c24ca850"}}, {"head": {"id": "7a7a2017-d954-433b-a49a-eeb42bee7e82", "name": "ERROR: stacktrace = Error: \u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:79:16\n Conversion of type 'void & Promise<ValueType>' to type 'number | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:97:16\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:115:16\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\u001b[31mArkTS:ERROR File: D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/common/UserStorage.ets:133:16\n Conversion of type 'void & Promise<ValueType>' to type 'string | null' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938609641500}, "additional": {"logType": "debug", "children": [], "durationId": "ff1c1560-a54c-4af7-972c-9141c24ca850"}}, {"head": {"id": "9473d702-88f0-4790-87a6-1ceea95c3e85", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938622688700, "endTime": 10938622737900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7c18a157-d7f4-4a2f-b349-1e207536afd2", "logId": "0c272bfb-b5e7-4a15-b002-35c5c8e0c950"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c272bfb-b5e7-4a15-b002-35c5c8e0c950", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938622688700, "endTime": 10938622737900}, "additional": {"logType": "info", "children": [], "durationId": "9473d702-88f0-4790-87a6-1ceea95c3e85"}}, {"head": {"id": "b453835b-fcbf-4523-80e8-63287fee301c", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10924078989500, "endTime": 10938622847300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 41}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "557ee55b-11e5-42c2-aabf-923a132481f6", "name": "BUILD FAILED in 14 s 544 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938622879800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "32e74195-6ed2-4378-a417-eb50738a0671", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938623057500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bede412c-499b-4499-a0df-ae0eca63bb45", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938623791900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f74002-d2da-4a28-af45-7b3162b3eeaf", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938623885500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258dcb82-00d6-4044-9485-e62632a00fd5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938623971600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "099344d4-954e-41f5-8ee9-5c2f3e3afb91", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938624056500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd37a663-e8cf-4dd4-b3ae-833a29e33122", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938624144500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26da543e-5bec-4cb4-b2ec-4dd816983c83", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938624229300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1a837e-438b-4bf5-bce3-1c2b505de689", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938625374700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa31683-4dbd-401e-8f73-2f10c9b6367a", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938638821100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5e8146-266f-4500-9f47-0f999592fb05", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938641857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b1e1f4-74e2-45db-b51e-f9f464e70130", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938642305700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09329f94-0931-4669-b358-9869a9c80d47", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938661886600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dbfca4c-68c1-4ef8-80f9-b88a902cb202", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:39 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938662628900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bf4d3f9-d261-4e4b-970b-54124a1b3ed0", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938662872200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d9f752-7d03-4da4-8310-9a73737a0eb1", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938663749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cafb099c-69a4-4ca2-98bf-343dc23fe688", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938664568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15197de-551e-4980-9f0b-e549d88c9ab5", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938664943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2452f5c1-57f6-432b-9ef7-c2562b348a38", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938665254300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6110e5d-eff6-4cac-8dc0-a64a2bb384a6", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938665670100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36e45fee-ce3d-413b-8094-5bf23bdb2a3f", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938669743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a6c983-513f-4f49-9c19-4dda4d6b3bdc", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938670629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ea7d38-2da8-4351-94d2-f3188b1d4b9f", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938670945500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c83f51df-5f5b-4146-911c-65b7a3442f89", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938686718700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fcb7db0-2780-4a48-968a-e627e1a8ed9f", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938687711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1fb43f-2104-4c28-a786-2fd8e79a95ee", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938688049200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73298ebf-eb83-4f29-a9ff-83c31ff8b278", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938688327500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa012e2-0117-4187-884d-af11e0c1a280", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938689731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec86d87b-9fcd-4afb-a033-92e45ab3a045", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938694935400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c776b5-7504-4a47-9630-809074a19c6a", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938695435400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6185ce98-6fd8-49a5-a9b4-4c48615d2e42", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938695868300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df417b94-73f6-43db-88e2-0f796590c6fc", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938696294600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c436d3e4-bab8-4dcd-a403-852e7b45e8c5", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:31 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 10938696743900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}