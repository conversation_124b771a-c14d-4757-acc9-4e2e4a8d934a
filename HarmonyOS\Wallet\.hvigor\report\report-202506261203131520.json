{"version": "2.0", "ppid": 13468, "events": [{"head": {"id": "2b77e8d6-4a12-48a1-a3e7-2f75d4fa04a8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11845432668500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7794b2a3-5a2e-4291-9131-525ce6028b99", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11990359025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75164b2c-32bf-4ff2-b9c6-c61a38365559", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11990360625400, "endTime": 11990360650600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "29601911-f249-4ac1-b732-51b586002563"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29601911-f249-4ac1-b732-51b586002563", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11990360625400, "endTime": 11990360650600}, "additional": {"logType": "info", "children": [], "durationId": "75164b2c-32bf-4ff2-b9c6-c61a38365559"}}, {"head": {"id": "322c1008-a444-4b62-9488-248615fe002e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992327614800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448f98bb-ff12-4b0b-a0ec-4e06a1acd565", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992329855900, "endTime": 11992329883500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "d424c74e-64ed-44d1-bd32-2e92d0733ad2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d424c74e-64ed-44d1-bd32-2e92d0733ad2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992329855900, "endTime": 11992329883500}, "additional": {"logType": "info", "children": [], "durationId": "448f98bb-ff12-4b0b-a0ec-4e06a1acd565"}}, {"head": {"id": "acef4176-56d7-4bb4-896d-2488d50ce96c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992330013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30bc3993-d0bc-45b4-9eb2-27cd96d4c20f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992330937600, "endTime": 11992330955000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "0f07b523-067b-43ea-adf8-66bebbb3465a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f07b523-067b-43ea-adf8-66bebbb3465a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992330937600, "endTime": 11992330955000}, "additional": {"logType": "info", "children": [], "durationId": "30bc3993-d0bc-45b4-9eb2-27cd96d4c20f"}}, {"head": {"id": "89b23ec9-ca02-4f72-94ac-12d94539f5f6", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992331032300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2104f99e-8e62-471d-8bb0-a48165a268e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992331824300, "endTime": 11992331839000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "6277b9a0-54e4-4d09-8557-199ba2a20c44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6277b9a0-54e4-4d09-8557-199ba2a20c44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992331824300, "endTime": 11992331839000}, "additional": {"logType": "info", "children": [], "durationId": "2104f99e-8e62-471d-8bb0-a48165a268e3"}}, {"head": {"id": "41dd2695-6990-42c3-afd6-e1b7032a8911", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992331910700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07777d69-9613-41ff-84a0-2b688fc7d20d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992332759300, "endTime": 11992332776800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "15986dda-c685-4e4d-b6cb-e6d6e13bf2db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15986dda-c685-4e4d-b6cb-e6d6e13bf2db", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992332759300, "endTime": 11992332776800}, "additional": {"logType": "info", "children": [], "durationId": "07777d69-9613-41ff-84a0-2b688fc7d20d"}}, {"head": {"id": "bc5a2642-0613-45bc-aab1-7a97c9fc1d01", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992332858500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e16dffd-bf9f-4c5f-90c1-d098cd270659", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992333616700, "endTime": 11992333631300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "1b76b7ad-dc05-417d-9d74-14e4ed5def4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b76b7ad-dc05-417d-9d74-14e4ed5def4b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992333616700, "endTime": 11992333631300}, "additional": {"logType": "info", "children": [], "durationId": "0e16dffd-bf9f-4c5f-90c1-d098cd270659"}}, {"head": {"id": "d4b59c78-781a-4514-80f1-f0cd4c9498fc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992333696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8eca40c-8474-4f6f-8e92-57a3d6df8d86", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992334442700, "endTime": 11992334457500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "d0c50496-7f98-40cb-bd40-8d9079a68dd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0c50496-7f98-40cb-bd40-8d9079a68dd8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992334442700, "endTime": 11992334457500}, "additional": {"logType": "info", "children": [], "durationId": "b8eca40c-8474-4f6f-8e92-57a3d6df8d86"}}, {"head": {"id": "62d2f801-7c2f-4016-bba2-2808f9b3fbde", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992334568600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659b8544-b5b0-4310-af35-d52d3c2a3c44", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992335372600, "endTime": 11992335385900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "a89d26db-c1ce-42a4-87eb-e13c8577a54c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a89d26db-c1ce-42a4-87eb-e13c8577a54c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992335372600, "endTime": 11992335385900}, "additional": {"logType": "info", "children": [], "durationId": "659b8544-b5b0-4310-af35-d52d3c2a3c44"}}, {"head": {"id": "1318b9ab-8a90-4ded-9934-5ab566d2400f", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992335480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ac1416-9c71-4f2d-a4ad-812439fbf31e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992336197400, "endTime": 11992336210800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "7ab813f6-b895-47d8-91af-c1901626e751"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ab813f6-b895-47d8-91af-c1901626e751", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11992336197400, "endTime": 11992336210800}, "additional": {"logType": "info", "children": [], "durationId": "a2ac1416-9c71-4f2d-a4ad-812439fbf31e"}}, {"head": {"id": "b514bb97-2a4c-42b5-844f-928ce42cb355", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12249808660800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5fd1073-378c-4c7c-a060-62ee7eb15743", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12249810533300, "endTime": 12249810564600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa4bc0c1-5dfc-42b7-821a-a09029d75355", "logId": "4f6d9956-9c71-48af-b6d8-1233b19c3033"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f6d9956-9c71-48af-b6d8-1233b19c3033", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12249810533300, "endTime": 12249810564600}, "additional": {"logType": "info", "children": [], "durationId": "a5fd1073-378c-4c7c-a060-62ee7eb15743"}}, {"head": {"id": "ecb802a9-1e33-407e-baa5-4b808319b5e0", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12251439623600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be66ee30-a9da-4da9-bd25-3f98ac12e048", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12251439835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "690fa48e-fd14-489d-a96b-7689ce58cb64", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253017538900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15e998c9-a942-40bd-9404-15fd100429ef", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253024588300, "endTime": 12253185478000}, "additional": {"children": ["9e738a68-d8c4-42e7-90fb-13e1b5eb7b0f", "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "2fb46e8f-df2f-40a8-a67d-c20376842d58", "db0044b9-10a9-443c-9c39-66982dbcbef2", "c76ed434-e60c-44f8-9d4e-d09a249b0c9b", "88964086-4018-460c-9e75-3fa6e7fc31e1", "cd1224aa-fbae-490a-a2d3-dfca22076758"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e738a68-d8c4-42e7-90fb-13e1b5eb7b0f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253024590000, "endTime": 12253037178000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "35d55852-0f87-4b0b-850b-ddbb134c1111"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253037197200, "endTime": 12253184061200}, "additional": {"children": ["091decbe-2e52-4313-88b1-51cfcf700acd", "1e4d89d3-b726-4c4d-9614-f20d1eac4a78", "f81bd72d-7812-4c38-a820-5b03d6180efd", "75a7eaa3-45be-4f22-a7a6-199f31c57233", "f3f51685-76ef-4ad8-ab48-c0dab9ef8235", "248cd358-ddb8-4500-8c1e-8dd6bd1fbbf9", "29d4c779-9e2a-4bd7-93cd-2e88e904f21d", "767a5f97-247a-4968-8a9b-c770a0e5340b", "2ff48112-5150-4f10-bc22-38bda356d211"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fb46e8f-df2f-40a8-a67d-c20376842d58", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253184097800, "endTime": 12253185461900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "16568fc5-80eb-4d92-b754-d4b1e297159a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db0044b9-10a9-443c-9c39-66982dbcbef2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253185467100, "endTime": 12253185473600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "fdaf0fc8-0679-4227-8418-ceb4c2307a93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c76ed434-e60c-44f8-9d4e-d09a249b0c9b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253027642100, "endTime": 12253027671800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "9ebd3ef9-7109-4f87-8cb1-0265b0d3df39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ebd3ef9-7109-4f87-8cb1-0265b0d3df39", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253027642100, "endTime": 12253027671800}, "additional": {"logType": "info", "children": [], "durationId": "c76ed434-e60c-44f8-9d4e-d09a249b0c9b", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "88964086-4018-460c-9e75-3fa6e7fc31e1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253032164500, "endTime": 12253032184300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "526c6a7c-52d8-4046-b4c0-58e8852b3f5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "526c6a7c-52d8-4046-b4c0-58e8852b3f5e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253032164500, "endTime": 12253032184300}, "additional": {"logType": "info", "children": [], "durationId": "88964086-4018-460c-9e75-3fa6e7fc31e1", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "ca4d780a-a7e1-49ae-84a8-31694c65d48b", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253032265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09f39d7d-47d9-4eb6-9319-306540dc78b2", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253037050500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d55852-0f87-4b0b-850b-ddbb134c1111", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253024590000, "endTime": 12253037178000}, "additional": {"logType": "info", "children": [], "durationId": "9e738a68-d8c4-42e7-90fb-13e1b5eb7b0f", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "091decbe-2e52-4313-88b1-51cfcf700acd", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253041818800, "endTime": 12253041827600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "fae51384-5366-470d-8495-afe078b774fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e4d89d3-b726-4c4d-9614-f20d1eac4a78", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253041839300, "endTime": 12253046065200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "8e90378d-3598-4f7f-993e-e1e98d9c8396"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f81bd72d-7812-4c38-a820-5b03d6180efd", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253046078400, "endTime": 12253122976800}, "additional": {"children": ["c0689aea-1522-4256-ac81-8dc1dbbb6fbc", "75eccb3f-3c0c-4bd5-81b2-20bdcd018cf5", "75377809-9ef4-449b-9ea0-d0a290bd982c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "a2e6fe8f-abae-42f1-b724-0b61fbb67312"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75a7eaa3-45be-4f22-a7a6-199f31c57233", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122995900, "endTime": 12253147347700}, "additional": {"children": ["32a3a245-57e8-4e58-875d-3a5c58aa0d4b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "e6c48aa5-00a1-4a79-b539-4c2bd1abd968"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3f51685-76ef-4ad8-ab48-c0dab9ef8235", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253147358300, "endTime": 12253161799600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "68d6203c-d362-4242-ae09-2ff3739a5417"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "248cd358-ddb8-4500-8c1e-8dd6bd1fbbf9", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253162796900, "endTime": 12253170745600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "08a0f620-947b-44f2-9dfe-3946a18dcbc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29d4c779-9e2a-4bd7-93cd-2e88e904f21d", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253170767100, "endTime": 12253183887800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "f2bfbdc1-d57c-43a4-b88e-95c3ec23cbc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "767a5f97-247a-4968-8a9b-c770a0e5340b", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253183912000, "endTime": 12253184040300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "a2e82410-a5a3-4352-a988-1b7ffd648bce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fae51384-5366-470d-8495-afe078b774fb", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253041818800, "endTime": 12253041827600}, "additional": {"logType": "info", "children": [], "durationId": "091decbe-2e52-4313-88b1-51cfcf700acd", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "8e90378d-3598-4f7f-993e-e1e98d9c8396", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253041839300, "endTime": 12253046065200}, "additional": {"logType": "info", "children": [], "durationId": "1e4d89d3-b726-4c4d-9614-f20d1eac4a78", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "c0689aea-1522-4256-ac81-8dc1dbbb6fbc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253046651000, "endTime": 12253046668400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f81bd72d-7812-4c38-a820-5b03d6180efd", "logId": "f90dd260-a432-4212-b2b7-24e0ccf2eb82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f90dd260-a432-4212-b2b7-24e0ccf2eb82", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253046651000, "endTime": 12253046668400}, "additional": {"logType": "info", "children": [], "durationId": "c0689aea-1522-4256-ac81-8dc1dbbb6fbc", "parent": "a2e6fe8f-abae-42f1-b724-0b61fbb67312"}}, {"head": {"id": "75eccb3f-3c0c-4bd5-81b2-20bdcd018cf5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253048535700, "endTime": 12253122344000}, "additional": {"children": ["80a17aea-cf6d-42dd-b4f2-5dc5534943a1", "310497c4-c70a-45bd-8d6e-ef41d7988cb8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f81bd72d-7812-4c38-a820-5b03d6180efd", "logId": "7e1a4ab5-23fb-49b1-9053-54b88c4dd0ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80a17aea-cf6d-42dd-b4f2-5dc5534943a1", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253048536800, "endTime": 12253056995900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75eccb3f-3c0c-4bd5-81b2-20bdcd018cf5", "logId": "d4fdc8c5-0c85-4364-9127-a7f341ec6411"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "310497c4-c70a-45bd-8d6e-ef41d7988cb8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253057015200, "endTime": 12253122328700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75eccb3f-3c0c-4bd5-81b2-20bdcd018cf5", "logId": "1615a891-c896-4183-beb7-76dcf59ce43c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab3bfad0-02d4-42f1-9bf0-5a30932ef95d", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253048541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eddef26-d194-49b8-bec2-e14726e6f163", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253056861000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4fdc8c5-0c85-4364-9127-a7f341ec6411", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253048536800, "endTime": 12253056995900}, "additional": {"logType": "info", "children": [], "durationId": "80a17aea-cf6d-42dd-b4f2-5dc5534943a1", "parent": "7e1a4ab5-23fb-49b1-9053-54b88c4dd0ca"}}, {"head": {"id": "3878f6e6-b79f-47ab-ad63-afc2a4cf93fb", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253057033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f95a17-7a3f-4564-85fb-b12ea560ed8c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253063210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785bcad0-7aa5-4547-be78-d6a248c9ecbb", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253063316900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a55c6ce-ada0-410c-9d40-f38b46195888", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253063447000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5097a230-6a7c-4aa3-aa4a-93c93b9dae04", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253063543800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19286695-a2ee-47a8-a4c6-b8eaa7c85aed", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253065073200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f158323-f748-416e-a659-1cd0549a368e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253069208400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cec7dd82-ec27-4b9f-bac2-31352f49048c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253077352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae677274-1ed4-4b46-b556-1153e9e80344", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253101423000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5f8923-ea4b-448f-8808-fdf0420b0e6f", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253101563400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 3}, "markType": "other"}}, {"head": {"id": "47f4c052-9b72-4d78-a33f-d0d08ce652ae", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253101581700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 3}, "markType": "other"}}, {"head": {"id": "222df9e1-140e-4c9d-9fb6-e0849fb07ac3", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122026200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1265a9b5-a381-4338-9f23-14d93ef754b9", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122171500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "528804fd-c565-4351-8489-55ba1557be32", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122236800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47831fce-7f5f-41e7-a68a-8b9ce27620de", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122285300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1615a891-c896-4183-beb7-76dcf59ce43c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253057015200, "endTime": 12253122328700}, "additional": {"logType": "info", "children": [], "durationId": "310497c4-c70a-45bd-8d6e-ef41d7988cb8", "parent": "7e1a4ab5-23fb-49b1-9053-54b88c4dd0ca"}}, {"head": {"id": "7e1a4ab5-23fb-49b1-9053-54b88c4dd0ca", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253048535700, "endTime": 12253122344000}, "additional": {"logType": "info", "children": ["d4fdc8c5-0c85-4364-9127-a7f341ec6411", "1615a891-c896-4183-beb7-76dcf59ce43c"], "durationId": "75eccb3f-3c0c-4bd5-81b2-20bdcd018cf5", "parent": "a2e6fe8f-abae-42f1-b724-0b61fbb67312"}}, {"head": {"id": "75377809-9ef4-449b-9ea0-d0a290bd982c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122942700, "endTime": 12253122959400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f81bd72d-7812-4c38-a820-5b03d6180efd", "logId": "4b58ce49-cc9e-4eb9-a260-b18b07125955"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b58ce49-cc9e-4eb9-a260-b18b07125955", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122942700, "endTime": 12253122959400}, "additional": {"logType": "info", "children": [], "durationId": "75377809-9ef4-449b-9ea0-d0a290bd982c", "parent": "a2e6fe8f-abae-42f1-b724-0b61fbb67312"}}, {"head": {"id": "a2e6fe8f-abae-42f1-b724-0b61fbb67312", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253046078400, "endTime": 12253122976800}, "additional": {"logType": "info", "children": ["f90dd260-a432-4212-b2b7-24e0ccf2eb82", "7e1a4ab5-23fb-49b1-9053-54b88c4dd0ca", "4b58ce49-cc9e-4eb9-a260-b18b07125955"], "durationId": "f81bd72d-7812-4c38-a820-5b03d6180efd", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "32a3a245-57e8-4e58-875d-3a5c58aa0d4b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253123583600, "endTime": 12253147328600}, "additional": {"children": ["307a7d7b-1f92-43e0-8ef0-2ad22796ff8e", "ddeb8762-ac8f-431a-9680-72876fca81c3", "9c564ca8-bf3b-4aec-9244-eecbae081d7c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75a7eaa3-45be-4f22-a7a6-199f31c57233", "logId": "eec8be54-6da2-4981-aa4d-fc230adce044"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "307a7d7b-1f92-43e0-8ef0-2ad22796ff8e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253126633600, "endTime": 12253126668900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32a3a245-57e8-4e58-875d-3a5c58aa0d4b", "logId": "c36de72a-36e6-49e4-9897-7efafe33bd1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c36de72a-36e6-49e4-9897-7efafe33bd1c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253126633600, "endTime": 12253126668900}, "additional": {"logType": "info", "children": [], "durationId": "307a7d7b-1f92-43e0-8ef0-2ad22796ff8e", "parent": "eec8be54-6da2-4981-aa4d-fc230adce044"}}, {"head": {"id": "ddeb8762-ac8f-431a-9680-72876fca81c3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253128490300, "endTime": 12253145817300}, "additional": {"children": ["ab148475-ed65-4634-bb97-970016f2ec82", "a2b2806c-3d07-4fc7-9ba0-b520418fe7ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32a3a245-57e8-4e58-875d-3a5c58aa0d4b", "logId": "9cd8e9bc-e2f1-4c27-9277-e4232f903425"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab148475-ed65-4634-bb97-970016f2ec82", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253128491500, "endTime": 12253133693300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddeb8762-ac8f-431a-9680-72876fca81c3", "logId": "a692ad12-1d79-4dff-bd6d-79a5cebf9759"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2b2806c-3d07-4fc7-9ba0-b520418fe7ed", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253133714100, "endTime": 12253145804000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddeb8762-ac8f-431a-9680-72876fca81c3", "logId": "bfaf156c-2931-4a11-990a-7a7c1df1c03b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13a65ad1-0a8e-411d-8537-9df61c6dc691", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253128496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1be55f-fb1f-488d-b262-c03b7f3e8568", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253133576500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a692ad12-1d79-4dff-bd6d-79a5cebf9759", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253128491500, "endTime": 12253133693300}, "additional": {"logType": "info", "children": [], "durationId": "ab148475-ed65-4634-bb97-970016f2ec82", "parent": "9cd8e9bc-e2f1-4c27-9277-e4232f903425"}}, {"head": {"id": "a8fdbee9-ebbd-4f59-9c75-1318c244ad55", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253133728900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c2407b-dba8-409e-9698-68553e08e94e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253140016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b38adcd-da4b-4f20-adfd-a48171065912", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253140164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b94791e-829d-4127-84d5-010d964d5887", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253141451800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64817814-f2ba-4eb5-8fac-31ceb30532f6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253141666400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da726f04-e6b5-4049-be84-e1da0e946411", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253141748900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3e7ea9b-3784-4f78-9e75-f38d7455510a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253141874500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d496b8-ab6c-4b4b-95a2-4f5cf27642b7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253141937800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38750d3c-57b5-419d-ae03-150c02ab53a1", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253145439800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ed9483-64b4-47da-b0b2-998693ffd3c0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253145634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea4e163-8338-4f26-b00f-55e2d8947994", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253145706000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1384d725-2df4-4d7a-971c-787db38fafa3", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253145758400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfaf156c-2931-4a11-990a-7a7c1df1c03b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253133714100, "endTime": 12253145804000}, "additional": {"logType": "info", "children": [], "durationId": "a2b2806c-3d07-4fc7-9ba0-b520418fe7ed", "parent": "9cd8e9bc-e2f1-4c27-9277-e4232f903425"}}, {"head": {"id": "9cd8e9bc-e2f1-4c27-9277-e4232f903425", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253128490300, "endTime": 12253145817300}, "additional": {"logType": "info", "children": ["a692ad12-1d79-4dff-bd6d-79a5cebf9759", "bfaf156c-2931-4a11-990a-7a7c1df1c03b"], "durationId": "ddeb8762-ac8f-431a-9680-72876fca81c3", "parent": "eec8be54-6da2-4981-aa4d-fc230adce044"}}, {"head": {"id": "9c564ca8-bf3b-4aec-9244-eecbae081d7c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253147287800, "endTime": 12253147305700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32a3a245-57e8-4e58-875d-3a5c58aa0d4b", "logId": "266c0958-a5e6-4a27-ba71-f3b43ded19d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "266c0958-a5e6-4a27-ba71-f3b43ded19d5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253147287800, "endTime": 12253147305700}, "additional": {"logType": "info", "children": [], "durationId": "9c564ca8-bf3b-4aec-9244-eecbae081d7c", "parent": "eec8be54-6da2-4981-aa4d-fc230adce044"}}, {"head": {"id": "eec8be54-6da2-4981-aa4d-fc230adce044", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253123583600, "endTime": 12253147328600}, "additional": {"logType": "info", "children": ["c36de72a-36e6-49e4-9897-7efafe33bd1c", "9cd8e9bc-e2f1-4c27-9277-e4232f903425", "266c0958-a5e6-4a27-ba71-f3b43ded19d5"], "durationId": "32a3a245-57e8-4e58-875d-3a5c58aa0d4b", "parent": "e6c48aa5-00a1-4a79-b539-4c2bd1abd968"}}, {"head": {"id": "e6c48aa5-00a1-4a79-b539-4c2bd1abd968", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253122995900, "endTime": 12253147347700}, "additional": {"logType": "info", "children": ["eec8be54-6da2-4981-aa4d-fc230adce044"], "durationId": "75a7eaa3-45be-4f22-a7a6-199f31c57233", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "bbcace9c-8b63-4b72-8274-86487bf01729", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253161203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb1e3ce-dbf0-4d02-90ef-dff8fb5248f6", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253161699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68d6203c-d362-4242-ae09-2ff3739a5417", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253147358300, "endTime": 12253161799600}, "additional": {"logType": "info", "children": [], "durationId": "f3f51685-76ef-4ad8-ab48-c0dab9ef8235", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "2ff48112-5150-4f10-bc22-38bda356d211", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253162603300, "endTime": 12253162782700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "logId": "c5b63aec-6c91-4beb-810e-6e45eb828719"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c39fa2ea-81c4-4530-9201-3e6386647cd8", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253162628300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5b63aec-6c91-4beb-810e-6e45eb828719", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253162603300, "endTime": 12253162782700}, "additional": {"logType": "info", "children": [], "durationId": "2ff48112-5150-4f10-bc22-38bda356d211", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "c5b5f181-4740-4541-84a2-8a3a7d7b9def", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253164154600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a56943-b384-41d8-aae3-620c73895176", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253169993200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08a0f620-947b-44f2-9dfe-3946a18dcbc4", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253162796900, "endTime": 12253170745600}, "additional": {"logType": "info", "children": [], "durationId": "248cd358-ddb8-4500-8c1e-8dd6bd1fbbf9", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "db8bfbac-bbb6-41e9-9d42-46905c6def9c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253170782800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2abe1b98-de11-44b2-a99a-9f3cf2696edd", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253175767800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1f9657-65fb-48e5-9dea-01db7e717d66", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253175902900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a38b2e-d923-4b2e-a79f-cf792305dadc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253176209300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc23f14-1a39-4cca-a656-0e2af0b96eea", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253180902100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92490576-3f30-4193-baf9-6748f7b7257f", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253181010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2bfbdc1-d57c-43a4-b88e-95c3ec23cbc9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253170767100, "endTime": 12253183887800}, "additional": {"logType": "info", "children": [], "durationId": "29d4c779-9e2a-4bd7-93cd-2e88e904f21d", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "a00b0336-2e0c-4208-8889-acc6fa3991fb", "name": "Configuration phase cost:143 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253183941900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2e82410-a5a3-4352-a988-1b7ffd648bce", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253183912000, "endTime": 12253184040300}, "additional": {"logType": "info", "children": [], "durationId": "767a5f97-247a-4968-8a9b-c770a0e5340b", "parent": "8616d5d5-c60a-4af7-8f5c-a2644461a763"}}, {"head": {"id": "8616d5d5-c60a-4af7-8f5c-a2644461a763", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253037197200, "endTime": 12253184061200}, "additional": {"logType": "info", "children": ["fae51384-5366-470d-8495-afe078b774fb", "8e90378d-3598-4f7f-993e-e1e98d9c8396", "a2e6fe8f-abae-42f1-b724-0b61fbb67312", "e6c48aa5-00a1-4a79-b539-4c2bd1abd968", "68d6203c-d362-4242-ae09-2ff3739a5417", "08a0f620-947b-44f2-9dfe-3946a18dcbc4", "f2bfbdc1-d57c-43a4-b88e-95c3ec23cbc9", "a2e82410-a5a3-4352-a988-1b7ffd648bce", "c5b63aec-6c91-4beb-810e-6e45eb828719"], "durationId": "83a95a75-1fe7-4c54-8fe9-dd18284d942d", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "cd1224aa-fbae-490a-a2d3-dfca22076758", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253185435400, "endTime": 12253185450500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15e998c9-a942-40bd-9404-15fd100429ef", "logId": "b4b6c5d9-2c22-4f1a-81a6-5e8ce766f4b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4b6c5d9-2c22-4f1a-81a6-5e8ce766f4b5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253185435400, "endTime": 12253185450500}, "additional": {"logType": "info", "children": [], "durationId": "cd1224aa-fbae-490a-a2d3-dfca22076758", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "16568fc5-80eb-4d92-b754-d4b1e297159a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253184097800, "endTime": 12253185461900}, "additional": {"logType": "info", "children": [], "durationId": "2fb46e8f-df2f-40a8-a67d-c20376842d58", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "fdaf0fc8-0679-4227-8418-ceb4c2307a93", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253185467100, "endTime": 12253185473600}, "additional": {"logType": "info", "children": [], "durationId": "db0044b9-10a9-443c-9c39-66982dbcbef2", "parent": "76646a59-4054-432c-ac7d-85e9f0bf04dd"}}, {"head": {"id": "76646a59-4054-432c-ac7d-85e9f0bf04dd", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253024588300, "endTime": 12253185478000}, "additional": {"logType": "info", "children": ["35d55852-0f87-4b0b-850b-ddbb134c1111", "8616d5d5-c60a-4af7-8f5c-a2644461a763", "16568fc5-80eb-4d92-b754-d4b1e297159a", "fdaf0fc8-0679-4227-8418-ceb4c2307a93", "9ebd3ef9-7109-4f87-8cb1-0265b0d3df39", "526c6a7c-52d8-4046-b4c0-58e8852b3f5e", "b4b6c5d9-2c22-4f1a-81a6-5e8ce766f4b5"], "durationId": "15e998c9-a942-40bd-9404-15fd100429ef"}}, {"head": {"id": "5a1a4d2a-0db5-47cf-945b-7d982e82abbd", "name": "Configuration task cost before running: 165 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253185592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82eaabbb-dd49-4021-b750-2dafa135acf2", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253190798100, "endTime": 12253198629800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "bba3abd0-88ef-4636-89b9-85c7721cf6ef", "logId": "2eb7bda3-309c-4f7a-a3f1-ebb5144b2f99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bba3abd0-88ef-4636-89b9-85c7721cf6ef", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253186989200}, "additional": {"logType": "detail", "children": [], "durationId": "82eaabbb-dd49-4021-b750-2dafa135acf2"}}, {"head": {"id": "2a3c92b8-9ae9-4ab1-9b5c-5b708c11724a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253187456400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78d8615-a803-4d1f-9b19-fa9a4a39178c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253187539800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63fe87b4-8e37-4e10-a11d-f4e32173757a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253187593000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3952385-f910-4b0c-a8ad-facbc373a548", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253190808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f37cc14-f9f8-49e7-903a-a75693dea1ff", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253198423200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb432f5-c68b-45ca-b527-de223198f409", "name": "entry : default@PreBuild cost memory 0.27420806884765625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253198555300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb7bda3-309c-4f7a-a3f1-ebb5144b2f99", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253190798100, "endTime": 12253198629800}, "additional": {"logType": "info", "children": [], "durationId": "82eaabbb-dd49-4021-b750-2dafa135acf2"}}, {"head": {"id": "63bbe185-3ed0-423a-a8cc-1249e9507cf6", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253203568600, "endTime": 12253206587800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "255fc91e-4734-44e8-ba7d-51cef65737d8", "logId": "f9c86faf-24a7-4a74-8d32-871d3cdbcf91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "255fc91e-4734-44e8-ba7d-51cef65737d8", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253202321800}, "additional": {"logType": "detail", "children": [], "durationId": "63bbe185-3ed0-423a-a8cc-1249e9507cf6"}}, {"head": {"id": "4521e4d2-7ee5-4984-ae75-1c0538ac77fb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253202799000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e5ba5b0-9a33-4e2d-8ea7-85a15939104d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253202898600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a581d04-541e-4b39-9e44-dd4a66fc0c44", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253202954400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f69f965-c127-4113-ad47-3a5d87e0bfb8", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253203578100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825aa296-60a2-4792-93b9-eb4f02c49e68", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253206413600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2135d762-977e-472a-9a32-763ac5d6c5ab", "name": "entry : default@MergeProfile cost memory 0.15102386474609375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253206523100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c86faf-24a7-4a74-8d32-871d3cdbcf91", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253203568600, "endTime": 12253206587800}, "additional": {"logType": "info", "children": [], "durationId": "63bbe185-3ed0-423a-a8cc-1249e9507cf6"}}, {"head": {"id": "608a127b-5b98-4873-83df-61600f2a861e", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253209558100, "endTime": 12253211894000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "11dbc828-84e0-49f0-a6f0-9d1e75270905", "logId": "53f43f2b-3114-4a17-9bbe-a3a18d0cb415"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11dbc828-84e0-49f0-a6f0-9d1e75270905", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253208204800}, "additional": {"logType": "detail", "children": [], "durationId": "608a127b-5b98-4873-83df-61600f2a861e"}}, {"head": {"id": "83e44d87-9076-4d7d-90b1-18865e478213", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253208658300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b07fae9-d59a-4e30-8ef8-b20fd7e825d0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253208744200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "686552cc-10ee-4761-bfa0-749a48667044", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253208797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90192435-27a0-4c70-8d44-d298fe1160c9", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253209568100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78b397f7-e9e7-4128-9cfe-1ef384b97722", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253210533900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84eb4dc1-1de9-49b7-8619-86310bad52fc", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253211731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efd7ff34-6ef3-4184-86e2-55d5063fe8bc", "name": "entry : default@CreateBuildProfile cost memory 0.09771728515625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253211828600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f43f2b-3114-4a17-9bbe-a3a18d0cb415", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253209558100, "endTime": 12253211894000}, "additional": {"logType": "info", "children": [], "durationId": "608a127b-5b98-4873-83df-61600f2a861e"}}, {"head": {"id": "d186b2b2-2f4a-4f8a-80b3-dbc2c9861ef4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214535800, "endTime": 12253214926200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1be26d5c-b42a-4e7a-9443-c565d44f5bc8", "logId": "f5b3cca3-2393-4935-a8a5-7351befc681f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1be26d5c-b42a-4e7a-9443-c565d44f5bc8", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253213251900}, "additional": {"logType": "detail", "children": [], "durationId": "d186b2b2-2f4a-4f8a-80b3-dbc2c9861ef4"}}, {"head": {"id": "668b928a-900e-4575-b169-812f4badd3d5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253213696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e1eac4-daab-4603-9d5c-06da8a86d493", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253213779600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b730a5c1-bcb0-48a9-ba15-2d88a458a13f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253213830700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fbf2936-c534-438b-babb-056a629b0f85", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0ab66f5-2306-4fa3-a4bf-bd49eaa6d400", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214644900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d85afdc5-f50d-4330-a4ee-79a3f41945ea", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e8b0a6-5f81-42bb-b599-1b6d98a4026e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68c478b7-e394-40f3-b083-a495bb96ec78", "name": "entry : default@PreCheckSyscap cost memory 0.05034637451171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4911f859-7141-4277-ba21-9b61fae23633", "name": "runTaskFromQueue task cost before running: 194 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214872800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b3cca3-2393-4935-a8a5-7351befc681f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253214535800, "endTime": 12253214926200, "totalTime": 317600}, "additional": {"logType": "info", "children": [], "durationId": "d186b2b2-2f4a-4f8a-80b3-dbc2c9861ef4"}}, {"head": {"id": "1f4e505d-ec47-4810-9b15-53adcf9b5fd0", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253225285800, "endTime": 12253226323700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e1369936-777d-4838-ab65-4b295ce56091", "logId": "1407001d-c3db-457c-983f-a04052183cb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1369936-777d-4838-ab65-4b295ce56091", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253216234700}, "additional": {"logType": "detail", "children": [], "durationId": "1f4e505d-ec47-4810-9b15-53adcf9b5fd0"}}, {"head": {"id": "fc9cd274-6be1-4bdc-a9ab-f4fe306b0c9c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253216661800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d02c21e-44b3-4e2c-a4dc-5bfd752a6cf5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253216737300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93679a2b-4fec-410f-a87b-46c4b1de13b6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253216786200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5af3032c-106b-4379-80d5-f2ac2a5506b7", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253225304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da75bf5-8a59-4d24-9529-4519705b5ca7", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253225562000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc77866a-d91f-4a2d-bdc2-ebe47b564d98", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253226144200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62175b80-7f56-4111-974a-ea1fcb283483", "name": "entry : default@GeneratePkgContextInfo cost memory 0.06931304931640625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253226255200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1407001d-c3db-457c-983f-a04052183cb7", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253225285800, "endTime": 12253226323700}, "additional": {"logType": "info", "children": [], "durationId": "1f4e505d-ec47-4810-9b15-53adcf9b5fd0"}}, {"head": {"id": "d090c9f8-ebb9-4974-a7b0-c4c17b5af9b3", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253229759300, "endTime": 12253230831900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "353fc9d0-8b59-46d5-9361-78ec8c1d8302", "logId": "075342a3-34e9-4b60-b928-ca8613330bad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "353fc9d0-8b59-46d5-9361-78ec8c1d8302", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253227847100}, "additional": {"logType": "detail", "children": [], "durationId": "d090c9f8-ebb9-4974-a7b0-c4c17b5af9b3"}}, {"head": {"id": "1381026b-524f-433d-a7db-47bffa4b1d88", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253228324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b79c576-f043-44ab-8ec5-6de20464b28f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253228483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7541cb0-a4c9-40b0-aceb-b31fdb33817a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253228540900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae2d27e-4074-4579-b891-f7805c510075", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253229770700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c097571d-1a68-4990-878d-b97858cb3bab", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253230687600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bad658d4-5d7e-4656-9716-1be456e83412", "name": "entry : default@ProcessProfile cost memory 0.056610107421875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253230774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "075342a3-34e9-4b60-b928-ca8613330bad", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253229759300, "endTime": 12253230831900}, "additional": {"logType": "info", "children": [], "durationId": "d090c9f8-ebb9-4974-a7b0-c4c17b5af9b3"}}, {"head": {"id": "60be0a4a-5741-4cec-b86d-55441bcd6c8b", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253234257800, "endTime": 12253239947700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2c2a4875-a2a5-41ec-944f-e6a6d53d878b", "logId": "5cad2f56-0c12-4203-8eb5-bc5362b813ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c2a4875-a2a5-41ec-944f-e6a6d53d878b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253232193400}, "additional": {"logType": "detail", "children": [], "durationId": "60be0a4a-5741-4cec-b86d-55441bcd6c8b"}}, {"head": {"id": "5aefc8eb-6e10-421a-bd2e-ed79676aeec2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253232641500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f66314-a8b1-423b-bcca-a05efb471545", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253232718500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79dece04-39b3-4c3c-97ea-b3b54722a5e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253232766600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a4ad06-5324-4b5c-a190-1a0d16cf975f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253234266700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6e5ee2-3ef0-4733-abf3-c871f0538ee5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253239762200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2b5a6f6-38e2-4da1-9118-129d3e4de25b", "name": "entry : default@ProcessRouterMap cost memory 0.2078857421875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253239880200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cad2f56-0c12-4203-8eb5-bc5362b813ae", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253234257800, "endTime": 12253239947700}, "additional": {"logType": "info", "children": [], "durationId": "60be0a4a-5741-4cec-b86d-55441bcd6c8b"}}, {"head": {"id": "2e26e2ab-e99f-4112-819c-0c5224fdd6a3", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253246265400, "endTime": 12253249164500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "60908938-10a7-4c3c-90a4-30f18cf3705b", "logId": "e6ceb93e-86c7-474e-af97-4e914e970027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60908938-10a7-4c3c-90a4-30f18cf3705b", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253242561500}, "additional": {"logType": "detail", "children": [], "durationId": "2e26e2ab-e99f-4112-819c-0c5224fdd6a3"}}, {"head": {"id": "5ff8e12e-c3cc-4343-93cf-5407578b75ea", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253242998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d82283f-c560-4378-8fcb-9e63a7a78391", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253243084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4472de21-00da-4c95-a3f9-275f3d0b01c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253243136100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac9a2485-6e1f-4fc0-9cc1-a40f476d9c4a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253243963700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6756b2bb-dbed-4508-b481-600fa9134443", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253247573100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "929a0a27-039f-4f8e-b609-00150d9d7eaa", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253247696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88bc8557-69b1-4c6b-b9c4-696ed028e2ea", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253247751000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f6f9d1-b930-404b-96cf-c06f725392e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253247793100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd93dec7-e39c-49ef-9907-1983bf13ed42", "name": "entry : default@PreviewProcessResource cost memory 0.08831787109375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253247863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eba6e1b-45c1-4348-8da0-b0c141c30375", "name": "runTaskFromQueue task cost before running: 229 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253249079400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6ceb93e-86c7-474e-af97-4e914e970027", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253246265400, "endTime": 12253249164500, "totalTime": 1649100}, "additional": {"logType": "info", "children": [], "durationId": "2e26e2ab-e99f-4112-819c-0c5224fdd6a3"}}, {"head": {"id": "1fa95062-a993-47bc-89a3-207f498ef1a2", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253258077000, "endTime": 12253279956000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2d389b04-0852-4cfb-b79f-58ba3ac38d07", "logId": "0a4e69c4-fd26-4b48-beb7-b18fb881775a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d389b04-0852-4cfb-b79f-58ba3ac38d07", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253253123700}, "additional": {"logType": "detail", "children": [], "durationId": "1fa95062-a993-47bc-89a3-207f498ef1a2"}}, {"head": {"id": "a4f3e3b6-964b-43a8-86c5-668820441175", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253253755000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8747e4b-4b0e-4174-ab5d-46bd75234e0e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253253881200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97392888-7e56-4aa3-9f20-093209f8cfb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253253948200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fec69ce-87b8-4fd8-a586-cfc9c9e1a716", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253258091800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21edfc1d-d64b-4337-9659-30a6c4a90417", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253279580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e9dd1be-946d-4529-8d62-82a803fdf1b3", "name": "entry : default@GenerateLoaderJson cost memory 0.8242416381835938", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253279736500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a4e69c4-fd26-4b48-beb7-b18fb881775a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253258077000, "endTime": 12253279956000}, "additional": {"logType": "info", "children": [], "durationId": "1fa95062-a993-47bc-89a3-207f498ef1a2"}}, {"head": {"id": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253292044900, "endTime": 12253927325500}, "additional": {"children": ["e5284438-8647-4352-8fd9-38d96a675872", "d6fe01a7-0d5d-4c49-85ee-a5292601a0a5", "2b32a80b-0c14-46ef-a880-b5ea3ca92fab", "5a72b888-4443-478f-bfda-fcc2d4fdb4b8", "40e6bf0f-c627-42b9-a163-071b9afa8d88"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "6c680725-a785-4bdf-aa3d-6a09a81267f8", "logId": "3960a832-0c54-4ba9-8fad-d1db544502cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c680725-a785-4bdf-aa3d-6a09a81267f8", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253287184900}, "additional": {"logType": "detail", "children": [], "durationId": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc"}}, {"head": {"id": "22bf1fe6-accb-47b7-beaf-e282d9772b34", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253287711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aad112f-7cba-4ef6-bbaa-99d3be14bf5f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253287877500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb4b461-a2b7-4019-8af4-b31b221de0d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253287964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f944c8e6-7cbf-4405-8d27-b0e74b571d24", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253289206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c479271-ee73-43c8-b243-6c33d5b34a34", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253292085500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a753b5-72dc-436c-82f7-f1ead2f7cff2", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253331095600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff18f50e-1b30-4574-8a6f-e7b1a70f2302", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 39 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253331245100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5284438-8647-4352-8fd9-38d96a675872", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253332325200, "endTime": 12253357253000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc", "logId": "047a8a44-b80e-416f-b89d-7814a663a2a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "047a8a44-b80e-416f-b89d-7814a663a2a8", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253332325200, "endTime": 12253357253000}, "additional": {"logType": "info", "children": [], "durationId": "e5284438-8647-4352-8fd9-38d96a675872", "parent": "3960a832-0c54-4ba9-8fad-d1db544502cf"}}, {"head": {"id": "25ccc7cc-076a-454a-9ac6-6522993f835c", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253357382800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6fe01a7-0d5d-4c49-85ee-a5292601a0a5", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253358212000, "endTime": 12253483525600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc", "logId": "408ee6df-6eae-4a7c-abdb-a0bde0ec40db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23fe5d6d-daab-46b5-9915-08fcb0283291", "name": "current process  memoryUsage: {\n  rss: 113725440,\n  heapTotal: 121667584,\n  heapUsed: 112342208,\n  external: 3168477,\n  arrayBuffers: 162344\n} os memoryUsage :12.283000946044922", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253359223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0110e222-8005-4922-b86f-c83b4923bc01", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253481476700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408ee6df-6eae-4a7c-abdb-a0bde0ec40db", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253358212000, "endTime": 12253483525600}, "additional": {"logType": "info", "children": [], "durationId": "d6fe01a7-0d5d-4c49-85ee-a5292601a0a5", "parent": "3960a832-0c54-4ba9-8fad-d1db544502cf"}}, {"head": {"id": "b5054cf4-947d-4b03-8082-2e3df0c8be41", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253483742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b32a80b-0c14-46ef-a880-b5ea3ca92fab", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253484814400, "endTime": 12253626950200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc", "logId": "f8be50b0-37de-493f-be6d-b9d0a7a63ee1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f685e996-f6e8-4f97-92e7-60e683faeea3", "name": "current process  memoryUsage: {\n  rss: 113946624,\n  heapTotal: 121929728,\n  heapUsed: 110980968,\n  external: 3168603,\n  arrayBuffers: 144500\n} os memoryUsage :12.259239196777344", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253486279100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e9bff8c-e354-466f-b814-72e7bcc88561", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253624030500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8be50b0-37de-493f-be6d-b9d0a7a63ee1", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253484814400, "endTime": 12253626950200}, "additional": {"logType": "info", "children": [], "durationId": "2b32a80b-0c14-46ef-a880-b5ea3ca92fab", "parent": "3960a832-0c54-4ba9-8fad-d1db544502cf"}}, {"head": {"id": "9974e75d-3c44-4af9-8c06-02af8b7aed74", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253627199500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a72b888-4443-478f-bfda-fcc2d4fdb4b8", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253628508400, "endTime": 12253754980200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc", "logId": "25f1881f-f550-4850-a3e2-4077e4b6219f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dada83b-726e-47de-a5b0-da7a574d37c9", "name": "current process  memoryUsage: {\n  rss: 113963008,\n  heapTotal: 121929728,\n  heapUsed: 111253112,\n  external: 3150744,\n  arrayBuffers: 144690\n} os memoryUsage :12.271045684814453", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253630018600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cdb2e38-4f16-4426-9c3c-276eaf6b28fc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253752888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25f1881f-f550-4850-a3e2-4077e4b6219f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253628508400, "endTime": 12253754980200}, "additional": {"logType": "info", "children": [], "durationId": "5a72b888-4443-478f-bfda-fcc2d4fdb4b8", "parent": "3960a832-0c54-4ba9-8fad-d1db544502cf"}}, {"head": {"id": "990ac851-99cf-4dea-b395-44af4e464110", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253755232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e6bf0f-c627-42b9-a163-071b9afa8d88", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253756245800, "endTime": 12253925559400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc", "logId": "63ba3eb5-3261-43ab-89a2-45734389fc25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2104e05-290b-4f73-9cfb-ea9e56de590b", "name": "current process  memoryUsage: {\n  rss: 113979392,\n  heapTotal: 121929728,\n  heapUsed: 111559864,\n  external: 3159062,\n  arrayBuffers: 153927\n} os memoryUsage :12.272205352783203", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253757171000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a50c5d0-f35a-4efb-af0a-5b07f62693a4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253922236500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63ba3eb5-3261-43ab-89a2-45734389fc25", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253756245800, "endTime": 12253925559400}, "additional": {"logType": "info", "children": [], "durationId": "40e6bf0f-c627-42b9-a163-071b9afa8d88", "parent": "3960a832-0c54-4ba9-8fad-d1db544502cf"}}, {"head": {"id": "5bbe7f69-7f3b-4f00-ae55-cc2d48ea6865", "name": "entry : default@PreviewCompileResource cost memory -0.44203948974609375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253927003400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2376ab-6228-44dc-93ad-9dad30eeab49", "name": "runTaskFromQueue task cost before running: 907 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253927242200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3960a832-0c54-4ba9-8fad-d1db544502cf", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253292044900, "endTime": 12253927325500, "totalTime": 635134400}, "additional": {"logType": "info", "children": ["047a8a44-b80e-416f-b89d-7814a663a2a8", "408ee6df-6eae-4a7c-abdb-a0bde0ec40db", "f8be50b0-37de-493f-be6d-b9d0a7a63ee1", "25f1881f-f550-4850-a3e2-4077e4b6219f", "63ba3eb5-3261-43ab-89a2-45734389fc25"], "durationId": "8d5dfdd3-e63e-41a4-99b2-b3c15123dabc"}}, {"head": {"id": "8f245b26-11a9-45d6-90e1-523b9100ce1f", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930665900, "endTime": 12253931026000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "936082c5-96b6-4f3e-b950-c777c38f70c1", "logId": "e8c18ba4-462c-474c-9b4f-fac81558d3c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "936082c5-96b6-4f3e-b950-c777c38f70c1", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253929921200}, "additional": {"logType": "detail", "children": [], "durationId": "8f245b26-11a9-45d6-90e1-523b9100ce1f"}}, {"head": {"id": "495c11c7-b461-4587-8b5c-294b1429b9a0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930440600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ca3250-4ce8-4556-8c8d-ff46154fd6cb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d4f888-497c-45f7-bda1-94048dc16c8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0caf2929-2353-43ee-931a-6a6c967bb801", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49660926-cf10-4782-8fb2-3a29225fafe6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930756500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92356bd2-bc01-4246-aaa4-36203d0c9f65", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd15d26f-7117-4eac-8d21-6173d3ebe913", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930836500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a1900df-27ca-4f83-ae96-1ecb6d810d3d", "name": "entry : default@PreviewHookCompileResource cost memory 0.05146026611328125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930896100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "799f4109-c9f3-4303-9a44-18d0b51fa981", "name": "runTaskFromQueue task cost before running: 911 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930968200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c18ba4-462c-474c-9b4f-fac81558d3c5", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253930665900, "endTime": 12253931026000, "totalTime": 279700}, "additional": {"logType": "info", "children": [], "durationId": "8f245b26-11a9-45d6-90e1-523b9100ce1f"}}, {"head": {"id": "5d1b9ce3-87d6-46d0-a4a3-faa17ae740c5", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253933820000, "endTime": 12253940563800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "8a91f8c5-202b-4c80-b52c-f71597353d64", "logId": "ca54cfcb-56a8-47a9-9ac3-5ed887d08ab7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a91f8c5-202b-4c80-b52c-f71597353d64", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253932569900}, "additional": {"logType": "detail", "children": [], "durationId": "5d1b9ce3-87d6-46d0-a4a3-faa17ae740c5"}}, {"head": {"id": "2495e0b0-8f30-4cb9-9700-f8f41c6b247f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253933034100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "690f23da-0a05-4ce8-895e-df286ca5acd5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253933129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb81b9c-1dbe-4f28-a38a-1936e3bad9d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253933196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3795b9d-5b16-4fee-87c5-887476bf4d59", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253933830300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d649d2-c876-435e-a064-62b5b0e422bb", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253935224100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4647a9e8-b3f6-46e2-a685-fc4f808965b2", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253935337200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb76e694-e0af-41fc-884e-cec82434107e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253935425700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e79e9c1-532d-4698-a1e5-c33e3a0866af", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253935474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7367dc9-9b37-4843-99d9-d4ead0fb9a23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253935515900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df06c036-deba-434d-a889-506f34aadd07", "name": "entry : default@CopyPreviewProfile cost memory 0.22016143798828125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253940337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bea68d18-3498-4c1c-bbb9-550b6b9ab848", "name": "runTaskFromQueue task cost before running: 920 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253940496700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca54cfcb-56a8-47a9-9ac3-5ed887d08ab7", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253933820000, "endTime": 12253940563800, "totalTime": 6648200}, "additional": {"logType": "info", "children": [], "durationId": "5d1b9ce3-87d6-46d0-a4a3-faa17ae740c5"}}, {"head": {"id": "cec3c454-dc40-491a-bb40-dd21393ee5dc", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253943927600, "endTime": 12253944460800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "af7e3935-af18-4fc5-bbb0-dac99aced060", "logId": "686ab3fb-ca8e-4b9d-99f2-61c60c0d87d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af7e3935-af18-4fc5-bbb0-dac99aced060", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253942426400}, "additional": {"logType": "detail", "children": [], "durationId": "cec3c454-dc40-491a-bb40-dd21393ee5dc"}}, {"head": {"id": "0638d9b8-fcbb-4b16-9cef-d3161beb4edb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253942987100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57df9b52-e6ad-4807-ac11-a0514d385add", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253943120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19e474fc-6552-4d32-9d92-323091e13d52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253943186100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d050fdc1-a9cd-423e-803b-83fd63784cd7", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253943938500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3211ac0a-6320-4ba7-a6cb-68370a459c0f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253944039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "160ba22e-0ee8-4e33-ba94-420684bd138c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253944092800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04ebf5d-ea38-480f-ab75-ae411086a38a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253944182100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "928772eb-4aa1-47b1-ba29-e5e7a65ce646", "name": "entry : default@ReplacePreviewerPage cost memory 0.0541534423828125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253944289000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8a3c7eb-a849-4104-a57b-9696b7329ff0", "name": "runTaskFromQueue task cost before running: 924 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253944367700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "686ab3fb-ca8e-4b9d-99f2-61c60c0d87d4", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253943927600, "endTime": 12253944460800, "totalTime": 416700}, "additional": {"logType": "info", "children": [], "durationId": "cec3c454-dc40-491a-bb40-dd21393ee5dc"}}, {"head": {"id": "607c7ad1-c75f-47be-961b-ccbd68c6720c", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253946969100, "endTime": 12253947267700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "7ecd582e-371e-4c37-9baf-72331efeb2bd", "logId": "5b573aaf-69f8-4ae8-a1c3-522e502d8933"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ecd582e-371e-4c37-9baf-72331efeb2bd", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253946912200}, "additional": {"logType": "detail", "children": [], "durationId": "607c7ad1-c75f-47be-961b-ccbd68c6720c"}}, {"head": {"id": "0055cded-f3e3-469a-bdf9-4fe833199295", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253946977800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc586ff6-871b-45c9-aa87-0e7a630b9f74", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253947117800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da28f50-7874-4fa8-a697-4fb0522bd270", "name": "runTaskFromQueue task cost before running: 927 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253947207500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b573aaf-69f8-4ae8-a1c3-522e502d8933", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253946969100, "endTime": 12253947267700, "totalTime": 216800}, "additional": {"logType": "info", "children": [], "durationId": "607c7ad1-c75f-47be-961b-ccbd68c6720c"}}, {"head": {"id": "5052d0e6-be29-4ab6-a663-af9ceca7fd24", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253950255600, "endTime": 12253953975100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "3e7bbe02-1285-4d42-8701-75140b1a7e48", "logId": "9648a2fa-f707-414c-a27f-f7345806e530"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e7bbe02-1285-4d42-8701-75140b1a7e48", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253948878000}, "additional": {"logType": "detail", "children": [], "durationId": "5052d0e6-be29-4ab6-a663-af9ceca7fd24"}}, {"head": {"id": "780b1cb6-9e79-4fef-93c0-b1003230a299", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253949377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e27e5763-e1a7-444e-8d33-5e8ac2ae7448", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253949465400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc5397d-b69f-49f7-9d88-5b4309748f4b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253949515200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ddcb13f-8b3c-4725-9588-d0b1332ada05", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253950267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba1a3266-b179-4532-a588-070c7447cc2c", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253952410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5950da2-0a90-479c-9ec9-bb19e20c7771", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253952560900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6154ebaa-f6b3-4a41-b854-b9ab1d9f0698", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253952661200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e07edf65-7116-481a-89a7-c922f1293b64", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253952712500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab14700-cdf9-4e0c-a524-1aba1ee00b9c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253952752000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b694ea-3111-4aa0-b744-5bb743ad8d53", "name": "entry : default@PreviewUpdateAssets cost memory 0.142303466796875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253953755400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba68303f-94e6-4aba-88b6-95491a697974", "name": "runTaskFromQueue task cost before running: 933 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253953890700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9648a2fa-f707-414c-a27f-f7345806e530", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253950255600, "endTime": 12253953975100, "totalTime": 3611800}, "additional": {"logType": "info", "children": [], "durationId": "5052d0e6-be29-4ab6-a663-af9ceca7fd24"}}, {"head": {"id": "7d4771f6-061b-4f1a-8e6a-a121037dd847", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253965626300, "endTime": 12264218245500}, "additional": {"children": ["bb22c3ba-35fa-440d-ba5e-4cfe9721acef"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "7f122379-d439-4acd-8b75-7e61ca818c0b", "logId": "15cc82cb-c894-4363-8d62-5f92e7afe061"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f122379-d439-4acd-8b75-7e61ca818c0b", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253957454600}, "additional": {"logType": "detail", "children": [], "durationId": "7d4771f6-061b-4f1a-8e6a-a121037dd847"}}, {"head": {"id": "c0aee2c8-555f-46bb-8fcc-752002aee8a4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253958249100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc05b48-49b4-43a9-b6fe-5a85ee8b07e3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253958401100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20a8b00-494e-4c57-9a00-50f640315400", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253958472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546d8dc5-05b8-4b6d-8080-91136e95184b", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253965656700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758fec37-0109-43db-a994-33037048c798", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12254002794700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4554b81d-674a-4646-bbb4-10f93a5c9b8b", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12254002961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12254016743900, "endTime": 12264215669200}, "additional": {"children": ["d81621ec-945a-4cfe-9ea8-a13b0df694ed", "e4f67833-f584-4c32-85fa-d090a9ad7d07", "863744e9-0141-4938-9577-41e713973672", "cb60fa0f-aab4-4647-9b00-66227158b70a", "bc1de136-45fe-43aa-aa43-029ad939fa4a", "3072010a-cf9c-4d33-bfc8-97ccdda0c96a", "1e7aab02-92bd-46c6-9f62-8c391ae0e82b", "d63da29c-87b6-4b3b-8e00-a72cd6ca2302", "cb418d81-288e-4305-bcb4-8a5f41c78dc9", "58975ddb-35e9-40e8-8587-ae57137462c0", "2442e065-0c5f-4ce0-b35a-8f2b7b11929c", "b3ba1d0c-32a2-43d4-a368-9b001febfd10", "6d0f5eae-6589-4177-bac9-fd0f34daebdd"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7d4771f6-061b-4f1a-8e6a-a121037dd847", "logId": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eeb3a44c-676a-4337-8dda-dbda23ac8ae0", "name": "entry : default@PreviewArkTS cost memory 0.28255462646484375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12254019087400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02a2fb2c-c5be-4829-8904-06077d2eaa60", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12257645043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d81621ec-945a-4cfe-9ea8-a13b0df694ed", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12257645979000, "endTime": 12257645994900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "5ea579d3-fe3a-4909-b6d1-e52074f402d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ea579d3-fe3a-4909-b6d1-e52074f402d4", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12257645979000, "endTime": 12257645994900}, "additional": {"logType": "info", "children": [], "durationId": "d81621ec-945a-4cfe-9ea8-a13b0df694ed", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "c1fbae78-cb6c-4041-a2f6-3c663621197f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264208821900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f67833-f584-4c32-85fa-d090a9ad7d07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264209876400, "endTime": 12264209895100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "9ad6a820-f218-4b01-8359-d1abab54fcb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ad6a820-f218-4b01-8359-d1abab54fcb2", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264209876400, "endTime": 12264209895100}, "additional": {"logType": "info", "children": [], "durationId": "e4f67833-f584-4c32-85fa-d090a9ad7d07", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "80cb987b-40d0-4009-92c4-dccff79875b0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264209959100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863744e9-0141-4938-9577-41e713973672", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264210731800, "endTime": 12264210742900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "347546e9-2060-48eb-a70e-c8ce9bfe81bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "347546e9-2060-48eb-a70e-c8ce9bfe81bc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264210731800, "endTime": 12264210742900}, "additional": {"logType": "info", "children": [], "durationId": "863744e9-0141-4938-9577-41e713973672", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "f8c0844e-6ea9-4a46-a2d5-e399df854755", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264210790700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb60fa0f-aab4-4647-9b00-66227158b70a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264211452900, "endTime": 12264211463400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "ae1621d7-5de8-4b97-8058-d1a62fff0a8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae1621d7-5de8-4b97-8058-d1a62fff0a8c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264211452900, "endTime": 12264211463400}, "additional": {"logType": "info", "children": [], "durationId": "cb60fa0f-aab4-4647-9b00-66227158b70a", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "e0f97ece-3e1c-4e52-8a1b-e48b5e38a4d0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264211507800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc1de136-45fe-43aa-aa43-029ad939fa4a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264212187000, "endTime": 12264212200500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "0d6f3726-b231-4d73-a20b-cdea017b3064"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d6f3726-b231-4d73-a20b-cdea017b3064", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264212187000, "endTime": 12264212200500}, "additional": {"logType": "info", "children": [], "durationId": "bc1de136-45fe-43aa-aa43-029ad939fa4a", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "9ccf2e52-9405-42eb-93fc-b68cb362aee4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264212249800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3072010a-cf9c-4d33-bfc8-97ccdda0c96a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264212876000, "endTime": 12264212886400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "ecc1dac7-2067-4b0d-911c-5b9985369e74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecc1dac7-2067-4b0d-911c-5b9985369e74", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264212876000, "endTime": 12264212886400}, "additional": {"logType": "info", "children": [], "durationId": "3072010a-cf9c-4d33-bfc8-97ccdda0c96a", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "3f747aad-5467-4dc2-84b3-6ab3f3ae4262", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264212931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e7aab02-92bd-46c6-9f62-8c391ae0e82b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264213540900, "endTime": 12264213550500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "63e456ed-518e-461a-bcf6-d86974b0c2ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63e456ed-518e-461a-bcf6-d86974b0c2ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264213540900, "endTime": 12264213550500}, "additional": {"logType": "info", "children": [], "durationId": "1e7aab02-92bd-46c6-9f62-8c391ae0e82b", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "172b09e2-50f2-49fd-9856-57bbb1c56a01", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264213593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63da29c-87b6-4b3b-8e00-a72cd6ca2302", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264214193700, "endTime": 12264214202900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "2ba658d8-69b6-427e-8937-0f313ff9bfc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ba658d8-69b6-427e-8937-0f313ff9bfc3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264214193700, "endTime": 12264214202900}, "additional": {"logType": "info", "children": [], "durationId": "d63da29c-87b6-4b3b-8e00-a72cd6ca2302", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "ee645d02-2bf2-4691-b4ca-ac8688ee9597", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264214244800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb418d81-288e-4305-bcb4-8a5f41c78dc9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12264215543900, "endTime": 12264215562900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "befd097c-5302-450f-9172-72cc95e09cbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "befd097c-5302-450f-9172-72cc95e09cbf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264215543900, "endTime": 12264215562900}, "additional": {"logType": "info", "children": [], "durationId": "cb418d81-288e-4305-bcb4-8a5f41c78dc9", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "5904e4fe-57f6-480d-93f4-4b4ecc879338", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12254016743900, "endTime": 12264215669200}, "additional": {"logType": "info", "children": ["5ea579d3-fe3a-4909-b6d1-e52074f402d4", "9ad6a820-f218-4b01-8359-d1abab54fcb2", "347546e9-2060-48eb-a70e-c8ce9bfe81bc", "ae1621d7-5de8-4b97-8058-d1a62fff0a8c", "0d6f3726-b231-4d73-a20b-cdea017b3064", "ecc1dac7-2067-4b0d-911c-5b9985369e74", "63e456ed-518e-461a-bcf6-d86974b0c2ad", "2ba658d8-69b6-427e-8937-0f313ff9bfc3", "befd097c-5302-450f-9172-72cc95e09cbf", "e9974f26-93f9-4661-ac67-7b642653f4de", "acfa6879-8d71-4f3b-9a0f-65940b94c558", "955478ee-f01e-4f15-a376-83fea182c4fc", "e69f9b02-3b4a-4b66-99d2-c46d15d2472f"], "durationId": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "parent": "15cc82cb-c894-4363-8d62-5f92e7afe061"}}, {"head": {"id": "58975ddb-35e9-40e8-8587-ae57137462c0", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12256482731900, "endTime": 12257551011200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "e9974f26-93f9-4661-ac67-7b642653f4de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9974f26-93f9-4661-ac67-7b642653f4de", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12256482731900, "endTime": 12257551011200}, "additional": {"logType": "info", "children": [], "durationId": "58975ddb-35e9-40e8-8587-ae57137462c0", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "2442e065-0c5f-4ce0-b35a-8f2b7b11929c", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12257551245400, "endTime": 12257617362500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "acfa6879-8d71-4f3b-9a0f-65940b94c558"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acfa6879-8d71-4f3b-9a0f-65940b94c558", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12257551245400, "endTime": 12257617362500}, "additional": {"logType": "info", "children": [], "durationId": "2442e065-0c5f-4ce0-b35a-8f2b7b11929c", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "b3ba1d0c-32a2-43d4-a368-9b001febfd10", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12257617486900, "endTime": 12257617795900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "955478ee-f01e-4f15-a376-83fea182c4fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "955478ee-f01e-4f15-a376-83fea182c4fc", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12257617486900, "endTime": 12257617795900}, "additional": {"logType": "info", "children": [], "durationId": "b3ba1d0c-32a2-43d4-a368-9b001febfd10", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "6d0f5eae-6589-4177-bac9-fd0f34daebdd", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker4", "startTime": 12257617877400, "endTime": 12264208949700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bb22c3ba-35fa-440d-ba5e-4cfe9721acef", "logId": "e69f9b02-3b4a-4b66-99d2-c46d15d2472f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e69f9b02-3b4a-4b66-99d2-c46d15d2472f", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12257617877400, "endTime": 12264208949700}, "additional": {"logType": "info", "children": [], "durationId": "6d0f5eae-6589-4177-bac9-fd0f34daebdd", "parent": "5904e4fe-57f6-480d-93f4-4b4ecc879338"}}, {"head": {"id": "15cc82cb-c894-4363-8d62-5f92e7afe061", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253965626300, "endTime": 12264218245500, "totalTime": 10252612100}, "additional": {"logType": "info", "children": ["5904e4fe-57f6-480d-93f4-4b4ecc879338"], "durationId": "7d4771f6-061b-4f1a-8e6a-a121037dd847"}}, {"head": {"id": "cd78f258-719b-49a1-a13e-93c33206fc5b", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264222776200, "endTime": 12264223033900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ef4304a1-d212-482f-be52-3ae1befc067b", "logId": "21ac81fe-9bcb-4f84-a032-9df815b915f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef4304a1-d212-482f-be52-3ae1befc067b", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264222739300}, "additional": {"logType": "detail", "children": [], "durationId": "cd78f258-719b-49a1-a13e-93c33206fc5b"}}, {"head": {"id": "758c84a7-131f-4439-99fd-ad6100703cae", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264222786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287002b4-dd1f-4c34-996e-0122c7e0a22d", "name": "entry : PreviewBuild cost memory 0.01168060302734375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264222893700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a0a112a-0c37-4ed9-9652-b003cb0ddfed", "name": "runTaskFromQueue task cost before running: 11 s 203 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264222970600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ac81fe-9bcb-4f84-a032-9df815b915f1", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264222776200, "endTime": 12264223033900, "totalTime": 171400}, "additional": {"logType": "info", "children": [], "durationId": "cd78f258-719b-49a1-a13e-93c33206fc5b"}}, {"head": {"id": "b8cd90c0-e776-4b7d-b8eb-6d753517a624", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264232842000, "endTime": 12264232868300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "99d1bdd2-c155-42d1-b13a-510c0785d7b8", "logId": "6478c915-3aff-4398-981c-e241dae07f32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6478c915-3aff-4398-981c-e241dae07f32", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264232842000, "endTime": 12264232868300}, "additional": {"logType": "info", "children": [], "durationId": "b8cd90c0-e776-4b7d-b8eb-6d753517a624"}}, {"head": {"id": "fca4d1c7-32be-4b0b-86c2-fcc876c75480", "name": "BUILD SUCCESSFUL in 11 s 212 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264232921900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "038890b7-dd42-4415-97fd-97a0ec06c66e", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12253020942500, "endTime": 12264233175100}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 3}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "db35b8cc-a99c-4a29-9dd4-02840f80b98e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233201100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995a9013-81bb-4ba7-b3e0-816aa3a5a254", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233271200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a5433c-8dd4-4b4e-873c-f749cf2bd46e", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233337200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545b8553-6320-4798-8bd0-02f1cedc92c9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9d4831-bda5-4da6-a0c7-64645a3820fa", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233433600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "324d9cec-a0ba-4e9b-8f1f-525c9b710434", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233470700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958ea1c5-eecf-4cc7-95b1-e3e6992c61be", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264233511600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1482d56e-4ea3-4b8c-8ad3-f6523f184fe8", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264235301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca548ea-2d3c-489d-bce2-4ce28cd72457", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264249025000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9607c0a-ec51-4f54-a2ac-61d0aee7225d", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264251588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431251cc-613c-4103-a2d8-1623b5f01eb3", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264251922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440c38e0-2ea7-407f-a17b-42fcafab69a3", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264269785900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb22cbe-5f2d-49a3-b5f9-9b099e281813", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:37 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264270415300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eff0fcf-5552-46b0-9633-4e2a9f47cd42", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264270625000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ded9160-ff3b-4ea5-bbf3-3ec4cf145ed2", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264271342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efe504eb-6fed-48b7-850f-4a139cf0bb05", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264272120300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eed616b-3368-4b8c-9d25-eb2fc2a8068f", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264272472900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1013418-452f-4942-b98f-027d874da9d7", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264272752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97a6511b-0b67-409e-a836-b52bcf37ba06", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264273048400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "770836ec-d1cc-41ca-800d-ae5d75439d62", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264275866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5460576e-5ba6-42f3-ba90-68930deea0fe", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264276576100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53892592-13f4-4bf7-a2eb-36072c371fde", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264276855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "577e3ee1-bc3d-474d-b0aa-95b820981592", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264291484600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec498790-c5ef-413d-bf30-1da1c477f5ac", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264292562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa47238-b7e4-4dc3-be06-3c145724dc4f", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264292661400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "876501bf-a5b0-454e-8036-a91bf272fc65", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264292930400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3063eba-9953-434e-ab29-b29508c076e9", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264293649200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f6d1ca-8790-4973-9e06-9110d1ec72e8", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264297360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87e3f095-110e-473e-83c1-42e9b5b715c4", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264297634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a39143b5-0a44-42a0-aba3-e57e637875bf", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264297888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3467cc26-1346-47f6-a021-0c178727bb28", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264298184400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ff436b-d9ff-40de-ba9d-ed81d3677352", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 12264298469300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}