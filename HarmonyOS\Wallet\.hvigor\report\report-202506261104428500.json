{"version": "2.0", "ppid": 4888, "events": [{"head": {"id": "ce878cd2-d06b-4b88-904d-ff518870f23e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8709608289600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd25169-117a-4409-9ac2-b4ffd422cd52", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8741703500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf7d3c06-504a-48c1-a42d-4c12c7ad42c2", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8741703729300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18a820a-a599-4f3f-a0c4-7c6553e66dad", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742714513800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bff584-652e-4a9c-801b-cb8f80f42678", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742722353900, "endTime": 8742877492400}, "additional": {"children": ["ea19ef08-7c04-44f3-a3f6-61aede9899ee", "d47b8180-ca18-40df-be12-0fad2e1706fc", "8fd43b61-384a-4b60-a563-1dc4bb229362", "db2a7135-b001-4d92-b9d7-8ea1916789c8", "bc8ee33e-5e15-4745-a0a8-de12c4f729db", "d36ccc69-3a79-4a6d-895c-a3b9606d6925", "b3945488-050d-40a7-bc87-3d6abc4df3d2"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea19ef08-7c04-44f3-a3f6-61aede9899ee", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742722355800, "endTime": 8742734844200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "c1cbc6b2-46cc-479a-901d-90505d875fdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d47b8180-ca18-40df-be12-0fad2e1706fc", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742734860500, "endTime": 8742876381800}, "additional": {"children": ["1ae1bcb7-40fe-4159-b543-90a89b70c5e2", "2ad926fc-18ff-4d12-8593-36402428628c", "fd845da1-5685-4ccb-b353-5025aec6bde8", "a712ae97-f0e8-49f7-ba25-7eb8e562c561", "e1583bce-907b-4f9d-976e-0bd8a33ddec4", "48a19b8d-246b-42c2-9d64-ce52f6f60ac1", "73518f76-f959-494f-8456-6b8a88fdbd6b", "2316866b-d32b-438a-ad69-17d8a439aab6", "04e0c36a-df75-451a-b635-429053afc35f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "33eee1b8-0398-4744-a940-e4fffd27da41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fd43b61-384a-4b60-a563-1dc4bb229362", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742876403200, "endTime": 8742877484100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "48c145e6-af72-40cd-89a7-1295c8322dce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db2a7135-b001-4d92-b9d7-8ea1916789c8", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742877488100, "endTime": 8742877489200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "90ebd0a9-baa9-4a66-9aa3-1bee16641311"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc8ee33e-5e15-4745-a0a8-de12c4f729db", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742725560000, "endTime": 8742725608000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "d74c6046-df64-4467-a56d-91b2174dc76c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d74c6046-df64-4467-a56d-91b2174dc76c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742725560000, "endTime": 8742725608000}, "additional": {"logType": "info", "children": [], "durationId": "bc8ee33e-5e15-4745-a0a8-de12c4f729db", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "d36ccc69-3a79-4a6d-895c-a3b9606d6925", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742730081700, "endTime": 8742730097200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "82843996-101b-41bf-950d-56b45f473b55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82843996-101b-41bf-950d-56b45f473b55", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742730081700, "endTime": 8742730097200}, "additional": {"logType": "info", "children": [], "durationId": "d36ccc69-3a79-4a6d-895c-a3b9606d6925", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "b59836a1-f358-4557-8883-2a7acad36d1e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742730144900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a17d86b-e6b1-446a-9b5c-4f8e025b44ce", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742734722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1cbc6b2-46cc-479a-901d-90505d875fdd", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742722355800, "endTime": 8742734844200}, "additional": {"logType": "info", "children": [], "durationId": "ea19ef08-7c04-44f3-a3f6-61aede9899ee", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "1ae1bcb7-40fe-4159-b543-90a89b70c5e2", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742739684900, "endTime": 8742739691500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "49b447ca-8e8b-4651-9781-410603b96e12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ad926fc-18ff-4d12-8593-36402428628c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742739704500, "endTime": 8742744156900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "841f8890-0c5a-4b03-877f-0f252357b47a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd845da1-5685-4ccb-b353-5025aec6bde8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742744170400, "endTime": 8742817533400}, "additional": {"children": ["ea92fc92-e817-499f-bd0f-85abad3da287", "dc816394-45ff-4ae8-b24d-d35c69a967b3", "634155d7-cb91-4b72-acd6-35c797c41ff5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "032d64bc-4886-4b22-b3b6-460cb9c8ed1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a712ae97-f0e8-49f7-ba25-7eb8e562c561", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742817546200, "endTime": 8742836954100}, "additional": {"children": ["a001f036-0a87-48f3-b369-4fb38d2995dd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "08f62af5-92d7-474c-8efd-2e2e4a95360f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1583bce-907b-4f9d-976e-0bd8a33ddec4", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742836961100, "endTime": 8742849822300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "f7c9d9ff-4177-4e9a-8fcc-404e570aacfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48a19b8d-246b-42c2-9d64-ce52f6f60ac1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742850857300, "endTime": 8742862683200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "8e6270fe-2b40-4b01-98c2-7bc11abc6bc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73518f76-f959-494f-8456-6b8a88fdbd6b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742862701900, "endTime": 8742876257000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "1151ea33-4402-452b-936b-01300a8d2325"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2316866b-d32b-438a-ad69-17d8a439aab6", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742876283600, "endTime": 8742876371500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "2f0dbaa1-f450-4ba8-aa1e-d4278c1d8e0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49b447ca-8e8b-4651-9781-410603b96e12", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742739684900, "endTime": 8742739691500}, "additional": {"logType": "info", "children": [], "durationId": "1ae1bcb7-40fe-4159-b543-90a89b70c5e2", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "841f8890-0c5a-4b03-877f-0f252357b47a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742739704500, "endTime": 8742744156900}, "additional": {"logType": "info", "children": [], "durationId": "2ad926fc-18ff-4d12-8593-36402428628c", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "ea92fc92-e817-499f-bd0f-85abad3da287", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742744760600, "endTime": 8742744776900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd845da1-5685-4ccb-b353-5025aec6bde8", "logId": "b94be86f-c2fb-4927-96d2-4b4fc6e300dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b94be86f-c2fb-4927-96d2-4b4fc6e300dc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742744760600, "endTime": 8742744776900}, "additional": {"logType": "info", "children": [], "durationId": "ea92fc92-e817-499f-bd0f-85abad3da287", "parent": "032d64bc-4886-4b22-b3b6-460cb9c8ed1c"}}, {"head": {"id": "dc816394-45ff-4ae8-b24d-d35c69a967b3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742746629400, "endTime": 8742816918400}, "additional": {"children": ["c41f9ac7-83a8-4c7d-b516-4218559a11de", "a4ab19ee-a0df-48c2-80be-d1f7e5a85f39"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd845da1-5685-4ccb-b353-5025aec6bde8", "logId": "40fe5691-bcbd-4466-8930-2a1c26b40206"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c41f9ac7-83a8-4c7d-b516-4218559a11de", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742746630400, "endTime": 8742752137300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc816394-45ff-4ae8-b24d-d35c69a967b3", "logId": "cc2bf48b-3be7-4003-becf-40195682308c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4ab19ee-a0df-48c2-80be-d1f7e5a85f39", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742752153400, "endTime": 8742816907200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc816394-45ff-4ae8-b24d-d35c69a967b3", "logId": "43c0ff85-38d4-4899-9014-d9be0a814c1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c48f923f-254e-44ae-b385-3fd2a1311d33", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742746634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e44962-9432-4762-9632-277c2834e39f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742752003400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2bf48b-3be7-4003-becf-40195682308c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742746630400, "endTime": 8742752137300}, "additional": {"logType": "info", "children": [], "durationId": "c41f9ac7-83a8-4c7d-b516-4218559a11de", "parent": "40fe5691-bcbd-4466-8930-2a1c26b40206"}}, {"head": {"id": "36f38754-3b1d-4d8d-827f-ea80f287d89f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742752168100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45069443-e55e-42de-853f-11cad78fc37c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742758377300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d447d75e-a798-48ea-9105-25abc474f961", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742758517800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93e9b28-fb15-4369-8932-3d2d9b4f4ba6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742758650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85506d89-3f3c-448f-a1e7-27c2e0784b2b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742758747400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f11c2db7-2f17-45c2-bcb7-85b78394457b", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742761130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0938d1cb-f5ee-4e2b-b6cc-266958a2445b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742764985200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c8ab65-813d-4c90-9b99-7b901eaba575", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742774175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0f56a5-c710-40c9-90de-1ca558ea6e24", "name": "Sdk init in 31 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742796902400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1db2e4-4b46-409e-9dfe-d43dd30a3bbb", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742797066900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 4}, "markType": "other"}}, {"head": {"id": "36dca26b-ca6c-4c62-9481-bf8682e659cf", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742797079400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 4}, "markType": "other"}}, {"head": {"id": "970011df-4ddb-4e24-92bc-82bcfb75834f", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742816644300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb8676be-417c-467e-a740-c41a3731fcea", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742816763500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4425818-64bc-40be-95ab-350280602e0b", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742816823300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce1edd3-308b-4d3b-96d3-bf85a92a8a89", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742816865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c0ff85-38d4-4899-9014-d9be0a814c1d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742752153400, "endTime": 8742816907200}, "additional": {"logType": "info", "children": [], "durationId": "a4ab19ee-a0df-48c2-80be-d1f7e5a85f39", "parent": "40fe5691-bcbd-4466-8930-2a1c26b40206"}}, {"head": {"id": "40fe5691-bcbd-4466-8930-2a1c26b40206", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742746629400, "endTime": 8742816918400}, "additional": {"logType": "info", "children": ["cc2bf48b-3be7-4003-becf-40195682308c", "43c0ff85-38d4-4899-9014-d9be0a814c1d"], "durationId": "dc816394-45ff-4ae8-b24d-d35c69a967b3", "parent": "032d64bc-4886-4b22-b3b6-460cb9c8ed1c"}}, {"head": {"id": "634155d7-cb91-4b72-acd6-35c797c41ff5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742817511000, "endTime": 8742817523300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd845da1-5685-4ccb-b353-5025aec6bde8", "logId": "41e632af-fb32-4c29-934c-ff6029a4f1ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41e632af-fb32-4c29-934c-ff6029a4f1ce", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742817511000, "endTime": 8742817523300}, "additional": {"logType": "info", "children": [], "durationId": "634155d7-cb91-4b72-acd6-35c797c41ff5", "parent": "032d64bc-4886-4b22-b3b6-460cb9c8ed1c"}}, {"head": {"id": "032d64bc-4886-4b22-b3b6-460cb9c8ed1c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742744170400, "endTime": 8742817533400}, "additional": {"logType": "info", "children": ["b94be86f-c2fb-4927-96d2-4b4fc6e300dc", "40fe5691-bcbd-4466-8930-2a1c26b40206", "41e632af-fb32-4c29-934c-ff6029a4f1ce"], "durationId": "fd845da1-5685-4ccb-b353-5025aec6bde8", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "a001f036-0a87-48f3-b369-4fb38d2995dd", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742818104600, "endTime": 8742836942500}, "additional": {"children": ["b801fa80-2715-4ace-b4c8-bc632540ad17", "1b05210a-a2cd-4fb5-a6d0-40caf611345f", "ce4094b6-50d3-4ad3-900b-2255c9c1ac50"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a712ae97-f0e8-49f7-ba25-7eb8e562c561", "logId": "a06a5efa-f759-49b5-a4f1-153439169b20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b801fa80-2715-4ace-b4c8-bc632540ad17", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742820833700, "endTime": 8742820843700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a001f036-0a87-48f3-b369-4fb38d2995dd", "logId": "71a990cf-58dc-4d5a-b565-e97bf275ed6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71a990cf-58dc-4d5a-b565-e97bf275ed6e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742820833700, "endTime": 8742820843700}, "additional": {"logType": "info", "children": [], "durationId": "b801fa80-2715-4ace-b4c8-bc632540ad17", "parent": "a06a5efa-f759-49b5-a4f1-153439169b20"}}, {"head": {"id": "1b05210a-a2cd-4fb5-a6d0-40caf611345f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742822565000, "endTime": 8742835699900}, "additional": {"children": ["2769df5f-b01c-48d8-a594-ac648d28529b", "7cbe151b-391d-4c60-968f-5a17f96e6ccc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a001f036-0a87-48f3-b369-4fb38d2995dd", "logId": "0a33e195-6af2-410a-a877-b0609dc52b55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2769df5f-b01c-48d8-a594-ac648d28529b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742822566000, "endTime": 8742825625600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b05210a-a2cd-4fb5-a6d0-40caf611345f", "logId": "e3e237b7-d06c-4c62-b88a-91a149d4b126"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cbe151b-391d-4c60-968f-5a17f96e6ccc", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742825639200, "endTime": 8742835689000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b05210a-a2cd-4fb5-a6d0-40caf611345f", "logId": "3a6121ef-13c5-4415-b892-5cde0c8faf72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d9de144-1941-4cd2-a8d4-1b883887e243", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742822568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a0eea5-ec4a-41b8-bb91-91fc7df791cc", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742825527300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e237b7-d06c-4c62-b88a-91a149d4b126", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742822566000, "endTime": 8742825625600}, "additional": {"logType": "info", "children": [], "durationId": "2769df5f-b01c-48d8-a594-ac648d28529b", "parent": "0a33e195-6af2-410a-a877-b0609dc52b55"}}, {"head": {"id": "f46637c1-d390-4801-adc7-3db047eb9700", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742825651500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "262b8e97-4fc8-4816-9de1-023ee57beece", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832160200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "156e242d-2f5b-4c88-8872-e48fe65d93aa", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832264700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3524898a-bed6-49b8-a021-76d9927622ba", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832431800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ce7325-3f8d-496e-a41f-84055f7dd098", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832558800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37a70dbc-0735-49e7-809f-1314e3da4595", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832616400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee187e4-9a5d-442c-9f05-192e89da5902", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb68dbe6-22b7-48ba-b5a1-9fbe8d9cea28", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742832702700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ecdafd9-a436-4b17-af59-ab00fc85b057", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742835426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b00833f-71ce-41fc-8ff4-bc281fe52496", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742835546900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad2a377-6996-4765-830a-ff1fe051a352", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742835605800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8ec129-e72f-45f1-beb2-7c08a9c8c5f9", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742835648800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6121ef-13c5-4415-b892-5cde0c8faf72", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742825639200, "endTime": 8742835689000}, "additional": {"logType": "info", "children": [], "durationId": "7cbe151b-391d-4c60-968f-5a17f96e6ccc", "parent": "0a33e195-6af2-410a-a877-b0609dc52b55"}}, {"head": {"id": "0a33e195-6af2-410a-a877-b0609dc52b55", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742822565000, "endTime": 8742835699900}, "additional": {"logType": "info", "children": ["e3e237b7-d06c-4c62-b88a-91a149d4b126", "3a6121ef-13c5-4415-b892-5cde0c8faf72"], "durationId": "1b05210a-a2cd-4fb5-a6d0-40caf611345f", "parent": "a06a5efa-f759-49b5-a4f1-153439169b20"}}, {"head": {"id": "ce4094b6-50d3-4ad3-900b-2255c9c1ac50", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742836919700, "endTime": 8742836930700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a001f036-0a87-48f3-b369-4fb38d2995dd", "logId": "ccf4b030-7dfc-4d75-91fe-3b1cf0993e28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ccf4b030-7dfc-4d75-91fe-3b1cf0993e28", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742836919700, "endTime": 8742836930700}, "additional": {"logType": "info", "children": [], "durationId": "ce4094b6-50d3-4ad3-900b-2255c9c1ac50", "parent": "a06a5efa-f759-49b5-a4f1-153439169b20"}}, {"head": {"id": "a06a5efa-f759-49b5-a4f1-153439169b20", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742818104600, "endTime": 8742836942500}, "additional": {"logType": "info", "children": ["71a990cf-58dc-4d5a-b565-e97bf275ed6e", "0a33e195-6af2-410a-a877-b0609dc52b55", "ccf4b030-7dfc-4d75-91fe-3b1cf0993e28"], "durationId": "a001f036-0a87-48f3-b369-4fb38d2995dd", "parent": "08f62af5-92d7-474c-8efd-2e2e4a95360f"}}, {"head": {"id": "08f62af5-92d7-474c-8efd-2e2e4a95360f", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742817546200, "endTime": 8742836954100}, "additional": {"logType": "info", "children": ["a06a5efa-f759-49b5-a4f1-153439169b20"], "durationId": "a712ae97-f0e8-49f7-ba25-7eb8e562c561", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "124fba21-3ddd-4569-9d87-459bcf707bc2", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742849374700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb70e8d-d4f5-4ba6-b8a6-3738ae6af269", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742849727700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7c9d9ff-4177-4e9a-8fcc-404e570aacfb", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742836961100, "endTime": 8742849822300}, "additional": {"logType": "info", "children": [], "durationId": "e1583bce-907b-4f9d-976e-0bd8a33ddec4", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "04e0c36a-df75-451a-b635-429053afc35f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742850655000, "endTime": 8742850845400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d47b8180-ca18-40df-be12-0fad2e1706fc", "logId": "0b4f355d-716b-499c-9b58-a174f029409b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba24dcac-41f8-4b64-b094-55616d0f930c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742850695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4f355d-716b-499c-9b58-a174f029409b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742850655000, "endTime": 8742850845400}, "additional": {"logType": "info", "children": [], "durationId": "04e0c36a-df75-451a-b635-429053afc35f", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "cbdf7bb7-8ec8-4f19-a476-c92922690030", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742852226700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a23ff1a7-e20e-46e2-86fe-28f4f119707d", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742861765500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e6270fe-2b40-4b01-98c2-7bc11abc6bc7", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742850857300, "endTime": 8742862683200}, "additional": {"logType": "info", "children": [], "durationId": "48a19b8d-246b-42c2-9d64-ce52f6f60ac1", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "f5748ab2-65c8-462c-ba57-de02c1774ca4", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742862711900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfca2f35-b129-4643-9b38-18010103fc54", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742868159900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "624aaeca-ff85-4c66-9d36-117d568703e5", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742868253400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da6ad7f-4d4e-4c2a-b146-d32582aa843d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742868459100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592c48ea-189a-455f-85fb-56c69f333774", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742873393600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42bf87d2-2b25-45e2-afef-b140de0066e3", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742873473800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1151ea33-4402-452b-936b-01300a8d2325", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742862701900, "endTime": 8742876257000}, "additional": {"logType": "info", "children": [], "durationId": "73518f76-f959-494f-8456-6b8a88fdbd6b", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "a4d44e41-4a71-4066-90bb-770155822769", "name": "Configuration phase cost:137 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742876301200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0dbaa1-f450-4ba8-aa1e-d4278c1d8e0d", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742876283600, "endTime": 8742876371500}, "additional": {"logType": "info", "children": [], "durationId": "2316866b-d32b-438a-ad69-17d8a439aab6", "parent": "33eee1b8-0398-4744-a940-e4fffd27da41"}}, {"head": {"id": "33eee1b8-0398-4744-a940-e4fffd27da41", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742734860500, "endTime": 8742876381800}, "additional": {"logType": "info", "children": ["49b447ca-8e8b-4651-9781-410603b96e12", "841f8890-0c5a-4b03-877f-0f252357b47a", "032d64bc-4886-4b22-b3b6-460cb9c8ed1c", "08f62af5-92d7-474c-8efd-2e2e4a95360f", "f7c9d9ff-4177-4e9a-8fcc-404e570aacfb", "8e6270fe-2b40-4b01-98c2-7bc11abc6bc7", "1151ea33-4402-452b-936b-01300a8d2325", "2f0dbaa1-f450-4ba8-aa1e-d4278c1d8e0d", "0b4f355d-716b-499c-9b58-a174f029409b"], "durationId": "d47b8180-ca18-40df-be12-0fad2e1706fc", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "b3945488-050d-40a7-bc87-3d6abc4df3d2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742877462700, "endTime": 8742877475400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49bff584-652e-4a9c-801b-cb8f80f42678", "logId": "ddbc1738-31b2-45a9-97a2-5cfcee1f231d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddbc1738-31b2-45a9-97a2-5cfcee1f231d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742877462700, "endTime": 8742877475400}, "additional": {"logType": "info", "children": [], "durationId": "b3945488-050d-40a7-bc87-3d6abc4df3d2", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "48c145e6-af72-40cd-89a7-1295c8322dce", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742876403200, "endTime": 8742877484100}, "additional": {"logType": "info", "children": [], "durationId": "8fd43b61-384a-4b60-a563-1dc4bb229362", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "90ebd0a9-baa9-4a66-9aa3-1bee16641311", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742877488100, "endTime": 8742877489200}, "additional": {"logType": "info", "children": [], "durationId": "db2a7135-b001-4d92-b9d7-8ea1916789c8", "parent": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de"}}, {"head": {"id": "9f0bdaa0-0e6f-4821-a275-b70a9d0b68de", "name": "init", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742722353900, "endTime": 8742877492400}, "additional": {"logType": "info", "children": ["c1cbc6b2-46cc-479a-901d-90505d875fdd", "33eee1b8-0398-4744-a940-e4fffd27da41", "48c145e6-af72-40cd-89a7-1295c8322dce", "90ebd0a9-baa9-4a66-9aa3-1bee16641311", "d74c6046-df64-4467-a56d-91b2174dc76c", "82843996-101b-41bf-950d-56b45f473b55", "ddbc1738-31b2-45a9-97a2-5cfcee1f231d"], "durationId": "49bff584-652e-4a9c-801b-cb8f80f42678"}}, {"head": {"id": "9b38f79a-7546-4cf4-bc5f-752528407334", "name": "Configuration task cost before running: 160 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742877974100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd59648-acf1-4ac6-a784-2a14d933bd04", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742882470700, "endTime": 8742890565300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "302c225c-de9e-4436-a3a6-0e7fb8b28660", "logId": "202f987d-f4b5-4572-9611-949ddf04d8b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "302c225c-de9e-4436-a3a6-0e7fb8b28660", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742879301900}, "additional": {"logType": "detail", "children": [], "durationId": "3dd59648-acf1-4ac6-a784-2a14d933bd04"}}, {"head": {"id": "a88973a7-df8e-4545-95f2-2ba7211beefa", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742879782600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8504ce5d-be53-424f-8baa-abd72c49b8e0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742879856600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc8b90b-624a-4009-bb55-dc884ecc7812", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742879906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf941cb-d6dc-4ee2-bbd9-2931ca062359", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742882480500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b0e83d-c1ab-4e7e-af7f-d55587ad873d", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742890344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48d43724-a8ad-458c-ab16-063cea814bec", "name": "entry : default@PreBuild cost memory 0.2777557373046875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742890486500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202f987d-f4b5-4572-9611-949ddf04d8b3", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742882470700, "endTime": 8742890565300}, "additional": {"logType": "info", "children": [], "durationId": "3dd59648-acf1-4ac6-a784-2a14d933bd04"}}, {"head": {"id": "e9b94127-7199-4cda-a8a9-3cb7ef3fd93e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742896558600, "endTime": 8742899762900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "054ef87b-2e18-4ca2-b6a5-3301197c2f84", "logId": "de9be7fe-a4ab-47f8-9a37-d1befb1b1027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "054ef87b-2e18-4ca2-b6a5-3301197c2f84", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742894676800}, "additional": {"logType": "detail", "children": [], "durationId": "e9b94127-7199-4cda-a8a9-3cb7ef3fd93e"}}, {"head": {"id": "9c364067-67d4-40fd-9673-07d40e3f073a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742895617800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad49b12c-b4a8-410f-acf9-66b1c0036ab3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742895752800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50cd04c4-b4e6-492d-9be6-3d5345181246", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742895821000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5d3bab5-912a-47c3-80c6-805fbbd8c97b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742896569900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4ccd34-4c00-4b9b-81a7-877726c7021f", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742899578400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb749c8e-736a-4dec-b1c3-7ca91831a22a", "name": "entry : default@MergeProfile cost memory 0.127899169921875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742899691200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9be7fe-a4ab-47f8-9a37-d1befb1b1027", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742896558600, "endTime": 8742899762900}, "additional": {"logType": "info", "children": [], "durationId": "e9b94127-7199-4cda-a8a9-3cb7ef3fd93e"}}, {"head": {"id": "cbecaebf-41e1-4564-bc20-102661a7445b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742902952900, "endTime": 8742905202200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3b476c7c-67b1-4e53-a8ca-9f549fea1c0b", "logId": "a47353a8-4aa8-40fd-867c-eb99694a58ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b476c7c-67b1-4e53-a8ca-9f549fea1c0b", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742901499500}, "additional": {"logType": "detail", "children": [], "durationId": "cbecaebf-41e1-4564-bc20-102661a7445b"}}, {"head": {"id": "b80fc595-8447-4113-a99f-00eca0f235dd", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742902029600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded3a96b-47f6-497d-9e9a-3f78f619d3f9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742902140900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9ce67e-36fb-4712-9f7d-df869876e382", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742902197900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99fc192d-3429-4b99-b6f9-8b9682ff5a35", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742902961400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6830e648-0972-46ef-b192-18799d7c30f0", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742903810200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c4aa4e-f53b-4a96-8f16-a075b77890ee", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742905007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd966324-4c06-4507-b275-f9f12fef366b", "name": "entry : default@CreateBuildProfile cost memory 0.09662628173828125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742905110000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47353a8-4aa8-40fd-867c-eb99694a58ca", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742902952900, "endTime": 8742905202200}, "additional": {"logType": "info", "children": [], "durationId": "cbecaebf-41e1-4564-bc20-102661a7445b"}}, {"head": {"id": "5d0f95b0-3229-49c8-930d-e3000ef02d2a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907877100, "endTime": 8742908351600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d2187010-d0e7-4a06-ab17-35a2c6c35149", "logId": "331651b4-d0d0-441e-8ef8-9e0a9cfdf804"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2187010-d0e7-4a06-ab17-35a2c6c35149", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742906590900}, "additional": {"logType": "detail", "children": [], "durationId": "5d0f95b0-3229-49c8-930d-e3000ef02d2a"}}, {"head": {"id": "76f3d01f-f59d-4a2d-a62d-fa9c51b5beb1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12b724eb-d7fe-4832-a0f5-2f29aa3435eb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907158300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "416a546c-879b-45b6-ac64-28fd48786fe8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39790bc0-fd6e-45f9-9ef6-dab0e9d76323", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907884700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c47067c-0a6a-426c-b609-350ab04cc3f7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907984700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "511810c0-a968-45ea-b175-e0d05347ad6a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742908033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10aac096-05ed-46f3-8c6e-370f7088f35a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742908081200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f30ff97-8e80-4abd-8434-3f7927bb1625", "name": "entry : default@PreCheckSyscap cost memory 0.05034637451171875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742908164900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51dd3880-7f78-42f1-a6aa-8210104430d1", "name": "runTaskFromQueue task cost before running: 191 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742908297800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331651b4-d0d0-441e-8ef8-9e0a9cfdf804", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742907877100, "endTime": 8742908351600, "totalTime": 401000}, "additional": {"logType": "info", "children": [], "durationId": "5d0f95b0-3229-49c8-930d-e3000ef02d2a"}}, {"head": {"id": "c3ecb883-b5e0-4dc0-a249-4bd566cdc746", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742918579600, "endTime": 8742919514200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e382cbef-0c40-4c02-a449-8e5d6464d36b", "logId": "178ec05f-623a-4f7f-b4d6-ecd3226a354a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e382cbef-0c40-4c02-a449-8e5d6464d36b", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742909989800}, "additional": {"logType": "detail", "children": [], "durationId": "c3ecb883-b5e0-4dc0-a249-4bd566cdc746"}}, {"head": {"id": "dbe4af38-9a23-44e1-9a6d-e8e4ca30c4d7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742911073700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fc202e4-3deb-426f-9a89-8bb644ca988d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742911166000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a5abc7-f120-4d01-ac68-17c171f6664f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742911217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9360ff8-36b9-4aa0-912d-185ff8a4eec9", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742918591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780e9af5-30e6-43d3-8fc1-eaec254ba3aa", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742918778800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc1edb9-6118-4a10-b5a6-34989a84bd10", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742919364300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3e4405e-e548-4d14-abd2-e14901663f2f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0686798095703125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742919451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "178ec05f-623a-4f7f-b4d6-ecd3226a354a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742918579600, "endTime": 8742919514200}, "additional": {"logType": "info", "children": [], "durationId": "c3ecb883-b5e0-4dc0-a249-4bd566cdc746"}}, {"head": {"id": "c2be9433-4d74-4b78-8e8b-fe1219bbbdf2", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742922638500, "endTime": 8742923674800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e4186d20-4318-4591-96a3-e01252a1ce6e", "logId": "4143100c-83ff-4ad4-8d03-f1e6af4433d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4186d20-4318-4591-96a3-e01252a1ce6e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742920949200}, "additional": {"logType": "detail", "children": [], "durationId": "c2be9433-4d74-4b78-8e8b-fe1219bbbdf2"}}, {"head": {"id": "a12fa1cd-3ed6-448c-aecb-17f7ec7d0e40", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742921472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d65eaff-da2e-4dd4-bb7d-2b45396f2e24", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742921547300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5e710d-0fe3-486f-8957-7ec8ae62b190", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742921595700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f7a79c5-91ec-4737-99d6-ddbef0a49c78", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742922644600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf85c16-38ab-42e3-862c-61f86e6c54ca", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742923531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0695c972-afe5-4dbf-bf94-7fe7e7d3681b", "name": "entry : default@ProcessProfile cost memory 0.0562286376953125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742923613900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4143100c-83ff-4ad4-8d03-f1e6af4433d2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742922638500, "endTime": 8742923674800}, "additional": {"logType": "info", "children": [], "durationId": "c2be9433-4d74-4b78-8e8b-fe1219bbbdf2"}}, {"head": {"id": "7f9edf91-df0f-4554-a30f-a7caa9c30686", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742927251100, "endTime": 8742933162700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0800b52b-cb43-4f62-8364-dfd19330fac7", "logId": "5694bd5c-d1f9-46ac-80fa-199b029c8486"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0800b52b-cb43-4f62-8364-dfd19330fac7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742925015400}, "additional": {"logType": "detail", "children": [], "durationId": "7f9edf91-df0f-4554-a30f-a7caa9c30686"}}, {"head": {"id": "321e8e50-e0b0-40fe-8c80-07dcd6ac3b7b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742925514500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efac9137-01b4-47a7-a17e-551fdd9decc5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742925600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d1ace8-4a23-4169-9bd8-fa5022b137ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742925650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6655ab7-59d9-426f-bd5e-c988703654bb", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742927259600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf17573a-eb40-4870-976d-b1d5502051b5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742932977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c66cff9-b2d7-4954-be3c-9fc38fb96c9a", "name": "entry : default@ProcessRouterMap cost memory 0.207427978515625", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742933095400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5694bd5c-d1f9-46ac-80fa-199b029c8486", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742927251100, "endTime": 8742933162700}, "additional": {"logType": "info", "children": [], "durationId": "7f9edf91-df0f-4554-a30f-a7caa9c30686"}}, {"head": {"id": "47bd80a2-dedc-45df-acec-709955278d11", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742939756500, "endTime": 8742942614700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7068a216-70f1-4ae2-bf0b-c136712a3637", "logId": "a110123e-11e8-4c38-8b94-2c837922ef80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7068a216-70f1-4ae2-bf0b-c136712a3637", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742935892900}, "additional": {"logType": "detail", "children": [], "durationId": "47bd80a2-dedc-45df-acec-709955278d11"}}, {"head": {"id": "a9bb1e1d-cc5e-4e91-9fc0-bdaeb7e863ef", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742936378900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e003d8d0-dcd1-42e3-a32a-020f3ee2e846", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742936457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e6504ca-31d9-4f88-94c6-45aeab185698", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742936506800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1775a749-1331-4240-9531-febdfc044977", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742937390800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc64cabb-e21f-40d3-a272-b43c112d986f", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742940949000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d4e08c-a7dd-4403-9131-e8e12edebc41", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742941083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3d2acef-b48f-4882-8f69-2d773f86c6e8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742941139500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2515a4-3611-43b6-a6a2-70c3dda0c889", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742941182700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f91e84-adbe-48d1-b41f-004352d92a8a", "name": "entry : default@PreviewProcessResource cost memory 0.0882415771484375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742941253800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70a00ae0-0014-42dd-9b40-9d8892e22a89", "name": "runTaskFromQueue task cost before running: 225 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742942528300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a110123e-11e8-4c38-8b94-2c837922ef80", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742939756500, "endTime": 8742942614700, "totalTime": 1553100}, "additional": {"logType": "info", "children": [], "durationId": "47bd80a2-dedc-45df-acec-709955278d11"}}, {"head": {"id": "86b72824-3e39-4c12-a7b8-ca0f5d465552", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742949816400, "endTime": 8742970077800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "798867b5-af5a-41ec-b651-b0691a66a9b9", "logId": "a77e0b46-c7c4-40c8-9adf-abaf7cbb85a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "798867b5-af5a-41ec-b651-b0691a66a9b9", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742945821300}, "additional": {"logType": "detail", "children": [], "durationId": "86b72824-3e39-4c12-a7b8-ca0f5d465552"}}, {"head": {"id": "d96d7406-10d7-4172-8acc-e6f9ef4354b4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742946312800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d54740-1935-4d1f-be36-ebf90c34506c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742946389300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b3c7e86-ffe7-404e-a3de-637edb9d177e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742946436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3167fbe3-1683-4ba6-80d6-9a329a11de15", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742949830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a76db37-3ef9-4ac9-a16b-55d3891635e9", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742969858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe98b36f-e907-414c-ae93-e13061d86fcb", "name": "entry : default@GenerateLoaderJson cost memory 0.8206710815429688", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742969997800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a77e0b46-c7c4-40c8-9adf-abaf7cbb85a6", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742949816400, "endTime": 8742970077800}, "additional": {"logType": "info", "children": [], "durationId": "86b72824-3e39-4c12-a7b8-ca0f5d465552"}}, {"head": {"id": "43b5d648-a0e5-4aba-befe-33be4c1c4618", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742981424700, "endTime": 8743643674000}, "additional": {"children": ["14e9bc5b-2216-4ca0-9a4f-2a95b356ef0d", "7b756b48-d085-4bd9-9d00-b96fa45286cd", "ef1c486b-fbea-4609-a7c3-c4da1fb783da", "81c64de8-155f-4196-a9ec-5dd3b5f36584", "d2a1c911-c711-475c-a9ef-57fffe9fd0a4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "9f0f5d68-0858-4954-93a8-651a563d4b9d", "logId": "19bba38c-3613-40ed-8177-fb1e20adcaec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f0f5d68-0858-4954-93a8-651a563d4b9d", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742977320000}, "additional": {"logType": "detail", "children": [], "durationId": "43b5d648-a0e5-4aba-befe-33be4c1c4618"}}, {"head": {"id": "41b8da73-8217-41b2-ab67-2af281552bda", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742977843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ca863e-fdeb-442d-8ef2-339dd11d31da", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742977944300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58514ddd-ad05-40da-bd31-3acb667f8231", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742978000700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53607209-3584-4219-af5e-9fc64b6bd9a1", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742978896400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38e2229-4401-4331-b737-68b8fe96408d", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742981532800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00cbf17-9cb1-4ef2-bdf7-8562bb48a9c5", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743022125800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6af1a82-de1c-4b6f-b2ba-87bd495ebfdd", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 40 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743022324200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e9bc5b-2216-4ca0-9a4f-2a95b356ef0d", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743023534100, "endTime": 8743051209700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "43b5d648-a0e5-4aba-befe-33be4c1c4618", "logId": "04c5816d-b56e-4c5f-86af-6c029ba553f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04c5816d-b56e-4c5f-86af-6c029ba553f8", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743023534100, "endTime": 8743051209700}, "additional": {"logType": "info", "children": [], "durationId": "14e9bc5b-2216-4ca0-9a4f-2a95b356ef0d", "parent": "19bba38c-3613-40ed-8177-fb1e20adcaec"}}, {"head": {"id": "f55ea1a3-d412-41f6-9198-72dd8b62e887", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743051496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b756b48-d085-4bd9-9d00-b96fa45286cd", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743052393300, "endTime": 8743182491100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "43b5d648-a0e5-4aba-befe-33be4c1c4618", "logId": "8577f44f-d181-4907-8917-e7a1c43469fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c2b5376-b657-4fac-b791-d8fc6a5a1479", "name": "current process  memoryUsage: {\n  rss: 173621248,\n  heapTotal: 116686848,\n  heapUsed: 108565048,\n  external: 3125619,\n  arrayBuffers: 101629\n} os memoryUsage :11.879871368408203", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743053668400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4235fc4a-3605-4cca-830c-7ccea587bf0f", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743179900900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8577f44f-d181-4907-8917-e7a1c43469fd", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743052393300, "endTime": 8743182491100}, "additional": {"logType": "info", "children": [], "durationId": "7b756b48-d085-4bd9-9d00-b96fa45286cd", "parent": "19bba38c-3613-40ed-8177-fb1e20adcaec"}}, {"head": {"id": "ed28130c-c37a-4ca7-b20a-68d5ff45f210", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743182665900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef1c486b-fbea-4609-a7c3-c4da1fb783da", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743184012900, "endTime": 8743336547800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "43b5d648-a0e5-4aba-befe-33be4c1c4618", "logId": "1972b54f-59c6-4669-bcbb-b56be2c76770"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a0b1a37-739c-4697-8d40-8d072cc720f1", "name": "current process  memoryUsage: {\n  rss: 173666304,\n  heapTotal: 116686848,\n  heapUsed: 108848424,\n  external: 3107886,\n  arrayBuffers: 101770\n} os memoryUsage :11.880451202392578", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743184917500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "305b3b1f-57da-4b33-954d-db06b50612e4", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743334098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1972b54f-59c6-4669-bcbb-b56be2c76770", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743184012900, "endTime": 8743336547800}, "additional": {"logType": "info", "children": [], "durationId": "ef1c486b-fbea-4609-a7c3-c4da1fb783da", "parent": "19bba38c-3613-40ed-8177-fb1e20adcaec"}}, {"head": {"id": "b56d9e3f-0281-49bf-bbe1-0e74dd65a204", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743336676800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81c64de8-155f-4196-a9ec-5dd3b5f36584", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743337780100, "endTime": 8743467879300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "43b5d648-a0e5-4aba-befe-33be4c1c4618", "logId": "98837080-1bda-4c9c-8715-e2a452e919ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78000e05-a39f-493b-858f-5f457a55b141", "name": "current process  memoryUsage: {\n  rss: 173686784,\n  heapTotal: 116686848,\n  heapUsed: 109135032,\n  external: 3108012,\n  arrayBuffers: 101960\n} os memoryUsage :11.891040802001953", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743338636100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b509264-8737-4462-920d-32dd0cac46a2", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743465733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98837080-1bda-4c9c-8715-e2a452e919ec", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743337780100, "endTime": 8743467879300}, "additional": {"logType": "info", "children": [], "durationId": "81c64de8-155f-4196-a9ec-5dd3b5f36584", "parent": "19bba38c-3613-40ed-8177-fb1e20adcaec"}}, {"head": {"id": "4b0f126a-cefa-40f5-860a-c2a0738524bc", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743468085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a1c911-c711-475c-a9ef-57fffe9fd0a4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743468970800, "endTime": 8743642505100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "43b5d648-a0e5-4aba-befe-33be4c1c4618", "logId": "d154b628-d842-4a36-98be-373f2e97b080"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "637f6bfe-c478-4c6e-b3aa-03626b589330", "name": "current process  memoryUsage: {\n  rss: 173699072,\n  heapTotal: 116686848,\n  heapUsed: 109650336,\n  external: 3116330,\n  arrayBuffers: 111197\n} os memoryUsage :11.89310073852539", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743469678400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c648f6-0b57-4ef8-b77f-ce89751134b9", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743639233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d154b628-d842-4a36-98be-373f2e97b080", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743468970800, "endTime": 8743642505100}, "additional": {"logType": "info", "children": [], "durationId": "d2a1c911-c711-475c-a9ef-57fffe9fd0a4", "parent": "19bba38c-3613-40ed-8177-fb1e20adcaec"}}, {"head": {"id": "eba05496-382e-4c96-932f-fcf922e47b39", "name": "entry : default@PreviewCompileResource cost memory -0.5058822631835938", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743643414800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "276942fe-fba9-4337-a632-312c61f83d16", "name": "runTaskFromQueue task cost before running: 926 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743643594500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19bba38c-3613-40ed-8177-fb1e20adcaec", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742981424700, "endTime": 8743643674000, "totalTime": 662119500}, "additional": {"logType": "info", "children": ["04c5816d-b56e-4c5f-86af-6c029ba553f8", "8577f44f-d181-4907-8917-e7a1c43469fd", "1972b54f-59c6-4669-bcbb-b56be2c76770", "98837080-1bda-4c9c-8715-e2a452e919ec", "d154b628-d842-4a36-98be-373f2e97b080"], "durationId": "43b5d648-a0e5-4aba-befe-33be4c1c4618"}}, {"head": {"id": "6f195eb8-49c1-40f9-a60e-e4d1c20217e1", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646827100, "endTime": 8743647254600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6326c869-05e8-4b79-abd2-a58a4737cc2f", "logId": "8512afa1-d273-425f-a527-31ef36b6f96a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6326c869-05e8-4b79-abd2-a58a4737cc2f", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646094800}, "additional": {"logType": "detail", "children": [], "durationId": "6f195eb8-49c1-40f9-a60e-e4d1c20217e1"}}, {"head": {"id": "7475f15c-8507-405d-b5b9-2f6d024964a3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76217d86-f158-4dbe-8cce-d78ab281d685", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646695400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2cc8dc-fa55-4826-8e40-134432a7fca5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646747400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d44c26b-da8d-456c-b86d-7bf48bddb811", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646834700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb88b5d3-ec22-4635-9607-28859c43e9cb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646919600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4ebd14-9f6b-4058-9198-02ee8cb3b911", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646966000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8925b8f4-bf1b-4b3c-8337-fe11b6da3413", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743647005500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19825634-771b-4006-b06f-7e3c26f1aa9c", "name": "entry : default@PreviewHookCompileResource cost memory 0.05146026611328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743647127900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c53d007-92a4-4270-ba35-f926a43d91e7", "name": "runTaskFromQueue task cost before running: 929 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743647202600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8512afa1-d273-425f-a527-31ef36b6f96a", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743646827100, "endTime": 8743647254600, "totalTime": 358100}, "additional": {"logType": "info", "children": [], "durationId": "6f195eb8-49c1-40f9-a60e-e4d1c20217e1"}}, {"head": {"id": "219039d1-2b8f-48d4-8ff8-07df753c0479", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743650350000, "endTime": 8743657449800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "730bdef4-7b38-4465-bfba-5b8ca8903119", "logId": "3f4f5421-d250-43db-9a78-c43bffc5185b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "730bdef4-7b38-4465-bfba-5b8ca8903119", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743649025600}, "additional": {"logType": "detail", "children": [], "durationId": "219039d1-2b8f-48d4-8ff8-07df753c0479"}}, {"head": {"id": "919d0f09-a766-44ea-94d9-db1c5fd0e935", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743649591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e18ed2f-1703-410a-b83d-30bef9342c75", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743649691800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac84d6cc-4285-49bb-824b-61b64a6dc131", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743649744600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e810341-9d9d-4cc2-b94d-2df6c156b982", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743650358700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47eb8c40-fdca-4c0d-a1fb-100b75a951ce", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743651824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365170ca-b381-4e10-8d25-b8e64c340ab7", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743651932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c2435a5-bb79-4990-9521-20feaafe5450", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743652033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe33364c-9d80-4539-8868-e4b922f98233", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743652081500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9c9a53-bddc-47b4-a718-4e38acbe0577", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743652121200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84095117-be5f-4d85-a63a-5be08e7b9b69", "name": "entry : default@CopyPreviewProfile cost memory 0.2236175537109375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743657154000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b852aaf2-04e4-4659-a45d-f9d7ff6a2b0e", "name": "runTaskFromQueue task cost before running: 940 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743657350400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f4f5421-d250-43db-9a78-c43bffc5185b", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743650350000, "endTime": 8743657449800, "totalTime": 6960400}, "additional": {"logType": "info", "children": [], "durationId": "219039d1-2b8f-48d4-8ff8-07df753c0479"}}, {"head": {"id": "b2bdf8d6-706b-4813-ab7f-d661a6bc7407", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661648800, "endTime": 8743662094700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "3195b73c-aa3c-42dd-8fb3-f77e134d29ce", "logId": "ffc2f4e2-7be2-4821-8a79-71ecf77f94b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3195b73c-aa3c-42dd-8fb3-f77e134d29ce", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743659572600}, "additional": {"logType": "detail", "children": [], "durationId": "b2bdf8d6-706b-4813-ab7f-d661a6bc7407"}}, {"head": {"id": "fa607202-075b-47e2-95a0-a15672373c04", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743660127000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b99c0dc-9d8e-4499-800f-9f8b3630ac9a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743660229100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba4f73d-75cd-456c-b1dd-6235c17f6c6b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743660814900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42267528-eb0d-4098-a412-1345bbfa9b9d", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661658400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899a88c5-7e1d-4082-9209-61b1ee3db493", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661773100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8337eca2-51f9-460b-bad1-3058665b3e81", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661825300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5925f62f-a89a-4762-83ee-54c76acf913d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661869300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f9d2c3f-82df-40c9-a622-8608869dee24", "name": "entry : default@ReplacePreviewerPage cost memory 0.0514373779296875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661965500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f123210b-0fb3-4ca7-b442-6aecf05226e2", "name": "runTaskFromQueue task cost before running: 944 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743662043800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffc2f4e2-7be2-4821-8a79-71ecf77f94b9", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743661648800, "endTime": 8743662094700, "totalTime": 378100}, "additional": {"logType": "info", "children": [], "durationId": "b2bdf8d6-706b-4813-ab7f-d661a6bc7407"}}, {"head": {"id": "161ac734-458c-466b-b8f7-aa8d25f9abe3", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743663667200, "endTime": 8743663929000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "84d0ac36-835e-49b0-aa5b-3dac10ba0da2", "logId": "3bf26392-f2ca-4a71-b930-f1d0a417391f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84d0ac36-835e-49b0-aa5b-3dac10ba0da2", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743663623100}, "additional": {"logType": "detail", "children": [], "durationId": "161ac734-458c-466b-b8f7-aa8d25f9abe3"}}, {"head": {"id": "163d262c-7f37-4f80-8db8-7408c8c3ee6b", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743663675400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e58415ba-52e4-49e4-87c5-56594087041d", "name": "entry : buildPreviewerResource cost memory 0.01180267333984375", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743663792500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b05e2de9-bd3e-459b-82c7-ef3a979b0e2c", "name": "runTaskFromQueue task cost before running: 946 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743663874000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bf26392-f2ca-4a71-b930-f1d0a417391f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743663667200, "endTime": 8743663929000, "totalTime": 189400}, "additional": {"logType": "info", "children": [], "durationId": "161ac734-458c-466b-b8f7-aa8d25f9abe3"}}, {"head": {"id": "1787bad1-b891-4b91-a6da-c0853b9a86f9", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743666846500, "endTime": 8743669898300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "a39fd56e-62fb-4d6a-b137-9bd56724a0cb", "logId": "861a7339-4489-4104-93e2-309631773e6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a39fd56e-62fb-4d6a-b137-9bd56724a0cb", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743665443800}, "additional": {"logType": "detail", "children": [], "durationId": "1787bad1-b891-4b91-a6da-c0853b9a86f9"}}, {"head": {"id": "a0aa2280-31dd-4268-8112-e0de4e7114f5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743665968500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c5216ff-e51f-41e7-aa45-dc82e8c764a1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743666094100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdc0ee5c-75bb-46ca-940c-82186212ee86", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743666155600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dde3a18-941d-4f6a-9af0-29fdbfec7eee", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743666854300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34c2dfc-5e02-42b9-a644-701ec63cee76", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743668668000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b4f9a19-0181-4202-ac6a-7ae48b104422", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743668763100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d0ff26-221f-42c1-b570-9658fe663901", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743668837000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c365405a-cecb-4d58-8e13-2376e570d6e3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743668881600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65554ddc-e074-4b1f-97c7-a1b8302987bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743668919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80110aa8-b4d5-452d-8a13-fdbbb8581011", "name": "entry : default@PreviewUpdateAssets cost memory 0.149383544921875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743669747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e53e61f-7080-4058-a555-d57b5f78914d", "name": "runTaskFromQueue task cost before running: 952 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743669843300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "861a7339-4489-4104-93e2-309631773e6f", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743666846500, "endTime": 8743669898300, "totalTime": 2981000}, "additional": {"logType": "info", "children": [], "durationId": "1787bad1-b891-4b91-a6da-c0853b9a86f9"}}, {"head": {"id": "d994f6f8-aad6-4a66-8d33-91055dde97aa", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743677752700, "endTime": 8754037394300}, "additional": {"children": ["834cec69-35ef-4252-9671-3265bc16dab9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "13bf6bed-b1cf-48a9-91c4-897b4b345d2a", "logId": "4a4d5485-8455-4bed-a465-7d12d2776a1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13bf6bed-b1cf-48a9-91c4-897b4b345d2a", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743671999700}, "additional": {"logType": "detail", "children": [], "durationId": "d994f6f8-aad6-4a66-8d33-91055dde97aa"}}, {"head": {"id": "df1f911d-6d0b-4832-a027-618646dd09c3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743672531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee6a04b7-699d-46b6-8fad-aca8376730e2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743672623300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfe8bd4-2417-4bc9-bd88-ae2f33622ca3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743672675700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f63b8ac-f6a8-47ec-baa4-85ad9da3bdba", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743677764200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8918965f-e862-42ff-9d3b-144253859e67", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743711202600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab003d5a-6287-4676-a1ef-0c2fa4b626a6", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743711348800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834cec69-35ef-4252-9671-3265bc16dab9", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8743725388200, "endTime": 8754033351000}, "additional": {"children": ["22ab0ca4-f6e3-42bc-89aa-4e4b5ab72547", "1d742cff-dfda-494a-9650-acbe7b877ea6", "820de2ec-3c50-41e5-9b8b-9c3ac1e13b2a", "62d590fe-6eac-497c-b640-327ec9bf9f48", "b5d3c7d1-c3d3-4542-ae14-a38dd89bff49", "e72f4ffa-38b0-471f-9547-f27ceb547422", "1a73b32d-4ad8-43b2-8f86-22bea380e20a", "aa23a836-7b18-46c6-9a2f-f00bb3483f36", "d7e8526e-05f0-4b9f-a577-3b086fe5d1e9", "187db5eb-ed25-4e70-9f69-b86eae460984", "bc76eac1-a5f0-4869-aefc-1302d350f71d", "931d33bd-63c1-4a1e-95fb-5e922d9b44b8", "d09ec127-b55c-44f1-af56-08b48722ed24"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d994f6f8-aad6-4a66-8d33-91055dde97aa", "logId": "6c566421-fb1d-4c4b-8398-de7198d0923c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07b77681-8c18-4dd5-85dd-54b5d1380377", "name": "entry : default@PreviewArkTS cost memory 0.3988494873046875", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743727712600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ab214f-94c4-4f48-82d3-933a9c685776", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8747148187700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ab0ca4-f6e3-42bc-89aa-4e4b5ab72547", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8747149234600, "endTime": 8747149255300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "7c9c787e-9980-4864-8a07-2d7ae1f9ccb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c9c787e-9980-4864-8a07-2d7ae1f9ccb6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8747149234600, "endTime": 8747149255300}, "additional": {"logType": "info", "children": [], "durationId": "22ab0ca4-f6e3-42bc-89aa-4e4b5ab72547", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "72ee2e60-9cd3-4cbe-b819-dd772e843d89", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754022870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d742cff-dfda-494a-9650-acbe7b877ea6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754024445200, "endTime": 8754024469400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "6cba1a36-1746-4fc7-965d-42ce98df798e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cba1a36-1746-4fc7-965d-42ce98df798e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754024445200, "endTime": 8754024469400}, "additional": {"logType": "info", "children": [], "durationId": "1d742cff-dfda-494a-9650-acbe7b877ea6", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "51f29ada-2d44-43f3-ae4c-2d2cef99b35e", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754024556000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "820de2ec-3c50-41e5-9b8b-9c3ac1e13b2a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754025465700, "endTime": 8754025484100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "3b700e29-1cd4-4d38-99d8-9b2739a1a78c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b700e29-1cd4-4d38-99d8-9b2739a1a78c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754025465700, "endTime": 8754025484100}, "additional": {"logType": "info", "children": [], "durationId": "820de2ec-3c50-41e5-9b8b-9c3ac1e13b2a", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "ef19e9ba-c831-45a0-b3b6-1502c6610837", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754025551500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d590fe-6eac-497c-b640-327ec9bf9f48", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754026702700, "endTime": 8754026732900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "93bc7338-422a-4d50-b35a-3489ec2305ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93bc7338-422a-4d50-b35a-3489ec2305ee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754026702700, "endTime": 8754026732900}, "additional": {"logType": "info", "children": [], "durationId": "62d590fe-6eac-497c-b640-327ec9bf9f48", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "a85335cb-7438-40b3-a4a9-0b051231594a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754026831300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d3c7d1-c3d3-4542-ae14-a38dd89bff49", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754028285000, "endTime": 8754028312800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "c3a895cc-6b5d-48a8-a115-8360ae5b9bd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3a895cc-6b5d-48a8-a115-8360ae5b9bd5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754028285000, "endTime": 8754028312800}, "additional": {"logType": "info", "children": [], "durationId": "b5d3c7d1-c3d3-4542-ae14-a38dd89bff49", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "77d81673-a5f7-4946-96fe-3615bc0b477b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754028412600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72f4ffa-38b0-471f-9547-f27ceb547422", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754029534000, "endTime": 8754029553100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "29628af7-129c-43f2-9dad-b364ff3e9705"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29628af7-129c-43f2-9dad-b364ff3e9705", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754029534000, "endTime": 8754029553100}, "additional": {"logType": "info", "children": [], "durationId": "e72f4ffa-38b0-471f-9547-f27ceb547422", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "1e3d2211-cf03-4479-a12e-b2c35099991c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754029628200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a73b32d-4ad8-43b2-8f86-22bea380e20a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754030666200, "endTime": 8754030683500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "cf5707c2-627f-4b22-9d81-fdea0d690c66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf5707c2-627f-4b22-9d81-fdea0d690c66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754030666200, "endTime": 8754030683500}, "additional": {"logType": "info", "children": [], "durationId": "1a73b32d-4ad8-43b2-8f86-22bea380e20a", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "1ebad27d-d6a6-4a28-a67f-011237e45775", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754030775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa23a836-7b18-46c6-9a2f-f00bb3483f36", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754031892000, "endTime": 8754031912900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "1114501f-ebcd-4d24-af96-002212409e0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1114501f-ebcd-4d24-af96-002212409e0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754031892000, "endTime": 8754031912900}, "additional": {"logType": "info", "children": [], "durationId": "aa23a836-7b18-46c6-9a2f-f00bb3483f36", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "9853d48a-62fb-48d2-82b2-ed0d74f7551a", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754032004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e8526e-05f0-4b9f-a577-3b086fe5d1e9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8754033214000, "endTime": 8754033238300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "53a4ff6a-3e43-41c5-bd97-01d8d760ddf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53a4ff6a-3e43-41c5-bd97-01d8d760ddf7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754033214000, "endTime": 8754033238300}, "additional": {"logType": "info", "children": [], "durationId": "d7e8526e-05f0-4b9f-a577-3b086fe5d1e9", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "6c566421-fb1d-4c4b-8398-de7198d0923c", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8743725388200, "endTime": 8754033351000}, "additional": {"logType": "info", "children": ["7c9c787e-9980-4864-8a07-2d7ae1f9ccb6", "6cba1a36-1746-4fc7-965d-42ce98df798e", "3b700e29-1cd4-4d38-99d8-9b2739a1a78c", "93bc7338-422a-4d50-b35a-3489ec2305ee", "c3a895cc-6b5d-48a8-a115-8360ae5b9bd5", "29628af7-129c-43f2-9dad-b364ff3e9705", "cf5707c2-627f-4b22-9d81-fdea0d690c66", "1114501f-ebcd-4d24-af96-002212409e0e", "53a4ff6a-3e43-41c5-bd97-01d8d760ddf7", "540b28ef-c493-441b-87f3-b79e4c6d1557", "5e77394a-5b06-4a07-845d-2a45d9fb38df", "d315dcd5-52ee-4808-b62e-1f629f1b528f", "4b081ba9-fba3-40ff-a6f9-386389bd38e9"], "durationId": "834cec69-35ef-4252-9671-3265bc16dab9", "parent": "4a4d5485-8455-4bed-a465-7d12d2776a1e"}}, {"head": {"id": "187db5eb-ed25-4e70-9f69-b86eae460984", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8745970410500, "endTime": 8747051048300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "540b28ef-c493-441b-87f3-b79e4c6d1557"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "540b28ef-c493-441b-87f3-b79e4c6d1557", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8745970410500, "endTime": 8747051048300}, "additional": {"logType": "info", "children": [], "durationId": "187db5eb-ed25-4e70-9f69-b86eae460984", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "bc76eac1-a5f0-4869-aefc-1302d350f71d", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8747051242800, "endTime": 8747117320800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "5e77394a-5b06-4a07-845d-2a45d9fb38df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e77394a-5b06-4a07-845d-2a45d9fb38df", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8747051242800, "endTime": 8747117320800}, "additional": {"logType": "info", "children": [], "durationId": "bc76eac1-a5f0-4869-aefc-1302d350f71d", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "931d33bd-63c1-4a1e-95fb-5e922d9b44b8", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8747117570400, "endTime": 8747118055700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "d315dcd5-52ee-4808-b62e-1f629f1b528f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d315dcd5-52ee-4808-b62e-1f629f1b528f", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8747117570400, "endTime": 8747118055700}, "additional": {"logType": "info", "children": [], "durationId": "931d33bd-63c1-4a1e-95fb-5e922d9b44b8", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "d09ec127-b55c-44f1-af56-08b48722ed24", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Worker12", "startTime": 8747118175900, "endTime": 8754023057400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "834cec69-35ef-4252-9671-3265bc16dab9", "logId": "4b081ba9-fba3-40ff-a6f9-386389bd38e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b081ba9-fba3-40ff-a6f9-386389bd38e9", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8747118175900, "endTime": 8754023057400}, "additional": {"logType": "info", "children": [], "durationId": "d09ec127-b55c-44f1-af56-08b48722ed24", "parent": "6c566421-fb1d-4c4b-8398-de7198d0923c"}}, {"head": {"id": "4a4d5485-8455-4bed-a465-7d12d2776a1e", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8743677752700, "endTime": 8754037394300, "totalTime": 10359620200}, "additional": {"logType": "info", "children": ["6c566421-fb1d-4c4b-8398-de7198d0923c"], "durationId": "d994f6f8-aad6-4a66-8d33-91055dde97aa"}}, {"head": {"id": "387bb1cf-154a-48b5-b391-2b0dbaeaf891", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754042338600, "endTime": 8754042592200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b9e58c37-fcc1-4e42-a873-e15989ef3ba9", "logId": "5e6bad04-6b64-4674-b564-d6357b117374"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9e58c37-fcc1-4e42-a873-e15989ef3ba9", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754042304400}, "additional": {"logType": "detail", "children": [], "durationId": "387bb1cf-154a-48b5-b391-2b0dbaeaf891"}}, {"head": {"id": "33086816-8364-498c-8a4a-9a59678d1477", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754042347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad09e24a-51e2-4d73-9984-b66256bdbc4f", "name": "entry : PreviewBuild cost memory 0.0116729736328125", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754042460400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "affa55de-c629-49be-985e-0b1f74efa427", "name": "runTaskFromQueue task cost before running: 11 s 325 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754042535800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e6bad04-6b64-4674-b564-d6357b117374", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754042338600, "endTime": 8754042592200, "totalTime": 181000}, "additional": {"logType": "info", "children": [], "durationId": "387bb1cf-154a-48b5-b391-2b0dbaeaf891"}}, {"head": {"id": "f5d6ca02-1a58-4bf8-a92f-db627057f6f0", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754052855700, "endTime": 8754053017800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f987224f-6dc5-4165-b017-168f99f929b5", "logId": "a2e2f8de-cea9-42cd-bba4-3c953a573eb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2e2f8de-cea9-42cd-bba4-3c953a573eb4", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754052855700, "endTime": 8754053017800}, "additional": {"logType": "info", "children": [], "durationId": "f5d6ca02-1a58-4bf8-a92f-db627057f6f0"}}, {"head": {"id": "49fea224-b92e-4685-8ced-e11f5c236c40", "name": "BUILD SUCCESSFUL in 11 s 335 ms ", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053075700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "625e6f8a-af0c-44ed-a630-5b1cfd22afbb", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8742718217800, "endTime": 8754053317300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 4}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f47923e5-c17a-461a-b182-b1dc6d57c178", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0fed50a-9f31-439f-bb1c-8358f9a0f0fd", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed4fdc90-58b8-4274-9401-ca6a0153d4ea", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250a685f-2283-4ccd-ab6f-7b0b6cc0910d", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc9a6bb-2b97-4c4e-a892-03298747905f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e56ceff-7ae0-4ce5-99ed-fd39c394f586", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053540700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94316fa-521e-4107-a3fc-9266971b9de3", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754053575300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e0d7ece-70f0-446a-b755-d66b417a3913", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754054249100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526a57ba-dd53-48d5-b926-6a25bf97dcf8", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754067688400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44121106-1fd4-4f48-95b9-85457841bed4", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754070254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57bd5d6-2a8d-41a6-8b00-7bb111bd5524", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754070597100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a04679e-2727-4614-bd9d-05ccdbbbddc1", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754087944800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e76bb909-400f-4332-9ff1-bbd5dc3136ac", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:35 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754088585300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c15bea2-7350-40f4-b963-026424d0e6f1", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754088802300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eef1946-8c13-4b67-b1f9-ed35b70ca5c1", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754089520400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f61f9b-8eb8-4ef7-b647-b93922735dbd", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754090532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c596b8f8-3a0b-456c-90be-426ff61ef635", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754090999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3210d859-707b-4520-a832-c0dc6d972bc8", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754091287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5c6c18-6014-4acd-a049-370c1f06fa89", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754091588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee19982e-de4f-42c8-a558-7c53e20803bc", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754094538400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be57cccd-7ac9-45fd-92df-a78613a78014", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754095283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c9fb0d-7ee2-475b-b555-d28fffd9c6a1", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754095557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f16bcf65-b108-410e-bfff-c82ec0e40734", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754110750200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bc9cb3c-256d-4d90-a453-18486a585bf1", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754111677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111e7d5e-9616-4880-b91d-759c06aba458", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754111762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d6ff63-6018-41a6-a05d-f2fa807c1f0d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754112007800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e7da5b-3bcd-4f99-8341-723510f98240", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754112701400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a948905-e7ed-4ccb-9d8e-e2ef022f14f9", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754115817400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23d7879c-09d3-4296-b671-6efd5730bac2", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754116080500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8720da66-8eb6-49bd-ba25-6880166dfbda", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754116330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad77863-8ae0-49e1-963a-90afc815fbfd", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754116592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec91306-36d8-4891-8b83-e8ff143677d9", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:26 ms .", "description": "", "type": "log"}, "body": {"pid": 29652, "tid": "Main Thread", "startTime": 8754116857900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}