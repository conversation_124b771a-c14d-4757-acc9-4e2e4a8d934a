package com.icss.wallet.controller;

import com.icss.wallet.result.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.HashMap;
import java.util.Map;

/**
 * 诊断控制器 - 用于检查应用状态和映射
 */
@CrossOrigin(origins = "*", allowedHeaders = "*")
@RestController
@RequestMapping("/diagnostic")
public class DiagnosticController {

    @Autowired
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public R<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("timestamp", System.currentTimeMillis());
        status.put("application", "E-Wallet");
        status.put("version", "1.0.0");
        
        return R.success("应用运行正常", status);
    }

    /**
     * 检查所有映射
     */
    @GetMapping("/mappings")
    public R<Map<String, Object>> mappings() {
        Map<String, Object> mappings = new HashMap<>();
        
        requestMappingHandlerMapping.getHandlerMethods().forEach((key, value) -> {
            String pattern = key.getPatternsCondition().getPatterns().toString();
            String method = key.getMethodsCondition().getMethods().toString();
            String handler = value.getMethod().getDeclaringClass().getSimpleName() + "." + value.getMethod().getName();
            
            mappings.put(pattern + " " + method, handler);
        });
        
        return R.success("获取映射成功", mappings);
    }

    /**
     * 测试支付相关映射
     */
    @GetMapping("/payment-mappings")
    public R<Map<String, String>> paymentMappings() {
        Map<String, String> paymentMappings = new HashMap<>();
        
        requestMappingHandlerMapping.getHandlerMethods().forEach((key, value) -> {
            String pattern = key.getPatternsCondition().getPatterns().toString();
            String method = key.getMethodsCondition().getMethods().toString();
            String handler = value.getMethod().getDeclaringClass().getSimpleName() + "." + value.getMethod().getName();
            
            // 只显示支付相关的映射
            if (pattern.contains("/payment")) {
                paymentMappings.put(pattern + " " + method, handler);
            }
        });
        
        return R.success("获取支付映射成功", paymentMappings);
    }

    /**
     * 测试数据库连接
     */
    @GetMapping("/db-test")
    public R<String> testDatabase() {
        try {
            // 这里可以添加简单的数据库查询测试
            return R.success("数据库连接正常", "Database connection is working");
        } catch (Exception e) {
            return R.failure("数据库连接失败: " + e.getMessage());
        }
    }

    /**
     * 环境信息
     */
    @GetMapping("/env")
    public R<Map<String, String>> environment() {
        Map<String, String> env = new HashMap<>();
        env.put("java.version", System.getProperty("java.version"));
        env.put("os.name", System.getProperty("os.name"));
        env.put("user.dir", System.getProperty("user.dir"));
        env.put("server.port", System.getProperty("server.port", "8091"));

        return R.success("获取环境信息成功", env);
    }

    /**
     * 检查管理员数据
     */
    @GetMapping("/check-admin")
    public R<Map<String, Object>> checkAdmin() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 这里需要注入AdminService来检查管理员数据
            // 暂时返回提示信息
            result.put("message", "请检查数据库中是否有管理员账号，并且管理员账号是否设置了手机号");
            result.put("suggestion", "如果没有管理员账号，请先通过注册功能创建一个管理员账号");

            return R.success("检查完成", result);
        } catch (Exception e) {
            return R.failure("检查失败: " + e.getMessage());
        }
    }
}
