/**
 * 用户认证工具类
 * 管理普通用户和管理员的登录状态
 */

// 用户类型常量
export const USER_TYPES = {
  USER: 'user',
  ADMIN: 'admin'
};

// Token键名常量
export const TOKEN_KEYS = {
  USER_TOKEN: 'userToken',
  ADMIN_TOKEN: 'adminToken',
  USER_TYPE: 'userType',
  USER_PHONE: 'userPhone',
  ADMIN_USERNAME: 'adminUsername'
};

/**
 * 获取当前用户类型
 * @returns {string} 用户类型 ('user' | 'admin')
 */
export const getUserType = () => {
  return localStorage.getItem(TOKEN_KEYS.USER_TYPE) || USER_TYPES.USER;
};

/**
 * 获取当前用户的Token
 * @returns {string|null} Token字符串
 */
export const getToken = () => {
  const userType = getUserType();
  return userType === USER_TYPES.ADMIN 
    ? localStorage.getItem(TOKEN_KEYS.ADMIN_TOKEN)
    : localStorage.getItem(TOKEN_KEYS.USER_TOKEN);
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isLoggedIn = () => {
  return !!getToken();
};

/**
 * 检查是否为管理员
 * @returns {boolean} 是否为管理员
 */
export const isAdmin = () => {
  return getUserType() === USER_TYPES.ADMIN && !!localStorage.getItem(TOKEN_KEYS.ADMIN_TOKEN);
};

/**
 * 检查是否为普通用户
 * @returns {boolean} 是否为普通用户
 */
export const isUser = () => {
  return getUserType() === USER_TYPES.USER && !!localStorage.getItem(TOKEN_KEYS.USER_TOKEN);
};

/**
 * 设置普通用户登录信息
 * @param {string} token 用户Token
 * @param {string} phone 用户手机号
 */
export const setUserLogin = (token, phone) => {
  localStorage.setItem(TOKEN_KEYS.USER_TOKEN, token);
  localStorage.setItem(TOKEN_KEYS.USER_PHONE, phone);
  localStorage.setItem(TOKEN_KEYS.USER_TYPE, USER_TYPES.USER);
  // 清除管理员信息
  localStorage.removeItem(TOKEN_KEYS.ADMIN_TOKEN);
  localStorage.removeItem(TOKEN_KEYS.ADMIN_USERNAME);
};

/**
 * 设置管理员登录信息
 * @param {string} token 管理员Token
 * @param {string} username 管理员用户名
 */
export const setAdminLogin = (token, username) => {
  localStorage.setItem(TOKEN_KEYS.ADMIN_TOKEN, token);
  localStorage.setItem(TOKEN_KEYS.ADMIN_USERNAME, username);
  localStorage.setItem(TOKEN_KEYS.USER_TYPE, USER_TYPES.ADMIN);
  // 清除普通用户信息
  localStorage.removeItem(TOKEN_KEYS.USER_TOKEN);
  localStorage.removeItem(TOKEN_KEYS.USER_PHONE);
};

/**
 * 获取当前用户信息
 * @returns {object} 用户信息对象
 */
export const getCurrentUser = () => {
  const userType = getUserType();
  const token = getToken();
  
  if (!token) {
    return null;
  }
  
  if (userType === USER_TYPES.ADMIN) {
    return {
      type: USER_TYPES.ADMIN,
      token: token,
      username: localStorage.getItem(TOKEN_KEYS.ADMIN_USERNAME)
    };
  } else {
    return {
      type: USER_TYPES.USER,
      token: token,
      phone: localStorage.getItem(TOKEN_KEYS.USER_PHONE)
    };
  }
};

/**
 * 退出登录
 * 清除所有登录信息
 */
export const logout = () => {
  localStorage.removeItem(TOKEN_KEYS.USER_TOKEN);
  localStorage.removeItem(TOKEN_KEYS.ADMIN_TOKEN);
  localStorage.removeItem(TOKEN_KEYS.USER_TYPE);
  localStorage.removeItem(TOKEN_KEYS.USER_PHONE);
  localStorage.removeItem(TOKEN_KEYS.ADMIN_USERNAME);
};

/**
 * 获取登录页面路径
 * @param {string} userType 用户类型
 * @returns {string} 登录页面路径
 */
export const getLoginPath = (userType = null) => {
  const type = userType || getUserType();
  return type === USER_TYPES.ADMIN ? '/admin-login' : '/login';
};

/**
 * 获取登录后的默认跳转路径
 * @param {string} userType 用户类型
 * @returns {string} 默认跳转路径
 */
export const getDefaultPath = (userType = null) => {
  const type = userType || getUserType();
  return type === USER_TYPES.ADMIN ? '/admin-dashboard' : '/home';
};

/**
 * 检查路由权限
 * @param {string} path 路由路径
 * @returns {boolean} 是否有权限访问
 */
export const hasRoutePermission = (path) => {
  const userType = getUserType();
  const token = getToken();

  // 没有token，无权限
  if (!token) {
    return false;
  }

  // 管理员专用路由检查
  if (path.startsWith('/admin')) {
    return userType === USER_TYPES.ADMIN;
  }

  // 普通用户路由检查（管理员也可以访问）
  if (path.startsWith('/home')) {
    return userType === USER_TYPES.USER || userType === USER_TYPES.ADMIN;
  }

  // 其他路径默认允许
  return true;
};

export default {
  USER_TYPES,
  TOKEN_KEYS,
  getUserType,
  getToken,
  isLoggedIn,
  isAdmin,
  isUser,
  setUserLogin,
  setAdminLogin,
  getCurrentUser,
  logout,
  getLoginPath,
  getDefaultPath,
  hasRoutePermission
};
