if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardPage_Params {
    cards?: BankCard[];
    currentTab?: number;
    showAddDialog?: boolean;
    isLoading?: boolean;
    userId?: number;
    newCardNumber?: string;
    newHolderName?: string;
    newPhoneNumber?: string;
    newBankName?: string;
    newCardType?: string;
    isAdding?: boolean;
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse, AxiosError } from "@normalized:N&&&@ohos/axios/index&2.2.6";
/**
 * API响应结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
/**
 * 后台返回的银行卡数据结构
 */
interface BankCardResponse {
    cardId: number;
    userId: number;
    bankName: string;
    cardNumber: string;
    cardType: number; // 1-储蓄卡, 2-信用卡
    status: number; // 0-未绑定, 1-已绑定
    isDefault: number; // 0-非默认, 1-默认
    cardHolder: string;
    phone: string;
    expiryDate?: string;
    cvv?: string;
    createTime?: string;
    updateTime?: string;
}
/**
 * 前端使用的银行卡信息
 */
interface BankCard {
    id: number;
    userId: number;
    bankName: string;
    cardNumber: string;
    cardType: string;
    status: number; // 0-未绑定, 1-已绑定
    isDefault: number; // 0-非默认, 1-默认
    holderName: string;
    phoneNumber: string;
    createTime?: string;
    updateTime?: string;
}
/**
 * 添加银行卡请求
 */
interface AddCardRequest {
    userId: number;
    cardNumber: string;
    bankName: string;
    cardType: number; // 1-储蓄卡, 2-信用卡
    cardHolder: string;
    phone: string;
}
export class BankCardPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cards = new ObservedPropertyObjectPU([], this, "cards");
        this.__currentTab = new ObservedPropertySimplePU(0, this, "currentTab");
        this.__showAddDialog = new ObservedPropertySimplePU(false, this, "showAddDialog");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__userId = new ObservedPropertySimplePU(1, this, "userId");
        this.__newCardNumber = new ObservedPropertySimplePU('', this, "newCardNumber");
        this.__newHolderName = new ObservedPropertySimplePU('', this, "newHolderName");
        this.__newPhoneNumber = new ObservedPropertySimplePU('', this, "newPhoneNumber");
        this.__newBankName = new ObservedPropertySimplePU('', this, "newBankName");
        this.__newCardType = new ObservedPropertySimplePU('储蓄卡', this, "newCardType");
        this.__isAdding = new ObservedPropertySimplePU(false, this, "isAdding");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardPage_Params) {
        if (params.cards !== undefined) {
            this.cards = params.cards;
        }
        if (params.currentTab !== undefined) {
            this.currentTab = params.currentTab;
        }
        if (params.showAddDialog !== undefined) {
            this.showAddDialog = params.showAddDialog;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
        if (params.newCardNumber !== undefined) {
            this.newCardNumber = params.newCardNumber;
        }
        if (params.newHolderName !== undefined) {
            this.newHolderName = params.newHolderName;
        }
        if (params.newPhoneNumber !== undefined) {
            this.newPhoneNumber = params.newPhoneNumber;
        }
        if (params.newBankName !== undefined) {
            this.newBankName = params.newBankName;
        }
        if (params.newCardType !== undefined) {
            this.newCardType = params.newCardType;
        }
        if (params.isAdding !== undefined) {
            this.isAdding = params.isAdding;
        }
    }
    updateStateVars(params: BankCardPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cards.purgeDependencyOnElmtId(rmElmtId);
        this.__currentTab.purgeDependencyOnElmtId(rmElmtId);
        this.__showAddDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
        this.__newCardNumber.purgeDependencyOnElmtId(rmElmtId);
        this.__newHolderName.purgeDependencyOnElmtId(rmElmtId);
        this.__newPhoneNumber.purgeDependencyOnElmtId(rmElmtId);
        this.__newBankName.purgeDependencyOnElmtId(rmElmtId);
        this.__newCardType.purgeDependencyOnElmtId(rmElmtId);
        this.__isAdding.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cards.aboutToBeDeleted();
        this.__currentTab.aboutToBeDeleted();
        this.__showAddDialog.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__userId.aboutToBeDeleted();
        this.__newCardNumber.aboutToBeDeleted();
        this.__newHolderName.aboutToBeDeleted();
        this.__newPhoneNumber.aboutToBeDeleted();
        this.__newBankName.aboutToBeDeleted();
        this.__newCardType.aboutToBeDeleted();
        this.__isAdding.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cards: ObservedPropertyObjectPU<BankCard[]>;
    get cards() {
        return this.__cards.get();
    }
    set cards(newValue: BankCard[]) {
        this.__cards.set(newValue);
    }
    private __currentTab: ObservedPropertySimplePU<number>; // 0-所有银行卡, 1-已绑定银行卡
    get currentTab() {
        return this.__currentTab.get();
    }
    set currentTab(newValue: number) {
        this.__currentTab.set(newValue);
    }
    private __showAddDialog: ObservedPropertySimplePU<boolean>;
    get showAddDialog() {
        return this.__showAddDialog.get();
    }
    set showAddDialog(newValue: boolean) {
        this.__showAddDialog.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __userId: ObservedPropertySimplePU<number>;
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    // 添加银行卡表单
    private __newCardNumber: ObservedPropertySimplePU<string>;
    get newCardNumber() {
        return this.__newCardNumber.get();
    }
    set newCardNumber(newValue: string) {
        this.__newCardNumber.set(newValue);
    }
    private __newHolderName: ObservedPropertySimplePU<string>;
    get newHolderName() {
        return this.__newHolderName.get();
    }
    set newHolderName(newValue: string) {
        this.__newHolderName.set(newValue);
    }
    private __newPhoneNumber: ObservedPropertySimplePU<string>;
    get newPhoneNumber() {
        return this.__newPhoneNumber.get();
    }
    set newPhoneNumber(newValue: string) {
        this.__newPhoneNumber.set(newValue);
    }
    private __newBankName: ObservedPropertySimplePU<string>;
    get newBankName() {
        return this.__newBankName.get();
    }
    set newBankName(newValue: string) {
        this.__newBankName.set(newValue);
    }
    private __newCardType: ObservedPropertySimplePU<string>;
    get newCardType() {
        return this.__newCardType.get();
    }
    set newCardType(newValue: string) {
        this.__newCardType.set(newValue);
    }
    private __isAdding: ObservedPropertySimplePU<boolean>;
    get isAdding() {
        return this.__isAdding.get();
    }
    set isAdding(newValue: boolean) {
        this.__isAdding.set(newValue);
    }
    aboutToAppear() {
        this.loadCards();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(84:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f8f9fa');
        }, Column);
        // 顶部导航栏
        this.buildHeader.bind(this)();
        // 标签页切换
        this.buildTabBar.bind(this)();
        // 添加银行卡按钮
        this.buildAddButton.bind(this)();
        // 银行卡列表
        this.buildCardList.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 添加银行卡弹窗
            if (this.showAddDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildAddDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    buildHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(109:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 16, right: 16 });
            Row.backgroundColor('#ffffff');
            Row.shadow({
                radius: 4,
                color: '#1a000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(110:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡管理');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(117:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 去重按钮
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(125:7)", "entry");
            // 去重按钮
            Image.width(24);
            // 去重按钮
            Image.height(24);
            // 去重按钮
            Image.onClick(() => {
                this.removeDuplicatesManually();
            });
        }, Image);
        Row.pop();
    }
    buildTabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(146:5)", "entry");
            Row.width('100%');
            Row.padding(16);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('所有银行卡');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(147:7)", "entry");
            Button.type(this.currentTab === 0 ? ButtonType.Capsule : ButtonType.Normal);
            Button.backgroundColor(this.currentTab === 0 ? '#4285f4' : '#f8f9fa');
            Button.fontColor(this.currentTab === 0 ? '#ffffff' : '#666666');
            Button.fontSize(14);
            Button.height(40);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.currentTab = 0;
                this.loadCards();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('已绑定银行卡');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(160:7)", "entry");
            Button.type(this.currentTab === 1 ? ButtonType.Capsule : ButtonType.Normal);
            Button.backgroundColor(this.currentTab === 1 ? '#4285f4' : '#f8f9fa');
            Button.fontColor(this.currentTab === 1 ? '#ffffff' : '#666666');
            Button.fontSize(14);
            Button.height(40);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.currentTab = 1;
                this.loadBoundCards();
            });
        }, Button);
        Button.pop();
        Row.pop();
    }
    buildAddButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(179:5)", "entry");
            Button.width('calc(100% - 32vp)');
            Button.height(48);
            Button.backgroundColor('#4285f4');
            Button.borderRadius(24);
            Button.margin({ left: 16, right: 16, bottom: 16 });
            Button.shadow({
                radius: 8,
                color: '#334285f4',
                offsetX: 0,
                offsetY: 4
            });
            Button.onClick(() => {
                this.showAddDialog = true;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(180:7)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(181:9)", "entry");
            Image.width(20);
            Image.height(20);
            Image.fillColor('#ffffff');
            Image.margin({ right: 8 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(187:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#ffffff');
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Row.pop();
        Button.pop();
    }
    buildCardList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(212:7)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/BankCardPage.ets(213:9)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#4285f4');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(218:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 12 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else if (this.cards.length === 0) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(227:7)", "entry");
                        Column.width('100%');
                        Column.height(200);
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
                        Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(228:9)", "entry");
                        Image.width(80);
                        Image.height(80);
                        Image.opacity(0.5);
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无银行卡');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(233:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('点击上方按钮添加银行卡');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(238:9)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#cccccc');
                        Text.margin({ top: 8 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create({ space: 12 });
                        List.debugLine("entry/src/main/ets/pages/BankCardPage.ets(247:7)", "entry");
                        List.width('100%');
                        List.layoutWeight(1);
                        List.padding({ left: 16, right: 16 });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.debugLine("entry/src/main/ets/pages/BankCardPage.ets(249:11)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.buildCardItem.bind(this)(card);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.cards, forEachItemGenFunction, (card: BankCard) => card.id.toString(), false, false);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
    }
    buildCardItem(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(262:5)", "entry");
            Column.width('100%');
            Column.backgroundColor('#ffffff');
            Column.borderRadius(16);
            Column.padding(20);
            Column.shadow({
                radius: 8,
                color: '#1a000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡头部信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(264:7)", "entry");
            // 银行卡头部信息
            Row.width('100%');
            // 银行卡头部信息
            Row.margin({ bottom: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行图标
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/BankCardPage.ets(266:9)", "entry");
            // 银行图标
            Stack.margin({ right: 16 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/BankCardPage.ets(267:11)", "entry");
            Circle.width(48);
            Circle.height(48);
            Circle.fill('#ffffff');
            Circle.shadow({
                radius: 4,
                color: '#1a000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.getBankIcon(card.bankName));
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(278:11)", "entry");
            Image.width(32);
            Image.height(32);
        }, Image);
        // 银行图标
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(284:9)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(285:11)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`**** **** **** ${card.cardNumber.slice(-4)}`);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(291:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.cardType);
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(297:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.margin({ top: 2 });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 状态标签
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(307:9)", "entry");
            // 状态标签
            Column.alignItems(HorizontalAlign.End);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.status === 1) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('已绑定');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(309:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#ffffff');
                        Text.backgroundColor('#34a853');
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.borderRadius(12);
                        Text.margin({ bottom: 6 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('未绑定');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(317:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#ffffff');
                        Text.backgroundColor('#ea4335');
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.borderRadius(12);
                        Text.margin({ bottom: 6 });
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.isDefault === 1) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(327:13)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#ffffff');
                        Text.backgroundColor('#ff9800');
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.borderRadius(12);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 状态标签
        Column.pop();
        // 银行卡头部信息
        Row.pop();
        // 操作按钮
        this.buildCardActions.bind(this)(card);
        Column.pop();
    }
    buildCardActions(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(357:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第一行操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(359:7)", "entry");
            // 第一行操作按钮
            Row.width('100%');
            // 第一行操作按钮
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.status === 1) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 已绑定状态
                        Button.createWithLabel('解除绑定');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(362:11)", "entry");
                        // 已绑定状态
                        Button.type(ButtonType.Capsule);
                        // 已绑定状态
                        Button.backgroundColor('#fff5f5');
                        // 已绑定状态
                        Button.fontColor('#ea4335');
                        // 已绑定状态
                        Button.fontSize(12);
                        // 已绑定状态
                        Button.height(32);
                        // 已绑定状态
                        Button.layoutWeight(1);
                        // 已绑定状态
                        Button.margin({ right: 6 });
                        // 已绑定状态
                        Button.onClick(() => {
                            this.unbindCard(card.id);
                        });
                    }, Button);
                    // 已绑定状态
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (card.isDefault === 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Button.createWithLabel('设为默认');
                                    Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(375:13)", "entry");
                                    Button.type(ButtonType.Capsule);
                                    Button.backgroundColor('#e3f2fd');
                                    Button.fontColor('#4285f4');
                                    Button.fontSize(12);
                                    Button.height(32);
                                    Button.layoutWeight(1);
                                    Button.margin({ left: 6 });
                                    Button.onClick(() => {
                                        this.setDefaultCard(card.id);
                                    });
                                }, Button);
                                Button.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 未绑定状态
                        Button.createWithLabel('绑定银行卡');
                        Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(389:11)", "entry");
                        // 未绑定状态
                        Button.type(ButtonType.Capsule);
                        // 未绑定状态
                        Button.backgroundColor('#4285f4');
                        // 未绑定状态
                        Button.fontColor('#ffffff');
                        // 未绑定状态
                        Button.fontSize(12);
                        // 未绑定状态
                        Button.height(32);
                        // 未绑定状态
                        Button.layoutWeight(1);
                        // 未绑定状态
                        Button.onClick(() => {
                            this.bindCard(card.id);
                        });
                    }, Button);
                    // 未绑定状态
                    Button.pop();
                });
            }
        }, If);
        If.pop();
        // 第一行操作按钮
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第二行操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(405:7)", "entry");
            // 第二行操作按钮
            Row.width('100%');
            // 第二行操作按钮
            Row.margin({ top: 8 });
            // 第二行操作按钮
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('查看详情');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(406:9)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f8f9fa');
            Button.fontColor('#666666');
            Button.fontSize(12);
            Button.height(32);
            Button.layoutWeight(1);
            Button.margin({ right: 6 });
            Button.onClick(() => {
                this.viewCardDetail(card.id);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('删除');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(418:9)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#fff5f5');
            Button.fontColor('#ea4335');
            Button.fontSize(12);
            Button.height(32);
            Button.layoutWeight(1);
            Button.margin({ left: 6 });
            Button.onClick(() => {
                this.deleteCard(card.id);
            });
        }, Button);
        Button.pop();
        // 第二行操作按钮
        Row.pop();
        Column.pop();
    }
    buildAddDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/BankCardPage.ets(439:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
            Stack.position({ x: 0, y: 0 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 背景遮罩
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(441:7)", "entry");
            // 背景遮罩
            Column.width('100%');
            // 背景遮罩
            Column.height('100%');
            // 背景遮罩
            Column.backgroundColor('rgba(0,0,0,0.5)');
            // 背景遮罩
            Column.onClick(() => {
                this.showAddDialog = false;
                this.clearForm();
            });
        }, Column);
        // 背景遮罩
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 弹窗内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(451:7)", "entry");
            // 弹窗内容
            Column.width('calc(100% - 48vp)');
            // 弹窗内容
            Column.backgroundColor('#ffffff');
            // 弹窗内容
            Column.borderRadius(16);
            // 弹窗内容
            Column.padding(24);
            // 弹窗内容
            Column.shadow({
                radius: 16,
                color: '#********',
                offsetX: 0,
                offsetY: 8
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 弹窗标题
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(453:9)", "entry");
            // 弹窗标题
            Row.width('100%');
            // 弹窗标题
            Row.margin({ bottom: 24 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(454:11)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#1a1a1a');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardPage.ets(460:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                this.showAddDialog = false;
                this.clearForm();
            });
        }, Image);
        // 弹窗标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(472:9)", "entry");
            // 表单内容
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡号
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(474:11)", "entry");
            // 银行卡号
            Column.width('100%');
            // 银行卡号
            Column.alignItems(HorizontalAlign.Start);
            // 银行卡号
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡号');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(475:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入16-19位银行卡号' });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPage.ets(481:13)", "entry");
            TextInput.type(InputType.Number);
            TextInput.maxLength(19);
            TextInput.onChange((value: string) => {
                this.newCardNumber = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 银行卡号
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 持卡人姓名
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(496:11)", "entry");
            // 持卡人姓名
            Column.width('100%');
            // 持卡人姓名
            Column.alignItems(HorizontalAlign.Start);
            // 持卡人姓名
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('持卡人姓名');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(497:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入持卡人姓名' });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPage.ets(503:13)", "entry");
            TextInput.onChange((value: string) => {
                this.newHolderName = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 持卡人姓名
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 开户银行
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(516:11)", "entry");
            // 开户银行
            Column.width('100%');
            // 开户银行
            Column.alignItems(HorizontalAlign.Start);
            // 开户银行
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('开户银行');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(517:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入开户银行' });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPage.ets(523:13)", "entry");
            TextInput.onChange((value: string) => {
                this.newBankName = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 开户银行
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 预留手机号
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardPage.ets(536:11)", "entry");
            // 预留手机号
            Column.width('100%');
            // 预留手机号
            Column.alignItems(HorizontalAlign.Start);
            // 预留手机号
            Column.margin({ bottom: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('预留手机号');
            Text.debugLine("entry/src/main/ets/pages/BankCardPage.ets(537:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入银行预留手机号' });
            TextInput.debugLine("entry/src/main/ets/pages/BankCardPage.ets(543:13)", "entry");
            TextInput.type(InputType.PhoneNumber);
            TextInput.maxLength(11);
            TextInput.onChange((value: string) => {
                this.newPhoneNumber = value;
            });
            TextInput.backgroundColor('#f8f9fa');
            TextInput.borderRadius(8);
            TextInput.height(48);
        }, TextInput);
        // 预留手机号
        Column.pop();
        // 表单内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 操作按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardPage.ets(560:9)", "entry");
            // 操作按钮
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(561:11)", "entry");
            Button.type(ButtonType.Normal);
            Button.backgroundColor('#f8f9fa');
            Button.fontColor('#666666');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.showAddDialog = false;
                this.clearForm();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.isAdding ? '添加中...' : '确认添加');
            Button.debugLine("entry/src/main/ets/pages/BankCardPage.ets(574:11)", "entry");
            Button.type(ButtonType.Capsule);
            Button.backgroundColor('#4285f4');
            Button.fontColor('#ffffff');
            Button.fontSize(16);
            Button.height(48);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.enabled(!this.isAdding);
            Button.onClick(() => {
                this.addCard();
            });
        }, Button);
        Button.pop();
        // 操作按钮
        Row.pop();
        // 弹窗内容
        Column.pop();
        Stack.pop();
    }
    // 加载银行卡列表
    loadCards() {
        this.isLoading = true;
        const url = this.currentTab === 0
            ? `http://localhost:8091/bankCards/user/${this.userId}`
            : `http://localhost:8091/bankCards/bound/user/${this.userId}`;
        axios({
            url: url,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<BankCardResponse[]>>) => {
            console.log('加载银行卡结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                // 转换数据并去重
                const convertedCards = res.data.data.map((item: BankCardResponse) => this.convertToBankCard(item));
                this.cards = this.removeDuplicateCards(convertedCards);
                console.log(`原始银行卡数量: ${res.data.data.length}, 去重后数量: ${this.cards.length}`);
            }
            else {
                promptAction.showToast({
                    message: res.data.msg || '加载失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('加载银行卡错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 加载已绑定银行卡
    loadBoundCards() {
        this.isLoading = true;
        axios({
            url: `http://localhost:8091/bankCards/bound/user/${this.userId}`,
            method: 'get'
        }).then((res: AxiosResponse<ApiResponse<BankCardResponse[]>>) => {
            console.log('加载已绑定银行卡结果:', JSON.stringify(res.data));
            if (res.data.code === 0) {
                // 转换数据并去重
                const convertedCards = res.data.data.map((item: BankCardResponse) => this.convertToBankCard(item));
                this.cards = this.removeDuplicateCards(convertedCards);
                console.log(`原始已绑定银行卡数量: ${res.data.data.length}, 去重后数量: ${this.cards.length}`);
            }
            else {
                promptAction.showToast({
                    message: res.data.msg || '加载失败',
                    duration: 2000
                });
            }
        }).catch((err: AxiosError) => {
            console.error('加载已绑定银行卡错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 添加银行卡
    addCard() {
        // 表单验证
        if (!this.newCardNumber || this.newCardNumber.length < 16) {
            promptAction.showToast({
                message: '请输入正确的银行卡号',
                duration: 2000
            });
            return;
        }
        if (!this.newHolderName || this.newHolderName.length < 2) {
            promptAction.showToast({
                message: '请输入正确的持卡人姓名',
                duration: 2000
            });
            return;
        }
        if (!this.newBankName) {
            promptAction.showToast({
                message: '请输入开户银行',
                duration: 2000
            });
            return;
        }
        if (!this.newPhoneNumber || this.newPhoneNumber.length !== 11) {
            promptAction.showToast({
                message: '请输入正确的手机号',
                duration: 2000
            });
            return;
        }
        // 检查是否已存在相同卡号的银行卡
        const existingCard = this.cards.find(card => card.cardNumber === this.newCardNumber);
        if (existingCard) {
            promptAction.showToast({
                message: `该银行卡已存在 (${existingCard.bankName})`,
                duration: 2000
            });
            return;
        }
        this.isAdding = true;
        const requestData: AddCardRequest = {
            userId: this.userId,
            cardNumber: this.newCardNumber,
            bankName: this.newBankName,
            cardType: this.newCardType === '储蓄卡' ? 1 : 2,
            cardHolder: this.newHolderName,
            phone: this.newPhoneNumber
        };
        axios({
            url: 'http://localhost:8091/bankCards/add',
            method: 'post',
            data: requestData
        }).then((res: AxiosResponse<ApiResponse<BankCardResponse>>) => {
            console.log('添加银行卡结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                this.showAddDialog = false;
                this.clearForm();
                this.loadCards();
            }
        }).catch((err: AxiosError) => {
            console.error('添加银行卡错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        }).finally(() => {
            this.isAdding = false;
        });
    }
    // 绑定银行卡
    bindCard(cardId: number) {
        axios({
            url: `http://localhost:8091/bankCards/bind/${cardId}`,
            method: 'post'
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
            console.log('绑定银行卡结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                // 绑定成功后刷新数据
                if (this.currentTab === 0) {
                    // 在"所有银行卡"选项卡，重新加载所有银行卡
                    this.loadCards();
                }
                else {
                    // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
                    this.loadBoundCards();
                }
            }
        }).catch((err: AxiosError) => {
            console.error('绑定银行卡错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        });
    }
    // 解除绑定银行卡
    unbindCard(cardId: number) {
        promptAction.showDialog({
            title: '确认解绑',
            message: '确定要解绑这张银行卡吗？',
            buttons: [
                { text: '取消', color: '#666666' },
                { text: '确定', color: '#ea4335' }
            ]
        }).then(result => {
            if (result.index === 1) {
                axios({
                    url: `http://localhost:8091/bankCards/unbind/${cardId}`,
                    method: 'post'
                }).then((res: AxiosResponse<ApiResponse<null>>) => {
                    console.log('解绑银行卡结果:', JSON.stringify(res.data));
                    promptAction.showToast({
                        message: res.data.msg || '操作完成',
                        duration: 2000
                    });
                    if (res.data.code === 0) {
                        // 解绑成功后刷新数据
                        if (this.currentTab === 0) {
                            // 在"所有银行卡"选项卡，重新加载所有银行卡
                            this.loadCards();
                        }
                        else {
                            // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
                            this.loadBoundCards();
                        }
                    }
                }).catch((err: AxiosError) => {
                    console.error('解绑银行卡错误:', err.message);
                    promptAction.showToast({
                        message: '网络错误，请重试',
                        duration: 2000
                    });
                });
            }
        });
    }
    // 设置默认银行卡
    setDefaultCard(cardId: number) {
        axios({
            url: `http://localhost:8091/bankCards/${cardId}/default`,
            method: 'put'
        }).then((res: AxiosResponse<ApiResponse<null>>) => {
            console.log('设置默认银行卡结果:', JSON.stringify(res.data));
            promptAction.showToast({
                message: res.data.msg || '操作完成',
                duration: 2000
            });
            if (res.data.code === 0) {
                // 设置默认卡成功后刷新数据
                if (this.currentTab === 0) {
                    // 在"所有银行卡"选项卡，重新加载所有银行卡
                    this.loadCards();
                }
                else {
                    // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
                    this.loadBoundCards();
                }
            }
        }).catch((err: AxiosError) => {
            console.error('设置默认银行卡错误:', err.message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000
            });
        });
    }
    // 删除银行卡
    deleteCard(cardId: number) {
        promptAction.showDialog({
            title: '确认删除',
            message: '确定要删除这张银行卡吗？删除后无法恢复。',
            buttons: [
                { text: '取消', color: '#666666' },
                { text: '确定', color: '#ea4335' }
            ]
        }).then(result => {
            if (result.index === 1) {
                axios({
                    url: `http://localhost:8091/bankCards/${cardId}`,
                    method: 'delete'
                }).then((res: AxiosResponse<ApiResponse<null>>) => {
                    console.log('删除银行卡结果:', JSON.stringify(res.data));
                    promptAction.showToast({
                        message: res.data.msg || '操作完成',
                        duration: 2000
                    });
                    if (res.data.code === 0) {
                        // 删除成功后刷新数据
                        if (this.currentTab === 0) {
                            // 在"所有银行卡"选项卡，重新加载所有银行卡
                            this.loadCards();
                        }
                        else {
                            // 在"已绑定银行卡"选项卡，重新加载已绑定银行卡
                            this.loadBoundCards();
                        }
                    }
                }).catch((err: AxiosError) => {
                    console.error('删除银行卡错误:', err.message);
                    promptAction.showToast({
                        message: '网络错误，请重试',
                        duration: 2000
                    });
                });
            }
        });
    }
    // 查看银行卡详情
    viewCardDetail(cardId: number) {
        // 找到对应的银行卡数据
        const card = this.cards.find(c => c.id === cardId);
        if (!card) {
            promptAction.showToast({
                message: '银行卡信息不存在',
                duration: 2000
            });
            return;
        }
        // 跳转到银行卡详情页面，传递完整的银行卡数据
        router.pushUrl({
            url: 'pages/CardDetailPage',
            params: {
                cardId: card.id,
                bankName: card.bankName,
                cardNumber: card.cardNumber,
                cardType: card.cardType === '储蓄卡' ? 1 : 2,
                cardHolder: card.holderName,
                phone: card.phoneNumber,
                status: card.status,
                isDefault: card.isDefault,
                createTime: card.createTime
            }
        }).then(() => {
            console.log('跳转到银行卡详情页面成功, cardId:', cardId);
        }).catch((err: Error) => {
            console.error('跳转到银行卡详情页面失败:', err.message);
            promptAction.showToast({
                message: '页面跳转失败',
                duration: 2000
            });
        });
    }
    // 数据转换：后台数据转换为前端数据
    convertToBankCard(response: BankCardResponse): BankCard {
        return {
            id: response.cardId,
            userId: response.userId,
            bankName: response.bankName,
            cardNumber: response.cardNumber,
            cardType: response.cardType === 1 ? '储蓄卡' : '信用卡',
            status: response.status,
            isDefault: response.isDefault,
            holderName: response.cardHolder,
            phoneNumber: response.phone,
            createTime: response.createTime,
            updateTime: response.updateTime
        };
    }
    // 去除重复银行卡
    removeDuplicateCards(cards: BankCard[]): BankCard[] {
        const cardMap = new Map<string, BankCard>();
        for (const card of cards) {
            const cardNumber = card.cardNumber;
            const existingCard = cardMap.get(cardNumber);
            if (!existingCard) {
                // 如果没有重复，直接添加
                cardMap.set(cardNumber, card);
                console.log(`添加银行卡: ${card.bankName} **** ${cardNumber.slice(-4)} (状态: ${card.status === 1 ? '已绑定' : '未绑定'})`);
            }
            else {
                // 如果有重复，选择优先级更高的银行卡
                let shouldReplace = false;
                let reason = '';
                // 优先级规则：
                // 1. 已绑定的银行卡优先于未绑定的
                if (card.status === 1 && existingCard.status === 0) {
                    shouldReplace = true;
                    reason = '已绑定优先于未绑定';
                }
                // 2. 如果状态相同，默认银行卡优先于非默认
                else if (card.status === existingCard.status && card.isDefault === 1 && existingCard.isDefault === 0) {
                    shouldReplace = true;
                    reason = '默认卡优先于非默认卡';
                }
                // 3. 如果状态和默认设置都相同，选择ID更大的（更新的）
                else if (card.status === existingCard.status && card.isDefault === existingCard.isDefault && card.id > existingCard.id) {
                    shouldReplace = true;
                    reason = '选择更新的记录';
                }
                if (shouldReplace) {
                    cardMap.set(cardNumber, card);
                    console.log(`替换重复银行卡: ${card.bankName} **** ${cardNumber.slice(-4)} (${reason})`);
                }
                else {
                    console.log(`保留原银行卡: ${existingCard.bankName} **** ${cardNumber.slice(-4)}, 丢弃重复项`);
                }
            }
        }
        const uniqueCards = Array.from(cardMap.values());
        console.log(`去重完成: 原始数量 ${cards.length}, 去重后数量 ${uniqueCards.length}`);
        return uniqueCards;
    }
    // 手动去重操作
    removeDuplicatesManually() {
        promptAction.showDialog({
            title: '去重确认',
            message: '是否要删除重复的银行卡？\n\n去重规则：\n• 已绑定优先于未绑定\n• 默认卡优先于非默认卡\n• 保留最新的记录',
            buttons: [
                { text: '取消', color: '#666666' },
                { text: '确定', color: '#4285f4' }
            ]
        }).then(result => {
            if (result.index === 1) {
                const originalCount = this.cards.length;
                this.cards = this.removeDuplicateCards(this.cards);
                const removedCount = originalCount - this.cards.length;
                if (removedCount > 0) {
                    promptAction.showToast({
                        message: `已删除 ${removedCount} 张重复银行卡`,
                        duration: 2000
                    });
                }
                else {
                    promptAction.showToast({
                        message: '没有发现重复的银行卡',
                        duration: 2000
                    });
                }
            }
        });
    }
    // 清空表单
    clearForm() {
        this.newCardNumber = '';
        this.newHolderName = '';
        this.newPhoneNumber = '';
        this.newBankName = '';
        this.newCardType = '储蓄卡';
    }
    // 获取银行图标
    getBankIcon(bankName: string): Resource {
        switch (bankName) {
            case '中国银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '建设银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '工商银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '农业银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '招商银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '交通银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '中信银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '光大银行': return { "id": 16777276, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '华夏银行': return { "id": 16777279, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '民生银行': return { "id": 16777278, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '兴业银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '浦发银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            default:
                return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardPage";
    }
}
registerNamedRoute(() => new BankCardPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/BankCardPage", pageFullPath: "entry/src/main/ets/pages/BankCardPage", integratedHsp: "false", moduleType: "followWithHap" });
