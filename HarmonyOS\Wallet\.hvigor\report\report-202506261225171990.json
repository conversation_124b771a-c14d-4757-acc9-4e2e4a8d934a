{"version": "2.0", "ppid": 15484, "events": [{"head": {"id": "fe32362a-a518-42ad-bbf1-29c40f4abce8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 224542665400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c2a18f7-3975-4332-b53c-dcf5fcf805a8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 224545211300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5148042a-87d9-410c-8f83-d64b8efe0c3f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 224545575700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9dfdf3-a059-43cd-858e-f8a5bbacb34d", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 224546341800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cdcaae-a897-4579-af31-719c05140fab", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 224546716900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49dc6c76-9696-4124-90dd-2c547f9534a6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241954844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19092e73-752a-43ba-a87c-7907325f3ebe", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241963043600, "endTime": 242140396000}, "additional": {"children": ["c6e40261-a8ee-4713-8aa7-8090cc319347", "942ef49e-8445-41a3-a647-42d91511fc5e", "91da98e2-20a1-4de9-b76b-7ee687d7ba72", "9ccb30eb-2692-4d17-a004-2f75add2525d", "5ed4e78c-5e3c-46ab-a0dc-bdb7afb0febc", "63484552-2700-4151-abd2-1eec06ab28ca", "390c64a9-121e-490e-ae3a-6104fc5e6f2c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7550dd04-74b6-490a-a741-b13948e04686"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6e40261-a8ee-4713-8aa7-8090cc319347", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241963055900, "endTime": 241977901600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "86ee399a-cb05-43f7-b316-807c7e7b30c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "942ef49e-8445-41a3-a647-42d91511fc5e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241977918700, "endTime": 242139195800}, "additional": {"children": ["3d90dcfe-8fcf-4080-a174-80010cf9c088", "32f6e873-612e-446b-a014-24f78ff3318d", "5cf35cf5-a6b6-4462-9ce0-725ee63258be", "9a8d5c66-ba95-421f-bcbc-0e78fa14fd32", "d276312a-ab12-4159-bf50-b2d76c9d5837", "309a685d-5e7b-4745-b6fb-2eee1306b30a", "a5914c96-7807-4b9d-ad02-80fdb6ca987b", "8904a709-cab3-49f9-b183-eaf1dfdfe3e0", "0b7cfa8e-d88c-4851-a3f5-2d4a4ad435a7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91da98e2-20a1-4de9-b76b-7ee687d7ba72", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242139220900, "endTime": 242140367400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "c02afd9b-422d-4c59-b7de-076538b33e88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ccb30eb-2692-4d17-a004-2f75add2525d", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242140373000, "endTime": 242140391100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "842d6dfb-4e92-406e-a9af-70720552a0ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ed4e78c-5e3c-46ab-a0dc-bdb7afb0febc", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241966798600, "endTime": 241966866900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "1f9fa5bb-6af5-4313-8f44-d7160fbe627c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f9fa5bb-6af5-4313-8f44-d7160fbe627c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241966798600, "endTime": 241966866900}, "additional": {"logType": "info", "children": [], "durationId": "5ed4e78c-5e3c-46ab-a0dc-bdb7afb0febc", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "63484552-2700-4151-abd2-1eec06ab28ca", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241973762300, "endTime": 241973788800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "c239e447-108a-41ef-a2b8-92608e275fe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c239e447-108a-41ef-a2b8-92608e275fe8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241973762300, "endTime": 241973788800}, "additional": {"logType": "info", "children": [], "durationId": "63484552-2700-4151-abd2-1eec06ab28ca", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "eb362820-d74c-45fc-8625-066a46025f52", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241973891700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28514943-ba34-4922-8105-1388bd44e73f", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241977763700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86ee399a-cb05-43f7-b316-807c7e7b30c1", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241963055900, "endTime": 241977901600}, "additional": {"logType": "info", "children": [], "durationId": "c6e40261-a8ee-4713-8aa7-8090cc319347", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "3d90dcfe-8fcf-4080-a174-80010cf9c088", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241985037400, "endTime": 241985050900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "27ab47c6-61b0-4880-90ea-521d01dfe3d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32f6e873-612e-446b-a014-24f78ff3318d", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241985070000, "endTime": 241989138100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "34b0d1e3-445f-4479-a9b9-f65e276887c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cf35cf5-a6b6-4462-9ce0-725ee63258be", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241989149000, "endTime": 242071494200}, "additional": {"children": ["e94e3042-744e-4aca-bcb5-0263975505e9", "be8ba8c9-9477-4acf-b3ee-e32e7ac73813", "84dfb943-34fb-4926-b6e1-e6b429700169"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "152f41f2-9731-4704-956c-8de05f398246"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a8d5c66-ba95-421f-bcbc-0e78fa14fd32", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242071515100, "endTime": 242095427300}, "additional": {"children": ["be69b4e0-9b2d-46c0-9eef-33b49e30d9fa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "5e8f9573-542b-4359-a133-e34899ec1554"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d276312a-ab12-4159-bf50-b2d76c9d5837", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242095435800, "endTime": 242109808700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "e2d2cb78-0804-4019-88d2-bf5a5df2af42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "309a685d-5e7b-4745-b6fb-2eee1306b30a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242110980600, "endTime": 242124350000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "f06e8141-ee74-4ae8-914e-36b5a30084bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5914c96-7807-4b9d-ad02-80fdb6ca987b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242124374000, "endTime": 242139041700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "539a1c03-de27-419e-9e85-62ed10a7ad24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8904a709-cab3-49f9-b183-eaf1dfdfe3e0", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242139059700, "endTime": 242139181900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "1142e92c-f10a-4d6a-8201-914be8e9cdc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27ab47c6-61b0-4880-90ea-521d01dfe3d1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241985037400, "endTime": 241985050900}, "additional": {"logType": "info", "children": [], "durationId": "3d90dcfe-8fcf-4080-a174-80010cf9c088", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "34b0d1e3-445f-4479-a9b9-f65e276887c3", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241985070000, "endTime": 241989138100}, "additional": {"logType": "info", "children": [], "durationId": "32f6e873-612e-446b-a014-24f78ff3318d", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "e94e3042-744e-4aca-bcb5-0263975505e9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241989798000, "endTime": 241989814900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5cf35cf5-a6b6-4462-9ce0-725ee63258be", "logId": "499198d4-87af-41d4-a8eb-8c4983629abb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "499198d4-87af-41d4-a8eb-8c4983629abb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241989798000, "endTime": 241989814900}, "additional": {"logType": "info", "children": [], "durationId": "e94e3042-744e-4aca-bcb5-0263975505e9", "parent": "152f41f2-9731-4704-956c-8de05f398246"}}, {"head": {"id": "be8ba8c9-9477-4acf-b3ee-e32e7ac73813", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241992603100, "endTime": 242070577200}, "additional": {"children": ["8a14378f-d0c2-487d-8969-b22d84657021", "5e71e0fa-5bb0-473b-a507-e5080d93ff35"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5cf35cf5-a6b6-4462-9ce0-725ee63258be", "logId": "ca549c4f-bf0a-41f3-9afa-4ea3b1abbc80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a14378f-d0c2-487d-8969-b22d84657021", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241992604300, "endTime": 241998486700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be8ba8c9-9477-4acf-b3ee-e32e7ac73813", "logId": "3921acfe-1609-4b19-83a8-b00060472f65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e71e0fa-5bb0-473b-a507-e5080d93ff35", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241998503600, "endTime": 242070564900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be8ba8c9-9477-4acf-b3ee-e32e7ac73813", "logId": "ff0143b9-a948-4d34-9a32-17efc0eb5c74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "264c83c6-7238-4369-b895-40eec963e3da", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241992611100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be11071-b21b-4769-ac6e-849bf4bf7a0d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241998351200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3921acfe-1609-4b19-83a8-b00060472f65", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241992604300, "endTime": 241998486700}, "additional": {"logType": "info", "children": [], "durationId": "8a14378f-d0c2-487d-8969-b22d84657021", "parent": "ca549c4f-bf0a-41f3-9afa-4ea3b1abbc80"}}, {"head": {"id": "0f35b9e3-18ad-43ba-a748-269a42e8df9f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241998519900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b75d9e86-e852-4221-be2a-3d98b3faa663", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242006571300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35c9c762-87ed-45a2-b9e9-8802e3a3ec52", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242006712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6f0347-a15f-4b67-a485-1d231043ff76", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242007025300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e5e6fb-3cb8-48a5-8bb9-f8b211acb4f6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242007274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ee6f7c-3d27-40e0-8574-b58a8ebc5edc", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242009013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745bb527-ec9b-4bb3-934b-81e4b29309b3", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242014028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e9a3224-41ea-4f3a-ba70-f2f9b2c2a201", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242024728200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e48881-329c-490e-80d4-2ac3afd14286", "name": "Sdk init in 33 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242047738700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c0e14b0-e569-4a2a-97b9-11af5da75f27", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242047878500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 25}, "markType": "other"}}, {"head": {"id": "ef1e354e-ed92-4e12-b361-063f1e56940c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242047920600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 25}, "markType": "other"}}, {"head": {"id": "7e84b6b3-f7ac-448f-af31-2bf7a1ae74f1", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242070272700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe588f7-b9f7-4939-9f0b-285fcdb37512", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242070399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070ce234-8d11-46cc-8ebe-5101127f7710", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242070464800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df27e83a-6140-44c8-8d38-dba1928de00b", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242070516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0143b9-a948-4d34-9a32-17efc0eb5c74", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241998503600, "endTime": 242070564900}, "additional": {"logType": "info", "children": [], "durationId": "5e71e0fa-5bb0-473b-a507-e5080d93ff35", "parent": "ca549c4f-bf0a-41f3-9afa-4ea3b1abbc80"}}, {"head": {"id": "ca549c4f-bf0a-41f3-9afa-4ea3b1abbc80", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241992603100, "endTime": 242070577200}, "additional": {"logType": "info", "children": ["3921acfe-1609-4b19-83a8-b00060472f65", "ff0143b9-a948-4d34-9a32-17efc0eb5c74"], "durationId": "be8ba8c9-9477-4acf-b3ee-e32e7ac73813", "parent": "152f41f2-9731-4704-956c-8de05f398246"}}, {"head": {"id": "84dfb943-34fb-4926-b6e1-e6b429700169", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242071271800, "endTime": 242071466800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5cf35cf5-a6b6-4462-9ce0-725ee63258be", "logId": "0928a6ea-11b9-4741-8f71-f8db4212f838"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0928a6ea-11b9-4741-8f71-f8db4212f838", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242071271800, "endTime": 242071466800}, "additional": {"logType": "info", "children": [], "durationId": "84dfb943-34fb-4926-b6e1-e6b429700169", "parent": "152f41f2-9731-4704-956c-8de05f398246"}}, {"head": {"id": "152f41f2-9731-4704-956c-8de05f398246", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241989149000, "endTime": 242071494200}, "additional": {"logType": "info", "children": ["499198d4-87af-41d4-a8eb-8c4983629abb", "ca549c4f-bf0a-41f3-9afa-4ea3b1abbc80", "0928a6ea-11b9-4741-8f71-f8db4212f838"], "durationId": "5cf35cf5-a6b6-4462-9ce0-725ee63258be", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "be69b4e0-9b2d-46c0-9eef-33b49e30d9fa", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242072265900, "endTime": 242095415600}, "additional": {"children": ["487dab58-b29e-4dc3-8e77-a93a9762b16e", "1850c99c-a2c0-4ddf-a037-e70e41e7ced2", "48c86e02-9d07-4ad5-a9dc-349a6e61f3e7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a8d5c66-ba95-421f-bcbc-0e78fa14fd32", "logId": "4fd8ef5a-6d70-4f81-837f-879fb788b789"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "487dab58-b29e-4dc3-8e77-a93a9762b16e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242075474600, "endTime": 242075493300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be69b4e0-9b2d-46c0-9eef-33b49e30d9fa", "logId": "ac0240ac-eb15-4218-8a0b-32d51f9d4f00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac0240ac-eb15-4218-8a0b-32d51f9d4f00", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242075474600, "endTime": 242075493300}, "additional": {"logType": "info", "children": [], "durationId": "487dab58-b29e-4dc3-8e77-a93a9762b16e", "parent": "4fd8ef5a-6d70-4f81-837f-879fb788b789"}}, {"head": {"id": "1850c99c-a2c0-4ddf-a037-e70e41e7ced2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242077672900, "endTime": 242094134300}, "additional": {"children": ["5938e9af-7fd0-4f3f-a289-0157f38c4a0d", "3c897b32-0fec-4c15-bc17-907273f63982"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be69b4e0-9b2d-46c0-9eef-33b49e30d9fa", "logId": "c709ddaa-9ee9-4b9e-8195-9cc1528da01a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5938e9af-7fd0-4f3f-a289-0157f38c4a0d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242077674100, "endTime": 242081855000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1850c99c-a2c0-4ddf-a037-e70e41e7ced2", "logId": "3f6c3f0f-72df-4eb7-83dc-dc86ab9972d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c897b32-0fec-4c15-bc17-907273f63982", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242081870700, "endTime": 242094123200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1850c99c-a2c0-4ddf-a037-e70e41e7ced2", "logId": "166690dc-ca8f-468c-af9d-b41df8826ca9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b14c7a7-4642-482f-8f7b-bbc7b33f6836", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242077713100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe62f79-fe5d-41f1-9082-3d0459fe7ea0", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242081740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f6c3f0f-72df-4eb7-83dc-dc86ab9972d1", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242077674100, "endTime": 242081855000}, "additional": {"logType": "info", "children": [], "durationId": "5938e9af-7fd0-4f3f-a289-0157f38c4a0d", "parent": "c709ddaa-9ee9-4b9e-8195-9cc1528da01a"}}, {"head": {"id": "21f0460e-4954-4f00-b863-5bd57889b825", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242081888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab85232-476b-4432-b2cc-d63db0062b86", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242089004600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a4595df-6e54-4c94-ae25-f828a22601a4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242089234900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9519e1d-aa2a-4717-910a-e780d4a0f88f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242090018700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5336c64f-70da-47d5-810d-e4bc7a2cc7cd", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242090680000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0000747-9bca-4f3a-a331-9635b0ab1be6", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242090774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db0cb14a-4453-4f1b-8492-af2b19bdc086", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242090831400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636e9685-ce89-4e3e-a9c1-d313d23aacdc", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242090893100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05b3b284-5b74-48cf-aff3-008383fe8d05", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242093836900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "924594f9-791c-4c0b-9a99-07f5520b254c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242093949100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e8a50f5-cb94-4436-a5ac-f24415f15d66", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242094017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2079d133-34c0-43e8-b1fc-fb57ea8a5c37", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242094069800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "166690dc-ca8f-468c-af9d-b41df8826ca9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242081870700, "endTime": 242094123200}, "additional": {"logType": "info", "children": [], "durationId": "3c897b32-0fec-4c15-bc17-907273f63982", "parent": "c709ddaa-9ee9-4b9e-8195-9cc1528da01a"}}, {"head": {"id": "c709ddaa-9ee9-4b9e-8195-9cc1528da01a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242077672900, "endTime": 242094134300}, "additional": {"logType": "info", "children": ["3f6c3f0f-72df-4eb7-83dc-dc86ab9972d1", "166690dc-ca8f-468c-af9d-b41df8826ca9"], "durationId": "1850c99c-a2c0-4ddf-a037-e70e41e7ced2", "parent": "4fd8ef5a-6d70-4f81-837f-879fb788b789"}}, {"head": {"id": "48c86e02-9d07-4ad5-a9dc-349a6e61f3e7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242095388400, "endTime": 242095401600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be69b4e0-9b2d-46c0-9eef-33b49e30d9fa", "logId": "532ebd77-9858-44c7-9329-6c602ae693b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "532ebd77-9858-44c7-9329-6c602ae693b9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242095388400, "endTime": 242095401600}, "additional": {"logType": "info", "children": [], "durationId": "48c86e02-9d07-4ad5-a9dc-349a6e61f3e7", "parent": "4fd8ef5a-6d70-4f81-837f-879fb788b789"}}, {"head": {"id": "4fd8ef5a-6d70-4f81-837f-879fb788b789", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242072265900, "endTime": 242095415600}, "additional": {"logType": "info", "children": ["ac0240ac-eb15-4218-8a0b-32d51f9d4f00", "c709ddaa-9ee9-4b9e-8195-9cc1528da01a", "532ebd77-9858-44c7-9329-6c602ae693b9"], "durationId": "be69b4e0-9b2d-46c0-9eef-33b49e30d9fa", "parent": "5e8f9573-542b-4359-a133-e34899ec1554"}}, {"head": {"id": "5e8f9573-542b-4359-a133-e34899ec1554", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242071515100, "endTime": 242095427300}, "additional": {"logType": "info", "children": ["4fd8ef5a-6d70-4f81-837f-879fb788b789"], "durationId": "9a8d5c66-ba95-421f-bcbc-0e78fa14fd32", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "a2825505-f722-47da-ab74-e4f35000f5e9", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242109139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8770f10-1682-46df-a19d-d92445a69498", "name": "hvigorfile, resolve hvigorfile dependencies in 15 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242109715800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d2cb78-0804-4019-88d2-bf5a5df2af42", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242095435800, "endTime": 242109808700}, "additional": {"logType": "info", "children": [], "durationId": "d276312a-ab12-4159-bf50-b2d76c9d5837", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "0b7cfa8e-d88c-4851-a3f5-2d4a4ad435a7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242110775200, "endTime": 242110966700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "942ef49e-8445-41a3-a647-42d91511fc5e", "logId": "e6f42e03-864d-4ab8-834b-533377c3dbf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "992177ff-e7d3-4270-9201-b209f8ca0813", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242110803700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f42e03-864d-4ab8-834b-533377c3dbf6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242110775200, "endTime": 242110966700}, "additional": {"logType": "info", "children": [], "durationId": "0b7cfa8e-d88c-4851-a3f5-2d4a4ad435a7", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "ac7110da-1adf-417e-8dd0-e834053692db", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242112734400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bb1d381-2774-485d-953c-5bdd50663ba7", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242123397200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06e8141-ee74-4ae8-914e-36b5a30084bd", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242110980600, "endTime": 242124350000}, "additional": {"logType": "info", "children": [], "durationId": "309a685d-5e7b-4745-b6fb-2eee1306b30a", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "b8e443ef-2645-4319-9704-ad113326da17", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242124391600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7894554a-25b0-470e-a34f-b660b94db2f5", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242130269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb38432-bb5c-4697-a5ae-9888c829bbea", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242130368200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f72ba5f-49a3-43b1-97fa-43fa12c0d69b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242130590300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e89a451b-2132-46ae-80b7-abd3063bf67b", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242136009400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ffe3d3-4a62-4b2d-a30c-c37112e1a354", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242136118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539a1c03-de27-419e-9e85-62ed10a7ad24", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242124374000, "endTime": 242139041700}, "additional": {"logType": "info", "children": [], "durationId": "a5914c96-7807-4b9d-ad02-80fdb6ca987b", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "64d9b34d-22dc-418d-ac74-c5deb20626b3", "name": "Configuration phase cost:155 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242139091100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1142e92c-f10a-4d6a-8201-914be8e9cdc6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242139059700, "endTime": 242139181900}, "additional": {"logType": "info", "children": [], "durationId": "8904a709-cab3-49f9-b183-eaf1dfdfe3e0", "parent": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c"}}, {"head": {"id": "b32b0ac5-4df2-4945-8e7c-35f763fcc87c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241977918700, "endTime": 242139195800}, "additional": {"logType": "info", "children": ["27ab47c6-61b0-4880-90ea-521d01dfe3d1", "34b0d1e3-445f-4479-a9b9-f65e276887c3", "152f41f2-9731-4704-956c-8de05f398246", "5e8f9573-542b-4359-a133-e34899ec1554", "e2d2cb78-0804-4019-88d2-bf5a5df2af42", "f06e8141-ee74-4ae8-914e-36b5a30084bd", "539a1c03-de27-419e-9e85-62ed10a7ad24", "1142e92c-f10a-4d6a-8201-914be8e9cdc6", "e6f42e03-864d-4ab8-834b-533377c3dbf6"], "durationId": "942ef49e-8445-41a3-a647-42d91511fc5e", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "390c64a9-121e-490e-ae3a-6104fc5e6f2c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242140345900, "endTime": 242140357400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19092e73-752a-43ba-a87c-7907325f3ebe", "logId": "a28901b7-4ccc-480c-9520-d8018145545d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a28901b7-4ccc-480c-9520-d8018145545d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242140345900, "endTime": 242140357400}, "additional": {"logType": "info", "children": [], "durationId": "390c64a9-121e-490e-ae3a-6104fc5e6f2c", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "c02afd9b-422d-4c59-b7de-076538b33e88", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242139220900, "endTime": 242140367400}, "additional": {"logType": "info", "children": [], "durationId": "91da98e2-20a1-4de9-b76b-7ee687d7ba72", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "842d6dfb-4e92-406e-a9af-70720552a0ce", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242140373000, "endTime": 242140391100}, "additional": {"logType": "info", "children": [], "durationId": "9ccb30eb-2692-4d17-a004-2f75add2525d", "parent": "7550dd04-74b6-490a-a741-b13948e04686"}}, {"head": {"id": "7550dd04-74b6-490a-a741-b13948e04686", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241963043600, "endTime": 242140396000}, "additional": {"logType": "info", "children": ["86ee399a-cb05-43f7-b316-807c7e7b30c1", "b32b0ac5-4df2-4945-8e7c-35f763fcc87c", "c02afd9b-422d-4c59-b7de-076538b33e88", "842d6dfb-4e92-406e-a9af-70720552a0ce", "1f9fa5bb-6af5-4313-8f44-d7160fbe627c", "c239e447-108a-41ef-a2b8-92608e275fe8", "a28901b7-4ccc-480c-9520-d8018145545d"], "durationId": "19092e73-752a-43ba-a87c-7907325f3ebe"}}, {"head": {"id": "e5b3b693-26b6-4839-9abe-4fbb6f112a48", "name": "Configuration task cost before running: 182 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242141077000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f072e5fa-b6c5-43fa-892e-98fcac4e3191", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242148130900, "endTime": 242160193300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "41a20fef-5eaf-4a16-8a7f-398927dc33ea", "logId": "09dba2ff-696d-4230-8a09-75b138d12307"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41a20fef-5eaf-4a16-8a7f-398927dc33ea", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242142847800}, "additional": {"logType": "detail", "children": [], "durationId": "f072e5fa-b6c5-43fa-892e-98fcac4e3191"}}, {"head": {"id": "ea5f1ed8-c8b2-4e45-ae9e-7606eb4e8259", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242143709600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bfc0ede-4716-43e1-872a-af29b7a9202c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242143801800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd26049e-1f61-4c29-896e-a7757b0168eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242143860200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106082db-6485-4c39-949b-2f5ee1b08627", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242148142900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e492532-6fc1-4654-ae95-68a78e2b3950", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242159918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8822a945-3eb5-483c-9e37-b0ffa073054a", "name": "entry : default@PreBuild cost memory 0.**********", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242160067200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09dba2ff-696d-4230-8a09-75b138d12307", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242148130900, "endTime": 242160193300}, "additional": {"logType": "info", "children": [], "durationId": "f072e5fa-b6c5-43fa-892e-98fcac4e3191"}}, {"head": {"id": "8b64a8e1-8168-4005-9279-aa45b5dad24b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242165836800, "endTime": 242170238900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "420126fd-962a-4d55-abd5-25e9c5ccdcde", "logId": "cd34db45-3483-4c3f-aee4-318875340068"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "420126fd-962a-4d55-abd5-25e9c5ccdcde", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242164227000}, "additional": {"logType": "detail", "children": [], "durationId": "8b64a8e1-8168-4005-9279-aa45b5dad24b"}}, {"head": {"id": "02aba82a-2fab-449b-8590-cb53a3c5021e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242164827200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e567e61-b13f-4576-a89c-5109e7c7ef02", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242164919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee2d6eb2-d717-461e-9f7a-db27972eefd2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242164987100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ff8917-a65c-44fa-9f36-e1039d4b3206", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242165848400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf5b11c6-5003-45a0-9681-82e8d0141cd3", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242170025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0271b50e-2786-4474-ba62-b48ac2e499dc", "name": "entry : default@MergeProfile cost memory 0.13397216796875", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242170161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd34db45-3483-4c3f-aee4-318875340068", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242165836800, "endTime": 242170238900}, "additional": {"logType": "info", "children": [], "durationId": "8b64a8e1-8168-4005-9279-aa45b5dad24b"}}, {"head": {"id": "ba56e8fe-5041-42b5-9593-e622f2c69929", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242173801800, "endTime": 242176972700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "18cb958f-9e7e-473a-961c-819b1e4a481e", "logId": "059848d7-61c9-434d-b28c-4cfc3d4a23b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18cb958f-9e7e-473a-961c-819b1e4a481e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242172109800}, "additional": {"logType": "detail", "children": [], "durationId": "ba56e8fe-5041-42b5-9593-e622f2c69929"}}, {"head": {"id": "c3c3b25e-7355-4259-922f-c00e046f7c8a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242172672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f49ba3ca-d517-441e-9951-ae6167754093", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242172784000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b5c8c9c-3ace-4b8d-9a6b-f0b5f0235713", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242172846000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ceda92-a91c-45a0-b39e-5c0c3de65a27", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242173813900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8fe58d-206a-423b-bf3b-94ee8c1a94f8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242175115500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1093380-cbbe-48f2-bb23-6fb3b83e9169", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242176784900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d795aaa-d869-4e64-a3da-b3167ba26140", "name": "entry : default@CreateBuildProfile cost memory 0.1045989990234375", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242176897600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "059848d7-61c9-434d-b28c-4cfc3d4a23b6", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242173801800, "endTime": 242176972700}, "additional": {"logType": "info", "children": [], "durationId": "ba56e8fe-5041-42b5-9593-e622f2c69929"}}, {"head": {"id": "ae083461-1124-46d1-b404-49c7519d1726", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242180448300, "endTime": 242181192500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8cb3f07d-f798-4e64-9eca-b0e8bbc60353", "logId": "5229daa2-b69f-4a5f-a359-2ebc01c81a44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cb3f07d-f798-4e64-9eca-b0e8bbc60353", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242178827500}, "additional": {"logType": "detail", "children": [], "durationId": "ae083461-1124-46d1-b404-49c7519d1726"}}, {"head": {"id": "d67f195e-3f47-496e-893c-8ccb4f9ca9cc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242179409500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b815f33-314e-44d5-980d-f9463219468f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242179544400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bc2e9d-10e7-458a-987e-0141d3769e67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242179609500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0124abcd-824a-4171-b4d0-d3682d9aed7d", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242180463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e490dcd-2b7d-4d8f-9aac-ecf148d38e3c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242180676300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c128fbe7-56b1-4b1b-95ea-ae461110c12b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242180752300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc46cff-b491-48c6-b28d-b0ccfa734818", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242180819000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbdcd8b1-4f90-4828-b4a5-0680aeed09b1", "name": "entry : default@PreCheckSyscap cost memory 0.05062103271484375", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242181020500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3460571-74b8-4f78-8c99-7bd509c0d4e5", "name": "runTaskFromQueue task cost before running: 222 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242181132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5229daa2-b69f-4a5f-a359-2ebc01c81a44", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242180448300, "endTime": 242181192500, "totalTime": 662700}, "additional": {"logType": "info", "children": [], "durationId": "ae083461-1124-46d1-b404-49c7519d1726"}}, {"head": {"id": "51c57831-ad21-4b07-95a2-fe8190f1bff8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242196424400, "endTime": 242197654100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2baff47a-b2ea-4209-8b9f-e67ac294681f", "logId": "e72f0133-aeb8-4071-a5ce-c13254ce1de9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2baff47a-b2ea-4209-8b9f-e67ac294681f", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242183688300}, "additional": {"logType": "detail", "children": [], "durationId": "51c57831-ad21-4b07-95a2-fe8190f1bff8"}}, {"head": {"id": "ed499dd0-e5fa-4bf6-85d1-01de869bc10d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242184333200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "458b1bd5-5516-4ef1-afb6-64cb2dfb9504", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242184454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99804ee-412f-4d20-ac20-5547d31049e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242184525600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226eaba6-b387-4368-9cb5-343daa9983c8", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242196445600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "135069a8-f83c-483e-b90e-66323d9fbf39", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242196766500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b2d4961-3061-4c0e-8bca-35fdb3abd43c", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242197484200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d99e086e-f5b7-4779-8210-5a4a6706ca5c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0705718994140625", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242197580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72f0133-aeb8-4071-a5ce-c13254ce1de9", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242196424400, "endTime": 242197654100}, "additional": {"logType": "info", "children": [], "durationId": "51c57831-ad21-4b07-95a2-fe8190f1bff8"}}, {"head": {"id": "91e5f493-b6eb-4a8a-af94-180b99168d51", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242201888200, "endTime": 242203729300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3bec4056-3a1f-4585-a976-a4d1d8b3e67e", "logId": "7ec3296c-7554-4e2c-b1d1-e4564dff743b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bec4056-3a1f-4585-a976-a4d1d8b3e67e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242199444800}, "additional": {"logType": "detail", "children": [], "durationId": "91e5f493-b6eb-4a8a-af94-180b99168d51"}}, {"head": {"id": "586a40e0-ce1f-4312-b2d4-e099b47c2763", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242200016300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b55824-60b0-474a-b758-edb8692040b2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242200117900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07daceb4-0cea-40b9-bbd9-0724a700f945", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242200178000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21cc495e-352f-46ce-a00c-b161787fed3a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242201899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73b94ff9-d695-4f50-8dd5-2e5d80f907b1", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242203510400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daed0752-9e72-4c6d-9c41-47d3a8bb5302", "name": "entry : default@ProcessProfile cost memory 0.05947113037109375", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242203642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec3296c-7554-4e2c-b1d1-e4564dff743b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242201888200, "endTime": 242203729300}, "additional": {"logType": "info", "children": [], "durationId": "91e5f493-b6eb-4a8a-af94-180b99168d51"}}, {"head": {"id": "64defd2c-3b7d-4af5-8d7f-485549829b9d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242208363200, "endTime": 242215807200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "62a1d854-4a63-46ef-8a2e-d33788181392", "logId": "9901be2c-363b-4f01-b8ab-f7e788d2a694"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62a1d854-4a63-46ef-8a2e-d33788181392", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242205691600}, "additional": {"logType": "detail", "children": [], "durationId": "64defd2c-3b7d-4af5-8d7f-485549829b9d"}}, {"head": {"id": "4025b718-bf1e-4899-a9ff-b071db57d971", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242206303500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7eaeed1-ee2d-4a1d-88ae-f5a6330599d1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242206411200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a3ebd3-7497-4790-9158-03da33af19a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242206473500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aaf753f-b1c5-444c-b192-dce57f800592", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242208377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9042996-f36f-4307-8a45-89c5bfad336e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242215591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e683920b-da0a-4277-85b0-6d9726dc228a", "name": "entry : default@ProcessRouterMap cost memory 0.217681884765625", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242215723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9901be2c-363b-4f01-b8ab-f7e788d2a694", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242208363200, "endTime": 242215807200}, "additional": {"logType": "info", "children": [], "durationId": "64defd2c-3b7d-4af5-8d7f-485549829b9d"}}, {"head": {"id": "7edb48c4-6ad6-46a6-a8fd-ce6c3699aba5", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242224655500, "endTime": 242229113200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9fd1c6b0-b25f-46c5-91ba-bb80cb14eca1", "logId": "f0225c6d-dad6-467b-a102-e7755bd1f7a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fd1c6b0-b25f-46c5-91ba-bb80cb14eca1", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242218997900}, "additional": {"logType": "detail", "children": [], "durationId": "7edb48c4-6ad6-46a6-a8fd-ce6c3699aba5"}}, {"head": {"id": "3103ecd7-4d51-4d72-831f-a6e27f7e7814", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242219543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054b84b6-7ee7-48d0-8ddb-c7546cf8ad60", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242219634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e84848c5-88b2-4c00-8a00-f55773da10ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242219692700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c25053c-ecea-425d-a70d-81e7e4eedabc", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242221103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c10f10f9-192a-4d97-a2e6-7771d3c2879b", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242227006700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2bced4-a967-48de-955f-5c69a3c6e5d8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242227185100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f950a4-de15-42de-800e-113a577b2c66", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242227253500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d25ef772-7865-4692-bfab-de410284b666", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242227308700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1b67ed-393c-4fa4-81ab-4bcd7e2eb82c", "name": "entry : default@PreviewProcessResource cost memory 0.3311309814453125", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242227403500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47c3699-6f4e-4638-929d-105f48ee72ae", "name": "runTaskFromQueue task cost before running: 270 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242229027800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0225c6d-dad6-467b-a102-e7755bd1f7a6", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242224655500, "endTime": 242229113200, "totalTime": 2817800}, "additional": {"logType": "info", "children": [], "durationId": "7edb48c4-6ad6-46a6-a8fd-ce6c3699aba5"}}, {"head": {"id": "49e4ec86-4069-4215-9492-0a990e5d8d00", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242237564800, "endTime": 242262587300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b241cdb1-0977-4a2f-ad51-99d3bb2299d8", "logId": "f5bc388f-1df0-4256-ab9a-4478f1ade2b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b241cdb1-0977-4a2f-ad51-99d3bb2299d8", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242232460100}, "additional": {"logType": "detail", "children": [], "durationId": "49e4ec86-4069-4215-9492-0a990e5d8d00"}}, {"head": {"id": "8fa38860-e4af-481e-80dd-cd3498b28b3f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242233015300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a91f454f-6678-45a3-b4ca-d25d5fb838cb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242233114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67753410-293e-46ca-b614-5fe3f6c5bacf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242233188300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be2e614-affc-4a7c-b231-0785968f9e46", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242237582100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c068e4ad-4814-429b-af1e-5e98adf38da9", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242262101500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40af3314-c3a1-4f33-8760-a6e5641b3d13", "name": "entry : default@GenerateLoaderJson cost memory 0.8509674072265625", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242262423600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5bc388f-1df0-4256-ab9a-4478f1ade2b1", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242237564800, "endTime": 242262587300}, "additional": {"logType": "info", "children": [], "durationId": "49e4ec86-4069-4215-9492-0a990e5d8d00"}}, {"head": {"id": "89d6e130-af0a-4d30-a7cf-24d221cd50e0", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242277270100, "endTime": 243249602700}, "additional": {"children": ["eabaf86e-259f-48a3-89f8-3f376c480696", "fc192e83-8004-4332-9113-c001ba4071ce", "adb7ee91-e8c6-4d16-adf0-a1ad2958be59", "c4b67c1c-bc4d-4ea7-8f5e-d6d06fdac1b2", "f0d95ae0-e4c6-4068-a98f-022233218738"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "e3666d69-4757-42d8-a19a-4dc0b03e9ddf", "logId": "38c8154c-3b82-44c3-a8ce-673546a1c17e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3666d69-4757-42d8-a19a-4dc0b03e9ddf", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242272789300}, "additional": {"logType": "detail", "children": [], "durationId": "89d6e130-af0a-4d30-a7cf-24d221cd50e0"}}, {"head": {"id": "060ea898-80c2-4b10-b821-cb1e56751998", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242273412100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b90886-da47-4560-995e-f75979c4b757", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242273515200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b5a2ecc-06cb-4248-9882-c41c0d47454a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242273574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5766f2d4-2d02-4b52-9469-bb893a578a6b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242274555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e881764-6a06-4bb3-bf6c-c687d75ad167", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242277357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abdeeeaa-1664-430d-9f11-0805e7500fea", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242348279700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5717e725-9063-4bc1-9d05-f43cab48ee95", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 71 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242348495500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eabaf86e-259f-48a3-89f8-3f376c480696", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242349926700, "endTime": 242385039400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89d6e130-af0a-4d30-a7cf-24d221cd50e0", "logId": "b22ed897-37b7-4051-9135-27affcaab18d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b22ed897-37b7-4051-9135-27affcaab18d", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242349926700, "endTime": 242385039400}, "additional": {"logType": "info", "children": [], "durationId": "eabaf86e-259f-48a3-89f8-3f376c480696", "parent": "38c8154c-3b82-44c3-a8ce-673546a1c17e"}}, {"head": {"id": "517120b6-45f6-4926-92e1-6e5b46296a18", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242385797600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc192e83-8004-4332-9113-c001ba4071ce", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242386863100, "endTime": 242612572100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89d6e130-af0a-4d30-a7cf-24d221cd50e0", "logId": "316c3e9d-6226-4124-9990-76657ce2cbf8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "816a1aaf-dcd4-43a3-ad1e-0cc3a4cbade1", "name": "current process  memoryUsage: {\n  rss: 160342016,\n  heapTotal: 119607296,\n  heapUsed: 107453616,\n  external: 3109235,\n  arrayBuffers: 103102\n} os memoryUsage :11.150402069091797", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242388626200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c4fb3fa-eb7c-4325-8031-4ba342ae0f19", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242610962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316c3e9d-6226-4124-9990-76657ce2cbf8", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242386863100, "endTime": 242612572100}, "additional": {"logType": "info", "children": [], "durationId": "fc192e83-8004-4332-9113-c001ba4071ce", "parent": "38c8154c-3b82-44c3-a8ce-673546a1c17e"}}, {"head": {"id": "ecf0c158-db17-4206-9dc5-047311f0c1b5", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242612808300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb7ee91-e8c6-4d16-adf0-a1ad2958be59", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242613762600, "endTime": 242826899400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89d6e130-af0a-4d30-a7cf-24d221cd50e0", "logId": "dc2cf1ff-56f5-435d-92ae-d7b1be580f57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8f90e2f-dfb3-4091-9fc3-7d470c87e0ba", "name": "current process  memoryUsage: {\n  rss: 162123776,\n  heapTotal: 119869440,\n  heapUsed: 109829664,\n  external: 3156681,\n  arrayBuffers: 150563\n} os memoryUsage :11.16024398803711", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242614682100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a786d97a-28d4-464b-a690-4e159ee3aab8", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242825077500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc2cf1ff-56f5-435d-92ae-d7b1be580f57", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242613762600, "endTime": 242826899400}, "additional": {"logType": "info", "children": [], "durationId": "adb7ee91-e8c6-4d16-adf0-a1ad2958be59", "parent": "38c8154c-3b82-44c3-a8ce-673546a1c17e"}}, {"head": {"id": "9374caa6-34af-434c-be23-093b455a8c3c", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242827251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4b67c1c-bc4d-4ea7-8f5e-d6d06fdac1b2", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242828248000, "endTime": 243016701900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89d6e130-af0a-4d30-a7cf-24d221cd50e0", "logId": "c6ac7e9e-67fa-4651-8237-3ef9d6503914"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53b9f1a7-dcd9-44b8-a0db-01d9975f9b29", "name": "current process  memoryUsage: {\n  rss: 161222656,\n  heapTotal: 119869440,\n  heapUsed: 110109216,\n  external: 3164999,\n  arrayBuffers: 158945\n} os memoryUsage :11.177326202392578", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242829244700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d2ee66-99b7-4cf2-be9b-61a2c17f2555", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243014380300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ac7e9e-67fa-4651-8237-3ef9d6503914", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242828248000, "endTime": 243016701900}, "additional": {"logType": "info", "children": [], "durationId": "c4b67c1c-bc4d-4ea7-8f5e-d6d06fdac1b2", "parent": "38c8154c-3b82-44c3-a8ce-673546a1c17e"}}, {"head": {"id": "cd631c8a-5336-47b7-aacb-b31854d80a5d", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243017358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d95ae0-e4c6-4068-a98f-022233218738", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243018407700, "endTime": 243247484000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "89d6e130-af0a-4d30-a7cf-24d221cd50e0", "logId": "521f05b2-c7cb-4747-ab4d-72abb35966b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47d99198-70c2-49e9-8afc-4d6458667e91", "name": "current process  memoryUsage: {\n  rss: 161607680,\n  heapTotal: 119869440,\n  heapUsed: 110424576,\n  external: 3165125,\n  arrayBuffers: 159990\n} os memoryUsage :11.185089111328125", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243019266200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd17da5f-64bd-41f2-816a-45d99f712933", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243244383100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521f05b2-c7cb-4747-ab4d-72abb35966b5", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243018407700, "endTime": 243247484000}, "additional": {"logType": "info", "children": [], "durationId": "f0d95ae0-e4c6-4068-a98f-022233218738", "parent": "38c8154c-3b82-44c3-a8ce-673546a1c17e"}}, {"head": {"id": "0b2658a5-827f-4866-a6a6-92a4b85342ef", "name": "entry : default@PreviewCompileResource cost memory 3.3404388427734375", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243249335200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "814dc066-d680-4eb5-affc-7c17c526753a", "name": "runTaskFromQueue task cost before running: 1 s 291 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243249523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c8154c-3b82-44c3-a8ce-673546a1c17e", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 242277270100, "endTime": 243249602700, "totalTime": 972205800}, "additional": {"logType": "info", "children": ["b22ed897-37b7-4051-9135-27affcaab18d", "316c3e9d-6226-4124-9990-76657ce2cbf8", "dc2cf1ff-56f5-435d-92ae-d7b1be580f57", "c6ac7e9e-67fa-4651-8237-3ef9d6503914", "521f05b2-c7cb-4747-ab4d-72abb35966b5"], "durationId": "89d6e130-af0a-4d30-a7cf-24d221cd50e0"}}, {"head": {"id": "13dd0a57-7a7d-4af7-b242-64dc6f1cdb5b", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252912000, "endTime": 243253374600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b387d3ad-b435-43f9-a2c0-1a592c00623f", "logId": "c318b69c-3160-427e-a8c5-bcc0797c3377"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b387d3ad-b435-43f9-a2c0-1a592c00623f", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252161300}, "additional": {"logType": "detail", "children": [], "durationId": "13dd0a57-7a7d-4af7-b242-64dc6f1cdb5b"}}, {"head": {"id": "e7c4ea91-7a60-46bb-8498-cdbfdfd80807", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252673300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e9ed44f-468f-4a5c-9b8c-7dfb43a8e6f1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0ccc3c8-cbe8-4031-bf11-54a545888bb8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252818100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea3eb709-659d-4e65-ad30-e1fb04b775f8", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252924000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b82ea31-8981-4078-b630-7a876704838c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243253016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46212d8c-ee4b-4a9b-a9e5-b8d008a48c14", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243253069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ee86e08-a6bc-4d69-8e46-4f6074b0153e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243253129100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e42a891a-52e2-4296-8368-0f8a40404b6d", "name": "entry : default@PreviewHookCompileResource cost memory 0.05193328857421875", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243253223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2712e40-bbf0-4b12-9240-bd435b87ceaf", "name": "runTaskFromQueue task cost before running: 1 s 295 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243253314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c318b69c-3160-427e-a8c5-bcc0797c3377", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243252912000, "endTime": 243253374600, "totalTime": 379500}, "additional": {"logType": "info", "children": [], "durationId": "13dd0a57-7a7d-4af7-b242-64dc6f1cdb5b"}}, {"head": {"id": "e575bcd9-01ea-4a7b-9357-5a4fd23d2441", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243256316200, "endTime": 243264347900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "3477a936-677e-47be-adf9-962609a53184", "logId": "4ad296fc-7570-4348-859b-524b5c9491c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3477a936-677e-47be-adf9-962609a53184", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243255027200}, "additional": {"logType": "detail", "children": [], "durationId": "e575bcd9-01ea-4a7b-9357-5a4fd23d2441"}}, {"head": {"id": "41969389-87a6-4531-aae5-dac3664d186d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243255562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec59d989-7822-4694-8acb-1eb5d9a5d8d8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243255658900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f8e588-6e3e-4a2b-a227-1c0b4d7de003", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243255718000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591320c6-ef40-4d29-bbd2-c0df7923abce", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243256330600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "105925da-d75d-4f3b-9fcb-bf8a2f68fb62", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243257825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12bf11d-e0eb-4317-af2e-c4bcf75ad953", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243257942600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "685f6510-3938-44fa-bbf2-3f7ce2ec0f9f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243258028400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bc4daf6-247d-4a43-af5c-e7e13fcae8e6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243258083400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6834ae8d-7f10-4a5c-b594-e24cbb826d54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243258137800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7734e213-3203-488e-a378-7c8d3eb2954c", "name": "entry : default@CopyPreviewProfile cost memory 0.22997283935546875", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243264087400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7547a0-4d30-4641-aa07-029b5d3bd1d7", "name": "runTaskFromQueue task cost before running: 1 s 305 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243264272100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad296fc-7570-4348-859b-524b5c9491c8", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243256316200, "endTime": 243264347900, "totalTime": 7918500}, "additional": {"logType": "info", "children": [], "durationId": "e575bcd9-01ea-4a7b-9357-5a4fd23d2441"}}, {"head": {"id": "0144dbdc-47bb-4076-a07a-22cfa7f519c2", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243268632500, "endTime": 243269259700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "8540783d-b127-45eb-ac35-34ae6eea01ea", "logId": "abd9fb2d-b4c0-41fa-bb3a-ab971e499229"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8540783d-b127-45eb-ac35-34ae6eea01ea", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243267190600}, "additional": {"logType": "detail", "children": [], "durationId": "0144dbdc-47bb-4076-a07a-22cfa7f519c2"}}, {"head": {"id": "17d943d7-600c-4435-b801-c51f7f5b90da", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243267731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c929c3c4-98ee-4204-8d84-ec4440547040", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243267832600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65b7bde7-ef26-4f04-8c0b-b3174345ff50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243267891800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a876d52c-1437-4050-b666-1007f584e3c1", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243268643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e02738-4540-4b1d-98df-2cc260844afc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243268762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d215a61-c2bb-4bba-b8f8-cfcc17c3f50e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243268818600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d5320e2-e50d-4b0c-95c1-36ad7c09e69a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243268867500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3573742-5e7e-456e-873a-c9ba1563c172", "name": "entry : default@ReplacePreviewerPage cost memory 0.05194854736328125", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243269099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76cb3406-1196-42ab-a941-6a4df3676f1e", "name": "runTaskFromQueue task cost before running: 1 s 310 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243269204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abd9fb2d-b4c0-41fa-bb3a-ab971e499229", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243268632500, "endTime": 243269259700, "totalTime": 547900}, "additional": {"logType": "info", "children": [], "durationId": "0144dbdc-47bb-4076-a07a-22cfa7f519c2"}}, {"head": {"id": "c94f7e18-7d9b-4a3f-99a8-4735a0822ebb", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243270888400, "endTime": 243271194400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e2a2c170-6896-422e-abec-0a7bb9c0af3a", "logId": "715ec849-5323-4b9e-80e9-24bdcea103d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2a2c170-6896-422e-abec-0a7bb9c0af3a", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243270826500}, "additional": {"logType": "detail", "children": [], "durationId": "c94f7e18-7d9b-4a3f-99a8-4735a0822ebb"}}, {"head": {"id": "945285ef-b544-451b-ada4-d70d455f9a75", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243270897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32006ca5-2283-4be4-a9bf-f388381c28e4", "name": "entry : buildPreviewerResource cost memory 0.01213836669921875", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243271040400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e5fb206-bc52-4636-bc95-8904665108c7", "name": "runTaskFromQueue task cost before running: 1 s 312 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243271138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715ec849-5323-4b9e-80e9-24bdcea103d0", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243270888400, "endTime": 243271194400, "totalTime": 230300}, "additional": {"logType": "info", "children": [], "durationId": "c94f7e18-7d9b-4a3f-99a8-4735a0822ebb"}}, {"head": {"id": "2dff2783-6f03-4b05-a629-fa43058eaf15", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243274481700, "endTime": 243278234200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "aaf65c95-4b4b-435b-9cbc-dd9727d946e2", "logId": "4de57ebb-2a83-4423-ac58-61420c7cc327"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaf65c95-4b4b-435b-9cbc-dd9727d946e2", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243273010900}, "additional": {"logType": "detail", "children": [], "durationId": "2dff2783-6f03-4b05-a629-fa43058eaf15"}}, {"head": {"id": "d85ccf1d-8565-4481-991d-764cd254cf15", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243273566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e07fbb-4ea2-4e25-9691-bcad4f1bb775", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243273713500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7d6c8a-7f54-47cd-8cfb-8d35154c5440", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243273771900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "308d6a47-03ad-43bb-8f96-c1a3dc7a420c", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243274494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dda6c17-c68a-4a98-87cf-766cf3edd1ce", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243276743500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f449b6cf-21d6-4f7a-9786-feff30adbd6f", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243276861300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6503614-72b2-43a8-a4d9-7f1bf12f5ead", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243276952200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9caeb211-d1e3-44f6-8364-a014d931c8a1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243277004600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed897386-44a7-48fb-9b75-2ea6c1c8ebb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243277058800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7406d726-0d4a-4b47-a3fe-70b01ec77762", "name": "entry : default@PreviewUpdateAssets cost memory 0.15375518798828125", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243278041100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4186b1-67db-4f51-86af-1b9f148412d5", "name": "runTaskFromQueue task cost before running: 1 s 319 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243278167400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de57ebb-2a83-4423-ac58-61420c7cc327", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243274481700, "endTime": 243278234200, "totalTime": 3656500}, "additional": {"logType": "info", "children": [], "durationId": "2dff2783-6f03-4b05-a629-fa43058eaf15"}}, {"head": {"id": "f9dc92c8-706b-44ad-a2b7-0f3bb5f071e1", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243286665800, "endTime": 254306639600}, "additional": {"children": ["c3dcb052-1df7-4015-b0d9-cd3baf74b503"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "d2a3e8b8-e4c1-46e6-bc44-659aa99<PERSON><PERSON>", "logId": "8daa848a-8dd9-43cc-9e29-c63d810203b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2a3e8b8-e4c1-46e6-bc44-659aa99<PERSON><PERSON>", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243280694200}, "additional": {"logType": "detail", "children": [], "durationId": "f9dc92c8-706b-44ad-a2b7-0f3bb5f071e1"}}, {"head": {"id": "19942557-7c2d-469b-83d4-edf98331b996", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243281206500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d892f74-7413-484c-b11d-35f3dc141670", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243281302400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51504817-68cf-4882-8986-efba538a0aed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243281361600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc13026-da3f-4261-a5fb-a5dc0f82c699", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243286689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b640b8f-7f5d-4eae-942d-0b8bd409c2b1", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243324711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1686f981-ef66-4fe7-a15f-e7faf2eed876", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243324889200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 243341611000, "endTime": 254303526400}, "additional": {"children": ["4cfe1628-ab70-4cc7-9882-de7f317a041c", "fceb78e8-ef84-4074-9ecd-d657524448b3", "8a8bdf6c-2e94-4f0d-a899-835c33a44c17", "52d6c985-b777-4e2a-8790-ca66b5db3d45", "aadc2d01-76ee-4d02-acea-0d0e3fa57456", "bdde10dd-077c-469e-87b7-e49af8af0f71", "8e77c32a-7eab-4ab3-89dc-beb1560b02e8", "842db97b-07aa-4ebc-b81b-37804ece1a5c", "866d3bd2-1134-4f56-ba10-fa8664a69c7b", "cbe7c062-f4dc-480f-bdef-5890e68ddd14", "73ce466b-76d2-4be4-abc3-335358310eca", "c26d1c8a-59f0-46df-8db4-f81017a35b8b", "a8aa67c4-b7e0-46bd-ad76-7118273406f3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f9dc92c8-706b-44ad-a2b7-0f3bb5f071e1", "logId": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa043997-8198-4e30-8ebc-c0135045134b", "name": "entry : default@PreviewArkTS cost memory -0.7759857177734375", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243344169700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a60da91b-2e5d-405b-ac34-bab1d7ab2edf", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 246688975000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cfe1628-ab70-4cc7-9882-de7f317a041c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 246690634000, "endTime": 246690716900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "2efac2f2-fd08-4935-85cb-7f40c02af90e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2efac2f2-fd08-4935-85cb-7f40c02af90e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 246690634000, "endTime": 246690716900}, "additional": {"logType": "info", "children": [], "durationId": "4cfe1628-ab70-4cc7-9882-de7f317a041c", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "5e9d5b96-552a-4f1c-97c8-7b1fa9a365a8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251446798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fceb78e8-ef84-4074-9ecd-d657524448b3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 251447891800, "endTime": 251447914300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "fa706bb3-4c78-4bc0-8e1f-d1d1ba55eae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa706bb3-4c78-4bc0-8e1f-d1d1ba55eae3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251447891800, "endTime": 251447914300}, "additional": {"logType": "info", "children": [], "durationId": "fceb78e8-ef84-4074-9ecd-d657524448b3", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "157d6ed9-62eb-4477-9977-67f400d392e4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251727577000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a8bdf6c-2e94-4f0d-a899-835c33a44c17", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 251728869500, "endTime": 251728894700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "37b04632-03e5-4d7e-a753-45cc75ddd4f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37b04632-03e5-4d7e-a753-45cc75ddd4f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251728869500, "endTime": 251728894700}, "additional": {"logType": "info", "children": [], "durationId": "8a8bdf6c-2e94-4f0d-a899-835c33a44c17", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "8decd5b4-3339-4dcb-9f29-3ea98dcce0cc", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251843209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52d6c985-b777-4e2a-8790-ca66b5db3d45", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 251844449200, "endTime": 251844471300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "bb51d42c-a88e-4d52-aa70-0af09cb222b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb51d42c-a88e-4d52-aa70-0af09cb222b6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251844449200, "endTime": 251844471300}, "additional": {"logType": "info", "children": [], "durationId": "52d6c985-b777-4e2a-8790-ca66b5db3d45", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "e9c47a4c-8da8-4bac-ac62-b7a7d496e80b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251938814100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aadc2d01-76ee-4d02-acea-0d0e3fa57456", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 251939774000, "endTime": 251939793800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "9b5458e5-0899-4721-b711-998669a2b02d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b5458e5-0899-4721-b711-998669a2b02d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 251939774000, "endTime": 251939793800}, "additional": {"logType": "info", "children": [], "durationId": "aadc2d01-76ee-4d02-acea-0d0e3fa57456", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "cbf0f52b-e604-47c7-a93f-763f228ed8e1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 252010792300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdde10dd-077c-469e-87b7-e49af8af0f71", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 252012071500, "endTime": 252012098800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "0a4ecb92-308b-4624-bc0d-a809110d8a31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a4ecb92-308b-4624-bc0d-a809110d8a31", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 252012071500, "endTime": 252012098800}, "additional": {"logType": "info", "children": [], "durationId": "bdde10dd-077c-469e-87b7-e49af8af0f71", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "8f2c17ba-4198-48e0-bb00-82450e17b47a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 252183731700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e77c32a-7eab-4ab3-89dc-beb1560b02e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 252185354100, "endTime": 252185377800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "4fac44b4-edaf-4d40-a0b2-f03415e1325f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fac44b4-edaf-4d40-a0b2-f03415e1325f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 252185354100, "endTime": 252185377800}, "additional": {"logType": "info", "children": [], "durationId": "8e77c32a-7eab-4ab3-89dc-beb1560b02e8", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "27f203c8-4309-4797-a825-5171fe2e22a2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 252281195200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "842db97b-07aa-4ebc-b81b-37804ece1a5c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 252282582200, "endTime": 252282611700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "8698b357-a9b5-4933-acad-76ead8658fa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8698b357-a9b5-4933-acad-76ead8658fa9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 252282582200, "endTime": 252282611700}, "additional": {"logType": "info", "children": [], "durationId": "842db97b-07aa-4ebc-b81b-37804ece1a5c", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "22554236-f65d-45d0-9143-196dc1acc301", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254302090800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "866d3bd2-1134-4f56-ba10-fa8664a69c7b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 254303285200, "endTime": 254303309300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "26f5eff6-9f33-45fc-8aad-93c497e3e699"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26f5eff6-9f33-45fc-8aad-93c497e3e699", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254303285200, "endTime": 254303309300}, "additional": {"logType": "info", "children": [], "durationId": "866d3bd2-1134-4f56-ba10-fa8664a69c7b", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 243341611000, "endTime": 254303526400}, "additional": {"logType": "info", "children": ["2efac2f2-fd08-4935-85cb-7f40c02af90e", "fa706bb3-4c78-4bc0-8e1f-d1d1ba55eae3", "37b04632-03e5-4d7e-a753-45cc75ddd4f8", "bb51d42c-a88e-4d52-aa70-0af09cb222b6", "9b5458e5-0899-4721-b711-998669a2b02d", "0a4ecb92-308b-4624-bc0d-a809110d8a31", "4fac44b4-edaf-4d40-a0b2-f03415e1325f", "8698b357-a9b5-4933-acad-76ead8658fa9", "26f5eff6-9f33-45fc-8aad-93c497e3e699", "884341d9-8f49-4fda-8eef-699ea500d737", "9c950fa9-bdce-45cc-81c9-204eca1b81ed", "06308484-9941-4103-9996-c2b73e430304", "4c620c04-1013-45ba-9f81-28a2ccbfdc79"], "durationId": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "parent": "8daa848a-8dd9-43cc-9e29-c63d810203b7"}}, {"head": {"id": "cbe7c062-f4dc-480f-bdef-5890e68ddd14", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 245590006300, "endTime": 246657069900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "884341d9-8f49-4fda-8eef-699ea500d737"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "884341d9-8f49-4fda-8eef-699ea500d737", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 245590006300, "endTime": 246657069900}, "additional": {"logType": "info", "children": [], "durationId": "cbe7c062-f4dc-480f-bdef-5890e68ddd14", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "73ce466b-76d2-4be4-abc3-335358310eca", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 246657295900, "endTime": 246662744800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "9c950fa9-bdce-45cc-81c9-204eca1b81ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c950fa9-bdce-45cc-81c9-204eca1b81ed", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 246657295900, "endTime": 246662744800}, "additional": {"logType": "info", "children": [], "durationId": "73ce466b-76d2-4be4-abc3-335358310eca", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "c26d1c8a-59f0-46df-8db4-f81017a35b8b", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 246662884300, "endTime": 246662892100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "06308484-9941-4103-9996-c2b73e430304"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06308484-9941-4103-9996-c2b73e430304", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 246662884300, "endTime": 246662892100}, "additional": {"logType": "info", "children": [], "durationId": "c26d1c8a-59f0-46df-8db4-f81017a35b8b", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "a8aa67c4-b7e0-46bd-ad76-7118273406f3", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Worker1", "startTime": 246662972800, "endTime": 254302162500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3dcb052-1df7-4015-b0d9-cd3baf74b503", "logId": "4c620c04-1013-45ba-9f81-28a2ccbfdc79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c620c04-1013-45ba-9f81-28a2ccbfdc79", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 246662972800, "endTime": 254302162500}, "additional": {"logType": "info", "children": [], "durationId": "a8aa67c4-b7e0-46bd-ad76-7118273406f3", "parent": "3aa5ad62-f298-4f74-9ed7-b1912cf5189f"}}, {"head": {"id": "8daa848a-8dd9-43cc-9e29-c63d810203b7", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 243286665800, "endTime": 254306639600, "totalTime": 11019932000}, "additional": {"logType": "info", "children": ["3aa5ad62-f298-4f74-9ed7-b1912cf5189f"], "durationId": "f9dc92c8-706b-44ad-a2b7-0f3bb5f071e1"}}, {"head": {"id": "f331c8a4-561f-45bb-925c-128ed8e30c68", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254311159100, "endTime": 254311416800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "54e709d0-8e57-40fc-8014-55d312fe9138", "logId": "60772f10-ac60-4148-8720-0918a7e74b6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54e709d0-8e57-40fc-8014-55d312fe9138", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254311119500}, "additional": {"logType": "detail", "children": [], "durationId": "f331c8a4-561f-45bb-925c-128ed8e30c68"}}, {"head": {"id": "360f3383-b543-48fe-92db-0eb71db792de", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254311171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "458a89c7-cca0-4852-b7b8-9b976aef66a2", "name": "entry : PreviewBuild cost memory 0.01201629638671875", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254311280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806544dd-c3f0-41c5-9987-d707491765e6", "name": "runTaskFromQueue task cost before running: 12 s 353 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254311361200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60772f10-ac60-4148-8720-0918a7e74b6a", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254311159100, "endTime": 254311416800, "totalTime": 178700}, "additional": {"logType": "info", "children": [], "durationId": "f331c8a4-561f-45bb-925c-128ed8e30c68"}}, {"head": {"id": "b9a97c3c-7cca-4635-85c5-8e89140a868d", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254319331700, "endTime": 254319349200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d3b23101-0d5e-45b8-ba6c-b3adfdd0e417", "logId": "66634fb0-9a7e-41e9-983e-1394b0d6a648"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66634fb0-9a7e-41e9-983e-1394b0d6a648", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254319331700, "endTime": 254319349200}, "additional": {"logType": "info", "children": [], "durationId": "b9a97c3c-7cca-4635-85c5-8e89140a868d"}}, {"head": {"id": "b9227670-f625-4171-a348-fcfcf7566e06", "name": "BUILD SUCCESSFUL in 12 s 361 ms ", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254319424900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "b3f9c5c4-1817-4ed7-a17f-73e11749146b", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 241959248200, "endTime": 254319867600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 25}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "203ef107-5a97-4021-a6a3-6f28252883e3", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320013900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed717f7-9654-4ef5-b362-a8cdd443ec19", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320094400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113c8d4f-2483-438c-a908-04e1a9db6718", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320145300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cc1a890-e4d7-4d75-a626-de487ac5b14f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320191300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c843de-4b73-49d7-9b74-02dd39280da3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320234700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c93b7a-cd33-4d24-849a-b2bbae01dec6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320275300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c2ecdf-def7-4e1f-99b7-8f756183b423", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254320326500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf91fbf-423a-47af-91ba-372a2fefe2bf", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254321133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8178242-bcf4-46b6-8bbf-b39ad060b50d", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254335224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9430cd45-67bb-47d8-b29b-9e5204bb09a6", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254337848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3c4042-16de-41eb-881b-38568223ddcf", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254338295000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08bbb592-75be-4d8e-be95-6f004d2b423e", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254356877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15499255-b8b8-4454-a4cc-c464f9dce4f1", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:38 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254357683800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2005e6ff-3f1c-40cd-885f-613c19b5fa89", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254357905300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d60ec4-9c79-4ef4-922b-96410db305da", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254358656000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809e0bdc-0afb-4621-b02c-e5b7be8695b9", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254360133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c701e47b-5de6-4988-8699-90044151a123", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254360645500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f39b8498-4e1b-43c3-87b2-ce0ac2c40ac2", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254360999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490771a3-ffc6-403f-bc6b-ee510be8f299", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254361436100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acd2a21-d7b8-46c1-9a58-8604482558b8", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254364549400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88e91a27-80d2-44e6-ad7b-686160acae7a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254365366000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e2dfb89-3667-4335-ac87-3e4abbd4f347", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254365649900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2823d02c-20e2-4941-8c77-f7a122fa3248", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254381215300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6c68440-3ebf-400e-b34b-04c56858aedf", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254382228000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f47d9471-3b40-4fd6-b287-4a7edf673200", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254382343000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2538dcb-c44b-4971-86f2-f9a62759f902", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254382627900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9019c56-541f-4bbc-a8f0-ac78fbcf6894", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254383411500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16f1e585-d9fc-4395-95ec-59921ed87b3c", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254387331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1131ae-4230-4642-973e-f77dd2344bed", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254387656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf665c17-c6a3-480d-b01c-35e378ab8382", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254387960300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4299192b-060f-4035-a49d-ed828e9dead8", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254388294000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546072e0-0780-4cfe-b3d5-778a4bd0315e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:28 ms .", "description": "", "type": "log"}, "body": {"pid": 9104, "tid": "Main Thread", "startTime": 254388662500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}