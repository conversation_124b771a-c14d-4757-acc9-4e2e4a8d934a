import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';

/**
 * API响应结构
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 用户信息
 */
interface User {
  userId: number;
  phone: string;
  realName: string;
  status: number;
  createTime: string;
}

/**
 * 钱包信息
 */
interface Wallet {
  walletId: number;
  userId: number;
  balance: number;
  status: number;
  createTime: string;
  updateTime: string;
}

/**
 * 轮播图项目
 */
interface BannerItem {
  id: number;
  image: Resource;
  title: string;
  subtitle?: string;
}

/**
 * 服务项目
 */
interface ServiceItem {
  id: number;
  title: string;
  subtitle: string;
  icon: Resource;
  route?: string;
  color: string;
  bgColor: string;
}
@Entry
@Component
export struct HomePage {
  @State userId: number = 1; // 当前用户ID
  @State user: User | null = null; // 用户信息
  @State wallet: Wallet | null = null; // 钱包信息
  @State isLoading: boolean = false;
  @State showBalance: boolean = true; // 是否显示余额

  // 轮播图数据
  @State bannerItems: BannerItem[] = [
    {
      id: 1,
      image: $r('app.media.banner1'),
      title: '新用户专享红包',
      subtitle: '注册即送100元红包'
    },
    {
      id: 2,
      image: $r('app.media.banner2'),
      title: '限时特惠活动',
      subtitle: '转账免手续费'
    },
    {
      id: 3,
      image: $r('app.media.banner3'),
      title: '信用卡还款优惠',
      subtitle: '还款享受积分奖励'
    }
  ];

  aboutToAppear() {
    this.loadUserData();
    this.loadWalletData();
  }

  build() {
    Column() {
      // 顶部状态栏
      this.buildStatusBar()

      // 主要内容区域
      Scroll() {
        Column() {
          // 用户信息卡片
          this.buildUserCard()

          // 轮播图
          this.buildBanner()

          // 快捷操作
          this.buildQuickActions()

          // 全部服务
          this.buildServices()

          // 底部间距
          Row().height(30)
        }
        .width('100%')
        .padding({ bottom: 20 })
      }
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)
      .backgroundColor('#f8f9fa')
    }
    .width('100%')
    .height('100%')
  }

  // 顶部状态栏
  @Builder
  buildStatusBar() {
    Row() {
      Text('E-Wallet')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .layoutWeight(1)

      Image($r('app.media.search'))
        .width(24)
        .height(24)
        .onClick(() => {
          promptAction.showToast({
            message: '暂无新消息',
            duration: 1500
          });
        })
    }
    .width('100%')
    .height(56)
    .padding({ left: 20, right: 20 })
    .backgroundColor('#ffffff')
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }

  // 用户信息卡片
  @Builder
  buildUserCard() {
    Column() {
      // 用户基本信息
      Row() {
        Column() {
          Text(`你好，${this.user?.realName || '用户'}`)
            .fontSize(16)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)

          Text(this.user?.phone || '138****5678')
            .fontSize(14)
            .fontColor('#999999')
            .alignSelf(ItemAlign.Start)
            .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)

        // 头像
        Image($r('app.media.icon_avatar'))
          .width(48)
          .height(48)
          .borderRadius(24)
          .border({ width: 2, color: '#f0f0f0' })
      }
      .width('100%')
      .margin({ bottom: 20 })

      // 余额显示
      Column() {
        Row() {
          Text('钱包余额')
            .fontSize(14)
            .fontColor('#666666')

          Image(this.showBalance ? $r('app.media.eye_open') : $r('app.media.eye_close'))
            .width(20)
            .height(20)
            .margin({ left: 8 })
            .onClick(() => {
              this.showBalance = !this.showBalance;
            })
        }
        .justifyContent(FlexAlign.Start)
        .margin({ bottom: 8 })

        Text(this.showBalance ? `¥${(this.wallet?.balance || 0).toFixed(2)}` : '******')
          .fontSize(36)
          .fontWeight(FontWeight.Bold)
          .fontColor('#1a1a1a')
          .letterSpacing(this.showBalance ? 0 : 6)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding(24)
    .backgroundColor('#ffffff')
    .borderRadius(16)
    .margin({ left: 16, right: 16, top: 16 })
    .shadow({
      radius: 12,
      color: '#10000000',
      offsetX: 0,
      offsetY: 4
    })
  }

  // 轮播图
  @Builder
  buildBanner() {
    Column() {
      Swiper() {
        ForEach(this.bannerItems, (item: BannerItem) => {
          Stack() {
            Image(item.image)
              .width('100%')
              .height(160)
              .borderRadius(16)
              .objectFit(ImageFit.Cover)

            // 轮播图文字覆盖层
            Column() {
              Text(item.title)
                .fontSize(18)
                .fontWeight(FontWeight.Bold)
                .fontColor('#ffffff')
                .textAlign(TextAlign.Start)
                .margin({ bottom: 4 })

              if (item.subtitle) {
                Text(item.subtitle)
                  .fontSize(14)
                  .fontColor('#ffffff')
                  .opacity(0.9)
                  .textAlign(TextAlign.Start)
              }
            }
            .alignItems(HorizontalAlign.Start)
            .justifyContent(FlexAlign.End)
            .width('100%')
            .height('100%')
            .padding(20)
          }
          .width('100%')
          .height(160)
        })
      }
      .width('100%')
      .height(160)
      .autoPlay(true)
      .interval(4000)
      .indicator(
        new DotIndicator()
          .itemWidth(8)
          .itemHeight(8)
          .selectedItemWidth(16)
          .selectedItemHeight(8)
          .color('#80ffffff')
          .selectedColor('#ffffff')
      )
      .borderRadius(16)
      .shadow({
        radius: 12,
        color: '#********',
        offsetX: 0,
        offsetY: 6
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 16 })
  }

  // 快捷操作
  @Builder
  buildQuickActions() {
    Column() {
      Text('快捷操作')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 20 })

      Row() {
        // 充值
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 48, height: 48 })
              .fill('#ff56b2fa')
              .shadow({
                radius: 8,
                color: '#407BA7E1',
                offsetX: 0,
                offsetY: 4
              })

            Image($r('app.media.wallet'))
              .width(24)
              .height(24)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 12 })

          Text('充值')
            .fontSize(14)
            .fontWeight(FontWeight.Medium)
            .fontColor('#1a1a1a')
        }
        .layoutWeight(1)
        .padding({ top: 20, bottom: 20, left: 12, right: 12 })
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 12,
          color: '#********',
          offsetX: 0,
          offsetY: 6
        })
        .onClick(() => {
          this.navigateToPage('pages/WalletPage');
        })

        Blank().width(16)

        // 提现
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 48, height: 48 })
              .fill('#fffac46b')
              .shadow({
                radius: 8,
                color: '#40E6B566',
                offsetX: 0,
                offsetY: 4
              })

            Image($r('app.media.withdraw'))
              .width(24)
              .height(24)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 12 })

          Text('提现')
            .fontSize(14)
            .fontWeight(FontWeight.Medium)
            .fontColor('#1a1a1a')
        }
        .layoutWeight(1)
        .padding({ top: 20, bottom: 20, left: 12, right: 12 })
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 12,
          color: '#********',
          offsetX: 0,
          offsetY: 6
        })
        .onClick(() => {
          this.navigateToPage('pages/WalletPage');
        })

        Blank().width(16)

        // 转账
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 48, height: 48 })
              .fill('#ffffec67')
              .shadow({
                radius: 8,
                color: '#407BC97B',
                offsetX: 0,
                offsetY: 4
              })

            Image($r('app.media.transfer'))
              .width(24)
              .height(24)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 12 })

          Text('转账')
            .fontSize(14)
            .fontWeight(FontWeight.Medium)
            .fontColor('#1a1a1a')
        }
        .layoutWeight(1)
        .padding({ top: 20, bottom: 20, left: 12, right: 12 })
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 12,
          color: '#********',
          offsetX: 0,
          offsetY: 6
        })
        .onClick(() => {
          this.navigateToPage('pages/BankAccountTransferPage');
        })
      }
      .width('100%')
    }
    .width('100%')
    .padding(24)
    .backgroundColor('#ffffff')
    .borderRadius(20)
    .margin({ left: 16, right: 16, top: 16 })
    .shadow({
      radius: 16,
      color: '#********',
      offsetX: 0,
      offsetY: 8
    })
  }

  // 全部服务
  @Builder
  buildServices() {
    Column() {
      Text('全部服务')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 20 })

      // 第一行服务
      Row() {
        // 钱包管理
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 40, height: 40 })
              .fill('#bef1afc4')
              .shadow({
                radius: 6,
                color: '#307BA7E1',
                offsetX: 0,
                offsetY: 3
              })

            Image($r('app.media.wallet_selected'))
              .width(20)
              .height(20)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 10 })

          Text('钱包管理')
            .fontSize(13)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 4 })

        }
        .layoutWeight(1)
        .height(110)
        .padding(12)
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 10,
          color: '#********',
          offsetX: 0,
          offsetY: 4
        })
        .onClick(() => {
          this.navigateToPage('pages/WalletPage');
        })

        Blank().width(12)

        // 银行卡
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 40, height: 40 })
              .fill('#c4e28350')
              .shadow({
                radius: 6,
                color: '#307BC97B',
                offsetX: 0,
                offsetY: 3
              })

            Image($r('app.media.ic_card'))
              .width(20)
              .height(20)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 10 })

          Text('银行卡')
            .fontSize(13)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 4 })

        }
        .layoutWeight(1)
        .height(110)
        .padding(12)
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 10,
          color: '#********',
          offsetX: 0,
          offsetY: 4
        })
        .onClick(() => {
          this.navigateToPage('pages/BankCardPage');
        })

        Blank().width(12)

        // 交易记录
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 40, height: 40 })
              .fill('#dc80a723')
              .shadow({
                radius: 6,
                color: '#30E6B566',
                offsetX: 0,
                offsetY: 3
              })

            Image($r('app.media.transaction'))
              .width(20)
              .height(20)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 10 })

          Text('交易记录')
            .fontSize(13)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 4 })

        }
        .layoutWeight(1)
        .height(110)
        .padding(12)
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 10,
          color: '#********',
          offsetX: 0,
          offsetY: 4
        })
        .onClick(() => {
          this.navigateToPage('pages/TransactionPage');
        })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 第二行服务
      Row() {
        // 银行账户
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 40, height: 40 })
              .fill('#cdece170')
              .shadow({
                radius: 6,
                color: '#30B885C9',
                offsetX: 0,
                offsetY: 3
              })

            Image($r('app.media.bank'))
              .width(20)
              .height(20)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 10 })

          Text('银行账户')
            .fontSize(13)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 4 })

        }
        .layoutWeight(1)
        .height(110)
        .padding(12)
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 10,
          color: '#********',
          offsetX: 0,
          offsetY: 4
        })
        .onClick(() => {
          this.navigateToPage('pages/BankAccountTransferPage');
        })

        Blank().width(12)

        // 设置
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 40, height: 40 })
              .fill('#c89ba8b0')
              .shadow({
                radius: 6,
                color: '#308FA3AD',
                offsetX: 0,
                offsetY: 3
              })

            Image($r('app.media.setting'))
              .width(20)
              .height(20)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 10 })

          Text('设置')
            .fontSize(13)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 4 })
        }
        .layoutWeight(1)
        .height(110)
        .padding(12)
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 10,
          color: '#********',
          offsetX: 0,
          offsetY: 4
        })
        .onClick(() => {
          this.navigateToPage('pages/ProfilePage');
        })

        Blank().width(12)

        // 关于
        Column() {
          // 图标容器
          Stack() {
            Circle({ width: 40, height: 40 })
              .fill('#ba6f7dde')
              .shadow({
                radius: 6,
                color: '#30A68B7A',
                offsetX: 0,
                offsetY: 3
              })

            Image($r('app.media.about'))
              .width(20)
              .height(20)
              .fillColor('#ffffff')
          }
          .margin({ bottom: 10 })

          Text('关于')
            .fontSize(13)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 4 })

        }
        .layoutWeight(1)
        .height(110)
        .padding(12)
        .backgroundColor('#ffffff')
        .borderRadius(16)
        .justifyContent(FlexAlign.Center)
        .shadow({
          radius: 10,
          color: '#********',
          offsetX: 0,
          offsetY: 4
        })
        .onClick(() => {
          promptAction.showToast({
            message: '关于功能开发中',
            duration: 1500
          });
        })
      }
      .width('100%')
    }
    .width('100%')
    .padding(24)
    .backgroundColor('#ffffff')
    .borderRadius(20)
    .margin({ left: 16, right: 16, top: 16 })
    .shadow({
      radius: 16,
      color: '#********',
      offsetX: 0,
      offsetY: 8
    })
  }

  // 加载用户数据
  loadUserData() {
    this.isLoading = true;
    axios({
      url: `http://localhost:8091/auth/users/${this.userId}`,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<User>>) => {
      console.log('用户信息:', JSON.stringify(res.data));
      if (res.data.code === 0) {
        this.user = res.data.data;
      } else {
        console.error('加载用户信息失败:', res.data.msg);
        promptAction.showToast({
          message: '加载用户信息失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('用户信息请求错误:', err.message);
      promptAction.showToast({
        message: '网络连接失败',
        duration: 2000
      });
    }).finally(() => {
      this.isLoading = false;
    });
  }

  // 加载钱包数据
  loadWalletData() {
    axios({
      url: `http://localhost:8091/wallet/balance/${this.userId}`,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<Wallet>>) => {
      console.log('钱包信息:', JSON.stringify(res.data));
      if (res.data.code === 0) {
        this.wallet = res.data.data;
      } else {
        console.error('加载钱包信息失败:', res.data.msg);
        promptAction.showToast({
          message: '加载钱包信息失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('钱包信息请求错误:', err.message);
      promptAction.showToast({
        message: '网络连接失败',
        duration: 2000
      });
    });
  }

  // 处理服务项点击
  handleServiceClick(item: ServiceItem) {
    if (item.route) {
      this.navigateToPage(item.route);
    } else {
      promptAction.showToast({
        message: `${item.title}功能开发中`,
        duration: 1500
      });
    }
  }

  // 页面导航
  navigateToPage(route: string) {
    router.pushUrl({
      url: route,
      params: {
        userId: this.userId
      }
    }).catch((err: Error) => {
      console.error('页面跳转失败:', err.message);
      promptAction.showToast({
        message: '页面跳转失败，请重试',
        duration: 2000
      });
    });
  }
}
