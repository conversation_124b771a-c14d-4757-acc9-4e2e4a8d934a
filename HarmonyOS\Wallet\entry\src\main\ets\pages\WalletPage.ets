import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import axios, { AxiosResponse, AxiosError } from '@ohos/axios';
import { UserStorage } from '../common/UserStorage';

/**
 * API响应结构
 */
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * 分页响应结构
 */
interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 钱包信息
 */
interface Wallet {
  id: number;
  userId: number;
  balance: number;
  status: number; // 0-冻结, 1-正常
  createTime: string;
  updateTime: string;
}

/**
 * 交易记录
 */
interface Transaction {
  id: number;
  userId: number;
  type: number; // 1-充值, 2-提现, 3-转账, 4-消费
  amount: number;
  balance: number;
  description: string;
  createTime: string;
}

/**
 * 银行卡信息
 */
interface BankCard {
  cardId: number;
  userId: number;
  cardNumber: string;
  bankName: string;
  cardType: number;
  cardHolder: string;
  isDefault: number;
  status: number;
}

@Entry
@Component
export struct WalletPage {
  @State balance: number = 0;
  @State currentUserId: number = 0; // 改名并初始化为0
  @State isLoading: boolean = false;
  @State recentTransactions: Transaction[] = [];

  // 弹窗状态
  @State showRecharge: boolean = false;
  @State showTransfer: boolean = false;
  @State showWithdraw: boolean = false;

  // 表单数据
  @State rechargeAmount: string = '';
  @State transferAmount: string = '';
  @State transferTarget: string = '';
  @State withdrawAmount: string = '';
  @State selectedBankCard: string = '';
  @State remark: string = '';

  // 银行卡列表
  @State bankCards: BankCard[] = [];
  @State defaultBankCard: BankCard | null = null;

  async aboutToAppear() {
    // 首先加载用户信息
    await this.loadUserInfo();

    // 然后加载钱包相关数据
    this.loadWalletBalance();
    this.loadBankCards();
    this.loadRecentTransactions();
  }

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 检查用户是否已登录
      const isLoggedIn = await UserStorage.isLoggedIn();
      if (!isLoggedIn) {
        console.log('用户未登录，跳转到登录页面');
        router.replaceUrl({ url: 'pages/LoginPage' });
        return;
      }

      // 获取当前用户ID
      const userId = await UserStorage.getCurrentUserId();
      if (userId) {
        this.currentUserId = userId;
        console.log('当前用户ID:', this.currentUserId);
      } else {
        console.error('无法获取用户ID');
        router.replaceUrl({ url: 'pages/LoginPage' });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      router.replaceUrl({ url: 'pages/LoginPage' });
    }
  }

  build() {
    Column() {
      // 余额卡片
      this.buildBalanceCard()

      // 银行卡入口
      this.buildBankCardEntry()

      // 操作按钮
      this.buildActionButtons()

      // 最近交易
      this.buildRecentTransactions()

      // 弹窗
      if (this.showRecharge) {
        this.buildRechargeDialog()
      }

      if (this.showTransfer) {
        this.buildTransferDialog()
      }

      if (this.showWithdraw) {
        this.buildWithdrawDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f7fa')
  }

  @Builder
  buildBalanceCard() {
    Column() {
      Text('账户余额')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ bottom: 8 })

      Text(`¥${this.balance.toFixed(2)}`)
        .fontSize(32)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(16)
    .padding(24)
    .margin({ left: 16, right: 16, top: 20 })
    .alignItems(HorizontalAlign.Start)
    .shadow({
      radius: 8,
      color: '#1a000000',
      offsetX: 0,
      offsetY: 2
    })
  }

  @Builder
  buildBankCardEntry() {
    Row() {
      Stack() {
        Circle()
          .width(32)
          .height(32)
          .fill('#4285f4')
          .opacity(0.1)

        Image($r('app.media.card_selected'))
          .width(16)
          .height(16)
      }
      .margin({ right: 12 })

      Text('我的银行卡')
        .fontSize(16)
        .fontColor('#1a1a1a')
        .layoutWeight(1)

      Text('>')
        .fontSize(16)
        .fontColor('#cccccc')
    }
    .width('100%')
    .height(56)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .padding({ left: 16, right: 16 })
    .margin({ left: 16, right: 16, top: 16 })
    .onClick(() => {
      router.pushUrl({ url: 'pages/CardsPage' });
    })
  }

  @Builder
  buildActionButtons() {
    Row() {
      // 充值按钮
      Column() {
        Stack() {
          Circle()
            .width(48)
            .height(48)
            .fill('#e3f2fd')

          Image($r('app.media.service2'))
            .width(24)
            .height(24)
        }
        .margin({ bottom: 8 })

        Text('充值')
          .fontSize(14)
          .fontColor('#1a1a1a')
      }
      .layoutWeight(1)
      .onClick(() => {
        this.clearRechargeForm();
        this.showRecharge = true;
      })

      // 转账按钮
      Column() {
        Stack() {
          Circle()
            .width(48)
            .height(48)
            .fill('#fff3e0')

          Image($r('app.media.transfer'))
            .width(24)
            .height(24)
        }
        .margin({ bottom: 8 })

        Text('转账')
          .fontSize(14)
          .fontColor('#1a1a1a')
      }
      .layoutWeight(1)
      .onClick(() => {
        this.clearTransferForm();
        this.showTransfer = true;
      })

      // 提现按钮
      Column() {
        Stack() {
          Circle()
            .width(48)
            .height(48)
            .fill('#e8f5e8')

          Image($r('app.media.withdraw'))
            .width(24)
            .height(24)
        }
        .margin({ bottom: 8 })

        Text('提现')
          .fontSize(14)
          .fontColor('#1a1a1a')
      }
      .layoutWeight(1)
      .onClick(() => {
        this.clearWithdrawForm();
        this.showWithdraw = true;
      })
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .padding(24)
    .margin({ left: 16, right: 16, top: 16 })
  }

  @Builder
  buildRecentTransactions() {
    Column() {
      // 标题栏
      Row() {
        Text('最近交易')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#1a1a1a')

        Blank()

        Text('查看全部')
          .fontSize(14)
          .fontColor('#4285f4')
          .onClick(() => {
            router.pushUrl({ url: 'pages/TransactionPage' });
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 交易列表
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#4285f4')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 12 })
        }
        .width('100%')
        .height(120)
        .justifyContent(FlexAlign.Center)
      } else if (this.recentTransactions.length === 0) {
        Column() {
          Text('📝')
            .fontSize(40)
            .opacity(0.5)

          Text('暂无交易记录')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 12 })
        }
        .width('100%')
        .height(120)
        .justifyContent(FlexAlign.Center)
      } else {
        Column() {
          ForEach(this.recentTransactions.slice(0, 3), (transaction: Transaction) => {
            this.buildTransactionItem(transaction)
          }, (transaction: Transaction) => transaction.id?.toString() || Math.random().toString())
        }
        .width('100%')
      }
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .padding(20)
    .margin({ left: 16, right: 16, top: 16 })
  }

  @Builder
  buildTransactionItem(transaction: Transaction) {
    Row() {
      // 交易图标
      Stack() {
        Circle()
          .width(40)
          .height(40)
          .fill(this.getTransactionColor(transaction.type))
          .opacity(0.1)

        Image(this.getTransactionIcon(transaction.type))
          .width(20)
          .height(20)
      }
      .margin({ right: 12 })

      // 交易信息
      Column() {
        Text(this.getTransactionTitle(transaction.type))
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .fontColor('#1a1a1a')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(this.formatDateTime(transaction.createTime))
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      // 金额
      Text(this.formatAmount(transaction.type, transaction.amount))
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor(this.getAmountColor(transaction.type))
    }
    .width('100%')
    .padding({ top: 12, bottom: 12 })
  }

  // 加载钱包余额
  loadWalletBalance() {
    this.isLoading = true;

    axios({
      url: `http://localhost:8091/wallet/balance/${this.currentUserId}`,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<Wallet>>) => {
      console.log('获取钱包余额结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        this.balance = res.data.data.balance;
      } else {
        promptAction.showToast({
          message: res.data.msg || '获取余额失败',
          duration: 2000
        });
      }
    }).catch((err: AxiosError) => {
      console.error('获取钱包余额错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    }).finally(() => {
      this.isLoading = false;
    });
  }

  // 加载银行卡列表
  loadBankCards() {
    axios({
      url: `http://localhost:8091/bankCards/bound/user/${this.currentUserId}`,
      method: 'get'
    }).then((res: AxiosResponse<ApiResponse<BankCard[]>>) => {
      console.log('获取银行卡列表结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        this.bankCards = res.data.data;
        // 找到默认银行卡
        this.defaultBankCard = this.bankCards.find(card => card.isDefault === 1) || null;
        if (this.defaultBankCard) {
          this.selectedBankCard = this.defaultBankCard.cardNumber;
        }
      }
    }).catch((err: AxiosError) => {
      console.error('获取银行卡列表错误:', err.message);
    });
  }

  // 加载最近交易记录
  loadRecentTransactions() {
    axios({
      url: `http://localhost:8091/transactions/user/${this.currentUserId}`,
      method: 'get',
      params: {
        pageNum: 1,
        pageSize: 3
      }
    }).then((res: AxiosResponse<ApiResponse<PageResponse<Transaction>>>) => {
      console.log('获取最近交易记录结果:', JSON.stringify(res.data));

      if (res.data.code === 0) {
        this.recentTransactions = res.data.data.records || [];
      }
    }).catch((err: AxiosError) => {
      console.error('获取最近交易记录错误:', err.message);
    });
  }

  // 充值
  recharge() {
    // 首先检查是否有银行卡
    if (this.bankCards.length === 0) {
      promptAction.showDialog({
        title: '提示',
        message: '您还没有绑定银行卡，请先添加银行卡后再进行充值操作。',
        buttons: [
          {
            text: '取消',
            color: '#666666'
          },
          {
            text: '去添加',
            color: '#4285f4'
          }
        ]
      }).then((result) => {
        if (result.index === 1) {
          this.showRecharge = false;
          router.pushUrl({ url: 'pages/BankCardPage' });
        }
      });
      return;
    }

    if (!this.rechargeAmount || parseFloat(this.rechargeAmount) <= 0) {
      promptAction.showToast({
        message: '请输入有效的充值金额',
        duration: 2000
      });
      return;
    }

    const amount = parseFloat(this.rechargeAmount);
    if (amount > 100000) {
      promptAction.showToast({
        message: '单次充值金额不能超过10万元',
        duration: 2000
      });
      return;
    }

    if (!this.selectedBankCard) {
      promptAction.showToast({
        message: '请选择银行卡',
        duration: 2000
      });
      return;
    }

    axios({
      url: 'http://localhost:8091/wallet/recharge',
      method: 'post',
      params: {
        userId: this.currentUserId,
        amount: amount,
        bankCardNumber: this.selectedBankCard,
        remark: this.remark || '钱包充值'
      }
    }).then((res: AxiosResponse<ApiResponse<null>>) => {
      console.log('充值结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        this.showRecharge = false;
        this.clearRechargeForm();
        this.loadWalletBalance();
        this.loadRecentTransactions();
      }
    }).catch((err: AxiosError) => {
      console.error('充值错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    });
  }

  // 提现
  withdraw() {
    // 首先检查是否有银行卡
    if (this.bankCards.length === 0) {
      promptAction.showDialog({
        title: '提示',
        message: '您还没有绑定银行卡，请先添加银行卡后再进行提现操作。',
        buttons: [
          {
            text: '取消',
            color: '#666666'
          },
          {
            text: '去添加',
            color: '#4caf50'
          }
        ]
      }).then((result) => {
        if (result.index === 1) {
          this.showWithdraw = false;
          router.pushUrl({ url: 'pages/BankCardPage' });
        }
      });
      return;
    }

    if (!this.withdrawAmount || parseFloat(this.withdrawAmount) <= 0) {
      promptAction.showToast({
        message: '请输入有效的提现金额',
        duration: 2000
      });
      return;
    }

    const amount = parseFloat(this.withdrawAmount);
    if (amount > 50000) {
      promptAction.showToast({
        message: '单次提现金额不能超过5万元',
        duration: 2000
      });
      return;
    }

    if (amount > this.balance) {
      promptAction.showToast({
        message: '余额不足',
        duration: 2000
      });
      return;
    }

    if (!this.selectedBankCard) {
      promptAction.showToast({
        message: '请选择银行卡',
        duration: 2000
      });
      return;
    }

    axios({
      url: 'http://localhost:8091/wallet/withdraw',
      method: 'post',
      params: {
        userId: this.currentUserId,
        amount: amount,
        bankCardNumber: this.selectedBankCard,
        remark: this.remark || '钱包提现'
      }
    }).then((res: AxiosResponse<ApiResponse<null>>) => {
      console.log('提现结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        this.showWithdraw = false;
        this.clearWithdrawForm();
        this.loadWalletBalance();
        this.loadRecentTransactions();
      }
    }).catch((err: AxiosError) => {
      console.error('提现错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    });
  }

  // 转账
  transfer() {
    if (!this.transferTarget || this.transferTarget.length < 10) {
      promptAction.showToast({
        message: '请输入有效的账户号码',
        duration: 2000
      });
      return;
    }

    if (!this.transferAmount || parseFloat(this.transferAmount) <= 0) {
      promptAction.showToast({
        message: '请输入有效的转账金额',
        duration: 2000
      });
      return;
    }

    const amount = parseFloat(this.transferAmount);
    if (amount > this.balance) {
      promptAction.showToast({
        message: '余额不足',
        duration: 2000
      });
      return;
    }

    // 这里使用钱包账户号码作为fromAccountNumber
    const fromAccountNumber = `WALLET_${this.currentUserId}`;

    axios({
      url: 'http://localhost:8091/transfer',
      method: 'post',
      params: {
        fromAccountNumber: fromAccountNumber,
        toAccountNumber: this.transferTarget,
        amount: amount
      }
    }).then((res: AxiosResponse<ApiResponse<null>>) => {
      console.log('转账结果:', JSON.stringify(res.data));

      promptAction.showToast({
        message: res.data.msg || '操作完成',
        duration: 2000
      });

      if (res.data.code === 0) {
        this.showTransfer = false;
        this.clearTransferForm();
        this.loadWalletBalance();
        this.loadRecentTransactions();
      }
    }).catch((err: AxiosError) => {
      console.error('转账错误:', err.message);
      promptAction.showToast({
        message: '网络错误，请重试',
        duration: 2000
      });
    });
  }

  @Builder
  buildRechargeDialog() {
    Stack() {
      // 背景遮罩
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0,0,0,0.5)')
        .onClick(() => {
          this.showRecharge = false;
          this.clearRechargeForm();
        })

      // 弹窗内容
      Column() {
        // 标题
        Row() {
          Text('钱包充值')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .layoutWeight(1)

          Text('×')
            .fontSize(24)
            .fontColor('#999999')
            .onClick(() => {
              this.showRecharge = false;
              this.clearRechargeForm();
            })
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 银行卡选择
        Column() {
          Text('充值银行卡')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          if (this.defaultBankCard) {
            Row() {
              Text(`${this.defaultBankCard.bankName} (****${this.defaultBankCard.cardNumber.slice(-4)})`)
                .fontSize(16)
                .fontColor('#1a1a1a')
                .layoutWeight(1)

              Text('默认')
                .fontSize(12)
                .fontColor('#ffffff')
                .backgroundColor('#4285f4')
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                .borderRadius(12)
            }
            .width('100%')
            .height(48)
            .backgroundColor('#f8f9fa')
            .borderRadius(8)
            .padding({ left: 16, right: 16 })
          } else {
            // 没有银行卡时的提示
            Row() {
              Column() {
                Text('暂无绑定银行卡')
                  .fontSize(14)
                  .fontColor('#999999')
                  .margin({ bottom: 4 })

                Text('请先添加银行卡')
                  .fontSize(12)
                  .fontColor('#666666')
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)

              Button('去添加')
                .type(ButtonType.Capsule)
                .backgroundColor('#4285f4')
                .fontColor('#ffffff')
                .fontSize(12)
                .height(32)
                .onClick(() => {
                  this.showRecharge = false;
                  router.pushUrl({ url: 'pages/BankCardPage' });
                })
            }
            .width('100%')
            .height(48)
            .backgroundColor('#fff3cd')
            .borderRadius(8)
            .padding({ left: 16, right: 16 })
          }
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 16 })

        // 充值金额
        Column() {
          Text('充值金额')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入充值金额' })
            .type(InputType.Number)
            .onChange((value: string) => {
              this.rechargeAmount = value;
            })
            .backgroundColor('#f8f9fa')
            .borderRadius(8)
            .height(48)
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 16 })

        // 快捷金额
        Row() {
          Button('100')
            .type(ButtonType.Normal)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(14)
            .height(36)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              this.rechargeAmount = '100';
            })

          Button('500')
            .type(ButtonType.Normal)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(14)
            .height(36)
            .layoutWeight(1)
            .margin({ left: 4, right: 4 })
            .onClick(() => {
              this.rechargeAmount = '500';
            })

          Button('1000')
            .type(ButtonType.Normal)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(14)
            .height(36)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              this.rechargeAmount = '1000';
            })
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 操作按钮
        Row() {
          Button('取消')
            .type(ButtonType.Normal)
            .backgroundColor('#f8f9fa')
            .fontColor('#666666')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              this.showRecharge = false;
              this.clearRechargeForm();
            })

          Button('确认充值')
            .type(ButtonType.Capsule)
            .backgroundColor('#4285f4')
            .fontColor('#ffffff')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              this.recharge();
            })
        }
        .width('100%')
      }
      .width('90%')
      .backgroundColor('#ffffff')
      .borderRadius(16)
      .padding(24)
      .shadow({
        radius: 16,
        color: '#33000000',
        offsetX: 0,
        offsetY: 8
      })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder
  buildTransferDialog() {
    Stack() {
      // 背景遮罩
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0,0,0,0.5)')
        .onClick(() => {
          this.showTransfer = false;
          this.clearTransferForm();
        })

      // 弹窗内容
      Column() {
        // 标题
        Row() {
          Text('钱包转账')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .layoutWeight(1)

          Text('×')
            .fontSize(24)
            .fontColor('#999999')
            .onClick(() => {
              this.showTransfer = false;
              this.clearTransferForm();
            })
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 收款账户
        Column() {
          Text('收款账户')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入收款账户号码' })
            .onChange((value: string) => {
              this.transferTarget = value;
            })
            .backgroundColor('#f8f9fa')
            .borderRadius(8)
            .height(48)
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 16 })

        // 转账金额
        Column() {
          Text('转账金额')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入转账金额' })
            .type(InputType.Number)
            .onChange((value: string) => {
              this.transferAmount = value;
            })
            .backgroundColor('#f8f9fa')
            .borderRadius(8)
            .height(48)
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 24 })

        // 操作按钮
        Row() {
          Button('取消')
            .type(ButtonType.Normal)
            .backgroundColor('#f8f9fa')
            .fontColor('#666666')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              this.showTransfer = false;
              this.clearTransferForm();
            })

          Button('确认转账')
            .type(ButtonType.Capsule)
            .backgroundColor('#ff9800')
            .fontColor('#ffffff')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              this.transfer();
            })
        }
        .width('100%')
      }
      .width('90%')
      .backgroundColor('#ffffff')
      .borderRadius(16)
      .padding(24)
      .shadow({
        radius: 16,
        color: '#33000000',
        offsetX: 0,
        offsetY: 8
      })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  @Builder
  buildWithdrawDialog() {
    Stack() {
      // 背景遮罩
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0,0,0,0.5)')
        .onClick(() => {
          this.showWithdraw = false;
          this.clearWithdrawForm();
        })

      // 弹窗内容
      Column() {
        // 标题
        Row() {
          Text('钱包提现')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .layoutWeight(1)

          Text('×')
            .fontSize(24)
            .fontColor('#999999')
            .onClick(() => {
              this.showWithdraw = false;
              this.clearWithdrawForm();
            })
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 银行卡选择
        Column() {
          Text('提现银行卡')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          if (this.defaultBankCard) {
            Row() {
              Text(`${this.defaultBankCard.bankName} (****${this.defaultBankCard.cardNumber.slice(-4)})`)
                .fontSize(16)
                .fontColor('#1a1a1a')
                .layoutWeight(1)

              Text('默认')
                .fontSize(12)
                .fontColor('#ffffff')
                .backgroundColor('#4caf50')
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
                .borderRadius(12)
            }
            .width('100%')
            .height(48)
            .backgroundColor('#f8f9fa')
            .borderRadius(8)
            .padding({ left: 16, right: 16 })
          } else {
            // 没有银行卡时的提示
            Row() {
              Column() {
                Text('暂无绑定银行卡')
                  .fontSize(14)
                  .fontColor('#999999')
                  .margin({ bottom: 4 })

                Text('请先添加银行卡')
                  .fontSize(12)
                  .fontColor('#666666')
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)

              Button('去添加')
                .type(ButtonType.Capsule)
                .backgroundColor('#4caf50')
                .fontColor('#ffffff')
                .fontSize(12)
                .height(32)
                .onClick(() => {
                  this.showWithdraw = false;
                  router.pushUrl({ url: 'pages/BankCardPage' });
                })
            }
            .width('100%')
            .height(48)
            .backgroundColor('#fff3cd')
            .borderRadius(8)
            .padding({ left: 16, right: 16 })
          }
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 16 })

        // 提现金额
        Column() {
          Text('提现金额')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          TextInput({ placeholder: '请输入提现金额' })
            .type(InputType.Number)
            .onChange((value: string) => {
              this.withdrawAmount = value;
            })
            .backgroundColor('#f8f9fa')
            .borderRadius(8)
            .height(48)
        }
        .width('100%')
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 16 })

        // 快捷金额
        Row() {
          Button('200')
            .type(ButtonType.Normal)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(14)
            .height(36)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              this.withdrawAmount = '200';
            })

          Button('500')
            .type(ButtonType.Normal)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(14)
            .height(36)
            .layoutWeight(1)
            .margin({ left: 4, right: 4 })
            .onClick(() => {
              this.withdrawAmount = '500';
            })

          Button('1000')
            .type(ButtonType.Normal)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(14)
            .height(36)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              this.withdrawAmount = '1000';
            })
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 操作按钮
        Row() {
          Button('取消')
            .type(ButtonType.Normal)
            .backgroundColor('#f8f9fa')
            .fontColor('#666666')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              this.showWithdraw = false;
              this.clearWithdrawForm();
            })

          Button('确认提现')
            .type(ButtonType.Capsule)
            .backgroundColor('#4caf50')
            .fontColor('#ffffff')
            .fontSize(16)
            .height(48)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              this.withdraw();
            })
        }
        .width('100%')
      }
      .width('90%')
      .backgroundColor('#ffffff')
      .borderRadius(16)
      .padding(24)
      .shadow({
        radius: 16,
        color: '#33000000',
        offsetX: 0,
        offsetY: 8
      })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }

  // 清空表单数据
  clearRechargeForm() {
    this.rechargeAmount = '';
    this.remark = '';
  }

  clearTransferForm() {
    this.transferAmount = '';
    this.transferTarget = '';
  }

  clearWithdrawForm() {
    this.withdrawAmount = '';
    this.remark = '';
  }

  // 获取交易图标
  getTransactionIcon(type: number): Resource {
    switch(type) {
      case 1: return $r('app.media.service2'); // 充值
      case 2: return $r('app.media.withdraw'); // 提现
      case 3: return $r('app.media.transfer'); // 转账
      case 4: return $r('app.media.scan'); // 消费
      default: return $r('app.media.transaction');
    }
  }

  // 获取交易颜色
  getTransactionColor(type: number): string {
    switch(type) {
      case 1: return '#4285f4'; // 充值 - 蓝色
      case 2: return '#4caf50'; // 提现 - 绿色
      case 3: return '#ff9800'; // 转账 - 橙色
      case 4: return '#f44336'; // 消费 - 红色
      default: return '#666666';
    }
  }

  // 获取交易标题
  getTransactionTitle(type: number): string {
    switch(type) {
      case 1: return '充值';
      case 2: return '提现';
      case 3: return '转账';
      case 4: return '消费';
      default: return '交易';
    }
  }

  // 格式化金额
  formatAmount(type: number, amount: number): string {
    const prefix = (type === 1) ? '+' : '-';
    return `${prefix}${Math.abs(amount).toFixed(2)}`;
  }

  // 获取金额颜色
  getAmountColor(type: number): string {
    return (type === 1) ? '#4285f4' : '#f44336';
  }

  // 格式化日期时间
  formatDateTime(dateTimeStr: string): string {
    try {
      const date = new Date(dateTimeStr);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      return dateTimeStr;
    }
  }
}