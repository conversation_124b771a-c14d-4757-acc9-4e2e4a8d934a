if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LoginPage_Params {
    loginType?: string;
    username?: string;
    password?: string;
    verifyCode?: string;
    countdown?: number;
    isLoading?: boolean;
    currentCode?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse, AxiosError } from "@normalized:N&&&@ohos/axios/index&2.2.6";
import { UserStorage } from "@normalized:N&&&entry/src/main/ets/common/UserStorage&";
/**
 * 响应数据结构
 */
interface R<T> {
    code: number;
    msg: string;
    data: T;
}
/**
 * 登录响应数据结构
 */
interface LoginResponse {
    token: string;
    userId: number;
    phone: string;
    realName?: string;
}
class LoginPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__loginType = new ObservedPropertySimplePU('password' // 'password' | 'code'
        , this, "loginType");
        this.__username = new ObservedPropertySimplePU('', this, "username");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__verifyCode = new ObservedPropertySimplePU('', this, "verifyCode");
        this.__countdown = new ObservedPropertySimplePU(0, this, "countdown");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__currentCode = new ObservedPropertySimplePU('', this, "currentCode");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LoginPage_Params) {
        if (params.loginType !== undefined) {
            this.loginType = params.loginType;
        }
        if (params.username !== undefined) {
            this.username = params.username;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.verifyCode !== undefined) {
            this.verifyCode = params.verifyCode;
        }
        if (params.countdown !== undefined) {
            this.countdown = params.countdown;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.currentCode !== undefined) {
            this.currentCode = params.currentCode;
        }
    }
    updateStateVars(params: LoginPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__loginType.purgeDependencyOnElmtId(rmElmtId);
        this.__username.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__verifyCode.purgeDependencyOnElmtId(rmElmtId);
        this.__countdown.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__currentCode.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__loginType.aboutToBeDeleted();
        this.__username.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__verifyCode.aboutToBeDeleted();
        this.__countdown.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__currentCode.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __loginType: ObservedPropertySimplePU<string>; // 'password' | 'code'
    get loginType() {
        return this.__loginType.get();
    }
    set loginType(newValue: string) {
        this.__loginType.set(newValue);
    }
    private __username: ObservedPropertySimplePU<string>;
    get username() {
        return this.__username.get();
    }
    set username(newValue: string) {
        this.__username.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __verifyCode: ObservedPropertySimplePU<string>;
    get verifyCode() {
        return this.__verifyCode.get();
    }
    set verifyCode(newValue: string) {
        this.__verifyCode.set(newValue);
    }
    private __countdown: ObservedPropertySimplePU<number>;
    get countdown() {
        return this.__countdown.get();
    }
    set countdown(newValue: number) {
        this.__countdown.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __currentCode: ObservedPropertySimplePU<string>;
    get currentCode() {
        return this.__currentCode.get();
    }
    set currentCode(newValue: string) {
        this.__currentCode.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(38:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f7fa');
            Column.padding({ left: 24, right: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Logo和标题
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(40:7)", "entry");
            // Logo和标题
            Column.width('100%');
            // Logo和标题
            Column.margin({ top: 80, bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('💳');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(41:9)", "entry");
            Text.fontSize(80);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('E-Wallet');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(45:9)", "entry");
            Text.fontSize(28);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#4FC3F7');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('电子钱包支付系统');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(51:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666');
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        // Logo和标题
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/LoginPage.ets(60:7)", "entry");
            // 登录表单
            Column.backgroundColor(Color.White);
            // 登录表单
            Column.borderRadius(12);
            // 登录表单
            Column.padding(24);
            // 登录表单
            Column.margin({ left: 24, right: 24, bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户登录');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(61:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录方式切换
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(68:9)", "entry");
            // 登录方式切换
            Row.justifyContent(FlexAlign.Center);
            // 登录方式切换
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('密码登录');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(69:11)", "entry");
            Button.type(this.loginType === 'password' ? ButtonType.Capsule : ButtonType.Normal);
            Button.backgroundColor(this.loginType === 'password' ? '#4FC3F7' : '#f0f0f0');
            Button.fontColor(this.loginType === 'password' ? Color.White : '#666');
            Button.onClick(() => {
                this.loginType = 'password';
            });
            Button.margin({ right: 12 });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('验证码登录');
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(78:11)", "entry");
            Button.type(this.loginType === 'code' ? ButtonType.Capsule : ButtonType.Normal);
            Button.backgroundColor(this.loginType === 'code' ? '#4FC3F7' : '#f0f0f0');
            Button.fontColor(this.loginType === 'code' ? Color.White : '#666');
            Button.onClick(() => {
                this.loginType = 'code';
            });
        }, Button);
        Button.pop();
        // 登录方式切换
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 手机号输入
            TextInput.create({ placeholder: '请输入手机号' });
            TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(90:9)", "entry");
            // 手机号输入
            TextInput.type(InputType.PhoneNumber);
            // 手机号输入
            TextInput.onChange((value: string) => {
                this.username = value;
            });
            // 手机号输入
            TextInput.margin({ bottom: 16 });
            // 手机号输入
            TextInput.height(48);
            // 手机号输入
            TextInput.borderRadius(8);
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 密码输入（密码登录时显示）
            if (this.loginType === 'password') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入密码' });
                        TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(101:11)", "entry");
                        TextInput.type(InputType.Password);
                        TextInput.onChange((value: string) => {
                            this.password = value;
                        });
                        TextInput.margin({ bottom: 24 });
                        TextInput.height(48);
                        TextInput.borderRadius(8);
                    }, TextInput);
                });
            }
            // 验证码输入（验证码登录时显示）
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 验证码输入（验证码登录时显示）
            if (this.loginType === 'code') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(113:11)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        TextInput.create({ placeholder: '请输入验证码' });
                        TextInput.debugLine("entry/src/main/ets/pages/LoginPage.ets(114:13)", "entry");
                        TextInput.type(InputType.Number);
                        TextInput.maxLength(6);
                        TextInput.onChange((value: string) => {
                            this.verifyCode = value;
                        });
                        TextInput.layoutWeight(1);
                        TextInput.height(48);
                        TextInput.borderRadius(8);
                        TextInput.margin({ right: 12 });
                    }, TextInput);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel(this.countdown > 0 ? `${this.countdown}s` : '获取验证码');
                        Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(125:13)", "entry");
                        Button.enabled(this.countdown === 0 && this.username.length === 11);
                        Button.onClick(() => {
                            this.sendVerifyCode();
                        });
                        Button.height(48);
                        Button.borderRadius(8);
                    }, Button);
                    Button.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 显示验证码（开发环境）
                        if (this.currentCode) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(`验证码：${this.currentCode}`);
                                    Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(138:13)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#4FC3F7');
                                    Text.margin({ bottom: 16 });
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
            // 登录按钮
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录按钮
            Button.createWithLabel(this.isLoading ? '登录中...' : (this.loginType === 'password' ? '密码登录' : '验证码登录'));
            Button.debugLine("entry/src/main/ets/pages/LoginPage.ets(146:9)", "entry");
            // 登录按钮
            Button.type(ButtonType.Capsule);
            // 登录按钮
            Button.backgroundColor('#4FC3F7');
            // 登录按钮
            Button.width('100%');
            // 登录按钮
            Button.height(48);
            // 登录按钮
            Button.fontSize(16);
            // 登录按钮
            Button.fontWeight(FontWeight.Medium);
            // 登录按钮
            Button.onClick(() => {
                this.handleLogin();
            });
            // 登录按钮
            Button.enabled(!this.isLoading);
            // 登录按钮
            Button.margin({ bottom: 16 });
        }, Button);
        // 登录按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 注册链接
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/LoginPage.ets(160:9)", "entry");
            // 注册链接
            Row.justifyContent(FlexAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('还没有账号？');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(161:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('立即注册');
            Text.debugLine("entry/src/main/ets/pages/LoginPage.ets(165:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#4FC3F7');
            Text.onClick(() => {
                router.pushUrl({ url: 'pages/RegisterPage' });
            });
        }, Text);
        Text.pop();
        // 注册链接
        Row.pop();
        // 登录表单
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/LoginPage.ets(179:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    // 处理登录
    handleLogin() {
        // 验证输入
        if (!this.username || this.username.length !== 11) {
            promptAction.showToast({
                message: '请输入正确的手机号',
                duration: 2000
            });
            return;
        }
        if (this.loginType === 'password') {
            if (!this.password || this.password.length < 6) {
                promptAction.showToast({
                    message: '密码长度不能少于6位',
                    duration: 2000
                });
                return;
            }
            this.passwordLogin();
        }
        else {
            if (!this.verifyCode || this.verifyCode.length !== 6) {
                promptAction.showToast({
                    message: '请输入6位验证码',
                    duration: 2000
                });
                return;
            }
            this.codeLogin();
        }
    }
    // 密码登录
    passwordLogin() {
        this.isLoading = true;
        axios({
            url: 'http://localhost:8091/auth/login',
            method: 'post',
            params: {
                username: this.username,
                password: this.password
            }
        }).then(async (res: AxiosResponse<R<LoginResponse>>) => {
            let message = "request result: " + JSON.stringify(res.data);
            let msg = res.data.msg;
            console.log(msg);
            promptAction.showToast({
                message: msg,
                duration: 2000,
                bottom: 50
            });
            if (res.data.code == 0) {
                const loginData = res.data.data;
                console.log('登录成功，用户信息:', loginData);
                try {
                    // 保存用户信息到本地存储
                    console.log('准备保存用户信息:', loginData);
                    await UserStorage.saveUserInfo(loginData.userId, loginData.token, loginData.phone, loginData.realName);
                    console.log('用户信息保存成功，准备跳转页面');
                    // 跳转到主页
                    router.replaceUrl({ url: 'pages/BarPage' });
                }
                catch (error) {
                    console.error('保存用户信息失败:', error);
                    console.error('错误类型:', typeof error);
                    console.error('错误消息:', error.message);
                    promptAction.showToast({
                        message: '登录信息保存失败: ' + error.message,
                        duration: 3000,
                        bottom: 50
                    });
                }
            }
        }).catch((err: AxiosError) => {
            let message = "request error: " + err.message;
            console.log(message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000,
                bottom: 50
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 验证码登录
    codeLogin() {
        this.isLoading = true;
        axios({
            url: 'http://localhost:8091/auth/loginWithCode',
            method: 'post',
            params: {
                phone: this.username,
                code: this.verifyCode
            }
        }).then(async (res: AxiosResponse<R<LoginResponse>>) => {
            let message = "request result: " + JSON.stringify(res.data);
            let msg = res.data.msg;
            console.log(msg);
            promptAction.showToast({
                message: msg,
                duration: 2000,
                bottom: 50
            });
            if (res.data.code == 0) {
                const loginData = res.data.data;
                console.log('验证码登录成功，用户信息:', loginData);
                try {
                    // 保存用户信息到本地存储
                    console.log('准备保存验证码登录用户信息:', loginData);
                    await UserStorage.saveUserInfo(loginData.userId, loginData.token, loginData.phone, loginData.realName);
                    console.log('验证码登录用户信息保存成功，准备跳转页面');
                    // 跳转到主页
                    router.replaceUrl({ url: 'pages/BarPage' });
                }
                catch (error) {
                    console.error('保存用户信息失败:', error);
                    console.error('错误类型:', typeof error);
                    console.error('错误消息:', error.message);
                    promptAction.showToast({
                        message: '登录信息保存失败: ' + error.message,
                        duration: 3000,
                        bottom: 50
                    });
                }
            }
        }).catch((err: AxiosError) => {
            let message = "request error: " + err.message;
            console.log(message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000,
                bottom: 50
            });
        }).finally(() => {
            this.isLoading = false;
        });
    }
    // 发送验证码
    sendVerifyCode() {
        if (!this.username || this.username.length !== 11) {
            promptAction.showToast({
                message: '请输入正确的手机号',
                duration: 2000
            });
            return;
        }
        axios({
            url: 'http://localhost:8091/sms/send',
            method: 'post',
            params: {
                phone: this.username,
                type: 1 // 1表示登录验证码
            }
        }).then((res: AxiosResponse<R<string>>) => {
            let message = "request result: " + JSON.stringify(res.data);
            let msg = res.data.msg;
            console.log(msg);
            promptAction.showToast({
                message: msg,
                duration: 2000,
                bottom: 50
            });
            if (res.data.code == 0) {
                // 显示验证码（开发环境）
                this.currentCode = res.data.data;
                this.startCountdown();
            }
        }).catch((err: AxiosError) => {
            let message = "request error: " + err.message;
            console.log(message);
            promptAction.showToast({
                message: '网络错误，请重试',
                duration: 2000,
                bottom: 50
            });
        });
    }
    // 开始倒计时
    startCountdown() {
        this.countdown = 60;
        const timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
                clearInterval(timer);
                this.currentCode = '';
            }
        }, 1000);
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "LoginPage";
    }
}
registerNamedRoute(() => new LoginPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/LoginPage", pageFullPath: "entry/src/main/ets/pages/LoginPage", integratedHsp: "false", moduleType: "followWithHap" });
