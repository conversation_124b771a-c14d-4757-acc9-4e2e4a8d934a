{"version": "2.0", "ppid": 4508, "events": [{"head": {"id": "cedcc46e-d321-4a44-a475-e6ff2cb8d96f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877216501900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eefae5d-90a4-43e3-8313-cb8e8931722d", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877225424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61afb771-e8c0-4aad-9155-f306f572845d", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9877225612200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a21949-52da-467e-b5b9-c7c23f935fea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935546372400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b4480be-41a4-402b-a393-4e3b85a12623", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935552545900, "endTime": 9935719300500}, "additional": {"children": ["34ad0ce2-844f-4b75-a85a-d7af54f7ae57", "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "e3b82ed7-d8d7-427f-b157-0edc38ea86d4", "edb948c6-dc39-4197-98de-b4df0a866057", "3b6f1f64-2aef-441a-beb3-2ce54aebb53d", "b45188d6-7d3a-4ae0-a764-e055ba788c10", "739261db-190c-4cfc-a16d-4d828393d5ab"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34ad0ce2-844f-4b75-a85a-d7af54f7ae57", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935552547200, "endTime": 9935566014300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "5e3fa09a-0a35-4459-9cb4-cc1263aa033a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935566040400, "endTime": 9935718186700}, "additional": {"children": ["9476c56e-da83-4d1f-9f45-b5f9e626266b", "0ac78a74-b742-47e0-895d-6c5bd9491ee8", "57975e27-e621-488c-9cd1-8cd137916931", "6293c5b1-319d-4a2a-bfe2-bec88fa9c123", "14ad34c9-3a9a-41d9-a1bf-d3d220bd78e4", "4b1ff676-1ee2-44d9-a5da-1f644f45b0c9", "68969abe-7db5-41d3-8ad3-31bcae0a1cd3", "16c7553f-8031-4a2a-bb51-a7962fa67be5", "4b1f8b34-80d3-4f07-9bf0-c503a3bd6a67"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "66c08a17-1a84-423d-b67d-f0007fd4d393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3b82ed7-d8d7-427f-b157-0edc38ea86d4", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935718210500, "endTime": 9935719284800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "2be7827c-ee0f-4ce5-b6ed-087<PERSON><PERSON><PERSON><PERSON>"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edb948c6-dc39-4197-98de-b4df0a866057", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935719290100, "endTime": 9935719296200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "5a77eab1-eb8e-4468-9fb0-5750b1572fa0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b6f1f64-2aef-441a-beb3-2ce54aebb53d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935555353100, "endTime": 9935555396600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "43049993-c4c0-43ee-bd66-c71a73715661"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43049993-c4c0-43ee-bd66-c71a73715661", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935555353100, "endTime": 9935555396600}, "additional": {"logType": "info", "children": [], "durationId": "3b6f1f64-2aef-441a-beb3-2ce54aebb53d", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "b45188d6-7d3a-4ae0-a764-e055ba788c10", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935559955500, "endTime": 9935560014200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "fdfc79bf-8f5a-485a-a45d-55a0752aba8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdfc79bf-8f5a-485a-a45d-55a0752aba8c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935559955500, "endTime": 9935560014200}, "additional": {"logType": "info", "children": [], "durationId": "b45188d6-7d3a-4ae0-a764-e055ba788c10", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "239836ca-7e05-4dfe-a4c8-d979a0ff06ba", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935560128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab7f5c6d-c6ba-4533-b770-087563030f85", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935565858800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e3fa09a-0a35-4459-9cb4-cc1263aa033a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935552547200, "endTime": 9935566014300}, "additional": {"logType": "info", "children": [], "durationId": "34ad0ce2-844f-4b75-a85a-d7af54f7ae57", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "9476c56e-da83-4d1f-9f45-b5f9e626266b", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935572358800, "endTime": 9935572373300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "aa0cbb1b-b428-482a-9d9b-ca7df9d471da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ac78a74-b742-47e0-895d-6c5bd9491ee8", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935572404700, "endTime": 9935577957700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "6f534b6b-b61b-4eb1-b98c-cf5839406e9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57975e27-e621-488c-9cd1-8cd137916931", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935577972800, "endTime": 9935656820200}, "additional": {"children": ["c4398fb3-e726-488d-aad6-504780a2374f", "ce500168-949f-4c1e-8c50-9fe2e2f11dde", "f08aa6a0-6f8c-4fb5-ae8d-d96956c86a7f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "d160ed37-a20a-4371-a94b-d9aef587938f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6293c5b1-319d-4a2a-bfe2-bec88fa9c123", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935656836600, "endTime": 9935678707400}, "additional": {"children": ["5afa0443-9c63-44ae-a1cb-1d0128149cec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "890a7826-7b5a-49ff-972f-a53980c25ddb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14ad34c9-3a9a-41d9-a1bf-d3d220bd78e4", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935678714900, "endTime": 9935691975300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "0b58931e-1227-4381-9e25-b65981703dec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b1ff676-1ee2-44d9-a5da-1f644f45b0c9", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935692974100, "endTime": 9935705251900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "f2103f34-cac2-46e4-b718-ee934a3a42b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68969abe-7db5-41d3-8ad3-31bcae0a1cd3", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935705274500, "endTime": 9935718047400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "4141a475-2f54-44cc-a157-504c9c88c089"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16c7553f-8031-4a2a-bb51-a7962fa67be5", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935718079200, "endTime": 9935718175500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "f73696f1-3549-484b-b3b6-530e64dad82f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa0cbb1b-b428-482a-9d9b-ca7df9d471da", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935572358800, "endTime": 9935572373300}, "additional": {"logType": "info", "children": [], "durationId": "9476c56e-da83-4d1f-9f45-b5f9e626266b", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "6f534b6b-b61b-4eb1-b98c-cf5839406e9d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935572404700, "endTime": 9935577957700}, "additional": {"logType": "info", "children": [], "durationId": "0ac78a74-b742-47e0-895d-6c5bd9491ee8", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "c4398fb3-e726-488d-aad6-504780a2374f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935578620700, "endTime": 9935578643400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57975e27-e621-488c-9cd1-8cd137916931", "logId": "6a4de749-1976-4d80-9d14-cb035f2217f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a4de749-1976-4d80-9d14-cb035f2217f3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935578620700, "endTime": 9935578643400}, "additional": {"logType": "info", "children": [], "durationId": "c4398fb3-e726-488d-aad6-504780a2374f", "parent": "d160ed37-a20a-4371-a94b-d9aef587938f"}}, {"head": {"id": "ce500168-949f-4c1e-8c50-9fe2e2f11dde", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935582226100, "endTime": 9935655685600}, "additional": {"children": ["359d0d44-6bb2-4739-becb-ab1c38a615a8", "e283f373-54db-49ad-9d92-b2962e2a156f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57975e27-e621-488c-9cd1-8cd137916931", "logId": "924812b1-0300-4c45-a25e-dcb0ee97e1ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "359d0d44-6bb2-4739-becb-ab1c38a615a8", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935582228000, "endTime": 9935587277600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce500168-949f-4c1e-8c50-9fe2e2f11dde", "logId": "792253fe-bb0f-442b-a393-e301982ec1d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e283f373-54db-49ad-9d92-b2962e2a156f", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935587299800, "endTime": 9935655670100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce500168-949f-4c1e-8c50-9fe2e2f11dde", "logId": "92568560-96ef-47d7-aebc-e8f2fc8b01dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86613b44-2863-4d54-84e2-69183ba63b1d", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935582241600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea333c7a-3d91-49ad-a4db-255014774d5d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935587149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "792253fe-bb0f-442b-a393-e301982ec1d9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935582228000, "endTime": 9935587277600}, "additional": {"logType": "info", "children": [], "durationId": "359d0d44-6bb2-4739-becb-ab1c38a615a8", "parent": "924812b1-0300-4c45-a25e-dcb0ee97e1ee"}}, {"head": {"id": "02fbef8d-c8f3-4e37-ab41-060a6c00559b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935587314200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c18d2ab-40f3-4fe2-b3d4-8c289b50ab7c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935593810700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5b9134-498d-49c5-a9a3-b64ad8c82fac", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935594546200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00527bbc-8a19-4ff8-8ae3-b2b1a941c019", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935594748400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f63831a-8fb2-432d-a935-6f6236c7bae6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935594856800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84f804c8-80d1-45c2-9606-fe7d8d767072", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935596539800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc96aca-04e0-48e9-8636-bd0532e268d8", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935600843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7c655d-8af2-49d0-8733-8641f0dc22ee", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935610464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "688e1e7b-a621-4726-9f3a-87f2c4a4516e", "name": "Sdk init in 33 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935634926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b6856d-8641-4608-bb96-f9adada84811", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935635109500}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 24}, "markType": "other"}}, {"head": {"id": "42a9bc19-506b-45a2-89ed-4ad8e829e92a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935635142900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 24}, "markType": "other"}}, {"head": {"id": "dccca603-f3a3-4a65-a4f7-4c8abffdc89c", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935655269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7791086d-f3ac-4c31-a76d-6be9df6d1a59", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935655419000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f032e89-fae2-4342-914a-67efbb7bb669", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935655516200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a42256-461f-49c8-b8a4-13fef2477653", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935655605000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92568560-96ef-47d7-aebc-e8f2fc8b01dd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935587299800, "endTime": 9935655670100}, "additional": {"logType": "info", "children": [], "durationId": "e283f373-54db-49ad-9d92-b2962e2a156f", "parent": "924812b1-0300-4c45-a25e-dcb0ee97e1ee"}}, {"head": {"id": "924812b1-0300-4c45-a25e-dcb0ee97e1ee", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935582226100, "endTime": 9935655685600}, "additional": {"logType": "info", "children": ["792253fe-bb0f-442b-a393-e301982ec1d9", "92568560-96ef-47d7-aebc-e8f2fc8b01dd"], "durationId": "ce500168-949f-4c1e-8c50-9fe2e2f11dde", "parent": "d160ed37-a20a-4371-a94b-d9aef587938f"}}, {"head": {"id": "f08aa6a0-6f8c-4fb5-ae8d-d96956c86a7f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935656789100, "endTime": 9935656803400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57975e27-e621-488c-9cd1-8cd137916931", "logId": "f857cd30-593d-4a67-9fb2-55cb96263bee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f857cd30-593d-4a67-9fb2-55cb96263bee", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935656789100, "endTime": 9935656803400}, "additional": {"logType": "info", "children": [], "durationId": "f08aa6a0-6f8c-4fb5-ae8d-d96956c86a7f", "parent": "d160ed37-a20a-4371-a94b-d9aef587938f"}}, {"head": {"id": "d160ed37-a20a-4371-a94b-d9aef587938f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935577972800, "endTime": 9935656820200}, "additional": {"logType": "info", "children": ["6a4de749-1976-4d80-9d14-cb035f2217f3", "924812b1-0300-4c45-a25e-dcb0ee97e1ee", "f857cd30-593d-4a67-9fb2-55cb96263bee"], "durationId": "57975e27-e621-488c-9cd1-8cd137916931", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "5afa0443-9c63-44ae-a1cb-1d0128149cec", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935657531600, "endTime": 9935678694400}, "additional": {"children": ["642f4ae1-2c0f-4804-99c2-03b72adb2cc3", "a996e292-4e9c-4b0a-b217-0468b7668536", "64427fc2-68f5-4910-9fc4-dfa62292787b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6293c5b1-319d-4a2a-bfe2-bec88fa9c123", "logId": "4678e645-deb2-4017-bf30-136b2d5060e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "642f4ae1-2c0f-4804-99c2-03b72adb2cc3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935661094400, "endTime": 9935661109500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5afa0443-9c63-44ae-a1cb-1d0128149cec", "logId": "561fe699-ff84-4fdf-b2da-3d4d8999611a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "561fe699-ff84-4fdf-b2da-3d4d8999611a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935661094400, "endTime": 9935661109500}, "additional": {"logType": "info", "children": [], "durationId": "642f4ae1-2c0f-4804-99c2-03b72adb2cc3", "parent": "4678e645-deb2-4017-bf30-136b2d5060e3"}}, {"head": {"id": "a996e292-4e9c-4b0a-b217-0468b7668536", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935663083900, "endTime": 9935677447000}, "additional": {"children": ["8706cd14-5182-4b64-970f-aa74965de01a", "7612d169-6cb8-4db6-9593-1af08238399d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5afa0443-9c63-44ae-a1cb-1d0128149cec", "logId": "2f88be95-b7e0-4d6b-95e4-e8e830890fbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8706cd14-5182-4b64-970f-aa74965de01a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935663085200, "endTime": 9935667346200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a996e292-4e9c-4b0a-b217-0468b7668536", "logId": "17197aaa-75f4-4c0a-9fb9-42d87cd516e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7612d169-6cb8-4db6-9593-1af08238399d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935667363200, "endTime": 9935677433700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a996e292-4e9c-4b0a-b217-0468b7668536", "logId": "313f66ae-2efd-4184-9966-749f9f642083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fe19b65-dc52-4039-ade4-438e21528705", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935663090100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab97a65-ddac-4847-8442-0f82b2f4b445", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935667235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17197aaa-75f4-4c0a-9fb9-42d87cd516e7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935663085200, "endTime": 9935667346200}, "additional": {"logType": "info", "children": [], "durationId": "8706cd14-5182-4b64-970f-aa74965de01a", "parent": "2f88be95-b7e0-4d6b-95e4-e8e830890fbf"}}, {"head": {"id": "8a9296bc-e5e9-498b-99eb-47b6a1d85532", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935667375300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee45c5d-a84b-46e3-be2c-ca65b3fa20d7", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673356600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b200168-f44d-414d-a053-ad619c7b013b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673477600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "611cbb05-0f14-4fc7-8ad9-ce1e8286f66e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673678400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94a3ee4-667f-4903-82c9-45609558e709", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "670b60a1-a0db-44b4-bc22-37b296a2de0e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a01533eb-f082-4cf0-a5d2-f60e59e738bb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673937700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee3283b-ca55-4845-b562-04860f9593b7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935673990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296e9190-3ece-4098-b385-9f8276c9cd49", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935677183300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa4e2c3-de0e-49ba-b193-1338518d4e32", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935677290400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199d8624-73e5-4192-97d9-d07c8375486a", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935677346900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f928bea-1b38-4eda-bd07-7c8def816f63", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935677392000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "313f66ae-2efd-4184-9966-749f9f642083", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935667363200, "endTime": 9935677433700}, "additional": {"logType": "info", "children": [], "durationId": "7612d169-6cb8-4db6-9593-1af08238399d", "parent": "2f88be95-b7e0-4d6b-95e4-e8e830890fbf"}}, {"head": {"id": "2f88be95-b7e0-4d6b-95e4-e8e830890fbf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935663083900, "endTime": 9935677447000}, "additional": {"logType": "info", "children": ["17197aaa-75f4-4c0a-9fb9-42d87cd516e7", "313f66ae-2efd-4184-9966-749f9f642083"], "durationId": "a996e292-4e9c-4b0a-b217-0468b7668536", "parent": "4678e645-deb2-4017-bf30-136b2d5060e3"}}, {"head": {"id": "64427fc2-68f5-4910-9fc4-dfa62292787b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935678668100, "endTime": 9935678679400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5afa0443-9c63-44ae-a1cb-1d0128149cec", "logId": "81977857-f139-4c1a-97aa-f4c9c99d7002"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81977857-f139-4c1a-97aa-f4c9c99d7002", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935678668100, "endTime": 9935678679400}, "additional": {"logType": "info", "children": [], "durationId": "64427fc2-68f5-4910-9fc4-dfa62292787b", "parent": "4678e645-deb2-4017-bf30-136b2d5060e3"}}, {"head": {"id": "4678e645-deb2-4017-bf30-136b2d5060e3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935657531600, "endTime": 9935678694400}, "additional": {"logType": "info", "children": ["561fe699-ff84-4fdf-b2da-3d4d8999611a", "2f88be95-b7e0-4d6b-95e4-e8e830890fbf", "81977857-f139-4c1a-97aa-f4c9c99d7002"], "durationId": "5afa0443-9c63-44ae-a1cb-1d0128149cec", "parent": "890a7826-7b5a-49ff-972f-a53980c25ddb"}}, {"head": {"id": "890a7826-7b5a-49ff-972f-a53980c25ddb", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935656836600, "endTime": 9935678707400}, "additional": {"logType": "info", "children": ["4678e645-deb2-4017-bf30-136b2d5060e3"], "durationId": "6293c5b1-319d-4a2a-bfe2-bec88fa9c123", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "23e86761-090c-4fe9-a8fd-7e799e31b3d4", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935691489600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e48a22f-c1ef-4139-9c6b-0d59d9d25a20", "name": "hvigorfile, resolve hvigorfile dependencies in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935691885700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b58931e-1227-4381-9e25-b65981703dec", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935678714900, "endTime": 9935691975300}, "additional": {"logType": "info", "children": [], "durationId": "14ad34c9-3a9a-41d9-a1bf-d3d220bd78e4", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "4b1f8b34-80d3-4f07-9bf0-c503a3bd6a67", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935692776400, "endTime": 9935692959300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "logId": "0ed6ec2b-8f3b-42c7-8307-a637c8834ebe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e546147d-da17-4bed-8078-f9dc9c902420", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935692803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed6ec2b-8f3b-42c7-8307-a637c8834ebe", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935692776400, "endTime": 9935692959300}, "additional": {"logType": "info", "children": [], "durationId": "4b1f8b34-80d3-4f07-9bf0-c503a3bd6a67", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "8b0d0b62-08ac-4918-a2c3-350da84a1665", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935694254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5db4016-2d2a-4a46-9bf3-935f4a3fbf56", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935704395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2103f34-cac2-46e4-b718-ee934a3a42b8", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935692974100, "endTime": 9935705251900}, "additional": {"logType": "info", "children": [], "durationId": "4b1ff676-1ee2-44d9-a5da-1f644f45b0c9", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "662978d3-881f-43d6-bcab-68be7a7ac121", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935705291200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "355f39c1-cd8e-4b9c-a268-6135c232dfd1", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935710530100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a20f2d9-ca2d-4cff-9401-efbc3d0ffe6f", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935710623900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd3f642-e061-476a-9f20-a1f5adcacc70", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935710847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031407c7-7bc3-4e4d-ab84-f5f225c01085", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935715374000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89168389-a393-41cb-b63f-daa3caaa5b4f", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935715454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4141a475-2f54-44cc-a157-504c9c88c089", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935705274500, "endTime": 9935718047400}, "additional": {"logType": "info", "children": [], "durationId": "68969abe-7db5-41d3-8ad3-31bcae0a1cd3", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "71881d9b-d76e-43df-a181-3b32ef8b1379", "name": "Configuration phase cost:146 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935718106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f73696f1-3549-484b-b3b6-530e64dad82f", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935718079200, "endTime": 9935718175500}, "additional": {"logType": "info", "children": [], "durationId": "16c7553f-8031-4a2a-bb51-a7962fa67be5", "parent": "66c08a17-1a84-423d-b67d-f0007fd4d393"}}, {"head": {"id": "66c08a17-1a84-423d-b67d-f0007fd4d393", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935566040400, "endTime": 9935718186700}, "additional": {"logType": "info", "children": ["aa0cbb1b-b428-482a-9d9b-ca7df9d471da", "6f534b6b-b61b-4eb1-b98c-cf5839406e9d", "d160ed37-a20a-4371-a94b-d9aef587938f", "890a7826-7b5a-49ff-972f-a53980c25ddb", "0b58931e-1227-4381-9e25-b65981703dec", "f2103f34-cac2-46e4-b718-ee934a3a42b8", "4141a475-2f54-44cc-a157-504c9c88c089", "f73696f1-3549-484b-b3b6-530e64dad82f", "0ed6ec2b-8f3b-42c7-8307-a637c8834ebe"], "durationId": "e1d494a1-3d82-4ebb-8e3b-ab8d88da1c7a", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "739261db-190c-4cfc-a16d-4d828393d5ab", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935719263600, "endTime": 9935719275500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b4480be-41a4-402b-a393-4e3b85a12623", "logId": "4edee870-9d34-4273-bce1-b45d185b5921"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4edee870-9d34-4273-bce1-b45d185b5921", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935719263600, "endTime": 9935719275500}, "additional": {"logType": "info", "children": [], "durationId": "739261db-190c-4cfc-a16d-4d828393d5ab", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "2be7827c-ee0f-4ce5-b6ed-087<PERSON><PERSON><PERSON><PERSON>", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935718210500, "endTime": 9935719284800}, "additional": {"logType": "info", "children": [], "durationId": "e3b82ed7-d8d7-427f-b157-0edc38ea86d4", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "5a77eab1-eb8e-4468-9fb0-5750b1572fa0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935719290100, "endTime": 9935719296200}, "additional": {"logType": "info", "children": [], "durationId": "edb948c6-dc39-4197-98de-b4df0a866057", "parent": "15be46cf-8da3-4fdb-9661-c4585e8fed11"}}, {"head": {"id": "15be46cf-8da3-4fdb-9661-c4585e8fed11", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935552545900, "endTime": 9935719300500}, "additional": {"logType": "info", "children": ["5e3fa09a-0a35-4459-9cb4-cc1263aa033a", "66c08a17-1a84-423d-b67d-f0007fd4d393", "2be7827c-ee0f-4ce5-b6ed-087<PERSON><PERSON><PERSON><PERSON>", "5a77eab1-eb8e-4468-9fb0-5750b1572fa0", "43049993-c4c0-43ee-bd66-c71a73715661", "fdfc79bf-8f5a-485a-a45d-55a0752aba8c", "4edee870-9d34-4273-bce1-b45d185b5921"], "durationId": "3b4480be-41a4-402b-a393-4e3b85a12623"}}, {"head": {"id": "bc035d42-6c16-4a84-9482-d9e39b91ed71", "name": "Configuration task cost before running: 171 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935719576700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f311ef44-c0bb-4f00-94e2-a1f7372d940d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935723722400, "endTime": 9935731323600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b9b78fa3-acb0-495b-8080-b729fb94b252", "logId": "82a14a13-7bd1-4468-a740-ad2e6c0149d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9b78fa3-acb0-495b-8080-b729fb94b252", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935720805000}, "additional": {"logType": "detail", "children": [], "durationId": "f311ef44-c0bb-4f00-94e2-a1f7372d940d"}}, {"head": {"id": "1c130ef9-972d-45d8-9506-378188e5db24", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935721224600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a67532-0447-486a-9cc4-dab4ff50e671", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935721297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb515fb1-5b69-4490-87ef-54a5cd1340ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935721344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a57c129d-3884-443f-b303-51a89c77cd75", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935723732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e937f791-5177-44e7-83ca-0a846fc75ecd", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935731121800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa600c44-4146-4c3d-a412-d3f0d7617d60", "name": "entry : default@PreBuild cost memory 0.29566192626953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935731254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a14a13-7bd1-4468-a740-ad2e6c0149d9", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935723722400, "endTime": 9935731323600}, "additional": {"logType": "info", "children": [], "durationId": "f311ef44-c0bb-4f00-94e2-a1f7372d940d"}}, {"head": {"id": "2609c5d8-82bd-4107-b305-7770e22fe007", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935735933700, "endTime": 9935738994700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "cb907f12-be7f-4b15-8d60-b2037f20cd75", "logId": "aa4d4b42-3342-4a9e-8a39-c346e09eff6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb907f12-be7f-4b15-8d60-b2037f20cd75", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935734703000}, "additional": {"logType": "detail", "children": [], "durationId": "2609c5d8-82bd-4107-b305-7770e22fe007"}}, {"head": {"id": "96cc96fc-8424-46cc-a92c-38facafd0164", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935735166100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7cedcb-4c82-4f95-8463-49d9ff794de9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935735252700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed80205d-36fa-4489-be4e-c52397b01746", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935735304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ca14484-595c-4049-84c0-a9bfb005f02e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935735943800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f65742e4-4844-42db-853e-9c796f1060a9", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935738780700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "261150a3-209b-4bca-b520-788163572071", "name": "entry : default@MergeProfile cost memory 0.1324920654296875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935738916200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4d4b42-3342-4a9e-8a39-c346e09eff6d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935735933700, "endTime": 9935738994700}, "additional": {"logType": "info", "children": [], "durationId": "2609c5d8-82bd-4107-b305-7770e22fe007"}}, {"head": {"id": "e5aab889-db66-43a1-82de-cc2cce777152", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935742823100, "endTime": 9935746059300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fa907f25-3e1d-41b7-986f-d7b89b5e25a7", "logId": "9ae61eb2-bd5f-4e2e-8d98-d7cd2d5254b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa907f25-3e1d-41b7-986f-d7b89b5e25a7", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935741411400}, "additional": {"logType": "detail", "children": [], "durationId": "e5aab889-db66-43a1-82de-cc2cce777152"}}, {"head": {"id": "2f3d4aa2-882d-4dd1-8aad-faa4e59f296a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935741896600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48903a4-239c-4c66-9387-9d51c02e126a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935741999600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ee99f04-b495-42d8-9cb3-5c0f79ef8c5c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935742055200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41bb4fb3-3653-4f4f-902d-75c03a9fc9fe", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935742835200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e481cedd-c76e-4f36-94f0-5027676a6738", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935744712200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb0fa455-4160-4ed8-9fe0-8e1dbe5ff9f2", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935745896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0373cd8a-1860-4067-aad7-d9f0f34e40a2", "name": "entry : default@CreateBuildProfile cost memory -1.6410369873046875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935745994300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ae61eb2-bd5f-4e2e-8d98-d7cd2d5254b1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935742823100, "endTime": 9935746059300}, "additional": {"logType": "info", "children": [], "durationId": "e5aab889-db66-43a1-82de-cc2cce777152"}}, {"head": {"id": "5c33a138-6a5d-4526-ae6e-96da8e798484", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935748929900, "endTime": 9935749391400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "41c2ea6a-6c92-44d0-b7c7-2bb879ee0f56", "logId": "0695a276-0bea-4f87-9517-3e3bab480ac2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41c2ea6a-6c92-44d0-b7c7-2bb879ee0f56", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935747501800}, "additional": {"logType": "detail", "children": [], "durationId": "5c33a138-6a5d-4526-ae6e-96da8e798484"}}, {"head": {"id": "a81bd1ab-47f8-4cdb-a229-ae0ee4a5b3f2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935747975200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cdfa55c-5864-45f9-b078-82dd9da6dd7e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935748076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5d44fc-18b3-4635-aa8f-bd93ef460e09", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935748156400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02eb4e0f-f53d-41be-8130-eaa4450aafe2", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935748941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f755764-93b8-482c-b757-58b5dc01523c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935749051600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ee60e8-6da4-47d1-95ca-84c3ac94459e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935749139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38935ee4-af7c-4e04-9a61-328fbefcd76d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935749187900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b140758f-e188-4de9-be4b-4a5cd3ea848a", "name": "entry : default@PreCheckSyscap cost memory 0.050567626953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935749261700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e049659-9b48-45b4-a706-235603c5f01a", "name": "runTaskFromQueue task cost before running: 200 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935749337800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0695a276-0bea-4f87-9517-3e3bab480ac2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935748929900, "endTime": 9935749391400, "totalTime": 387100}, "additional": {"logType": "info", "children": [], "durationId": "5c33a138-6a5d-4526-ae6e-96da8e798484"}}, {"head": {"id": "10a48ab3-b7a1-41a2-9326-9658125ae5b3", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935759147100, "endTime": 9935760186300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "71c92785-3ce9-418c-a528-0712e9536d61", "logId": "64c323b3-093e-4e11-83b7-ecd53b8ea86a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71c92785-3ce9-418c-a528-0712e9536d61", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935750950700}, "additional": {"logType": "detail", "children": [], "durationId": "10a48ab3-b7a1-41a2-9326-9658125ae5b3"}}, {"head": {"id": "d3e39006-1ce4-44b7-b437-2f95bed66f74", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935751474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ae5611-15b2-4613-a991-c5074d820b66", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935751589500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4550a8-f707-4c63-a634-9b40fa8b224d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935751649600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "185c3879-d70e-481d-b34d-d88015f2d4fb", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935759165500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6792f5-1e56-4e71-a39e-b86ff5969110", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935759402600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337fcd40-b78b-4004-9a94-683f969d879f", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935760015100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18b6e1e3-52d1-454e-9c36-feb5acabb33e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0698394775390625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935760117300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c323b3-093e-4e11-83b7-ecd53b8ea86a", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935759147100, "endTime": 9935760186300}, "additional": {"logType": "info", "children": [], "durationId": "10a48ab3-b7a1-41a2-9326-9658125ae5b3"}}, {"head": {"id": "ffbdb707-c143-406e-95ed-2a7587765a9f", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935763358000, "endTime": 9935764704900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dd1c4761-b3cb-46b8-9453-87bc29a0461b", "logId": "7105916a-4c87-434e-9696-6de2d28f24f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd1c4761-b3cb-46b8-9453-87bc29a0461b", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935761690000}, "additional": {"logType": "detail", "children": [], "durationId": "ffbdb707-c143-406e-95ed-2a7587765a9f"}}, {"head": {"id": "d2c582f8-ead8-4991-8804-b4b73940a939", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935762168200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "105d3afc-6570-41be-8b04-ec84402984b6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935762252800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3b593a-0a0f-4cfb-b4d9-3eef7961ff14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935762304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de18457b-c0ca-4ec6-88d8-3378084a750e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935763370000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a04f017-8108-406f-8fea-ed2167c25f45", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935764499500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb90fc30-947c-4915-ab3f-096db49f1eb2", "name": "entry : default@ProcessProfile cost memory 0.0573577880859375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935764621200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7105916a-4c87-434e-9696-6de2d28f24f8", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935763358000, "endTime": 9935764704900}, "additional": {"logType": "info", "children": [], "durationId": "ffbdb707-c143-406e-95ed-2a7587765a9f"}}, {"head": {"id": "c7e4a318-9c83-4d12-bc7c-1decf46bb2f7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935768532500, "endTime": 9935774935500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2355fc85-e8e6-44cd-92b6-ce793a5d4a6e", "logId": "ba03f0ad-584e-4958-9ec4-5987e942da77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2355fc85-e8e6-44cd-92b6-ce793a5d4a6e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935766301500}, "additional": {"logType": "detail", "children": [], "durationId": "c7e4a318-9c83-4d12-bc7c-1decf46bb2f7"}}, {"head": {"id": "650655a3-bbdb-4644-8208-a1af06a17022", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935766771100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa912f75-5c77-4316-a6db-222f11d91ca4", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935766873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feb3ae5a-bb19-4e63-b533-1f0e0a0c76c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935766932500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94fef8c-56e4-4356-9fc5-30735412ba82", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935768545300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f811b218-9767-447e-901e-92c93315289a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935774710200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd33ae5e-ed94-45a3-8ad3-0749839ecbe8", "name": "entry : default@ProcessRouterMap cost memory -1.3871536254882812", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935774856700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba03f0ad-584e-4958-9ec4-5987e942da77", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935768532500, "endTime": 9935774935500}, "additional": {"logType": "info", "children": [], "durationId": "c7e4a318-9c83-4d12-bc7c-1decf46bb2f7"}}, {"head": {"id": "0142de64-9220-4979-9b6f-fbc2df452f6a", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935781941100, "endTime": 9935784820600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6725db8e-8437-4c2f-a1ab-e90c0120b059", "logId": "cf559437-edc8-410f-9eb9-1e394e4c301d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6725db8e-8437-4c2f-a1ab-e90c0120b059", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935777878200}, "additional": {"logType": "detail", "children": [], "durationId": "0142de64-9220-4979-9b6f-fbc2df452f6a"}}, {"head": {"id": "decf07df-755b-4b06-b728-6aad0a9b8ed2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935778320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988b68bb-ac30-41e7-b7ad-b994766b639b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935778401200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ae45e47-bb08-48d0-b65b-7a06ee437a6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935778449900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f2e8634-c406-4149-ac04-4d4901ca7e71", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935779273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72628bee-8e71-4219-9d75-a61f8483d0e8", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935783135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7bb852a-5086-430d-805c-4e2f9debf0d4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935783281300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af50c3b8-ee7d-4a30-9a67-2fd94542aa4b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935783341700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6330d856-53ae-48ae-8124-8ec0e1823f85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935783388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2602a4f-0d19-4a02-ab40-52d28d9bef79", "name": "entry : default@PreviewProcessResource cost memory 0.0930938720703125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935783461400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43cb95de-13d7-40bd-a1d3-38f265a9ce21", "name": "runTaskFromQueue task cost before running: 236 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935784734100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf559437-edc8-410f-9eb9-1e394e4c301d", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935781941100, "endTime": 9935784820600, "totalTime": 1576500}, "additional": {"logType": "info", "children": [], "durationId": "0142de64-9220-4979-9b6f-fbc2df452f6a"}}, {"head": {"id": "f98449f3-a984-4751-861d-b86db35b503c", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935791646600, "endTime": 9935812220600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e2741eb4-992c-4bc9-a3bb-805487173507", "logId": "30fa9958-18d4-4233-abcd-4c193d4842ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2741eb4-992c-4bc9-a3bb-805487173507", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935787730600}, "additional": {"logType": "detail", "children": [], "durationId": "f98449f3-a984-4751-861d-b86db35b503c"}}, {"head": {"id": "56055756-511d-4e17-a5ad-2425e1c689a4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935788164200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eabb3258-af33-4364-a154-da7ddfeb58fb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935788242300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bba40972-4fe7-4bb6-a227-159080c9cc67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935788293100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f71b84fa-3c42-46bf-bef1-7654e78c5687", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935791665200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e42ba0e-348c-4b5b-a3ae-6bac7d01a8d0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935812004000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da9f173-4d34-4b1d-a22d-eb1cbb9d9eb1", "name": "entry : default@GenerateLoaderJson cost memory -0.8087158203125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935812143200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30fa9958-18d4-4233-abcd-4c193d4842ec", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935791646600, "endTime": 9935812220600}, "additional": {"logType": "info", "children": [], "durationId": "f98449f3-a984-4751-861d-b86db35b503c"}}, {"head": {"id": "899f477e-286f-4642-8a05-158216074f2b", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935823131200, "endTime": 9935859914200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8373513b-c41c-4d78-97f3-431edec202ca", "logId": "4a3a8b9b-ea19-4dc2-8358-ecd5cbc5b27d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8373513b-c41c-4d78-97f3-431edec202ca", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935819264800}, "additional": {"logType": "detail", "children": [], "durationId": "899f477e-286f-4642-8a05-158216074f2b"}}, {"head": {"id": "67873d97-cdf8-4427-b07d-7d5802518ff5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935819706300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "711b76e4-1b99-4437-b75a-6df2498e0371", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935819789600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce37587c-4899-41ce-8b32-d85895ed0d72", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935819841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88934864-e660-4c84-8682-bf20ae544517", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935820653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3694f0ea-a85e-4bb9-9555-83d1f919be9b", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935823157400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12dae11d-ea5a-4a1c-8dfb-b447f2ace000", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935859577100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0c6ad3-9246-4054-94ec-42bda63a13f7", "name": "entry : default@PreviewCompileResource cost memory -0.32718658447265625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935859778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3a8b9b-ea19-4dc2-8358-ecd5cbc5b27d", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935823131200, "endTime": 9935859914200}, "additional": {"logType": "info", "children": [], "durationId": "899f477e-286f-4642-8a05-158216074f2b"}}, {"head": {"id": "71335ebd-f722-4efd-984c-dee06975e159", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862869200, "endTime": 9935863234600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "dce76868-7ac2-41f3-a336-12cdc04dd4a0", "logId": "c8618c40-9c9b-444e-a85c-ea602a4e419e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dce76868-7ac2-41f3-a336-12cdc04dd4a0", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862174900}, "additional": {"logType": "detail", "children": [], "durationId": "71335ebd-f722-4efd-984c-dee06975e159"}}, {"head": {"id": "1225b220-c0c3-4f1d-b446-eae64fdae054", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862633100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc411c5f-ebca-4968-8fa9-a6bfdd80254e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862727700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbca1b8b-20f2-461b-93a0-e4aabfdbd1b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862782100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6060a174-3937-4c0a-a723-c3e826b16ea0", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862879600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a4506f-4241-4bae-aeef-84f223f6c36b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862964500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988a1615-fb3b-4d86-a784-9fb9b6712548", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935863011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e584642-b601-4392-9ea7-7c7470b70b5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935863049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e469a3-9a53-4889-997f-84ff1baf4bfc", "name": "entry : default@PreviewHookCompileResource cost memory 0.05152130126953125", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935863109300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce68a51-b392-47fa-9555-276958207558", "name": "runTaskFromQueue task cost before running: 314 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935863179700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8618c40-9c9b-444e-a85c-ea602a4e419e", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935862869200, "endTime": 9935863234600, "totalTime": 291200}, "additional": {"logType": "info", "children": [], "durationId": "71335ebd-f722-4efd-984c-dee06975e159"}}, {"head": {"id": "50c8869c-79b6-4701-82e7-864058ddc1d6", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935867103400, "endTime": 9935869909000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "9efa3cf7-d546-42ce-a1f9-80a2c3259076", "logId": "42987d49-c388-41d1-8302-8c423f1ff058"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9efa3cf7-d546-42ce-a1f9-80a2c3259076", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935865067000}, "additional": {"logType": "detail", "children": [], "durationId": "50c8869c-79b6-4701-82e7-864058ddc1d6"}}, {"head": {"id": "20e926ee-985d-4c16-a45e-58f31396c871", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935865837100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e95f53b8-583f-40f1-8c2d-1d97c063b571", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935866017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d1ad5d-3652-492d-9c09-405f1af97a68", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935866114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff408174-244d-4a5c-9f28-71a7dc1fcc2f", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935867124600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5eb96f-cba6-4de7-aa18-7c3e6beab86c", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935869706000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36abf8c9-c0a2-4a16-8eaa-81b909d5b7aa", "name": "entry : default@CopyPreviewProfile cost memory 0.1527252197265625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935869841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42987d49-c388-41d1-8302-8c423f1ff058", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935867103400, "endTime": 9935869909000}, "additional": {"logType": "info", "children": [], "durationId": "50c8869c-79b6-4701-82e7-864058ddc1d6"}}, {"head": {"id": "f84a5398-56fa-4516-9a31-d5ac871540e9", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935872860400, "endTime": 9935873343900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "21171dd1-b16a-4f82-946f-ab68d15c2bbe", "logId": "544c0cc1-39ba-44b4-9c7d-9fab6feb728b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21171dd1-b16a-4f82-946f-ab68d15c2bbe", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935871515900}, "additional": {"logType": "detail", "children": [], "durationId": "f84a5398-56fa-4516-9a31-d5ac871540e9"}}, {"head": {"id": "e5dd3cd3-7a2c-4ad6-8d48-659662a75bd4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935871983300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d0f8fc-ca43-4efa-8708-df9aa7b8a93b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935872074900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b0576c-e9a1-4e37-a349-ce2ddae3d446", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935872138700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "805d054d-d1e8-42ea-9c7c-b6226a8fdf11", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935872871100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d843591-fa6a-45ba-9997-f0875d072109", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935872988700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ecf9f11-054a-4ba1-aea0-c2f0befe5909", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935873043100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e352f4-2a28-48dd-840d-59c43ca82d8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935873086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "047aa0db-c6b1-4518-9f0b-f69f1645a815", "name": "entry : default@ReplacePreviewerPage cost memory 0.05181121826171875", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935873207300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57e2cdd-352b-411c-b2c7-77091a92767a", "name": "runTaskFromQueue task cost before running: 324 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935873289700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "544c0cc1-39ba-44b4-9c7d-9fab6feb728b", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935872860400, "endTime": 9935873343900, "totalTime": 408400}, "additional": {"logType": "info", "children": [], "durationId": "f84a5398-56fa-4516-9a31-d5ac871540e9"}}, {"head": {"id": "ac508378-46e8-438b-80b3-6fe85506922c", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935874886900, "endTime": 9935875114900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8b43ace9-1247-4864-97e8-9c1f195c3218", "logId": "18aad90b-e55b-4795-8e0f-e722b5318d62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b43ace9-1247-4864-97e8-9c1f195c3218", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935874842300}, "additional": {"logType": "detail", "children": [], "durationId": "ac508378-46e8-438b-80b3-6fe85506922c"}}, {"head": {"id": "75910125-533a-47f2-9871-a7bfd3ad91f3", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935874895000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15271de9-3767-4ac5-b6a8-e22ccbdf1f6b", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935874994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a2be5c2-a760-4a46-8068-9d0eeeb5d98c", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935875068000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18aad90b-e55b-4795-8e0f-e722b5318d62", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935874886900, "endTime": 9935875114900, "totalTime": 163100}, "additional": {"logType": "info", "children": [], "durationId": "ac508378-46e8-438b-80b3-6fe85506922c"}}, {"head": {"id": "16427431-c5af-4fd7-87db-18e78915c29b", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935878424400, "endTime": 9935880868900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "321b9d71-9b64-4cd2-bfec-eb9a7d5bdeda", "logId": "cf79c637-e8b5-46d4-8b71-06e42c1576bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "321b9d71-9b64-4cd2-bfec-eb9a7d5bdeda", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935876620200}, "additional": {"logType": "detail", "children": [], "durationId": "16427431-c5af-4fd7-87db-18e78915c29b"}}, {"head": {"id": "6f12c8e1-1e74-4fc2-96ab-cdde35b55501", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935877104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2b242b-c2d9-40b1-ac14-eb30434c22cc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935877200300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8697e5ab-e425-4e18-87ce-617843936b49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935877254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15efb985-d04c-4b37-a91d-5b109340ca7a", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935878437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb51d12-9574-4ddd-9026-97a375f5093f", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935880716600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c2d7322-f981-4b99-bfa0-677c333e23d3", "name": "entry : default@PreviewUpdateAssets cost memory 0.11761474609375", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935880810400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf79c637-e8b5-46d4-8b71-06e42c1576bf", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935878424400, "endTime": 9935880868900}, "additional": {"logType": "info", "children": [], "durationId": "16427431-c5af-4fd7-87db-18e78915c29b"}}, {"head": {"id": "4c39362d-b20d-40ff-8b3f-f5587e83cbae", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935888025000, "endTime": 9939383834200}, "additional": {"children": ["9c490bc4-3faa-4432-8859-247662bc8978"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cef59668-3a02-4bda-bd52-c3dc4c168546", "logId": "1dd5354b-d5b3-4fe3-b0b4-9effc796ec15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cef59668-3a02-4bda-bd52-c3dc4c168546", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935882858400}, "additional": {"logType": "detail", "children": [], "durationId": "4c39362d-b20d-40ff-8b3f-f5587e83cbae"}}, {"head": {"id": "14282c80-dc41-4ad7-9c05-9633041e9b8f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935883308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a448915a-5d29-4652-93bb-7c05f9aa7737", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935883393600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b50df9-d20c-4329-9b71-d27da8e5cce1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935883443900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2510b655-6fce-4488-a1d9-e37b1b1fa7cb", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935888036700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c490bc4-3faa-4432-8859-247662bc8978", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14580, "tid": "Worker3", "startTime": 9935911055600, "endTime": 9939383585400}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4c39362d-b20d-40ff-8b3f-f5587e83cbae", "logId": "e57d6251-082a-49ca-92c7-bb8b32ce698e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "374f697a-0731-4eb3-a223-3be480bd7a9c", "name": "entry : default@PreviewArkTS cost memory 1.2481307983398438", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935913142000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57d6251-082a-49ca-92c7-bb8b32ce698e", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Worker3", "startTime": 9935911055600, "endTime": 9939383585400}, "additional": {"logType": "error", "children": [], "durationId": "9c490bc4-3faa-4432-8859-247662bc8978", "parent": "1dd5354b-d5b3-4fe3-b0b4-9effc796ec15"}}, {"head": {"id": "72f5509f-281b-4bba-a1f2-e859ecb20522", "name": "default@PreviewArkTS watch work[3] failed.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939383644500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dd5354b-d5b3-4fe3-b0b4-9effc796ec15", "name": "Failed :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935888025000, "endTime": 9939383834200}, "additional": {"logType": "error", "children": ["e57d6251-082a-49ca-92c7-bb8b32ce698e"], "durationId": "4c39362d-b20d-40ff-8b3f-f5587e83cbae"}}, {"head": {"id": "2808c9b3-c94f-4a8a-909b-378f88f4b1e3", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939383947100}, "additional": {"logType": "debug", "children": [], "durationId": "4c39362d-b20d-40ff-8b3f-f5587e83cbae"}}, {"head": {"id": "899ebee3-fafa-41bd-ae8f-aed497917441", "name": "ERROR: stacktrace = Error: \u001b[31m ERROR: page 'D:/AAAqimo/wallet/HarmonyOS/Wallet/entry/src/main/ets/pages/Index.ets' does not exist. \u001b[39m\n    at handleResponse (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:1683)\n    at async Worker.<anonymous> (D:\\HarmonyOS\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\pool\\worker-pool\\watch-worker.js:1:2871)", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939384473300}, "additional": {"logType": "debug", "children": [], "durationId": "4c39362d-b20d-40ff-8b3f-f5587e83cbae"}}, {"head": {"id": "e0f776dc-50d3-4902-b218-780739a4132a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939396936800, "endTime": 9939396995200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea61e5d9-7d78-4f08-9394-a2a545e21000", "logId": "04d94a69-db9e-42c4-ae42-7d13a0caf967"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04d94a69-db9e-42c4-ae42-7d13a0caf967", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939396936800, "endTime": 9939396995200}, "additional": {"logType": "info", "children": [], "durationId": "e0f776dc-50d3-4902-b218-780739a4132a"}}, {"head": {"id": "c0c88e92-5283-48b9-a711-ff06a535c1fb", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9935549531500, "endTime": 9939397315700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 24}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "400909b9-8cce-4fd5-878d-29b2a72644d4", "name": "BUILD FAILED in 3 s 848 ms ", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397354500}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "794ae90e-b904-4b7a-9fec-433c4a764281", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397605900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46aca022-1a23-470f-a698-b65ec92129dc", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b06642b-d23e-4ca2-ab3b-a684afe1e051", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397788700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8172fa2a-631a-4d7f-861f-3f03ab5a26e6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a979ba1-0962-4e0d-8dd2-d5f58d0fd892", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f6624d-6263-4049-b612-27451bd822d3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939397991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f77057c-fdcf-4a83-ac26-e8d08fac34de", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939398050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2ae0d9d-ba34-4ff0-8879-61811f2a7ab1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939398116800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b50da2-2802-4604-888c-8c3a6d21c309", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939398191600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f38414-245b-4de4-8d4a-c606c7ae314c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939398265600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004daa66-c070-4063-9758-95ff198b439b", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939402382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c14e32-c797-4dbb-bdbd-6606be3cba34", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939403232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9402c1-90ce-4899-adaa-6944fd69d0e8", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939403533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6039d97-2c79-4bf0-9713-8af260b72bea", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939417648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e62a89c1-90f9-4a89-8ad9-707dd3367e07", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939418579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49fbc38-52f6-46a8-854a-e9631e3a4512", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939418867500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b3ca61-2316-406d-8412-c31a1dd97a99", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939419137800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe3a86ed-1197-4e30-aa83-358e72b02054", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939419905600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f165c033-00e4-4a71-846c-6863afc01a33", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939423353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfee5020-3ba3-4d02-b7d2-adfdfeacaa68", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939423615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "149bf6d3-df56-4fc8-8f8e-bf4f30ae5f2c", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939423865100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f97de48-4a2b-4e77-94ef-c2b9ca7beb8f", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939424145700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89b669d-97eb-4894-b547-e8c843afd38e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:27 ms .", "description": "", "type": "log"}, "body": {"pid": 14580, "tid": "Main Thread", "startTime": 9939424426000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}