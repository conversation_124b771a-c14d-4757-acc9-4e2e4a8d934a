import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: Login
    },
    
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/Home.vue'),
      children: [
        {
          path: '',
          name: 'bankAccount',
          component: () => import('../views/BankAccount/BankAccountManage.vue')
        },
        {
          path: 'cardDetail/:id',
          name: 'cardDetail',
          component: () => import('../views/cards/CardDetail.vue')
        },
            {
          path: 'cardsManage',
          name: 'cardsManage',
          component: () => import('../views/cards/CardsManage.vue')
        },
        {
          path: 'receipts',
          name: 'receipts',
          component: () => import('../views/transactions/Receipts.vue')
        },
        {
          path: 'transactions',
          name: 'transactions',
          component: () => import('../views/transactions/TransactionsManage.vue')
        },
        {
          path: 'user',
          name: 'user',
          component: () => import('../views/system/UserManage.vue')
        },
        {
          path: 'role',
          name: 'role',
          component: () => import('../views/system/RolePermission.vue')
        },
        {
          path: 'transfers',
          name: 'transfers',
          component: () => import('../views/transactions/Transfers.vue')
        },
        {
          path: 'transfer',
          name: 'transfer',
          component: () => import('../views/transactions/Transfer.vue')
        },
        {
          path: 'wallet',
          name: 'wallet',
          component: () => import('../views/wallet/WalletManage.vue')
        },

        {
          path: 'walletFixed',
          name: 'walletFixed',
          component: () => import('../views/wallet/WalletManageNewFixed.vue')
        },
        {
          path: 'cardsFixed',
          name: 'cardsFixed',
          component: () => import('../views/cards/CardsManageNewFixed.vue')
        },
     
        {
          path: 'payment',
          name: 'payment',
          component: () => import('../views/payment/PaymentManage.vue')
        },
        {
          path: 'paymentFixed',
          name: 'paymentFixed',
          component: () => import('../views/payment/PaymentFixed.vue')
        },
        {
          path: 'paymentTest',
          name: 'paymentTest',
          component: () => import('../views/payment/PaymentTest.vue')
        },
        {
          path: 'bankAccountOptimized',
          name: 'bankAccountOptimized',
          component: () => import('../views/BankAccount/BankAccountManageOptimized.vue')
        },
        {
          path: 'transactionsOptimized',
          name: 'transactionsOptimized',
          component: () => import('../views/transactions/TransactionsManageOptimized.vue')
        },
       
      ]
    }
  ]
})

import { getUserType, getToken, hasRoutePermission, getLoginPath, USER_TYPES } from '../utils/auth.js';

// 路由守卫
router.beforeEach((to, from, next) => {
  const userType = getUserType();
  const token = getToken();

  // 如果访问的是登录或注册页面，直接放行
  if (to.path === '/login' || to.path === '/admin-login' || to.path === '/admin-register' || to.path === '/admin-code-login' || to.path === '/login-nav') {
    next();
    return;
  }

  // 如果没有token，根据路径跳转到对应登录页
  if (!token) {
    if (to.path.startsWith('/admin')) {
      next('/admin-login');
    } else {
      next('/login');
    }
    return;
  }

  // 检查路由权限
  if (!hasRoutePermission(to.path)) {
    const loginPath = getLoginPath(userType);
    next(loginPath);
    return;
  }

  next();
});

export default router