package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icss.wallet.entity.Wallet;
import com.icss.wallet.result.R;
import com.icss.wallet.service.WalletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@CrossOrigin
@RestController
@RequestMapping("/wallet")
public class WalletController {
    
    @Autowired
    private WalletService walletService;
    
    /**
     * 获取钱包余额
     */
    @GetMapping("/balance/{userId}")
    public R<Wallet> getBalance(@PathVariable Long userId) {
        try {
            Wallet wallet = walletService.getWalletByUserId(userId);
            if (wallet == null) {
                return R.failure("钱包不存在");
            }
            return R.success("获取余额成功", wallet);
        } catch (Exception e) {
            return R.failure("获取余额失败: " + e.getMessage());
        }
    }
    
    /**
     * 钱包充值（从银行卡充值到钱包）
     */
    @PostMapping("/recharge")
    public R recharge(@RequestParam Long userId,
                     @RequestParam BigDecimal amount,
                     @RequestParam String bankCardNumber,
                     @RequestParam(required = false) String remark) {
        try {
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return R.failure("充值金额必须大于0");
            }
            
            if (amount.compareTo(new BigDecimal("100000")) > 0) {
                return R.failure("单次充值金额不能超过10万元");
            }
            
            boolean success = walletService.recharge(userId, amount, bankCardNumber, remark);
            if (success) {
                return R.success("充值成功");
            } else {
                return R.failure("充值失败，请检查钱包状态");
            }
        } catch (Exception e) {
            return R.failure("充值失败: " + e.getMessage());
        }
    }
    
    /**
     * 钱包提现（从钱包提现到银行卡）
     */
    @PostMapping("/withdraw")
    public R withdraw(@RequestParam Long userId,
                     @RequestParam BigDecimal amount,
                     @RequestParam String bankCardNumber,
                     @RequestParam(required = false) String remark) {
        try {
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return R.failure("提现金额必须大于0");
            }
            
            if (amount.compareTo(new BigDecimal("50000")) > 0) {
                return R.failure("单次提现金额不能超过5万元");
            }
            
            boolean success = walletService.withdraw(userId, amount, bankCardNumber, remark);
            if (success) {
                return R.success("提现成功");
            } else {
                return R.failure("提现失败，余额不足或钱包状态异常");
            }
        } catch (Exception e) {
            return R.failure("提现失败: " + e.getMessage());
        }
    }

    // ==================== 管理员专用API ====================

    /**
     * 管理员分页查询钱包信息（包含用户信息）
     */
    @GetMapping("/admin/page")
    public R getWalletsWithUserInfo(@RequestParam(defaultValue = "1") int pageNum,
                                   @RequestParam(defaultValue = "10") int pageSize,
                                   @RequestParam(required = false) String phone,
                                   @RequestParam(required = false) String realName,
                                   @RequestParam(required = false) Integer status) {
        try {
            IPage<Wallet> page = walletService.getWalletsWithUserInfo(pageNum, pageSize, phone, realName, status);
            return R.success("查询成功", page);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 管理员获取钱包统计信息
     */
    @GetMapping("/admin/statistics")
    public R getWalletStatistics() {
        try {
            java.util.Map<String, Object> statistics = walletService.getWalletStatistics();
            return R.success("获取统计信息成功", statistics);
        } catch (Exception e) {
            return R.failure("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 管理员调整钱包余额
     */
    @PostMapping("/admin/adjust")
    public R adjustWalletBalance(@RequestBody java.util.Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            java.util.List<Long> walletIds = (java.util.List<Long>) request.get("walletIds");
            String type = (String) request.get("type");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String reason = (String) request.get("reason");

            boolean result = walletService.adjustWalletBalance(walletIds, type, amount, reason);
            if (result) {
                return R.success("余额调整成功");
            } else {
                return R.failure("余额调整失败");
            }
        } catch (Exception e) {
            return R.failure("余额调整失败: " + e.getMessage());
        }
    }

    /**
     * 管理员切换钱包状态
     */
    @PostMapping("/admin/toggle-status")
    public R toggleWalletStatus(@RequestBody java.util.Map<String, Object> request) {
        try {
            Long walletId = Long.valueOf(request.get("walletId").toString());
            Integer status = Integer.valueOf(request.get("status").toString());

            boolean result = walletService.toggleWalletStatus(walletId, status);
            if (result) {
                return R.success("状态切换成功");
            } else {
                return R.failure("状态切换失败");
            }
        } catch (Exception e) {
            return R.failure("状态切换失败: " + e.getMessage());
        }
    }
}
