<script setup>
import { useRouter } from "vue-router";
const router = useRouter();
import {
  User,
  Coin,
  CreditCard,
  DocumentCopy,
  Setting,
  Wallet,
  Money,
} from "@element-plus/icons-vue";
const handleSelect = (key, keyPath) => {
  console.log(key, keyPath);
  if (key == "logout") {
    router.push("/login");
  }
};
</script>
<template>
  <el-menu
    active-text-color="#409eff"
    background-color="#393d42fa"
    class="el-menu-vertical-demo"
    default-active="/home"
    text-color="#bfcbd9"
    :router="true"
    style="height: 100vh; position: fixed; width: 230px;"
  >
    <el-sub-menu index="/home">
      <template #title>
        <el-icon><User /></el-icon>
        <span>银行账户管理</span>
      </template>
           <el-menu-item index="/home">银行账户</el-menu-item>
    </el-sub-menu>
    
     <el-sub-menu index="wallet">
      <template #title>
        <el-icon><Wallet /></el-icon>
        <span>钱包管理</span>
      </template>

      <el-menu-item index="/home/<USER>">钱包支付</el-menu-item>
    </el-sub-menu>

     <el-sub-menu index="payment">
      <template #title>
        <el-icon><Money /></el-icon>
        <span>支付管理</span>
      </template>
      <el-menu-item index="/home/<USER>">支付中心</el-menu-item>
    </el-sub-menu>

     <el-sub-menu index="cards">
      <template #title>
        <el-icon><CreditCard /></el-icon>
        <span>银行卡管理</span>
      </template>
      <el-menu-item index="/home/<USER>">银行卡</el-menu-item>
    </el-sub-menu>
    
     <el-sub-menu index="transactions">
      <template #title>
      <el-icon><DocumentCopy /></el-icon>
        <span>交易管理</span>
      </template>
      <el-menu-item index="/home/<USER>">发起转账</el-menu-item>
      <el-menu-item index="/home/<USER>">转账记录</el-menu-item>
         <el-menu-item index="/home/<USER>">收款记录</el-menu-item>
              <el-menu-item index="/home/<USER>">交易记录管理</el-menu-item>
    </el-sub-menu>
    <el-sub-menu index="system">
      <template #title>
        <el-icon><Setting /></el-icon>
        <span>系统设置</span>
      </template>
      <el-menu-item index="/home/<USER>">用户管理</el-menu-item>
      <el-menu-item index="/home/<USER>">角色权限</el-menu-item>
    </el-sub-menu>
  </el-menu>
</template>

<style scoped>
.el-menu-vertical-demo {
  border-right: none;
}
.el-menu-item.is-active {
  background-color: #454545d4 !important;
}
</style>