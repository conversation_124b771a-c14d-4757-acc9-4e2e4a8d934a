if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CardDetailPage_Params {
    params?: RouterParams;
    bankName?: string;
    fullCardNumber?: string;
    cardType?: string;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
interface RouterParams {
    bankName?: string;
    cardNumber?: string;
    fullCardNumber?: string;
    cardType?: string;
}
class CardDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.params = router.getParams() as RouterParams;
        this.__bankName = new ObservedPropertySimplePU(this.params.bankName || '', this, "bankName");
        this.__fullCardNumber = new ObservedPropertySimplePU(this.params.fullCardNumber || '', this, "fullCardNumber");
        this.__cardType = new ObservedPropertySimplePU(this.params.cardType || '', this, "cardType");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CardDetailPage_Params) {
        if (params.params !== undefined) {
            this.params = params.params;
        }
        if (params.bankName !== undefined) {
            this.bankName = params.bankName;
        }
        if (params.fullCardNumber !== undefined) {
            this.fullCardNumber = params.fullCardNumber;
        }
        if (params.cardType !== undefined) {
            this.cardType = params.cardType;
        }
    }
    updateStateVars(params: CardDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__bankName.purgeDependencyOnElmtId(rmElmtId);
        this.__fullCardNumber.purgeDependencyOnElmtId(rmElmtId);
        this.__cardType.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__bankName.aboutToBeDeleted();
        this.__fullCardNumber.aboutToBeDeleted();
        this.__cardType.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private params: RouterParams;
    private __bankName: ObservedPropertySimplePU<string>;
    get bankName() {
        return this.__bankName.get();
    }
    set bankName(newValue: string) {
        this.__bankName.set(newValue);
    }
    private __fullCardNumber: ObservedPropertySimplePU<string>;
    get fullCardNumber() {
        return this.__fullCardNumber.get();
    }
    set fullCardNumber(newValue: string) {
        this.__fullCardNumber.set(newValue);
    }
    private __cardType: ObservedPropertySimplePU<string>;
    get cardType() {
        return this.__cardType.get();
    }
    set cardType(newValue: string) {
        this.__cardType.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(21:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(23:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(50);
            // 顶部导航栏
            Row.backgroundColor('#ffffff');
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777260, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(24:9)", "entry");
            Image.width(24);
            Image.height(24);
            Image.margin({ left: 15, right: 15 });
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡号详情');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(32:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片详情
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(43:7)", "entry");
            // 卡片详情
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(44:9)", "entry");
            Stack.width('90%');
            Stack.height(250);
            Stack.backgroundColor('#1274ca');
            Stack.borderRadius(12);
            Stack.margin({ top: 20 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(45:11)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行图标和名称
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(47:13)", "entry");
            // 银行图标和名称
            Row.margin({ top: 20, bottom: 30 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(48:15)", "entry");
            Stack.margin({ right: 15 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(49:17)", "entry");
            Circle.width(40);
            Circle.height(40);
            Circle.fill(Color.White);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.getBankIcon());
            Image.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(53:17)", "entry");
            Image.width(30);
            Image.height(30);
        }, Image);
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.bankName);
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(59:15)", "entry");
            Text.fontSize(18);
            Text.fontColor('#ffffff');
        }, Text);
        Text.pop();
        // 银行图标和名称
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 完整卡号
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(66:13)", "entry");
            // 完整卡号
            Column.margin({ top: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('完整卡号');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(67:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#ffffff');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.fullCardNumber);
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(70:15)", "entry");
            Text.fontSize(18);
            Text.fontColor('#ffffff');
            Text.margin({ top: 10 });
        }, Text);
        Text.pop();
        // 完整卡号
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片类型
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(78:13)", "entry");
            // 卡片类型
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡片类型:');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(79:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#ffffff');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.cardType === 'debit' ? '借记卡' : '信用卡');
            Text.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(82:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#ffffff');
            Text.margin({ left: 10 });
        }, Text);
        Text.pop();
        // 卡片类型
        Row.pop();
        Column.pop();
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 复制卡号按钮
            Button.createWithLabel('复制卡号', { type: ButtonType.Capsule });
            Button.debugLine("entry/src/main/ets/pages/CardDetailPage.ets(99:9)", "entry");
            // 复制卡号按钮
            Button.width('90%');
            // 复制卡号按钮
            Button.height(50);
            // 复制卡号按钮
            Button.fontSize(16);
            // 复制卡号按钮
            Button.margin({ top: 20 });
            // 复制卡号按钮
            Button.onClick(() => {
                promptAction.showToast({ message: '卡号已复制', duration: 1000 });
            });
        }, Button);
        // 复制卡号按钮
        Button.pop();
        // 卡片详情
        Column.pop();
        Column.pop();
    }
    getBankIcon(): Resource {
        switch (this.bankName) {
            case '中国银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '建设银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '工商银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            default: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CardDetailPage";
    }
}
registerNamedRoute(() => new CardDetailPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/CardDetailPage", pageFullPath: "entry/src/main/ets/pages/CardDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
