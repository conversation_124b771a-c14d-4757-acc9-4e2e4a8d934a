{"version": "2.0", "ppid": 22720, "events": [{"head": {"id": "fe3f52cb-7f47-4848-a797-39318c0dbf07", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 10290875684800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6511cb-698b-42f4-9c70-736051f558cd", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11306619822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e275d04-0526-4e55-9031-2bba8c354021", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11306625698600, "endTime": 11306625794400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "4e43c52f-48f1-42fb-bef7-86534825e6a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e43c52f-48f1-42fb-bef7-86534825e6a0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11306625698600, "endTime": 11306625794400}, "additional": {"logType": "info", "children": [], "durationId": "6e275d04-0526-4e55-9031-2bba8c354021"}}, {"head": {"id": "111bf1e3-b093-4aec-a175-368eb9a428f1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308400836200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15518cbf-422a-4ce0-8811-d115eb6f716e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308402908700, "endTime": 11308402950900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "1cd7d424-8b61-40cd-8892-b4fd520e9f82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cd7d424-8b61-40cd-8892-b4fd520e9f82", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308402908700, "endTime": 11308402950900}, "additional": {"logType": "info", "children": [], "durationId": "15518cbf-422a-4ce0-8811-d115eb6f716e"}}, {"head": {"id": "1fc3a990-88a8-437b-9aca-fb3b10968d41", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308403106000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce030f24-2037-4c46-855b-0f64d8ceb92e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308404377900, "endTime": 11308404406500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b29b98c-05b4-483f-97ac-19dfdb04e562", "logId": "1de5de23-80ee-4f02-8d44-934378b69f11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1de5de23-80ee-4f02-8d44-934378b69f11", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308404377900, "endTime": 11308404406500}, "additional": {"logType": "info", "children": [], "durationId": "ce030f24-2037-4c46-855b-0f64d8ceb92e"}}, {"head": {"id": "4d1c9419-bc37-4152-a800-30961f9dd5a2", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308630143800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a938e36e-6f61-4b75-b846-70281baabc50", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308630597000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b1b9bf-cc7b-4442-bc92-5a70b49ec538", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308828042400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308836683300, "endTime": 11309053129800}, "additional": {"children": ["098432b6-92b4-4507-860b-219fb24c0316", "7cb96116-023a-4dce-b785-b86ea90ec7b9", "bbb53459-a3c9-4984-abe9-2d5106ad1996", "c5dc5082-36f3-4256-905d-c3ea14332f6e", "69f5bde0-ba3f-4f38-8510-1edf7eb939bc", "ee7a327d-4ff8-4cb9-b7a0-e2ef19d3caed", "2ee3ac4c-b54f-447b-8272-c5b403fb81aa"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "85a76e7e-323d-454d-b983-c0a920895e70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "098432b6-92b4-4507-860b-219fb24c0316", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308836685700, "endTime": 11308853096900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "63438e6f-b585-479a-888b-0848bc60db97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308853129600, "endTime": 11309051088200}, "additional": {"children": ["799458ff-4481-44bb-a590-59bef1b6b5a5", "b89b3960-ff56-4cbb-a963-61c7ec26ec78", "8c4b1ce0-23b6-444a-980e-8b38dc56ecd8", "05c2943e-276b-4339-8204-c47ef029cb1b", "1bcb1c1f-5c1a-430b-bc80-8b7949a82d34", "3f85d505-bb50-44c1-8e50-3b0c686489e1", "e72a23c5-7eae-4bd0-b772-6bca49c94d46", "a33a3ae7-ff73-486c-94d1-93dc7fbe016f", "ad6632db-2662-4576-90e5-984d463b5b8b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbb53459-a3c9-4984-abe9-2d5106ad1996", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309051138800, "endTime": 11309053090400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "5e930dce-3643-4bce-a5a7-abb737638d59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5dc5082-36f3-4256-905d-c3ea14332f6e", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309053099200, "endTime": 11309053122000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "d50768ce-5ff4-48a2-ae00-bc09a8616050"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69f5bde0-ba3f-4f38-8510-1edf7eb939bc", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308840704700, "endTime": 11308840743000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "8f458d22-e215-430b-bd2b-2048e680a052"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f458d22-e215-430b-bd2b-2048e680a052", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308840704700, "endTime": 11308840743000}, "additional": {"logType": "info", "children": [], "durationId": "69f5bde0-ba3f-4f38-8510-1edf7eb939bc", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "ee7a327d-4ff8-4cb9-b7a0-e2ef19d3caed", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308846022700, "endTime": 11308846039800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "7ae0602d-f219-4764-a0fb-fcf81264ae6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ae0602d-f219-4764-a0fb-fcf81264ae6d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308846022700, "endTime": 11308846039800}, "additional": {"logType": "info", "children": [], "durationId": "ee7a327d-4ff8-4cb9-b7a0-e2ef19d3caed", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "2705e298-3994-422c-a938-642fd6aca964", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308846183800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c44ec9-65e3-4f2b-a60b-5c0963b60c85", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308852922300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63438e6f-b585-479a-888b-0848bc60db97", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308836685700, "endTime": 11308853096900}, "additional": {"logType": "info", "children": [], "durationId": "098432b6-92b4-4507-860b-219fb24c0316", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "799458ff-4481-44bb-a590-59bef1b6b5a5", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308859610000, "endTime": 11308859623000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "438b5ccd-c372-4a3f-885a-0e57a06a0789"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b89b3960-ff56-4cbb-a963-61c7ec26ec78", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308859650400, "endTime": 11308866492100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "82b7ba4d-983b-4530-9210-f820e2d3d990"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c4b1ce0-23b6-444a-980e-8b38dc56ecd8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308866509000, "endTime": 11308976077700}, "additional": {"children": ["ffa7b1d6-5169-48ed-a893-8bfcb39b704d", "7151d177-b6a4-41bc-8e8f-7022c9e04099", "a674f4f1-242f-422e-87ee-bea16c48e614"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "a876f50f-2da2-4694-a8c1-55170e47b9dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05c2943e-276b-4339-8204-c47ef029cb1b", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308976095400, "endTime": 11309003162600}, "additional": {"children": ["c6a6f30e-e165-4e39-8d1a-308175583e7f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "a8c3c0c3-9830-41f1-ba83-8f535d33578d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bcb1c1f-5c1a-430b-bc80-8b7949a82d34", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309003171900, "endTime": 11309023066600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "a9d97ce5-0ff0-4b15-9be6-7d46fb8410be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f85d505-bb50-44c1-8e50-3b0c686489e1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309024222400, "endTime": 11309034827300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "f62ea75b-684f-4f9a-922c-d4e5497edfac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e72a23c5-7eae-4bd0-b772-6bca49c94d46", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309034861300, "endTime": 11309050856200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "b2766548-15a6-4482-8e10-a0d2baba8f3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a33a3ae7-ff73-486c-94d1-93dc7fbe016f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309050890900, "endTime": 11309051061500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "c5cee387-a589-4aab-88ec-2850de13274e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "438b5ccd-c372-4a3f-885a-0e57a06a0789", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308859610000, "endTime": 11308859623000}, "additional": {"logType": "info", "children": [], "durationId": "799458ff-4481-44bb-a590-59bef1b6b5a5", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "82b7ba4d-983b-4530-9210-f820e2d3d990", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308859650400, "endTime": 11308866492100}, "additional": {"logType": "info", "children": [], "durationId": "b89b3960-ff56-4cbb-a963-61c7ec26ec78", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "ffa7b1d6-5169-48ed-a893-8bfcb39b704d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308867351800, "endTime": 11308867369800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c4b1ce0-23b6-444a-980e-8b38dc56ecd8", "logId": "b01f0655-46a1-47b9-801f-70bfbe610c53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b01f0655-46a1-47b9-801f-70bfbe610c53", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308867351800, "endTime": 11308867369800}, "additional": {"logType": "info", "children": [], "durationId": "ffa7b1d6-5169-48ed-a893-8bfcb39b704d", "parent": "a876f50f-2da2-4694-a8c1-55170e47b9dd"}}, {"head": {"id": "7151d177-b6a4-41bc-8e8f-7022c9e04099", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308869894900, "endTime": 11308975065700}, "additional": {"children": ["6b5b19d9-07bf-400c-9440-a3ee8fed7770", "c06f7b99-ed4b-4180-b7ab-9a35e85218b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c4b1ce0-23b6-444a-980e-8b38dc56ecd8", "logId": "76d41faa-b833-45e3-97a7-cfa1899b9dfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b5b19d9-07bf-400c-9440-a3ee8fed7770", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308869896200, "endTime": 11308884358900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7151d177-b6a4-41bc-8e8f-7022c9e04099", "logId": "e60143e9-a6ae-417c-b690-216e666ba8d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c06f7b99-ed4b-4180-b7ab-9a35e85218b7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308884394700, "endTime": 11308975045500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7151d177-b6a4-41bc-8e8f-7022c9e04099", "logId": "ccfe6d01-595c-447b-a3aa-a1020e37787c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d892db0c-8fd4-439e-964b-7f783f1ee347", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308869905700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e403e5e7-e761-4db1-bd15-30ea3aebc66d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308884053200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e60143e9-a6ae-417c-b690-216e666ba8d6", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308869896200, "endTime": 11308884358900}, "additional": {"logType": "info", "children": [], "durationId": "6b5b19d9-07bf-400c-9440-a3ee8fed7770", "parent": "76d41faa-b833-45e3-97a7-cfa1899b9dfd"}}, {"head": {"id": "ed0a1cc0-7f3c-473b-8e4e-6ab137bce2a1", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308884433200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1711f4-4e8d-4c10-be43-94cee405becb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308895899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b0b505-e997-4dc4-96e2-90df12da86c7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308896217700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b850809e-78e5-4ab7-bec2-df3cf9bafa9d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308896859300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d30773-1d28-4d01-8ce4-f3fb41adfd3a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308897404700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ddc28be-05fb-4917-ace9-e345800757df", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308901361600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef946cb8-1417-40eb-b9ad-2d9fbc9cf6d7", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308907854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2056ae3-1a87-4787-af23-199dedbd4591", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308919357000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc507f1d-a03e-4c57-85f8-8ec715715f24", "name": "Sdk init in 41 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308949754500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d25b167-6b64-41b3-8141-30770f6e27c0", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308950936700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 47}, "markType": "other"}}, {"head": {"id": "7dcd41e3-2473-422d-ad04-f7e2182578a9", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308951022900}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 47}, "markType": "other"}}, {"head": {"id": "bc075fa4-d79a-45bb-813b-c569a4bd9f0a", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308974621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb00cea9-c54a-4d8a-8e55-7175fec2bbb6", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308974770000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34a385dd-9c34-4eaa-abbd-90b593b783f9", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308974876300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ea0c5b-fc8f-4102-9f34-bf38ee592899", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308974964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfe6d01-595c-447b-a3aa-a1020e37787c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308884394700, "endTime": 11308975045500}, "additional": {"logType": "info", "children": [], "durationId": "c06f7b99-ed4b-4180-b7ab-9a35e85218b7", "parent": "76d41faa-b833-45e3-97a7-cfa1899b9dfd"}}, {"head": {"id": "76d41faa-b833-45e3-97a7-cfa1899b9dfd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308869894900, "endTime": 11308975065700}, "additional": {"logType": "info", "children": ["e60143e9-a6ae-417c-b690-216e666ba8d6", "ccfe6d01-595c-447b-a3aa-a1020e37787c"], "durationId": "7151d177-b6a4-41bc-8e8f-7022c9e04099", "parent": "a876f50f-2da2-4694-a8c1-55170e47b9dd"}}, {"head": {"id": "a674f4f1-242f-422e-87ee-bea16c48e614", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308976043500, "endTime": 11308976059500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c4b1ce0-23b6-444a-980e-8b38dc56ecd8", "logId": "93a0a0ae-17de-444b-8b8c-bed8890d5af6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93a0a0ae-17de-444b-8b8c-bed8890d5af6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308976043500, "endTime": 11308976059500}, "additional": {"logType": "info", "children": [], "durationId": "a674f4f1-242f-422e-87ee-bea16c48e614", "parent": "a876f50f-2da2-4694-a8c1-55170e47b9dd"}}, {"head": {"id": "a876f50f-2da2-4694-a8c1-55170e47b9dd", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308866509000, "endTime": 11308976077700}, "additional": {"logType": "info", "children": ["b01f0655-46a1-47b9-801f-70bfbe610c53", "76d41faa-b833-45e3-97a7-cfa1899b9dfd", "93a0a0ae-17de-444b-8b8c-bed8890d5af6"], "durationId": "8c4b1ce0-23b6-444a-980e-8b38dc56ecd8", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "c6a6f30e-e165-4e39-8d1a-308175583e7f", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308976815800, "endTime": 11309003148900}, "additional": {"children": ["62492ec2-0056-43bb-9700-60e34ca61eab", "46102d32-3f20-48ad-b486-ce20498e9d04", "2f5f1e55-08d5-498b-89ca-829146ea455f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "05c2943e-276b-4339-8204-c47ef029cb1b", "logId": "17b38387-8cbb-4ecd-b63e-f84f259c7a4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62492ec2-0056-43bb-9700-60e34ca61eab", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308980271200, "endTime": 11308980286900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6a6f30e-e165-4e39-8d1a-308175583e7f", "logId": "fbe84ff8-b95a-44c8-943d-16f1ebbb8f03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbe84ff8-b95a-44c8-943d-16f1ebbb8f03", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308980271200, "endTime": 11308980286900}, "additional": {"logType": "info", "children": [], "durationId": "62492ec2-0056-43bb-9700-60e34ca61eab", "parent": "17b38387-8cbb-4ecd-b63e-f84f259c7a4c"}}, {"head": {"id": "46102d32-3f20-48ad-b486-ce20498e9d04", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308982256600, "endTime": 11309001563400}, "additional": {"children": ["924acb43-1f08-4adf-8db4-883f2b3df330", "e88d9bbc-9a2d-4936-8680-4158b7626939"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6a6f30e-e165-4e39-8d1a-308175583e7f", "logId": "d3db6893-1b58-4d54-9166-8ae0241dd336"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "924acb43-1f08-4adf-8db4-883f2b3df330", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308982257700, "endTime": 11308987263600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46102d32-3f20-48ad-b486-ce20498e9d04", "logId": "997974fc-7c80-4ed9-9801-fd2edf8dc71b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e88d9bbc-9a2d-4936-8680-4158b7626939", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308987285700, "endTime": 11309001549400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46102d32-3f20-48ad-b486-ce20498e9d04", "logId": "352e832b-fe9a-44c3-8cdd-d76fa7061d2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c451b948-2362-4ff7-8f07-83d1c8091f31", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308982264800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bf33cd-cd23-43a2-93a1-2bb211514687", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308987107000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "997974fc-7c80-4ed9-9801-fd2edf8dc71b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308982257700, "endTime": 11308987263600}, "additional": {"logType": "info", "children": [], "durationId": "924acb43-1f08-4adf-8db4-883f2b3df330", "parent": "d3db6893-1b58-4d54-9166-8ae0241dd336"}}, {"head": {"id": "faac966a-8d5f-4123-8e1d-75d28c4d8554", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308987305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824cb8ed-c3d3-4a44-b6ef-61abe1578f87", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308995692300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2bb2c5b-a197-4d83-8a62-5efabe02c2fd", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308996076300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f09277d-6a17-490d-b41a-85db60da948d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308996481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317b91bd-2c03-4ec5-ba3f-90dc958e7364", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308996769300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5285963-dc53-4b31-a963-be9410b0ea1a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308996935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "436335aa-9d06-4577-90f9-fa2c9d79c47c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308997027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cc7b1b3-f3b6-43da-a760-fc00ac792b73", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308997105300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20e25c8-44f2-4a7f-bd51-c4a201a258d2", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309001207800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacb504c-babd-466a-9d22-894751bc2ea5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309001367600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01d7c98d-7e0f-4381-ac66-cb8e65c68c25", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309001442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0727142b-4f8e-48aa-b3e6-ce213fe200a6", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309001496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352e832b-fe9a-44c3-8cdd-d76fa7061d2e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308987285700, "endTime": 11309001549400}, "additional": {"logType": "info", "children": [], "durationId": "e88d9bbc-9a2d-4936-8680-4158b7626939", "parent": "d3db6893-1b58-4d54-9166-8ae0241dd336"}}, {"head": {"id": "d3db6893-1b58-4d54-9166-8ae0241dd336", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308982256600, "endTime": 11309001563400}, "additional": {"logType": "info", "children": ["997974fc-7c80-4ed9-9801-fd2edf8dc71b", "352e832b-fe9a-44c3-8cdd-d76fa7061d2e"], "durationId": "46102d32-3f20-48ad-b486-ce20498e9d04", "parent": "17b38387-8cbb-4ecd-b63e-f84f259c7a4c"}}, {"head": {"id": "2f5f1e55-08d5-498b-89ca-829146ea455f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309003118400, "endTime": 11309003131400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6a6f30e-e165-4e39-8d1a-308175583e7f", "logId": "7860d6f8-394c-42cb-a6a7-95f927a55684"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7860d6f8-394c-42cb-a6a7-95f927a55684", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309003118400, "endTime": 11309003131400}, "additional": {"logType": "info", "children": [], "durationId": "2f5f1e55-08d5-498b-89ca-829146ea455f", "parent": "17b38387-8cbb-4ecd-b63e-f84f259c7a4c"}}, {"head": {"id": "17b38387-8cbb-4ecd-b63e-f84f259c7a4c", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308976815800, "endTime": 11309003148900}, "additional": {"logType": "info", "children": ["fbe84ff8-b95a-44c8-943d-16f1ebbb8f03", "d3db6893-1b58-4d54-9166-8ae0241dd336", "7860d6f8-394c-42cb-a6a7-95f927a55684"], "durationId": "c6a6f30e-e165-4e39-8d1a-308175583e7f", "parent": "a8c3c0c3-9830-41f1-ba83-8f535d33578d"}}, {"head": {"id": "a8c3c0c3-9830-41f1-ba83-8f535d33578d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308976095400, "endTime": 11309003162600}, "additional": {"logType": "info", "children": ["17b38387-8cbb-4ecd-b63e-f84f259c7a4c"], "durationId": "05c2943e-276b-4339-8204-c47ef029cb1b", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "56422965-2212-4ccf-b762-04892b1e4ad8", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309022242000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baafe898-b11b-4953-94b5-586b95fbd60b", "name": "hvigorfile, resolve hvigorfile dependencies in 20 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309022956100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d97ce5-0ff0-4b15-9be6-7d46fb8410be", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309003171900, "endTime": 11309023066600}, "additional": {"logType": "info", "children": [], "durationId": "1bcb1c1f-5c1a-430b-bc80-8b7949a82d34", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "ad6632db-2662-4576-90e5-984d463b5b8b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309023933100, "endTime": 11309024204100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "logId": "76c4a1bd-fe9b-4d78-afed-32d8a75eeb3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "281b12f2-0d25-4901-9831-cf862d3aa0af", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309023960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c4a1bd-fe9b-4d78-afed-32d8a75eeb3a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309023933100, "endTime": 11309024204100}, "additional": {"logType": "info", "children": [], "durationId": "ad6632db-2662-4576-90e5-984d463b5b8b", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "366a8d6f-6c4d-45f5-97c9-774807f1c601", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309025684600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afaede0f-6729-44e9-a7d0-fd1eb4ed4e4f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309033542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62ea75b-684f-4f9a-922c-d4e5497edfac", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309024222400, "endTime": 11309034827300}, "additional": {"logType": "info", "children": [], "durationId": "3f85d505-bb50-44c1-8e50-3b0c686489e1", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "0fbb433d-7fc5-4c42-82f1-380b398ef8e3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309034893900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8035519e-f149-4a97-8fc5-619c0052eb21", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309041166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b4b5a3e-df02-4d78-a1be-821941f0bd36", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309041307400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8640f8-dc70-4c0e-9d6b-e0edd5cc1d8e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309041945200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9095e6-f5f5-4061-a389-8e09a8b5bc18", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309047624800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e793a5-a817-4280-b389-29019349fb2d", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309047775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2766548-15a6-4482-8e10-a0d2baba8f3b", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309034861300, "endTime": 11309050856200}, "additional": {"logType": "info", "children": [], "durationId": "e72a23c5-7eae-4bd0-b772-6bca49c94d46", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "1d6431e2-bd45-4704-97fa-cab36d018cd9", "name": "Configuration phase cost:192 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309050933700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cee387-a589-4aab-88ec-2850de13274e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309050890900, "endTime": 11309051061500}, "additional": {"logType": "info", "children": [], "durationId": "a33a3ae7-ff73-486c-94d1-93dc7fbe016f", "parent": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff"}}, {"head": {"id": "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308853129600, "endTime": 11309051088200}, "additional": {"logType": "info", "children": ["438b5ccd-c372-4a3f-885a-0e57a06a0789", "82b7ba4d-983b-4530-9210-f820e2d3d990", "a876f50f-2da2-4694-a8c1-55170e47b9dd", "a8c3c0c3-9830-41f1-ba83-8f535d33578d", "a9d97ce5-0ff0-4b15-9be6-7d46fb8410be", "f62ea75b-684f-4f9a-922c-d4e5497edfac", "b2766548-15a6-4482-8e10-a0d2baba8f3b", "c5cee387-a589-4aab-88ec-2850de13274e", "76c4a1bd-fe9b-4d78-afed-32d8a75eeb3a"], "durationId": "7cb96116-023a-4dce-b785-b86ea90ec7b9", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "2ee3ac4c-b54f-447b-8272-c5b403fb81aa", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309053044400, "endTime": 11309053070400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9", "logId": "98fa8c7a-81c8-4f30-b1aa-d37519408c78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98fa8c7a-81c8-4f30-b1aa-d37519408c78", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309053044400, "endTime": 11309053070400}, "additional": {"logType": "info", "children": [], "durationId": "2ee3ac4c-b54f-447b-8272-c5b403fb81aa", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "5e930dce-3643-4bce-a5a7-abb737638d59", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309051138800, "endTime": 11309053090400}, "additional": {"logType": "info", "children": [], "durationId": "bbb53459-a3c9-4984-abe9-2d5106ad1996", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "d50768ce-5ff4-48a2-ae00-bc09a8616050", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309053099200, "endTime": 11309053122000}, "additional": {"logType": "info", "children": [], "durationId": "c5dc5082-36f3-4256-905d-c3ea14332f6e", "parent": "85a76e7e-323d-454d-b983-c0a920895e70"}}, {"head": {"id": "85a76e7e-323d-454d-b983-c0a920895e70", "name": "init", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308836683300, "endTime": 11309053129800}, "additional": {"logType": "info", "children": ["63438e6f-b585-479a-888b-0848bc60db97", "def0e1ff-c4d0-4a7a-b18c-18d02d5950ff", "5e930dce-3643-4bce-a5a7-abb737638d59", "d50768ce-5ff4-48a2-ae00-bc09a8616050", "8f458d22-e215-430b-bd2b-2048e680a052", "7ae0602d-f219-4764-a0fb-fcf81264ae6d", "98fa8c7a-81c8-4f30-b1aa-d37519408c78"], "durationId": "7b8694c4-5f2f-47d8-97de-cce2a4adecf9"}}, {"head": {"id": "c2eab0be-eed1-4e48-a40f-01ea9cbbdd1f", "name": "Configuration task cost before running: 221 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309053388700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c0b1092-e20b-4439-8061-49ef584367de", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309058765600, "endTime": 11309068787700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ee89ab9a-c795-4c76-afbc-eee24c51a1fe", "logId": "6c2ea9fe-1bdc-4099-bee9-6d27a83eecab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee89ab9a-c795-4c76-afbc-eee24c51a1fe", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309055011100}, "additional": {"logType": "detail", "children": [], "durationId": "8c0b1092-e20b-4439-8061-49ef584367de"}}, {"head": {"id": "08deda27-3b13-4b13-a148-2e6f3db0ca96", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309055555800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539ceb25-b5fc-4afe-845b-3207ae6df221", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309055660400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e909808-bf8d-4928-b1e2-079dea9a44e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309055725200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d38e38-de8f-4c0f-b0bb-84c252198839", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309058786400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4580b2f-a0e0-422a-b641-60e7188f6866", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309068379700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e65e20-6925-47b0-b369-e2234476a1c5", "name": "entry : default@PreBuild cost memory 0.29140472412109375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309068617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c2ea9fe-1bdc-4099-bee9-6d27a83eecab", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309058765600, "endTime": 11309068787700}, "additional": {"logType": "info", "children": [], "durationId": "8c0b1092-e20b-4439-8061-49ef584367de"}}, {"head": {"id": "c300ae09-3b8d-4f03-8b80-5d405c71f5c1", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309074155600, "endTime": 11309077732400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "51a5cf6d-384d-4ef5-b10d-ecd780b0b168", "logId": "63d5a2a0-fbfa-4e9c-9b98-d7c641935c73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51a5cf6d-384d-4ef5-b10d-ecd780b0b168", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309072748400}, "additional": {"logType": "detail", "children": [], "durationId": "c300ae09-3b8d-4f03-8b80-5d405c71f5c1"}}, {"head": {"id": "26748d50-fcbb-43d7-be7e-c97d7655fcbe", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309073258700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5852427-0c2a-4b27-b526-a1601ee985e1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309073373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8605ef49-92e1-4448-a722-527854b1cc64", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309073435200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c71e483a-cc9f-41ce-aefb-7e735c6764d5", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309074168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66039baa-6373-43ea-8eb2-e9fbff4babf5", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309077397300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51b61f9-920c-489c-aceb-599c5e66904a", "name": "entry : default@MergeProfile cost memory 0.13375091552734375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309077599300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d5a2a0-fbfa-4e9c-9b98-d7c641935c73", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309074155600, "endTime": 11309077732400}, "additional": {"logType": "info", "children": [], "durationId": "c300ae09-3b8d-4f03-8b80-5d405c71f5c1"}}, {"head": {"id": "1b29b53d-33b6-4a12-9ac1-e1ae6870dc88", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309081775500, "endTime": 11309084628100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "12a5bd44-8f23-4a05-834a-663f70ef0626", "logId": "52338a0a-85ad-4b12-b341-db547233e265"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12a5bd44-8f23-4a05-834a-663f70ef0626", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309079877300}, "additional": {"logType": "detail", "children": [], "durationId": "1b29b53d-33b6-4a12-9ac1-e1ae6870dc88"}}, {"head": {"id": "52628129-695c-419a-b720-98522ed5f36c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309080406800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c99b763a-d15f-4b39-8215-8b269943e8b3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309080526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44340ee1-68d4-4339-9096-9178b91c3f8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309080629200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66da7eaa-a073-4595-86f9-d13989345a75", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309081789500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d47ba53-6c6e-45f4-af78-de4178657863", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309082797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd40e7a-0e04-473b-85da-3496b947389b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309084278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b298868-6218-45ea-8029-29c93bd9a6f0", "name": "entry : default@CreateBuildProfile cost memory 0.1011199951171875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309084480800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52338a0a-85ad-4b12-b341-db547233e265", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309081775500, "endTime": 11309084628100}, "additional": {"logType": "info", "children": [], "durationId": "1b29b53d-33b6-4a12-9ac1-e1ae6870dc88"}}, {"head": {"id": "6824ad9b-2f8a-4bae-bbd9-fca54a9a9d8e", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309089604300, "endTime": 11309090186700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "420cd59b-fe2c-45ea-888c-52a892b8303d", "logId": "a815aa6d-3bef-4921-98ce-e7a6a9454a63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "420cd59b-fe2c-45ea-888c-52a892b8303d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309087958400}, "additional": {"logType": "detail", "children": [], "durationId": "6824ad9b-2f8a-4bae-bbd9-fca54a9a9d8e"}}, {"head": {"id": "caa9bc8a-abeb-45fb-908c-bf83a9391c20", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309088530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52e8348-7ff6-4fce-bc6a-1f66f69d5714", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309088649400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fff58ff-875a-4d61-823b-43a2fe31911a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309088715300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e7b81e-9ec4-486e-bba8-2b9a7bdedd61", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309089616100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7982ca7-df4a-48d5-8b28-2dbe38a4c91e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309089770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df9d9a6f-cdca-4a26-b0e6-6842005c2999", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309089844300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74866efe-48eb-49cc-96dc-4b3b4dca110f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309089907400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7472f7-bd48-41f5-a5d9-65a3ce84a4dc", "name": "entry : default@PreCheckSyscap cost memory 0.05078887939453125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309090000500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f024743d-2eaa-484e-94fe-8aaddc67b63e", "name": "runTaskFromQueue task cost before running: 258 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309090108300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a815aa6d-3bef-4921-98ce-e7a6a9454a63", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309089604300, "endTime": 11309090186700, "totalTime": 477100}, "additional": {"logType": "info", "children": [], "durationId": "6824ad9b-2f8a-4bae-bbd9-fca54a9a9d8e"}}, {"head": {"id": "b8d4bcef-e2ac-442d-a33e-558dd81aa46b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309101913100, "endTime": 11309103735300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb9bbdea-0939-4e89-ae48-ddf1b891da97", "logId": "0779249f-019e-49fc-8aa6-34fa982a3acc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb9bbdea-0939-4e89-ae48-ddf1b891da97", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309091887000}, "additional": {"logType": "detail", "children": [], "durationId": "b8d4bcef-e2ac-442d-a33e-558dd81aa46b"}}, {"head": {"id": "22329a4f-b8d1-4d61-9727-ac5b13fbb123", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309092418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec1edcd-e73f-4218-88a0-4f719a608263", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309092522900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd9c99f-07e6-4a56-b013-29a23bfdb3fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309092588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95b9b217-74fb-460d-88be-a142c28e9824", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309101939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6953e48-12bc-4443-8bdf-de9e280bc9d1", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309102529900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f951583f-8c64-4947-8252-82fbc20c4bb5", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309103474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83c03e44-c4bb-44ee-9637-82a9eea3d6db", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0704803466796875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309103641700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0779249f-019e-49fc-8aa6-34fa982a3acc", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309101913100, "endTime": 11309103735300}, "additional": {"logType": "info", "children": [], "durationId": "b8d4bcef-e2ac-442d-a33e-558dd81aa46b"}}, {"head": {"id": "5ebf1f48-be1c-4d8e-bd0b-2e2193cfd39d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309107442200, "endTime": 11309108889600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "61223379-d5b8-47d0-ace8-5838eb946302", "logId": "d60b92ac-61c7-4848-99f8-224cae7bd48a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61223379-d5b8-47d0-ace8-5838eb946302", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309105513600}, "additional": {"logType": "detail", "children": [], "durationId": "5ebf1f48-be1c-4d8e-bd0b-2e2193cfd39d"}}, {"head": {"id": "0838c662-7723-4ab2-8fc8-d410508c943c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309106014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37056db4-608a-4e54-bf6b-1f5ffa9638bc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309106102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f3d7ca-8780-4330-8e2f-3b81d0842d74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309106161100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000ecb32-de94-4840-8442-b35464d4d004", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309107460600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1710f5ee-d521-4c58-8ce2-2a88b8ea6d14", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309108672300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd223368-4703-425e-afff-fe108c9ea3a4", "name": "entry : default@ProcessProfile cost memory 0.0591888427734375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309108806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60b92ac-61c7-4848-99f8-224cae7bd48a", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309107442200, "endTime": 11309108889600}, "additional": {"logType": "info", "children": [], "durationId": "5ebf1f48-be1c-4d8e-bd0b-2e2193cfd39d"}}, {"head": {"id": "96de6056-1941-43d6-b2d2-a5a718fddfba", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309113487200, "endTime": 11309120463200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a962aede-c7dc-4fd5-b4f5-09ff12827b7c", "logId": "f76083d8-cb96-4e53-838e-098bb8ffdfb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a962aede-c7dc-4fd5-b4f5-09ff12827b7c", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309110758400}, "additional": {"logType": "detail", "children": [], "durationId": "96de6056-1941-43d6-b2d2-a5a718fddfba"}}, {"head": {"id": "cbe82696-f3ef-451b-b5c6-3306eca96a20", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309111482600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83a1175a-e8d1-4fe3-8c8f-12b3a13ca6ec", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309111614100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "420774bd-f61b-4357-9eaf-e904f4ced57d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309111684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b87d479-21b9-4056-9648-d2d1028d978a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309113500500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d8f45e-88cd-46e2-805e-ab071ce5ff60", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309120137500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2e19c20-118b-4fba-8a88-fb3a56f6b8ef", "name": "entry : default@ProcessRouterMap cost memory 0.21848297119140625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309120353600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76083d8-cb96-4e53-838e-098bb8ffdfb8", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309113487200, "endTime": 11309120463200}, "additional": {"logType": "info", "children": [], "durationId": "96de6056-1941-43d6-b2d2-a5a718fddfba"}}, {"head": {"id": "8dbfd1d0-8550-4b59-96a6-05f94dc5433f", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309131158200, "endTime": 11309134613000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "92ea9413-0c47-4498-8b9f-69342666b2c4", "logId": "0f5a6c58-0cc0-415d-a9cb-946a66c02cfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92ea9413-0c47-4498-8b9f-69342666b2c4", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309125624600}, "additional": {"logType": "detail", "children": [], "durationId": "8dbfd1d0-8550-4b59-96a6-05f94dc5433f"}}, {"head": {"id": "4fe9fefe-5acf-410a-a967-3abb83c62e9d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309126290800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a06f97-e739-4d67-b026-620230618598", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309126486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee572e4-f268-44ff-b57e-7fdd8788abe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309126580400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c009c4d-21e5-4d64-9c21-e8af23fe9828", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309128348500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b1b306-70b2-423b-9dcf-183911575532", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309132487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37525da3-baee-4ef8-b29c-94841e215b18", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309132678400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe6c274-0871-46fe-890c-d4fa8abef52e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309132751500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b679a87f-f914-4552-9b3c-745b89875e54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309132805200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70eda015-0c71-4d14-b5a9-fb96e65dbce6", "name": "entry : default@PreviewProcessResource cost memory 0.08972930908203125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309132905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "556fe1ed-652c-4469-a64d-4a1f76f2538e", "name": "runTaskFromQueue task cost before running: 302 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309134482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f5a6c58-0cc0-415d-a9cb-946a66c02cfb", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309131158200, "endTime": 11309134613000, "totalTime": 1948800}, "additional": {"logType": "info", "children": [], "durationId": "8dbfd1d0-8550-4b59-96a6-05f94dc5433f"}}, {"head": {"id": "54876135-69b6-4cdf-928f-05b85432464b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309143823100, "endTime": 11309169782900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5ebb2e68-1818-497d-b7ad-fc30f88dc0c8", "logId": "1488d2c4-3ddd-4b00-8ad5-eddaf45d166b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ebb2e68-1818-497d-b7ad-fc30f88dc0c8", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309137820300}, "additional": {"logType": "detail", "children": [], "durationId": "54876135-69b6-4cdf-928f-05b85432464b"}}, {"head": {"id": "b931cbd9-b2bd-4955-afe3-58e6991b3c31", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309138328500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f56dfc46-8a03-48e1-bf15-66e77fcb64da", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309138499500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3be5c44-7af3-461c-90ac-1d3565b8ce12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309138591200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66634045-d9ce-43f1-ad46-be436ce64d8b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309143867100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a397c4-6204-4155-b265-dcb727251c82", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309169098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ba97411-8cde-4d95-8eba-57fe3a67e89a", "name": "entry : default@GenerateLoaderJson cost memory 0.**********59375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309169475800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1488d2c4-3ddd-4b00-8ad5-eddaf45d166b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309143823100, "endTime": 11309169782900}, "additional": {"logType": "info", "children": [], "durationId": "54876135-69b6-4cdf-928f-05b85432464b"}}, {"head": {"id": "d8354806-3b6c-45f6-9008-f783917cd44c", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309189855200, "endTime": 11309236686500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ec28cf08-6cc0-4999-90e0-f32eb6c12d49", "logId": "e6aaaaa1-9244-42bb-a577-333c4e1e5da4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec28cf08-6cc0-4999-90e0-f32eb6c12d49", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309182852300}, "additional": {"logType": "detail", "children": [], "durationId": "d8354806-3b6c-45f6-9008-f783917cd44c"}}, {"head": {"id": "381b68cb-2317-4e32-89d9-f4eab96f6100", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309183582200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa14d6e-68dc-45af-bc37-19c491d75b19", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309183725400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8764de0b-1b70-4ec9-a875-f61479afe9dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309183814800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6168383e-7d2c-4f1d-933d-9d5da761b9d6", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309185342400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34af3c43-5c02-4d72-b6e8-3144bd27dfe2", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309189910700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f583f6-63bf-4c06-ba2a-abb1ed63479e", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 46 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309236412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44684e8a-9070-4dd3-9d24-3cb6b86c86a0", "name": "entry : default@PreviewCompileResource cost memory -0.217254638671875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309236576900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6aaaaa1-9244-42bb-a577-333c4e1e5da4", "name": "UP-TO-DATE :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309189855200, "endTime": 11309236686500}, "additional": {"logType": "info", "children": [], "durationId": "d8354806-3b6c-45f6-9008-f783917cd44c"}}, {"head": {"id": "a4ccc3fc-18b3-4642-b0b4-c6fd1e6fc597", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239799600, "endTime": 11309240256600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "3023d5e8-98b5-4fae-a4ef-a187e06bfa68", "logId": "a567f7e6-0f91-45bf-971d-362cf2ada0fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3023d5e8-98b5-4fae-a4ef-a187e06bfa68", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239080000}, "additional": {"logType": "detail", "children": [], "durationId": "a4ccc3fc-18b3-4642-b0b4-c6fd1e6fc597"}}, {"head": {"id": "a04c514a-e8c1-4c94-ad6f-280513e1c4f8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00c259c-f539-458d-becd-64c74a41cbd1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239661600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d37f96-0d5e-4d58-be96-606b3be05b4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b9c360a-9df9-4981-afed-dca6e0360c3b", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff268c2-2ef6-4cd4-b4b7-c516c04b4c12", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239903400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5bc4a4b-a743-4601-bf4e-07104fa9430b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4c195c-2df2-43c7-a693-dd49784daca4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309240013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271c63a6-eb50-47c1-991b-e0a82bb4baff", "name": "entry : default@PreviewHookCompileResource cost memory 0.05159759521484375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309240095200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ccf48e-aca5-4f91-85d3-ca2d95bf7dee", "name": "runTaskFromQueue task cost before running: 408 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309240197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a567f7e6-0f91-45bf-971d-362cf2ada0fd", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309239799600, "endTime": 11309240256600, "totalTime": 376900}, "additional": {"logType": "info", "children": [], "durationId": "a4ccc3fc-18b3-4642-b0b4-c6fd1e6fc597"}}, {"head": {"id": "daa15c45-f6b4-4054-8261-a038a490393a", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309242911700, "endTime": 11309245109200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "7ecb6245-11d8-4cc5-ae4f-2a74400cfc1b", "logId": "adc21ff7-b044-45ed-bfec-5a980e7b56e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ecb6245-11d8-4cc5-ae4f-2a74400cfc1b", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309241735200}, "additional": {"logType": "detail", "children": [], "durationId": "daa15c45-f6b4-4054-8261-a038a490393a"}}, {"head": {"id": "470d25fb-f38a-4f51-9f07-11e99aecb9d4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309242205800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b37923-db69-4a68-80da-14be7c79b41e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309242288500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e32aa70e-ccee-4638-98c3-7d9933f9518a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309242343300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa1a829-5454-4133-b03f-d8050e789c82", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309242926400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01605818-8d39-45cf-a8c3-6e6e496af15b", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309244945400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73db0ee-5d41-4abb-aacf-b3bc2ed582a3", "name": "entry : default@CopyPreviewProfile cost memory 0.09710693359375", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309245042000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc21ff7-b044-45ed-bfec-5a980e7b56e2", "name": "UP-TO-DATE :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309242911700, "endTime": 11309245109200}, "additional": {"logType": "info", "children": [], "durationId": "daa15c45-f6b4-4054-8261-a038a490393a"}}, {"head": {"id": "ee40d7a0-c745-43dc-b34a-d5e9b814977a", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248131200, "endTime": 11309248839600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "1e86c32f-a50f-4f50-bb28-a7093b8d9fbd", "logId": "5dd9f1dc-0069-44e8-8c72-0a5b40168603"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e86c32f-a50f-4f50-bb28-a7093b8d9fbd", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309246741200}, "additional": {"logType": "detail", "children": [], "durationId": "ee40d7a0-c745-43dc-b34a-d5e9b814977a"}}, {"head": {"id": "49cb39bb-2755-4330-90f3-6798bc560d8b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309247220500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8464cce-0252-4024-8166-515030b50090", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309247309600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acdde1d9-c840-4c36-b040-e6720a16fb7f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309247367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa8db3f5-241a-429d-87be-752a253ba42e", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248147200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8155ee9-58f4-40e9-a5ad-d75207e8efdb", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248380200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8207fa4e-1ece-4d26-8646-713ae9c5197c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248460100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e27902d-6588-40e2-be94-274c18a389fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248515300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e973f8b-0b6e-4410-8ac0-115aad444f87", "name": "entry : default@ReplacePreviewerPage cost memory 0.05173492431640625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248622700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b91be9-b92f-4fa4-872c-94dd268231d2", "name": "runTaskFromQueue task cost before running: 417 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd9f1dc-0069-44e8-8c72-0a5b40168603", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309248131200, "endTime": 11309248839600, "totalTime": 556900}, "additional": {"logType": "info", "children": [], "durationId": "ee40d7a0-c745-43dc-b34a-d5e9b814977a"}}, {"head": {"id": "8177d898-bbd9-43c1-8fa2-569b1e0f324b", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309250617600, "endTime": 11309250959400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f31ff1a2-fe5f-4c79-ba8d-878d391de349", "logId": "d397ba76-d3f4-413d-9c2d-b3f4323de6b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f31ff1a2-fe5f-4c79-ba8d-878d391de349", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309250528500}, "additional": {"logType": "detail", "children": [], "durationId": "8177d898-bbd9-43c1-8fa2-569b1e0f324b"}}, {"head": {"id": "db8bacb6-4f3a-4065-b8f0-70c9d630e88b", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309250629200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435db788-1d29-4cbd-87fb-8093cc2591fc", "name": "entry : buildPreviewerResource cost memory 0.01181793212890625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309250772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa86e198-8471-4ec3-af47-50cbe48466cb", "name": "runTaskFromQueue task cost before running: 419 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309250883200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d397ba76-d3f4-413d-9c2d-b3f4323de6b1", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309250617600, "endTime": 11309250959400, "totalTime": 233600}, "additional": {"logType": "info", "children": [], "durationId": "8177d898-bbd9-43c1-8fa2-569b1e0f324b"}}, {"head": {"id": "e4018e5e-b6c1-46d3-9b9a-a0f48ee2f88a", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309254020700, "endTime": 11309256536600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d022a019-b257-47ea-8f3c-ed3ccef2bdca", "logId": "79e402a2-7a5d-4f02-8dc3-73d5071e6907"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d022a019-b257-47ea-8f3c-ed3ccef2bdca", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309252619400}, "additional": {"logType": "detail", "children": [], "durationId": "e4018e5e-b6c1-46d3-9b9a-a0f48ee2f88a"}}, {"head": {"id": "46bab1a9-69d0-421c-8152-a2f2f19c7bd7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309253125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "725824f2-9704-4822-b2f3-54bbbe066fd2", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309253223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0958e99-858e-4718-bed4-530631325266", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309253280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c10b842-76ee-403a-915f-1d234e780510", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309254031800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116c4fdb-cbe4-4aea-bca7-79472c5063aa", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309256357800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7dc60b7-1ab1-4419-9258-3cc88caf1c9b", "name": "entry : default@PreviewUpdateAssets cost memory 0.10407257080078125", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309256466800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e402a2-7a5d-4f02-8dc3-73d5071e6907", "name": "UP-TO-DATE :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309254020700, "endTime": 11309256536600}, "additional": {"logType": "info", "children": [], "durationId": "e4018e5e-b6c1-46d3-9b9a-a0f48ee2f88a"}}, {"head": {"id": "628c05c2-f17b-4027-9af3-3b904baff0a9", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309265581100, "endTime": 11321200435800}, "additional": {"children": ["b1639ae3-c806-4697-9008-c8f7bbc95276"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets' has been changed."], "detailId": "bb0ac8b4-8d16-41f2-8271-b52e2353040d", "logId": "fa7325b1-b196-4ad4-853a-6c4b54da141f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb0ac8b4-8d16-41f2-8271-b52e2353040d", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309259531200}, "additional": {"logType": "detail", "children": [], "durationId": "628c05c2-f17b-4027-9af3-3b904baff0a9"}}, {"head": {"id": "d0c7a365-7ae8-49c2-80a5-a4d55dd24e3d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309260113100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a7f4c1b-aa4e-4ac0-b602-a888f4f9f63a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309260217800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a6b898-6566-48bb-8a52-3409ce6ab4f7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309260276300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fcfa0fc-949b-4efe-8370-7588d9eda13d", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309265598900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b7d83d-a0dd-4d27-968b-61f7a304f0d1", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309303449300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74d12ad-6b5b-4f9b-8e5b-de92647edbe5", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309303704200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1639ae3-c806-4697-9008-c8f7bbc95276", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11309321966400, "endTime": 11321196514300}, "additional": {"children": ["ad1e8c1f-464f-4a7b-b27d-365ab28218dc", "c4eaac63-a19e-41ba-984e-7a144172a477", "563730c0-a547-435c-9f04-083838da32c0", "bdbf04eb-f788-49d3-9419-688cb1d91514", "3607bcbd-c6d4-4440-9a73-1844068c5dd7", "197e2841-ca5c-4b91-9fa2-f564f8e013c3", "07f61ad8-f933-4489-9725-8a095693f86d", "ffdd2e3d-8637-49c6-b2b9-0084012fd460", "c2c1a01d-02b4-44af-a859-a0f754411627", "23f71b89-7806-4267-90cf-3fa6b64637dc", "15d4f5c5-2532-4ac9-ba50-410f55bfbfca", "1071e825-9390-4de8-9491-ae56e6ace6a6", "2aaa3086-d03d-48f8-b99a-4e79211a452b", "2518cf8f-ed38-4966-8d5e-b5b4d35a20b2", "227d850f-3106-4bbb-82e1-6a53e7f698fb"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "628c05c2-f17b-4027-9af3-3b904baff0a9", "logId": "20b9f32e-964c-4946-83d9-6be45476ce0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44f0d551-4c24-4c31-bcc4-790c67870cbe", "name": "entry : default@PreviewArkTS cost memory 0.41294097900390625", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309324292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337f458d-6bf7-4d3a-b56d-a38522dceffb", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11313391256500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1e8c1f-464f-4a7b-b27d-365ab28218dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11313392290600, "endTime": 11313392311400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "51e91218-8a66-4643-a5a7-7667fbe90868"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51e91218-8a66-4643-a5a7-7667fbe90868", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11313392290600, "endTime": 11313392311400}, "additional": {"logType": "info", "children": [], "durationId": "ad1e8c1f-464f-4a7b-b27d-365ab28218dc", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "84be14d8-23fe-484a-a733-df0e44c2019d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318028289400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4eaac63-a19e-41ba-984e-7a144172a477", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318029435000, "endTime": 11318029456700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "447ca8b6-b8b2-444a-b082-add4b70b72a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "447ca8b6-b8b2-444a-b082-add4b70b72a6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318029435000, "endTime": 11318029456700}, "additional": {"logType": "info", "children": [], "durationId": "c4eaac63-a19e-41ba-984e-7a144172a477", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "55030a69-13bc-4a06-84ee-df2f4a45a9eb", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318029533000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "563730c0-a547-435c-9f04-083838da32c0", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318030339800, "endTime": 11318030368200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "b643b53b-a418-4abe-9c9e-24778c38d096"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b643b53b-a418-4abe-9c9e-24778c38d096", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318030339800, "endTime": 11318030368200}, "additional": {"logType": "info", "children": [], "durationId": "563730c0-a547-435c-9f04-083838da32c0", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "a735716e-3dd5-426e-aaed-8494177b316d", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318347788400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdbf04eb-f788-49d3-9419-688cb1d91514", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318349554200, "endTime": 11318349834200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "143238df-a654-4956-b04c-052c1d12c9bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "143238df-a654-4956-b04c-052c1d12c9bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318349554200, "endTime": 11318349834200}, "additional": {"logType": "info", "children": [], "durationId": "bdbf04eb-f788-49d3-9419-688cb1d91514", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "b236b531-c9a6-4f3c-8987-85c461d814d1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318625705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3607bcbd-c6d4-4440-9a73-1844068c5dd7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318626854500, "endTime": 11318626876000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "369fcec5-fae5-4bbb-b3f6-d7a341a0531d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "369fcec5-fae5-4bbb-b3f6-d7a341a0531d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318626854500, "endTime": 11318626876000}, "additional": {"logType": "info", "children": [], "durationId": "3607bcbd-c6d4-4440-9a73-1844068c5dd7", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "920d347b-11c8-4f9d-9ee3-f270e745a64a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318740732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "197e2841-ca5c-4b91-9fa2-f564f8e013c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318742119400, "endTime": 11318742143900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "ead264ad-ea67-4ede-91e0-3a658049024d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ead264ad-ea67-4ede-91e0-3a658049024d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318742119400, "endTime": 11318742143900}, "additional": {"logType": "info", "children": [], "durationId": "197e2841-ca5c-4b91-9fa2-f564f8e013c3", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "cfcfc7b5-1499-414c-b467-b8625315986f", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318816631800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f61ad8-f933-4489-9725-8a095693f86d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318817839100, "endTime": 11318817862200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "2657d54f-23ba-4abb-8276-b7037daef2cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2657d54f-23ba-4abb-8276-b7037daef2cf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318817839100, "endTime": 11318817862200}, "additional": {"logType": "info", "children": [], "durationId": "07f61ad8-f933-4489-9725-8a095693f86d", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "fae9fa43-13dd-40a9-a0db-12086511328b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318956696400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdd2e3d-8637-49c6-b2b9-0084012fd460", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11318957708200, "endTime": 11318957728100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "4e8c0538-c880-44cb-9508-4229caf8fc24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e8c0538-c880-44cb-9508-4229caf8fc24", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11318957708200, "endTime": 11318957728100}, "additional": {"logType": "info", "children": [], "durationId": "ffdd2e3d-8637-49c6-b2b9-0084012fd460", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "a1575f75-0a21-4d78-9de9-4366ba1598aa", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11319087686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c1a01d-02b4-44af-a859-a0f754411627", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11319088824200, "endTime": 11319088843400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "c0d10951-52c5-4f6c-8370-d07954dca65d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0d10951-52c5-4f6c-8370-d07954dca65d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11319088824200, "endTime": 11319088843400}, "additional": {"logType": "info", "children": [], "durationId": "c2c1a01d-02b4-44af-a859-a0f754411627", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "a1e3a502-b5df-4586-bf02-da355886bd28", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11319136450100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f71b89-7806-4267-90cf-3fa6b64637dc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11319137580700, "endTime": 11319137600500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "662c8071-3aa6-4791-a078-56349062d02e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "662c8071-3aa6-4791-a078-56349062d02e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11319137580700, "endTime": 11319137600500}, "additional": {"logType": "info", "children": [], "durationId": "23f71b89-7806-4267-90cf-3fa6b64637dc", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "06db86b0-1c99-4196-9722-f1a268c6fe36", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321194913500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d4f5c5-2532-4ac9-ba50-410f55bfbfca", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11321196237500, "endTime": 11321196271000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "cc732b6e-1cf7-4333-8ea0-72f0fe1db090"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc732b6e-1cf7-4333-8ea0-72f0fe1db090", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321196237500, "endTime": 11321196271000}, "additional": {"logType": "info", "children": [], "durationId": "15d4f5c5-2532-4ac9-ba50-410f55bfbfca", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "20b9f32e-964c-4946-83d9-6be45476ce0d", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11309321966400, "endTime": 11321196514300}, "additional": {"logType": "info", "children": ["51e91218-8a66-4643-a5a7-7667fbe90868", "447ca8b6-b8b2-444a-b082-add4b70b72a6", "b643b53b-a418-4abe-9c9e-24778c38d096", "143238df-a654-4956-b04c-052c1d12c9bf", "369fcec5-fae5-4bbb-b3f6-d7a341a0531d", "ead264ad-ea67-4ede-91e0-3a658049024d", "2657d54f-23ba-4abb-8276-b7037daef2cf", "4e8c0538-c880-44cb-9508-4229caf8fc24", "c0d10951-52c5-4f6c-8370-d07954dca65d", "662c8071-3aa6-4791-a078-56349062d02e", "cc732b6e-1cf7-4333-8ea0-72f0fe1db090", "657238a7-4a42-42d3-8857-907797736e70", "1be0cbf0-84bd-4065-9ef0-72a0a2c6abd5", "969a0fa0-3aa0-4f09-b94c-90e713034e73", "9c393ade-082b-49c6-b9c7-afe1efbfae24"], "durationId": "b1639ae3-c806-4697-9008-c8f7bbc95276", "parent": "fa7325b1-b196-4ad4-853a-6c4b54da141f"}}, {"head": {"id": "1071e825-9390-4de8-9491-ae56e6ace6a6", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11312155684400, "endTime": 11313362428000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "657238a7-4a42-42d3-8857-907797736e70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "657238a7-4a42-42d3-8857-907797736e70", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11312155684400, "endTime": 11313362428000}, "additional": {"logType": "info", "children": [], "durationId": "1071e825-9390-4de8-9491-ae56e6ace6a6", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "2aaa3086-d03d-48f8-b99a-4e79211a452b", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11313362623100, "endTime": 11313366923200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "1be0cbf0-84bd-4065-9ef0-72a0a2c6abd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1be0cbf0-84bd-4065-9ef0-72a0a2c6abd5", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11313362623100, "endTime": 11313366923200}, "additional": {"logType": "info", "children": [], "durationId": "2aaa3086-d03d-48f8-b99a-4e79211a452b", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "2518cf8f-ed38-4966-8d5e-b5b4d35a20b2", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11313367017500, "endTime": 11313367022900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "969a0fa0-3aa0-4f09-b94c-90e713034e73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "969a0fa0-3aa0-4f09-b94c-90e713034e73", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11313367017500, "endTime": 11313367022900}, "additional": {"logType": "info", "children": [], "durationId": "2518cf8f-ed38-4966-8d5e-b5b4d35a20b2", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "227d850f-3106-4bbb-82e1-6a53e7f698fb", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Worker2", "startTime": 11313367086800, "endTime": 11321194984800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b1639ae3-c806-4697-9008-c8f7bbc95276", "logId": "9c393ade-082b-49c6-b9c7-afe1efbfae24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c393ade-082b-49c6-b9c7-afe1efbfae24", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11313367086800, "endTime": 11321194984800}, "additional": {"logType": "info", "children": [], "durationId": "227d850f-3106-4bbb-82e1-6a53e7f698fb", "parent": "20b9f32e-964c-4946-83d9-6be45476ce0d"}}, {"head": {"id": "fa7325b1-b196-4ad4-853a-6c4b54da141f", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11309265581100, "endTime": 11321200435800, "totalTime": 11934834300}, "additional": {"logType": "info", "children": ["20b9f32e-964c-4946-83d9-6be45476ce0d"], "durationId": "628c05c2-f17b-4027-9af3-3b904baff0a9"}}, {"head": {"id": "233b7773-8c74-4b27-a301-7599dc876fde", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321206370200, "endTime": 11321206805200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "284e436b-135e-4cbc-8358-73666d7de93b", "logId": "e0490b30-2502-4d36-8fdc-37d7ed30f4cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "284e436b-135e-4cbc-8358-73666d7de93b", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321206297500}, "additional": {"logType": "detail", "children": [], "durationId": "233b7773-8c74-4b27-a301-7599dc876fde"}}, {"head": {"id": "8256b329-84c5-42f6-8c51-5937fcdcf757", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321206390800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "471f7f90-1899-43b7-b8d6-ffa42de03a03", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321206589700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4f97b7-7df0-4e3b-b9cb-5f0f43d15b60", "name": "runTaskFromQueue task cost before running: 12 s 375 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321206726900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0490b30-2502-4d36-8fdc-37d7ed30f4cc", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321206370200, "endTime": 11321206805200, "totalTime": 318100}, "additional": {"logType": "info", "children": [], "durationId": "233b7773-8c74-4b27-a301-7599dc876fde"}}, {"head": {"id": "fd8a3d35-2362-4e8b-bc45-e9c7ea6d39b0", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321224940400, "endTime": 11321224976900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1934ecc5-a975-4e2e-8ce9-472edf0d2ec7", "logId": "fe272f4c-073c-430b-a857-64a45d1dd321"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe272f4c-073c-430b-a857-64a45d1dd321", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321224940400, "endTime": 11321224976900}, "additional": {"logType": "info", "children": [], "durationId": "fd8a3d35-2362-4e8b-bc45-e9c7ea6d39b0"}}, {"head": {"id": "db4663f7-ba65-4225-b373-ddcf3d3ff817", "name": "BUILD SUCCESSFUL in 12 s 393 ms ", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225080900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "729fe4d5-909b-419e-92cf-e521fb6b7399", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11308832621100, "endTime": 11321225423700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 47}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "596c6595-9edf-405f-a1c2-f48d4614b97d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e204b8d-6457-4dfb-825d-73f58f2755cd", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6c3881-7b29-4642-9d6a-d5fa1e84be40", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225606000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9470296-0f2d-41aa-b10b-bbd2b9a1fa01", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225656300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa97a43-8aa0-43ff-8d4d-9c9c3e4e4b40", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225700200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1111331-263c-4d72-9707-c05cb40d15e4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225742100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d28efcc9-52d3-4d3e-94d6-d6f26726f342", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225812300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f0ee6df-0516-4b78-b9a6-5c2a736a9df5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewCompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321225880000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "548cba8a-f72e-4dbc-9a85-7549580e8703", "name": "There is no need to refresh cache, since the incremental task entry:default@CopyPreviewProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321226050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61505394-1f47-4b38-b728-6e2a533d1a23", "name": "There is no need to refresh cache, since the incremental task entry:default@PreviewUpdateAssets is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321226124500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f300e9-2a7b-45b3-9b82-ea5c5717165c", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321230547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad85b52d-b107-41da-be30-c6064462477d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321231881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2938bcf-ef03-44c2-9ff4-f6bb4522e3f7", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321232324800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f49c7466-013b-40ed-ac40-13c93bd9150a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321252068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5930225c-7b41-4a6d-bf19-4d40668f87ce", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321253790500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "452bf8a5-c807-44bd-b406-e6a974581f2e", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321254322600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6020594b-4e94-4c22-b9de-f416cfb18119", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321254833700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8d4193-407a-4e03-9861-ec03674628b3", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321256129900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5722acd3-1848-4f41-82b7-01c53e68d81e", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321256246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d24c353-9e28-4165-9f56-d78755ae8f8e", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321256560000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4b0bb5-fbca-4798-ad29-02ef4c2ef3cb", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321256865300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756bbe30-29d6-49f3-9ff1-c26b1b9a8682", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321257274700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a649417-c3d0-4684-bba7-70dd94ba595c", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:32 ms .", "description": "", "type": "log"}, "body": {"pid": 29116, "tid": "Main Thread", "startTime": 11321257606300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}