{"version": "2.0", "ppid": 24836, "events": [{"head": {"id": "4474192e-748e-484f-a88a-3ab312a2fcf0", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131505790600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "242aebe0-b1c9-49d1-96f6-dedfc40c4739", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131507977000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2157327a-e5cd-4a9b-b0bb-3fe7bac6eb1c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131508362200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e903b03-7154-4e79-955d-3fd42fc89524", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131509238000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3f2ed9-ae15-4d27-9fe3-ba0abe92acc8", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131509867000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3268b83a-74b9-4487-8bea-6dbb767a84ee", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131804672800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131811019700, "endTime": 13131957391300}, "additional": {"children": ["dcb71944-e904-4c9a-bb36-97e6729064ce", "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "e77851e3-2a3a-4795-af50-6804a93017c4", "a5c65af1-9569-4b7d-af68-adb486853d60", "405d9f68-8929-4bc8-ab70-25c5be372277", "d56feb10-99e7-49f1-81a7-abc62a9145f9", "02559bd9-b429-428c-96f0-923e61de6628"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcb71944-e904-4c9a-bb36-97e6729064ce", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131811020800, "endTime": 13131821161200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "6dc7534a-972a-4b36-ac95-1ed1edffec9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131821177300, "endTime": 13131956244000}, "additional": {"children": ["a622bd23-b6a2-4dfb-99a6-4b643d7a0704", "611badea-c551-4aba-8ec7-4f216fdf93c2", "67a07ffb-46b5-4648-bbd6-49799d26f689", "f46e065c-9634-4714-afa3-eb1ecc509758", "125f55bb-4d93-45d0-8801-b028b041972b", "ff87c92b-91d6-472f-b9f6-0304ef1677e7", "e23d7caa-f251-4cbb-863d-e0fd9fa15401", "dc355281-c46f-43d5-ae5c-6144456c11c2", "d7cc2df2-2fae-46f9-8bd6-c7fa83122e3e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e77851e3-2a3a-4795-af50-6804a93017c4", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131956267000, "endTime": 13131957369900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "de4c0595-02a7-43bb-b7ca-41b5b13b9585"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5c65af1-9569-4b7d-af68-adb486853d60", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131957380100, "endTime": 13131957386700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "357fac0e-9539-4995-985b-4004aea1df7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "405d9f68-8929-4bc8-ab70-25c5be372277", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131813821900, "endTime": 13131813847500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "7063f994-82df-430e-8273-7705e8e8694d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7063f994-82df-430e-8273-7705e8e8694d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131813821900, "endTime": 13131813847500}, "additional": {"logType": "info", "children": [], "durationId": "405d9f68-8929-4bc8-ab70-25c5be372277", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "d56feb10-99e7-49f1-81a7-abc62a9145f9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131818398900, "endTime": 13131818413700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "917ec20c-fa94-493f-83e1-343f3da0f139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "917ec20c-fa94-493f-83e1-343f3da0f139", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131818398900, "endTime": 13131818413700}, "additional": {"logType": "info", "children": [], "durationId": "d56feb10-99e7-49f1-81a7-abc62a9145f9", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "39a8113c-fa75-4912-b9ee-321486ccbda3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131818482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e8f581-d579-4a97-ba70-e15f5d9e32b1", "name": "Cache service initialization finished in 3 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131821057100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dc7534a-972a-4b36-ac95-1ed1edffec9d", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131811020800, "endTime": 13131821161200}, "additional": {"logType": "info", "children": [], "durationId": "dcb71944-e904-4c9a-bb36-97e6729064ce", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "a622bd23-b6a2-4dfb-99a6-4b643d7a0704", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131826470300, "endTime": 13131826482700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "a788360a-5425-4cd5-a715-83563be7c10f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "611badea-c551-4aba-8ec7-4f216fdf93c2", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131826497900, "endTime": 13131830313300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "092b758d-4ef5-4d7a-87b9-fd32c5ec1101"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67a07ffb-46b5-4648-bbd6-49799d26f689", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131830323600, "endTime": 13131899569300}, "additional": {"children": ["14ab2e13-5c26-4f93-ade8-8d160e448835", "e5f763cc-7faf-4ddb-bde9-d149d0cf1507", "e55e61c5-617f-4368-befd-95c177d2416c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "2c427dac-9975-4170-9732-83f7e42ca18a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f46e065c-9634-4714-afa3-eb1ecc509758", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131899585100, "endTime": 13131920446700}, "additional": {"children": ["7ce649d8-9aef-47b1-b626-09abb0473d81"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "b006281a-c0e0-4867-acb3-4e0543f7a5a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "125f55bb-4d93-45d0-8801-b028b041972b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131920453800, "endTime": 13131933412300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "fd489ac0-8e05-48a9-b2fd-b9f959089b87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff87c92b-91d6-472f-b9f6-0304ef1677e7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131934607000, "endTime": 13131942477400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "68596ea3-5d80-4cb5-9141-d6686bcfa4df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e23d7caa-f251-4cbb-863d-e0fd9fa15401", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131942495100, "endTime": 13131956110300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "957daf73-f9b9-44ca-856e-78f076eefcfc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc355281-c46f-43d5-ae5c-6144456c11c2", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131956126100, "endTime": 13131956233400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "0588d030-15e6-4447-af30-3484fdc7afb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a788360a-5425-4cd5-a715-83563be7c10f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131826470300, "endTime": 13131826482700}, "additional": {"logType": "info", "children": [], "durationId": "a622bd23-b6a2-4dfb-99a6-4b643d7a0704", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "092b758d-4ef5-4d7a-87b9-fd32c5ec1101", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131826497900, "endTime": 13131830313300}, "additional": {"logType": "info", "children": [], "durationId": "611badea-c551-4aba-8ec7-4f216fdf93c2", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "14ab2e13-5c26-4f93-ade8-8d160e448835", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131830922400, "endTime": 13131830939700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67a07ffb-46b5-4648-bbd6-49799d26f689", "logId": "6c04cb9f-a3c3-446a-a0f7-a9f55b9202e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c04cb9f-a3c3-446a-a0f7-a9f55b9202e1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131830922400, "endTime": 13131830939700}, "additional": {"logType": "info", "children": [], "durationId": "14ab2e13-5c26-4f93-ade8-8d160e448835", "parent": "2c427dac-9975-4170-9732-83f7e42ca18a"}}, {"head": {"id": "e5f763cc-7faf-4ddb-bde9-d149d0cf1507", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131832795400, "endTime": 13131898649000}, "additional": {"children": ["cc6825be-4329-43cb-90c0-d2cf5c96f3ec", "68c6de8d-ceaa-4b6d-8cc8-e19f5b8543c4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67a07ffb-46b5-4648-bbd6-49799d26f689", "logId": "d0a59057-b13e-4d2a-a091-b09bc66fbdfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc6825be-4329-43cb-90c0-d2cf5c96f3ec", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131832796500, "endTime": 13131837671700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5f763cc-7faf-4ddb-bde9-d149d0cf1507", "logId": "5ed8f5e6-5657-4866-a28b-7317d2e2ab39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68c6de8d-ceaa-4b6d-8cc8-e19f5b8543c4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131837689400, "endTime": 13131898636600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5f763cc-7faf-4ddb-bde9-d149d0cf1507", "logId": "9f7e5711-4100-450e-b0d4-8949fe292c69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdb61f0a-c671-4871-b3a0-8aaeff6f8c6d", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131832803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f215bfb1-6c4e-4dcf-84d2-9c46f6ae77ff", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131837541800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ed8f5e6-5657-4866-a28b-7317d2e2ab39", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131832796500, "endTime": 13131837671700}, "additional": {"logType": "info", "children": [], "durationId": "cc6825be-4329-43cb-90c0-d2cf5c96f3ec", "parent": "d0a59057-b13e-4d2a-a091-b09bc66fbdfa"}}, {"head": {"id": "2becee8f-96d8-48e1-a586-5d440a22b090", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131837705400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32710ad-e73b-42ec-8e87-e37b30fabf64", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131844445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26889da5-dbc7-4b10-8555-4948eea6a589", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131844579400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57ae51e4-bc99-46d1-bf79-70eeee92ad5b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131844869400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37372d5e-2699-4e6d-b847-6f57d986cc29", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131845133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7a6cda-932e-4691-ab47-765c24454530", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131846681600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6661d3d-17c8-4273-aaec-f615e27661ff", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131850479200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc36aa7-4556-43cf-95ee-ee985ee0d091", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131859173000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76872764-0e62-4b81-9ce9-505125d0f7d3", "name": "Sdk init in 28 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131879047200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9502d876-2a8e-4842-9c77-6d27467106de", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131879182000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 17}, "markType": "other"}}, {"head": {"id": "3af7c2fa-5487-4be5-885f-f33a6bdc81e1", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131879197000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 17}, "markType": "other"}}, {"head": {"id": "aea21cbb-c1b4-4679-9855-a94a35960731", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131898331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13456111-0988-435f-821b-a5d3d3c39a20", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131898465700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254ff454-dd67-47a3-b7a4-d76eb9948634", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131898536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f66f576e-d509-4694-b827-82ea133eba90", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131898586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7e5711-4100-450e-b0d4-8949fe292c69", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131837689400, "endTime": 13131898636600}, "additional": {"logType": "info", "children": [], "durationId": "68c6de8d-ceaa-4b6d-8cc8-e19f5b8543c4", "parent": "d0a59057-b13e-4d2a-a091-b09bc66fbdfa"}}, {"head": {"id": "d0a59057-b13e-4d2a-a091-b09bc66fbdfa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131832795400, "endTime": 13131898649000}, "additional": {"logType": "info", "children": ["5ed8f5e6-5657-4866-a28b-7317d2e2ab39", "9f7e5711-4100-450e-b0d4-8949fe292c69"], "durationId": "e5f763cc-7faf-4ddb-bde9-d149d0cf1507", "parent": "2c427dac-9975-4170-9732-83f7e42ca18a"}}, {"head": {"id": "e55e61c5-617f-4368-befd-95c177d2416c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131899371800, "endTime": 13131899544000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67a07ffb-46b5-4648-bbd6-49799d26f689", "logId": "0f391371-2228-476a-a621-0a12ac4a92a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f391371-2228-476a-a621-0a12ac4a92a6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131899371800, "endTime": 13131899544000}, "additional": {"logType": "info", "children": [], "durationId": "e55e61c5-617f-4368-befd-95c177d2416c", "parent": "2c427dac-9975-4170-9732-83f7e42ca18a"}}, {"head": {"id": "2c427dac-9975-4170-9732-83f7e42ca18a", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131830323600, "endTime": 13131899569300}, "additional": {"logType": "info", "children": ["6c04cb9f-a3c3-446a-a0f7-a9f55b9202e1", "d0a59057-b13e-4d2a-a091-b09bc66fbdfa", "0f391371-2228-476a-a621-0a12ac4a92a6"], "durationId": "67a07ffb-46b5-4648-bbd6-49799d26f689", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "7ce649d8-9aef-47b1-b626-09abb0473d81", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131900489900, "endTime": 13131920434400}, "additional": {"children": ["8a59b0fd-697e-4ef2-a9e5-99b48eb80d8d", "6ec5beef-455a-47de-b7f3-9047f3e9e5be", "ab827836-2143-4853-89a9-3c95ca650b86"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f46e065c-9634-4714-afa3-eb1ecc509758", "logId": "8fe609c9-9405-4840-ab20-ad367f4a5c6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a59b0fd-697e-4ef2-a9e5-99b48eb80d8d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131903550500, "endTime": 13131903568500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ce649d8-9aef-47b1-b626-09abb0473d81", "logId": "e7827b36-0e58-4a47-8c61-14148c864789"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7827b36-0e58-4a47-8c61-14148c864789", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131903550500, "endTime": 13131903568500}, "additional": {"logType": "info", "children": [], "durationId": "8a59b0fd-697e-4ef2-a9e5-99b48eb80d8d", "parent": "8fe609c9-9405-4840-ab20-ad367f4a5c6b"}}, {"head": {"id": "6ec5beef-455a-47de-b7f3-9047f3e9e5be", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131905568900, "endTime": 13131919170100}, "additional": {"children": ["4db317f5-06fe-44d5-be91-af1119d1da53", "e6567332-d702-4da7-b107-a94efd6705d7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ce649d8-9aef-47b1-b626-09abb0473d81", "logId": "2a581571-b185-4e33-ba4d-4f0432591cf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4db317f5-06fe-44d5-be91-af1119d1da53", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131905570100, "endTime": 13131909470000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ec5beef-455a-47de-b7f3-9047f3e9e5be", "logId": "dfc7d9a6-6632-47c7-a8d7-4bd6ce6f36d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6567332-d702-4da7-b107-a94efd6705d7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131909483900, "endTime": 13131919159300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ec5beef-455a-47de-b7f3-9047f3e9e5be", "logId": "b09b9f14-1a8f-4f05-99d5-aec96e9b4412"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "258d7503-3409-4821-ab88-63f5fc71f992", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131905603400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33f2ecf-7582-4732-ad12-8746a62c07ca", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131909358200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc7d9a6-6632-47c7-a8d7-4bd6ce6f36d3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131905570100, "endTime": 13131909470000}, "additional": {"logType": "info", "children": [], "durationId": "4db317f5-06fe-44d5-be91-af1119d1da53", "parent": "2a581571-b185-4e33-ba4d-4f0432591cf0"}}, {"head": {"id": "33c02b5e-aa47-4b47-886c-dd5d7544c51e", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131909498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f82168a-e2ed-4587-8d46-71986fda1657", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131915361700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc23520-4090-4c81-9ed1-fb5cf1e8a5b3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131915509400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7274c8f5-d291-4769-9536-d0bb9ca78139", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131915858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b114dbe-0d0b-417f-ace6-48070f9d95de", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131916037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1633f76d-093d-4c8f-99ad-bbb189735333", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131916106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438da6e4-72bd-4c99-aad4-156acd35a58c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131916162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8122094-23c5-4fc6-a4d1-437994fb83d9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131916218900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb27794-33fe-4177-8b46-34ce3ec96dde", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131918877400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c270e07-9a76-4812-bca1-9ee72b51ea60", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131918993600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b3e868-5187-440e-b9fe-d60d48448052", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131919057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e37b8e-e785-469a-9a3c-13eef2bb5a1b", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131919111900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09b9f14-1a8f-4f05-99d5-aec96e9b4412", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131909483900, "endTime": 13131919159300}, "additional": {"logType": "info", "children": [], "durationId": "e6567332-d702-4da7-b107-a94efd6705d7", "parent": "2a581571-b185-4e33-ba4d-4f0432591cf0"}}, {"head": {"id": "2a581571-b185-4e33-ba4d-4f0432591cf0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131905568900, "endTime": 13131919170100}, "additional": {"logType": "info", "children": ["dfc7d9a6-6632-47c7-a8d7-4bd6ce6f36d3", "b09b9f14-1a8f-4f05-99d5-aec96e9b4412"], "durationId": "6ec5beef-455a-47de-b7f3-9047f3e9e5be", "parent": "8fe609c9-9405-4840-ab20-ad367f4a5c6b"}}, {"head": {"id": "ab827836-2143-4853-89a9-3c95ca650b86", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131920405400, "endTime": 13131920419900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ce649d8-9aef-47b1-b626-09abb0473d81", "logId": "efb020dc-cefa-4ab6-994b-3cfc9e00bcae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efb020dc-cefa-4ab6-994b-3cfc9e00bcae", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131920405400, "endTime": 13131920419900}, "additional": {"logType": "info", "children": [], "durationId": "ab827836-2143-4853-89a9-3c95ca650b86", "parent": "8fe609c9-9405-4840-ab20-ad367f4a5c6b"}}, {"head": {"id": "8fe609c9-9405-4840-ab20-ad367f4a5c6b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131900489900, "endTime": 13131920434400}, "additional": {"logType": "info", "children": ["e7827b36-0e58-4a47-8c61-14148c864789", "2a581571-b185-4e33-ba4d-4f0432591cf0", "efb020dc-cefa-4ab6-994b-3cfc9e00bcae"], "durationId": "7ce649d8-9aef-47b1-b626-09abb0473d81", "parent": "b006281a-c0e0-4867-acb3-4e0543f7a5a7"}}, {"head": {"id": "b006281a-c0e0-4867-acb3-4e0543f7a5a7", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131899585100, "endTime": 13131920446700}, "additional": {"logType": "info", "children": ["8fe609c9-9405-4840-ab20-ad367f4a5c6b"], "durationId": "f46e065c-9634-4714-afa3-eb1ecc509758", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "2a45d045-d0f3-4ce7-961e-b7241e807c3f", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131932990300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ae49560-e42c-4d4a-8f44-2fd9777da3d0", "name": "hvigorfile, resolve hvigorfile dependencies in 13 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131933318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd489ac0-8e05-48a9-b2fd-b9f959089b87", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131920453800, "endTime": 13131933412300}, "additional": {"logType": "info", "children": [], "durationId": "125f55bb-4d93-45d0-8801-b028b041972b", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "d7cc2df2-2fae-46f9-8bd6-c7fa83122e3e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131934400100, "endTime": 13131934593900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "logId": "884bf605-1b2f-405f-9d8d-c7b761e0e03d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db2c1c38-9c23-4cbc-9d3d-d06ab4dab901", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131934430300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884bf605-1b2f-405f-9d8d-c7b761e0e03d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131934400100, "endTime": 13131934593900}, "additional": {"logType": "info", "children": [], "durationId": "d7cc2df2-2fae-46f9-8bd6-c7fa83122e3e", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "bdb52747-9304-463c-b52a-1f58c50563ed", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131936077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c1be6a5-7d66-4089-845e-c7633a658366", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131941749200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68596ea3-5d80-4cb5-9141-d6686bcfa4df", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131934607000, "endTime": 13131942477400}, "additional": {"logType": "info", "children": [], "durationId": "ff87c92b-91d6-472f-b9f6-0304ef1677e7", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "46fc47bc-79c9-43c0-a52c-4439050391d8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131942509400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8906c2a1-d291-44d0-a892-c460e2856ad0", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131947853100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b46445-fdc3-43bf-97be-45a7c2f6583c", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131947952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de575855-c485-4fef-98c1-0b1124f52b26", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131948169600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e897d17-8038-4bd2-a298-44d881b24640", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131953240500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70d0dc7-8787-40be-b9c4-f0fcd745a771", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131953369200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "957daf73-f9b9-44ca-856e-78f076eefcfc", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131942495100, "endTime": 13131956110300}, "additional": {"logType": "info", "children": [], "durationId": "e23d7caa-f251-4cbb-863d-e0fd9fa15401", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "feb4f89f-b178-4e6d-9026-82f98ae54af7", "name": "Configuration phase cost:130 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131956155100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0588d030-15e6-4447-af30-3484fdc7afb0", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131956126100, "endTime": 13131956233400}, "additional": {"logType": "info", "children": [], "durationId": "dc355281-c46f-43d5-ae5c-6144456c11c2", "parent": "6db9f128-81d7-4247-bf81-b8fb53319ac6"}}, {"head": {"id": "6db9f128-81d7-4247-bf81-b8fb53319ac6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131821177300, "endTime": 13131956244000}, "additional": {"logType": "info", "children": ["a788360a-5425-4cd5-a715-83563be7c10f", "092b758d-4ef5-4d7a-87b9-fd32c5ec1101", "2c427dac-9975-4170-9732-83f7e42ca18a", "b006281a-c0e0-4867-acb3-4e0543f7a5a7", "fd489ac0-8e05-48a9-b2fd-b9f959089b87", "68596ea3-5d80-4cb5-9141-d6686bcfa4df", "957daf73-f9b9-44ca-856e-78f076eefcfc", "0588d030-15e6-4447-af30-3484fdc7afb0", "884bf605-1b2f-405f-9d8d-c7b761e0e03d"], "durationId": "125c38bb-c4a8-479f-bd76-9dc00773e7ec", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "02559bd9-b429-428c-96f0-923e61de6628", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131957346800, "endTime": 13131957359300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8", "logId": "868e1669-c8ed-4e84-9cc5-23a9bee96360"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "868e1669-c8ed-4e84-9cc5-23a9bee96360", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131957346800, "endTime": 13131957359300}, "additional": {"logType": "info", "children": [], "durationId": "02559bd9-b429-428c-96f0-923e61de6628", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "de4c0595-02a7-43bb-b7ca-41b5b13b9585", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131956267000, "endTime": 13131957369900}, "additional": {"logType": "info", "children": [], "durationId": "e77851e3-2a3a-4795-af50-6804a93017c4", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "357fac0e-9539-4995-985b-4004aea1df7f", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131957380100, "endTime": 13131957386700}, "additional": {"logType": "info", "children": [], "durationId": "a5c65af1-9569-4b7d-af68-adb486853d60", "parent": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645"}}, {"head": {"id": "5c0cb1e2-9942-4fb2-8c16-34a70cb45645", "name": "init", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131811019700, "endTime": 13131957391300}, "additional": {"logType": "info", "children": ["6dc7534a-972a-4b36-ac95-1ed1edffec9d", "6db9f128-81d7-4247-bf81-b8fb53319ac6", "de4c0595-02a7-43bb-b7ca-41b5b13b9585", "357fac0e-9539-4995-985b-4004aea1df7f", "7063f994-82df-430e-8273-7705e8e8694d", "917ec20c-fa94-493f-83e1-343f3da0f139", "868e1669-c8ed-4e84-9cc5-23a9bee96360"], "durationId": "ecf1ff38-b2a7-40a8-bd64-c9fd4b817dc8"}}, {"head": {"id": "83105ee4-fc2e-4cb1-bf8c-c01c6c56cf03", "name": "Configuration task cost before running: 150 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131957759100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a5df98-dade-4e32-b145-92e229a0fcc4", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131964965200, "endTime": 13131976792000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "44ab692e-397e-42fb-a63e-c367882184f1", "logId": "bcd72a0c-8ead-4fca-8d46-340e7cab13b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44ab692e-397e-42fb-a63e-c367882184f1", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131959537900}, "additional": {"logType": "detail", "children": [], "durationId": "36a5df98-dade-4e32-b145-92e229a0fcc4"}}, {"head": {"id": "8dda82c7-cf21-4a92-b8d0-81d5d73d729b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131960475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35887e8c-7f6f-46fe-b4b1-bcdfefaa13f9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131960581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b3ec3f1-3f5e-44bc-82f8-098958b0aac1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131960641400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f29d5a1-4c67-4335-818c-de0bb7d53abd", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131964981000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ad60dd-8284-4f86-81b8-1da194e66ece", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131976482400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63747e72-5920-426c-a318-ad97825a3348", "name": "entry : default@PreBuild cost memory 0.29714202880859375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131976657800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd72a0c-8ead-4fca-8d46-340e7cab13b1", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131964965200, "endTime": 13131976792000}, "additional": {"logType": "info", "children": [], "durationId": "36a5df98-dade-4e32-b145-92e229a0fcc4"}}, {"head": {"id": "ce29dfa2-c78e-48b6-98da-d5917c14f74b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131982241100, "endTime": 13131986100500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b56a3da3-b6c7-46b5-81c1-208966e6d76c", "logId": "821cca62-236d-4c54-88b4-dd935655a9d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b56a3da3-b6c7-46b5-81c1-208966e6d76c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131980735200}, "additional": {"logType": "detail", "children": [], "durationId": "ce29dfa2-c78e-48b6-98da-d5917c14f74b"}}, {"head": {"id": "a24af300-c1bf-43f1-a46c-deeb7503b352", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131981312400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d4c8970-8e2b-4755-8a84-002f3a6af7d1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131981415300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "527e41e1-b4ab-435e-a6a3-3ed37b8f0096", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131981477400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d205c48-6d67-430d-93b7-ab3551d3f045", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131982252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "687d2017-4be1-4f8b-b5db-e34d82725f60", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131985919700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6783f7dc-139e-4c16-ba46-c41a55cf68aa", "name": "entry : default@MergeProfile cost memory 0.13327789306640625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131986029000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821cca62-236d-4c54-88b4-dd935655a9d6", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131982241100, "endTime": 13131986100500}, "additional": {"logType": "info", "children": [], "durationId": "ce29dfa2-c78e-48b6-98da-d5917c14f74b"}}, {"head": {"id": "954d413a-9be5-4465-8c37-f6e39e2bd7d1", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131989399800, "endTime": 13131992281500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e48f73b4-acd1-4487-8553-891265eb8b2e", "logId": "3a5dbee4-e52f-448c-a8d4-66bfc4d3463f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e48f73b4-acd1-4487-8553-891265eb8b2e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131987779500}, "additional": {"logType": "detail", "children": [], "durationId": "954d413a-9be5-4465-8c37-f6e39e2bd7d1"}}, {"head": {"id": "b7cfc01d-6707-4faf-a480-2b5d251e5c8d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131988285600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32bbb24-248b-4a37-aa88-9fc7c0ffc969", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131988384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ca92c9-bf12-46b7-885c-b12<PERSON><PERSON><PERSON>e9d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131988448800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da27da6-e429-4f41-b628-639e9171511b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131989414300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd80b52-fadd-49a0-9148-2549833ce9db", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131990713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8254612b-35b5-43f9-a3ce-fbdaa2819dab", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131992082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "062bc679-2d54-4b50-bfc9-f74abf81bf0e", "name": "entry : default@CreateBuildProfile cost memory 0.10173797607421875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131992206300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5dbee4-e52f-448c-a8d4-66bfc4d3463f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131989399800, "endTime": 13131992281500}, "additional": {"logType": "info", "children": [], "durationId": "954d413a-9be5-4465-8c37-f6e39e2bd7d1"}}, {"head": {"id": "302ec13d-faae-44a5-ba13-03a65e2fb8fb", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995431500, "endTime": 13131996143600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "991dd7cc-c910-4aef-9bdd-6d89eee09efa", "logId": "d805c6b4-070e-4770-8c74-85f4568d1cfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "991dd7cc-c910-4aef-9bdd-6d89eee09efa", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131993983900}, "additional": {"logType": "detail", "children": [], "durationId": "302ec13d-faae-44a5-ba13-03a65e2fb8fb"}}, {"head": {"id": "8c959fba-9247-4ec3-a187-da10150728c7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131994509000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279b6921-400b-4289-99eb-8abc3f82ec04", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131994615500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "563f9fea-848b-4e7d-b782-6db395e96238", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131994674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b778c660-8910-4d6e-bfe3-58216f1d301e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a654ea69-b576-49cc-804f-d20c08f25207", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe06706f-76ac-4688-825f-2e2ae45a0e8e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995732600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f08bcb-e3a3-4e1b-9b57-95981c94053c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995783800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d723c86-bbe0-4eb5-9167-f09b9940debd", "name": "entry : default@PreCheckSyscap cost memory 0.050689697265625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995981900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d048f90-4efd-4e03-a4e4-6c4d45ecaeb2", "name": "runTaskFromQueue task cost before running: 189 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131996084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d805c6b4-070e-4770-8c74-85f4568d1cfe", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131995431500, "endTime": 13131996143600, "totalTime": 631200}, "additional": {"logType": "info", "children": [], "durationId": "302ec13d-faae-44a5-ba13-03a65e2fb8fb"}}, {"head": {"id": "74756c70-423e-4148-a5cb-3573e2579bf5", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132009503000, "endTime": 13132010927300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e4f9206e-04cb-4278-a406-c70ca9c43cc2", "logId": "6bb90f58-04b1-40d2-8f85-3b29e21ecd65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4f9206e-04cb-4278-a406-c70ca9c43cc2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131997795000}, "additional": {"logType": "detail", "children": [], "durationId": "74756c70-423e-4148-a5cb-3573e2579bf5"}}, {"head": {"id": "cf067669-9f1a-4355-8ad0-d9793e631e5b", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131998278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eed20f0-f1f7-43b8-87e0-5c96988ffd35", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131998368200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a73bb85-80e0-4540-ae33-bd8f11d47d07", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131998433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bd39274-2855-4f50-9361-c71627b4e6e5", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132009521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ded2b5-e94d-4c5a-b422-195546aea170", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132009840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393c7873-e0e4-425a-a2a3-89ff99229c34", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132010705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b1d332-03f0-47a1-b6fc-2fe25aae1460", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07050323486328125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132010840200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bb90f58-04b1-40d2-8f85-3b29e21ecd65", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132009503000, "endTime": 13132010927300}, "additional": {"logType": "info", "children": [], "durationId": "74756c70-423e-4148-a5cb-3573e2579bf5"}}, {"head": {"id": "d7b38904-34da-408b-b67a-f68196094113", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132015020900, "endTime": 13132016318900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "93f59c88-63a2-4ff2-b9c0-0d23b262aae9", "logId": "e49227b8-0697-4752-94ab-b7d2eedc1446"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93f59c88-63a2-4ff2-b9c0-0d23b262aae9", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132012705000}, "additional": {"logType": "detail", "children": [], "durationId": "d7b38904-34da-408b-b67a-f68196094113"}}, {"head": {"id": "e6555baa-5a34-491d-983b-3a6aa3160352", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132013206400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d10a33e9-d7b0-4af2-9d6d-a7a4b221b069", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132013299200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e89adb0a-ffbf-4248-87f4-b2c218cff556", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132013356500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fc1d856-23e9-4b26-b10b-b13402dc872b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132015032000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da102a9-f2c1-4361-a610-dc995775025d", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132016149500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1473ee8-1970-45e1-b149-5c775e5e4035", "name": "entry : default@ProcessProfile cost memory 0.05892181396484375", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132016247300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e49227b8-0697-4752-94ab-b7d2eedc1446", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132015020900, "endTime": 13132016318900}, "additional": {"logType": "info", "children": [], "durationId": "d7b38904-34da-408b-b67a-f68196094113"}}, {"head": {"id": "88fa018a-6b64-46f8-8c12-8b9b55a6f83c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132020210900, "endTime": 13132026664000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "20159157-7d43-4492-800e-41fbf7869087", "logId": "7173081b-a2b7-4d53-b5bf-87d17eb146cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20159157-7d43-4492-800e-41fbf7869087", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132017912000}, "additional": {"logType": "detail", "children": [], "durationId": "88fa018a-6b64-46f8-8c12-8b9b55a6f83c"}}, {"head": {"id": "9cf90ea5-3383-443d-8733-9f98aa6ecee9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132018424700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "960d1251-3537-460d-a9ff-99ed4afe4420", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132018514400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f78d1af-7077-4a1d-9854-6bb9da83d278", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132018568700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8efea52e-526d-49ad-8fe6-c7ef1de6a60b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132020224000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db944af-7449-4e42-9e2d-9ae36e62fd9d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132026314700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ee1b02-9092-4eac-8510-4dd67b983442", "name": "entry : default@ProcessRouterMap cost memory 0.21795654296875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132026513300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7173081b-a2b7-4d53-b5bf-87d17eb146cf", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132020210900, "endTime": 13132026664000}, "additional": {"logType": "info", "children": [], "durationId": "88fa018a-6b64-46f8-8c12-8b9b55a6f83c"}}, {"head": {"id": "250f678e-7641-49db-8fc5-b1dd4b1e9fec", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132036845300, "endTime": 13132040864000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "131f77e5-2f9e-4dcb-a39c-dc781a77528b", "logId": "5d75fdc4-2bdf-47db-8605-afeeea7bd3ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "131f77e5-2f9e-4dcb-a39c-dc781a77528b", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132030013200}, "additional": {"logType": "detail", "children": [], "durationId": "250f678e-7641-49db-8fc5-b1dd4b1e9fec"}}, {"head": {"id": "41b94404-7edc-4de8-b968-9c59ca4469c2", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132030514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41df2c56-0a64-4717-aec5-0d5e96289f91", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132032473500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b9c360-5e52-4a07-8117-fdd7d66eb3fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132032549000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c60aa17b-0614-4b22-9976-50340446e86f", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132034147500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "520430b7-745e-46a3-9cdb-ca9a893a8a99", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132038874700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f26b632-e0fc-4660-98e3-a555599afc5c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132039071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6908c8d-549f-4106-af63-898923c312fa", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132039142100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd9e2c64-33b5-409a-a401-8e1d1133512f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132039195400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "714a6029-591b-4f6f-ae86-913b1cbe9a53", "name": "entry : default@PreviewProcessResource cost memory 0.09384918212890625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132039291100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b062a241-25ca-4332-aecc-158efe259c7a", "name": "runTaskFromQueue task cost before running: 233 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132040772400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d75fdc4-2bdf-47db-8605-afeeea7bd3ae", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132036845300, "endTime": 13132040864000, "totalTime": 2516100}, "additional": {"logType": "info", "children": [], "durationId": "250f678e-7641-49db-8fc5-b1dd4b1e9fec"}}, {"head": {"id": "7515112d-2499-4a25-8fb0-bb193ff46d69", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132049122800, "endTime": 13132072754700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cc9b5c5a-9da3-436c-affd-3b82b692e29d", "logId": "a4f901a7-2b2e-4b51-985e-f842f87f3507"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc9b5c5a-9da3-436c-affd-3b82b692e29d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132044183300}, "additional": {"logType": "detail", "children": [], "durationId": "7515112d-2499-4a25-8fb0-bb193ff46d69"}}, {"head": {"id": "df0a66c6-1c07-432d-83d9-09ac7b286226", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132044716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c991ab-244b-489f-8bc7-21c1f90e850f", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132044814000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce54408e-c3e2-4273-8dcf-b969399f0ef4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132044870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "644042c2-5b57-4970-9383-81d6e373c7ed", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132049141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f72d0a-ec52-4761-a0d4-4f9cd0b95684", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132072516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4e07a1-9f98-481f-98b2-5fcf88a2019a", "name": "entry : default@GenerateLoaderJson cost memory 0.**********953125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132072673200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4f901a7-2b2e-4b51-985e-f842f87f3507", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132049122800, "endTime": 13132072754700}, "additional": {"logType": "info", "children": [], "durationId": "7515112d-2499-4a25-8fb0-bb193ff46d69"}}, {"head": {"id": "85e9d280-8428-47bb-aca1-9a6de6091fce", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132085201500, "endTime": 13132684241600}, "additional": {"children": ["9b378c81-ed40-4eb6-81fd-fa89025d300b", "89200eea-b852-4050-a5a7-5fb7d12dacc4", "b8c7ef49-48fc-4880-9b70-0dc8b4e91504", "719d9f6b-db59-4c19-8ddc-0eddbc00bd5a", "4e7c3e48-208e-4ec2-bb23-d7323c02b8f8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "7655fffc-972b-4ac0-b330-8d11d002a7c8", "logId": "368c8062-9552-43ad-b1a4-0c9ffb6c9434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7655fffc-972b-4ac0-b330-8d11d002a7c8", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132080696900}, "additional": {"logType": "detail", "children": [], "durationId": "85e9d280-8428-47bb-aca1-9a6de6091fce"}}, {"head": {"id": "d661171a-6239-4cc4-8809-784a3bacf67f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132081342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2785f652-46de-4c7c-916a-7c70e875054c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132081459600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38c7146-5a45-4c73-9e28-76092da74fa0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132081521100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd08322-0b67-458f-8822-df3c3afb34bf", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132082508800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26092595-c817-4751-b65d-b6c9e4c1517e", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132085316200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d36dfb-2b99-462b-90b2-0223069f364d", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132134252400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea79e03-e109-484c-a5e1-b1005902399d", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 49 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132134471200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b378c81-ed40-4eb6-81fd-fa89025d300b", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132135723700, "endTime": 13132164152300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e9d280-8428-47bb-aca1-9a6de6091fce", "logId": "b3b3bf13-8e2e-4acb-80c4-a85f3eaffe1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3b3bf13-8e2e-4acb-80c4-a85f3eaffe1e", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132135723700, "endTime": 13132164152300}, "additional": {"logType": "info", "children": [], "durationId": "9b378c81-ed40-4eb6-81fd-fa89025d300b", "parent": "368c8062-9552-43ad-b1a4-0c9ffb6c9434"}}, {"head": {"id": "cedc5b63-238c-43c0-9d9a-1ead6128d670", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132164771500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89200eea-b852-4050-a5a7-5fb7d12dacc4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132165827200, "endTime": 13132285404500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e9d280-8428-47bb-aca1-9a6de6091fce", "logId": "68952bca-68ce-4bf3-be43-81d01ebe9eb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b51d6e3c-1e9e-498c-9771-a5b8e26a18d9", "name": "current process  memoryUsage: {\n  rss: 205582336,\n  heapTotal: 156045312,\n  heapUsed: 125974792,\n  external: 3201255,\n  arrayBuffers: 190478\n} os memoryUsage :11.256641387939453", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132167091500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f340ce23-27d6-4b48-8139-e4be7c9ac6c1", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132283804600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68952bca-68ce-4bf3-be43-81d01ebe9eb9", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132165827200, "endTime": 13132285404500}, "additional": {"logType": "info", "children": [], "durationId": "89200eea-b852-4050-a5a7-5fb7d12dacc4", "parent": "368c8062-9552-43ad-b1a4-0c9ffb6c9434"}}, {"head": {"id": "08cf0188-16dc-4f89-8792-5b392593621f", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132285617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c7ef49-48fc-4880-9b70-0dc8b4e91504", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132286502400, "endTime": 13132416495600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e9d280-8428-47bb-aca1-9a6de6091fce", "logId": "0b447562-145c-40bb-9f99-b366b218cc81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01e82edd-ecf2-48cf-a17f-4a634b9817b1", "name": "current process  memoryUsage: {\n  rss: 205918208,\n  heapTotal: 156045312,\n  heapUsed: 128067848,\n  external: 3248701,\n  arrayBuffers: 237939\n} os memoryUsage :11.261550903320312", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132287330700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7989c4ca-a1aa-455e-9352-60541a6cf75e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132414198200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b447562-145c-40bb-9f99-b366b218cc81", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132286502400, "endTime": 13132416495600}, "additional": {"logType": "info", "children": [], "durationId": "b8c7ef49-48fc-4880-9b70-0dc8b4e91504", "parent": "368c8062-9552-43ad-b1a4-0c9ffb6c9434"}}, {"head": {"id": "24492062-6ca8-4749-b190-b10009cb2a7e", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132417009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719d9f6b-db59-4c19-8ddc-0eddbc00bd5a", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132417961100, "endTime": 13132530018000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e9d280-8428-47bb-aca1-9a6de6091fce", "logId": "ef809229-1efc-4ea4-9053-41706730f286"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc0254c0-bf49-4b1f-83bf-c39310588f70", "name": "current process  memoryUsage: {\n  rss: 205983744,\n  heapTotal: 156045312,\n  heapUsed: 128334024,\n  external: 3257019,\n  arrayBuffers: 246321\n} os memoryUsage :11.266681671142578", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132418751000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a840eee-2526-42c2-b3ff-74a449e16e03", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132527962900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef809229-1efc-4ea4-9053-41706730f286", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132417961100, "endTime": 13132530018000}, "additional": {"logType": "info", "children": [], "durationId": "719d9f6b-db59-4c19-8ddc-0eddbc00bd5a", "parent": "368c8062-9552-43ad-b1a4-0c9ffb6c9434"}}, {"head": {"id": "5bd5cfcf-5a02-4015-8097-59a5eb6d247c", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132530630200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e7c3e48-208e-4ec2-bb23-d7323c02b8f8", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132531530700, "endTime": 13132682008700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e9d280-8428-47bb-aca1-9a6de6091fce", "logId": "1528704f-631f-434f-8835-5bd587693290"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbcfa3b3-bdf9-4bdc-9395-247fc5d37629", "name": "current process  memoryUsage: {\n  rss: 206036992,\n  heapTotal: 156045312,\n  heapUsed: 128635256,\n  external: 3257145,\n  arrayBuffers: 247366\n} os memoryUsage :11.262062072753906", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132532282600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f5d39a-6ef0-43c5-b3c6-d657e7670378", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132678434400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1528704f-631f-434f-8835-5bd587693290", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132531530700, "endTime": 13132682008700}, "additional": {"logType": "info", "children": [], "durationId": "4e7c3e48-208e-4ec2-bb23-d7323c02b8f8", "parent": "368c8062-9552-43ad-b1a4-0c9ffb6c9434"}}, {"head": {"id": "01f48a17-eacf-4d8c-9850-c931960cf8b1", "name": "entry : default@PreviewCompileResource cost memory 4.7832489013671875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132683917900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604c1e24-aa72-417b-a00c-b145793cecd9", "name": "runTaskFromQueue task cost before running: 877 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132684146600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368c8062-9552-43ad-b1a4-0c9ffb6c9434", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132085201500, "endTime": 13132684241600, "totalTime": 598891100}, "additional": {"logType": "info", "children": ["b3b3bf13-8e2e-4acb-80c4-a85f3eaffe1e", "68952bca-68ce-4bf3-be43-81d01ebe9eb9", "0b447562-145c-40bb-9f99-b366b218cc81", "ef809229-1efc-4ea4-9053-41706730f286", "1528704f-631f-434f-8835-5bd587693290"], "durationId": "85e9d280-8428-47bb-aca1-9a6de6091fce"}}, {"head": {"id": "fc26858b-7ba4-49f4-814c-1f9b366b9b64", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687571100, "endTime": 13132688009800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6f986dfd-9186-487e-8cca-7ff3834face8", "logId": "3a4b0ce1-deec-452f-9274-9ac05b73b7a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f986dfd-9186-487e-8cca-7ff3834face8", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132686783700}, "additional": {"logType": "detail", "children": [], "durationId": "fc26858b-7ba4-49f4-814c-1f9b366b9b64"}}, {"head": {"id": "156f7754-a069-4cd8-9356-b5a9bfa27c4d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687302100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096d3e1a-05b7-4808-a04c-bf3b6017cdce", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687397000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d9a14e-01ac-44ce-925a-fbe3fc32ec64", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687455700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7f924a-08f0-426e-bd12-c8f197203364", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687584000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d77c295a-6b8b-40e3-9bb7-92093e6dcc0d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687680900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ba2569-c1be-4526-9e84-de8991f82ffc", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687732000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fffd7244-8930-4f51-8501-71e184d8b1af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd380341-f58c-4f9a-9608-0e77754b171f", "name": "entry : default@PreviewHookCompileResource cost memory 0.05199432373046875", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687873100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22da5cb6-5cb9-4e14-aa7e-83b038f858ed", "name": "runTaskFromQueue task cost before running: 880 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687956800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4b0ce1-deec-452f-9274-9ac05b73b7a3", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132687571100, "endTime": 13132688009800, "totalTime": 365800}, "additional": {"logType": "info", "children": [], "durationId": "fc26858b-7ba4-49f4-814c-1f9b366b9b64"}}, {"head": {"id": "4d58c627-4510-4b6e-b450-0b0969cdf5d7", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132691105500, "endTime": 13132699452100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "00fb0a5e-bd4a-4298-b729-5e875654882d", "logId": "084a58f4-ed15-4a9e-a8f1-d5d428debe87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00fb0a5e-bd4a-4298-b729-5e875654882d", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132689779600}, "additional": {"logType": "detail", "children": [], "durationId": "4d58c627-4510-4b6e-b450-0b0969cdf5d7"}}, {"head": {"id": "093abf18-4ea6-456d-89d0-44007f699bc0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132690313100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f48acf-7615-4143-a950-20bd7b9f66db", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132690420500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b7a131-3dfa-4aca-8432-dd98ec893a25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132690483300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d48a98-eedc-40a7-a5a6-c9ef1237bd60", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132691119500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d968df2-bc15-444a-ad72-f837faa4527a", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132692619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f18977-2b88-4f7d-aefe-91c3169153d1", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132692728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc4934ce-86f6-4dd4-909e-db0bb514c0c6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132692813400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38b64d50-3623-45f6-8d21-6374f3c5361e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132692866700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1c75dd5-834f-49b7-92cb-82465e88ac3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132692912500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1446c64-c5a4-45a8-9e7b-1ad33b3b93fe", "name": "entry : default@CopyPreviewProfile cost memory 0.22992706298828125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132699202100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d19260be-08e7-44e8-9ec1-531e5d9142a8", "name": "runTaskFromQueue task cost before running: 892 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132699377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084a58f4-ed15-4a9e-a8f1-d5d428debe87", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132691105500, "endTime": 13132699452100, "totalTime": 8243000}, "additional": {"logType": "info", "children": [], "durationId": "4d58c627-4510-4b6e-b450-0b0969cdf5d7"}}, {"head": {"id": "b8cbb689-d178-4658-b4ad-e615c1546238", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132702858100, "endTime": 13132703457700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "87e93f1c-0bac-4418-af70-25cfbd2d9692", "logId": "27127674-e943-46b9-9e2a-ece7b0fc8c94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e93f1c-0bac-4418-af70-25cfbd2d9692", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132701481100}, "additional": {"logType": "detail", "children": [], "durationId": "b8cbb689-d178-4658-b4ad-e615c1546238"}}, {"head": {"id": "680e2699-db79-468a-9c39-89e510fe2a71", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132701999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0f378d-e56b-4670-b06d-7850004bf4d5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132702091900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3e9440-f842-426a-8b25-03db183eed37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132702145500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347051b0-4928-4cb6-b686-dd9c52318654", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132702868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92fbc770-78a7-46fd-88d9-1e68c4578985", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132702980500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3447fa96-e00d-460b-831c-d57dbd305006", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132703036600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c94f080f-08ce-41dc-a045-0986d52ed986", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132703082700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7adb0fc2-32b2-45cb-bbcc-cf1fa4cafd51", "name": "entry : default@ReplacePreviewerPage cost memory 0.052001953125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132703309600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c855694d-2651-4797-b6e3-18637f943b19", "name": "runTaskFromQueue task cost before running: 896 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132703402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27127674-e943-46b9-9e2a-ece7b0fc8c94", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132702858100, "endTime": 13132703457700, "totalTime": 524100}, "additional": {"logType": "info", "children": [], "durationId": "b8cbb689-d178-4658-b4ad-e615c1546238"}}, {"head": {"id": "54e36b4d-d2a5-48ca-97f9-72823f6dcd5d", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132705021600, "endTime": 13132705327100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "56ab9439-5d83-48c7-996e-824f9b80b97d", "logId": "c1e15935-a0d5-483d-8ad9-6472d73809f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56ab9439-5d83-48c7-996e-824f9b80b97d", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132704967400}, "additional": {"logType": "detail", "children": [], "durationId": "54e36b4d-d2a5-48ca-97f9-72823f6dcd5d"}}, {"head": {"id": "aab0539d-811a-4d47-ba92-f5295f4475f6", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132705029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00d2f16a-072c-4471-bc3c-4952f0c7ae1c", "name": "entry : buildPreviewerResource cost memory 0.01215362548828125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132705182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd51bd6c-b8e6-4f58-89b3-5fe6a13ff5e0", "name": "runTaskFromQueue task cost before running: 898 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132705271400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e15935-a0d5-483d-8ad9-6472d73809f6", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132705021600, "endTime": 13132705327100, "totalTime": 230600}, "additional": {"logType": "info", "children": [], "durationId": "54e36b4d-d2a5-48ca-97f9-72823f6dcd5d"}}, {"head": {"id": "4f2137d5-7392-48fa-9169-b90e6baef8eb", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132708573500, "endTime": 13132712378100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "ce66ebbf-4ce1-4f6c-93c3-180143c3202e", "logId": "fffa91e9-fac7-40f9-bd90-9743d919a594"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce66ebbf-4ce1-4f6c-93c3-180143c3202e", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132707076400}, "additional": {"logType": "detail", "children": [], "durationId": "4f2137d5-7392-48fa-9169-b90e6baef8eb"}}, {"head": {"id": "dc9382e4-dbab-46ee-b5f7-c4afaa1a70ce", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132707636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b722246d-81ef-4621-af16-780eefa87044", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132707792000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91bf33a-f34a-489f-b8e2-7e1dc03731bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132707855300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2afa10-ba0c-446c-b791-180c80aeff6a", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132708585700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33fa6ae9-4902-46bd-bba3-29b2ba8bc48e", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132710720000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b809904-f037-4a10-8879-4e033ccd07dc", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132710831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea91fb79-ed77-42af-9c1d-9c26d3c2a71a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132710918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c23adfc-e50e-4b6c-a9d3-cc5139551619", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132710969900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01957a16-c0f7-4104-b0ac-f81fcb7727b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132711013900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc204294-de9c-4f8e-8181-6770cc5a036a", "name": "entry : default@PreviewUpdateAssets cost memory 0.3786773681640625", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132712168700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240b75e2-1e20-4735-946c-d7e46ebe063b", "name": "runTaskFromQueue task cost before running: 905 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132712305400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fffa91e9-fac7-40f9-bd90-9743d919a594", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132708573500, "endTime": 13132712378100, "totalTime": 3702600}, "additional": {"logType": "info", "children": [], "durationId": "4f2137d5-7392-48fa-9169-b90e6baef8eb"}}, {"head": {"id": "f980ec0e-c1de-4eae-a6e7-7166a4f8d63e", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132720436100, "endTime": 13142843124100}, "additional": {"children": ["c2d0f19a-2257-49eb-bccd-8a7d56a57781"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "3afe7e13-35ae-472e-b9ef-f9880d9a5ef1", "logId": "1cbe44ad-5210-4dc8-a124-cb8ecc1a575e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3afe7e13-35ae-472e-b9ef-f9880d9a5ef1", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132714833100}, "additional": {"logType": "detail", "children": [], "durationId": "f980ec0e-c1de-4eae-a6e7-7166a4f8d63e"}}, {"head": {"id": "b767ed9e-c434-45b4-94df-7506c791f351", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132715330600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c63fabe-7195-49f4-adde-6988a87598af", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132715426800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1237ff-2c19-4b52-87b3-6d8701feeb32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132715483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ea90e93-3bfe-4ed8-a333-8100aacc8658", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132720450500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b2459ee-ddd5-49c3-9318-4f9f007f2117", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132752740000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0276a89c-828d-4ad9-9ca7-3a48c8ba94f2", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132752908000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13132768745100, "endTime": 13142840138400}, "additional": {"children": ["b8f98cde-91b3-4743-9464-866b6e3eec85", "393f3b7e-22be-4feb-9b3a-9d02fa5630b8", "917a5459-6272-4a9a-b4e2-195561b8d7ff", "6ab7c4aa-63a9-4bc4-84da-52a59692dd0e", "2b1ae6c8-9f03-4f0a-92ce-4a68cb3d48c3", "05f484fe-05b4-4529-a981-036d14733f83", "24907d3d-23de-4af8-94c5-0d703397549a", "ff7120eb-d5de-4ddc-a8ab-c7d2f68a5959", "79317781-4061-4545-9783-22f571606339", "ecbecdd4-546a-4300-92b5-377e3d85894b", "c200d33c-4736-4ffa-884d-0ff2ac3c0f92", "99db16aa-ac28-4bd4-b0c3-bf2bebb6b69e", "52f68cb9-f74c-4d30-bd9b-513f5155f375"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f980ec0e-c1de-4eae-a6e7-7166a4f8d63e", "logId": "1541ca24-a68d-475f-a43a-ee82d9670641"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e12deb56-425c-4a51-a599-300efd1a8114", "name": "entry : default@PreviewArkTS cost memory 2.3003768920898438", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132771230300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "867245c9-164c-42c3-be71-ee13b79ea503", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13136265062600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f98cde-91b3-4743-9464-866b6e3eec85", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13136266153600, "endTime": 13136266207400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "d902cd2f-0966-4095-9a0d-09fd9bd658e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d902cd2f-0966-4095-9a0d-09fd9bd658e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13136266153600, "endTime": 13136266207400}, "additional": {"logType": "info", "children": [], "durationId": "b8f98cde-91b3-4743-9464-866b6e3eec85", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "5e268243-0b3d-43f9-a91d-11a19dae5a20", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142833490700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393f3b7e-22be-4feb-9b3a-9d02fa5630b8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142834557800, "endTime": 13142834576100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "80142b75-b6cb-4d5c-9d65-5b6f8edd0205"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80142b75-b6cb-4d5c-9d65-5b6f8edd0205", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142834557800, "endTime": 13142834576100}, "additional": {"logType": "info", "children": [], "durationId": "393f3b7e-22be-4feb-9b3a-9d02fa5630b8", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "adc366d2-6a9d-4613-abab-a2e5616d59c1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142834636800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917a5459-6272-4a9a-b4e2-195561b8d7ff", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142835325300, "endTime": 13142835337500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "e84f26a4-ef65-420d-b095-d89010534799"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e84f26a4-ef65-420d-b095-d89010534799", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142835325300, "endTime": 13142835337500}, "additional": {"logType": "info", "children": [], "durationId": "917a5459-6272-4a9a-b4e2-195561b8d7ff", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "8528a0f1-2ac9-449e-9d4d-58aa448ee6e8", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142835384300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab7c4aa-63a9-4bc4-84da-52a59692dd0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142836046400, "endTime": 13142836059000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "7bbd8365-fc24-4d3e-b02c-87fff9a4abaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bbd8365-fc24-4d3e-b02c-87fff9a4abaa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142836046400, "endTime": 13142836059000}, "additional": {"logType": "info", "children": [], "durationId": "6ab7c4aa-63a9-4bc4-84da-52a59692dd0e", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "3669362a-7109-4bb7-b1af-81ed1ccc03d5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142836116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1ae6c8-9f03-4f0a-92ce-4a68cb3d48c3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142836774300, "endTime": 13142836787500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "dc4f8083-7c12-44d6-8aa1-569e19a58980"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc4f8083-7c12-44d6-8aa1-569e19a58980", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142836774300, "endTime": 13142836787500}, "additional": {"logType": "info", "children": [], "durationId": "2b1ae6c8-9f03-4f0a-92ce-4a68cb3d48c3", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "99f59ee6-33d1-4bf1-99b5-04cb3993e810", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142836839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f484fe-05b4-4529-a981-036d14733f83", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142837557100, "endTime": 13142837570700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "5edd97ca-a29b-47ec-9c40-696db4c8f685"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5edd97ca-a29b-47ec-9c40-696db4c8f685", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142837557100, "endTime": 13142837570700}, "additional": {"logType": "info", "children": [], "durationId": "05f484fe-05b4-4529-a981-036d14733f83", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "443ae31a-5a0c-40da-aca1-564c28a5d6e1", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142837626200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24907d3d-23de-4af8-94c5-0d703397549a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142838351300, "endTime": 13142838365900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "5901508b-f118-4116-b1ce-9cbebc452d6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5901508b-f118-4116-b1ce-9cbebc452d6c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142838351300, "endTime": 13142838365900}, "additional": {"logType": "info", "children": [], "durationId": "24907d3d-23de-4af8-94c5-0d703397549a", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "6559afab-cd3d-4635-8a79-adb0ca66a126", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142838423000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7120eb-d5de-4ddc-a8ab-c7d2f68a5959", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142839104300, "endTime": 13142839119300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "82e9b7e1-bd2f-4c93-ab5f-0182c4430f66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82e9b7e1-bd2f-4c93-ab5f-0182c4430f66", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142839104300, "endTime": 13142839119300}, "additional": {"logType": "info", "children": [], "durationId": "ff7120eb-d5de-4ddc-a8ab-c7d2f68a5959", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "070c6d06-f017-4eba-9a7b-15b5adb37a94", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142839170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79317781-4061-4545-9783-22f571606339", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13142839853700, "endTime": 13142839867500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "02f2b18f-f998-4eeb-b75c-81ab77404372"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02f2b18f-f998-4eeb-b75c-81ab77404372", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142839853700, "endTime": 13142839867500}, "additional": {"logType": "info", "children": [], "durationId": "79317781-4061-4545-9783-22f571606339", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "1541ca24-a68d-475f-a43a-ee82d9670641", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13132768745100, "endTime": 13142840138400}, "additional": {"logType": "info", "children": ["d902cd2f-0966-4095-9a0d-09fd9bd658e3", "80142b75-b6cb-4d5c-9d65-5b6f8edd0205", "e84f26a4-ef65-420d-b095-d89010534799", "7bbd8365-fc24-4d3e-b02c-87fff9a4abaa", "dc4f8083-7c12-44d6-8aa1-569e19a58980", "5edd97ca-a29b-47ec-9c40-696db4c8f685", "5901508b-f118-4116-b1ce-9cbebc452d6c", "82e9b7e1-bd2f-4c93-ab5f-0182c4430f66", "02f2b18f-f998-4eeb-b75c-81ab77404372", "5902c966-a820-4088-aa13-f495ce2d80ed", "35c594a2-e1bb-4a6b-ab15-408644ba272b", "ee420a65-37ab-41b5-82bf-8ef54203b52b", "0eb1f296-4cc8-4bce-a0e3-7f05faf6b28d"], "durationId": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "parent": "1cbe44ad-5210-4dc8-a124-cb8ecc1a575e"}}, {"head": {"id": "ecbecdd4-546a-4300-92b5-377e3d85894b", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13135099885100, "endTime": 13136167613400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "5902c966-a820-4088-aa13-f495ce2d80ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5902c966-a820-4088-aa13-f495ce2d80ed", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13135099885100, "endTime": 13136167613400}, "additional": {"logType": "info", "children": [], "durationId": "ecbecdd4-546a-4300-92b5-377e3d85894b", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "c200d33c-4736-4ffa-884d-0ff2ac3c0f92", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13136167835600, "endTime": 13136229964800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "35c594a2-e1bb-4a6b-ab15-408644ba272b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35c594a2-e1bb-4a6b-ab15-408644ba272b", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13136167835600, "endTime": 13136229964800}, "additional": {"logType": "info", "children": [], "durationId": "c200d33c-4736-4ffa-884d-0ff2ac3c0f92", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "99db16aa-ac28-4bd4-b0c3-bf2bebb6b69e", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13136230081500, "endTime": 13136230369400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "ee420a65-37ab-41b5-82bf-8ef54203b52b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee420a65-37ab-41b5-82bf-8ef54203b52b", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13136230081500, "endTime": 13136230369400}, "additional": {"logType": "info", "children": [], "durationId": "99db16aa-ac28-4bd4-b0c3-bf2bebb6b69e", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "52f68cb9-f74c-4d30-bd9b-513f5155f375", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Worker1", "startTime": 13136230444500, "endTime": 13142833571500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c2d0f19a-2257-49eb-bccd-8a7d56a57781", "logId": "0eb1f296-4cc8-4bce-a0e3-7f05faf6b28d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0eb1f296-4cc8-4bce-a0e3-7f05faf6b28d", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13136230444500, "endTime": 13142833571500}, "additional": {"logType": "info", "children": [], "durationId": "52f68cb9-f74c-4d30-bd9b-513f5155f375", "parent": "1541ca24-a68d-475f-a43a-ee82d9670641"}}, {"head": {"id": "1cbe44ad-5210-4dc8-a124-cb8ecc1a575e", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13132720436100, "endTime": 13142843124100, "totalTime": 10122618200}, "additional": {"logType": "info", "children": ["1541ca24-a68d-475f-a43a-ee82d9670641"], "durationId": "f980ec0e-c1de-4eae-a6e7-7166a4f8d63e"}}, {"head": {"id": "b2803c9f-0dae-43ed-94b9-3b71e0b79838", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142848708700, "endTime": 13142849017500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "55c53ad0-5445-403d-87de-712d98aa7631", "logId": "0d0d79df-436e-4692-9f1e-36e4c48e40f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55c53ad0-5445-403d-87de-712d98aa7631", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142848653200}, "additional": {"logType": "detail", "children": [], "durationId": "b2803c9f-0dae-43ed-94b9-3b71e0b79838"}}, {"head": {"id": "2dedeb00-9864-4e47-be5a-9c8a43c26c6c", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142848722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe81d04-85e5-40f4-95d5-62109714e538", "name": "entry : PreviewBuild cost memory 0.01202392578125", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142848866500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60da13cc-b737-4409-ac1c-e6549d1a42e7", "name": "runTaskFromQueue task cost before running: 11 s 41 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142848957200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0d79df-436e-4692-9f1e-36e4c48e40f1", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142848708700, "endTime": 13142849017500, "totalTime": 224400}, "additional": {"logType": "info", "children": [], "durationId": "b2803c9f-0dae-43ed-94b9-3b71e0b79838"}}, {"head": {"id": "26d2ac2d-213b-4094-b62e-9520bdaab269", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142863029100, "endTime": 13142863053300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "872f61f4-3fb4-472f-a0ec-dfd421e174cb", "logId": "b31e5b7b-41e2-4dc7-a540-b19b9ef7e3d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b31e5b7b-41e2-4dc7-a540-b19b9ef7e3d7", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142863029100, "endTime": 13142863053300}, "additional": {"logType": "info", "children": [], "durationId": "26d2ac2d-213b-4094-b62e-9520bdaab269"}}, {"head": {"id": "d94b8063-c934-4972-85c4-e3bfa44be3f5", "name": "BUILD SUCCESSFUL in 11 s 56 ms ", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142863160000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "9772a7a3-56af-4c0d-8554-dc35c9f6ca94", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13131807969800, "endTime": 13142863675400}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 18}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "605b6c7d-12e9-4307-9a1b-965ff2362098", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142863837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb3ba97-dd0c-4ad5-8b23-b9d2bcb0c602", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142863922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3029c55-d8c6-444a-a6bd-aff3b8db87d2", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142863979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "676a377d-0a96-4644-ba47-ef582b142de3", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142864029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cedf0a6d-57bf-4ebb-9202-8bd51098e69f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142864085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22e4f32e-abea-4c57-94c0-a43658ac7139", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142864162200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ec4331-a991-49fe-b0b9-6200c64c6f94", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142864223600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "832391f0-a2f1-46dd-ba09-ba886559d258", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142865346500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "895a2c40-fb72-49d0-a48c-a13a1b4439c4", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142882924300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90e6e6e1-eb94-46dd-9612-414a6aa278f2", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142886090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27960cde-d93d-4ea0-8a2b-c69c791c81a0", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142886609900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20b4ed11-37c9-41e4-a9b0-56eef95e8f47", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142907356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf307db0-f7d3-446e-8488-67f13d52dd9f", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:45 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142908325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc4448b2-5507-40aa-9baa-966378d1b1ad", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142908587100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ff2e18-bbe4-4750-9bf1-2d87ec8e9a8c", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142909459000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d995dcff-c34c-4be6-b00a-32a7bab04aea", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142910462400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65310c5e-d5af-453d-8f1e-5355f4b2f0d1", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142911098100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3057b9f-243e-4472-acb4-c55160539478", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142911487700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71af203a-194a-4cb1-9236-e424356622b2", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142911888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55bf50fd-7e7f-41a3-9bb3-f9341ddf39dd", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142915477200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0e56a57-6e8f-473e-9e04-78d3df53cc5a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142916497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f9e59e8-fbe9-472b-9981-d84af5a9079d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142916864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62395695-7d6a-419b-a90f-f131d41b952e", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142937768400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cae8ad0d-aae8-4e8a-bcad-24554d7fe647", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142938808200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42ba1b4-f4ae-4aa8-a95b-4f3a1d90bb70", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142938934500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e08acab-d37a-413e-bbf8-910d88ee80ff", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142939239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a79aec1-35bb-496b-b6a7-c20afc04319a", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142940036700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b886ad-df19-46eb-9e93-f6f1e53bacad", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142944083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e00c475a-20cb-4b25-a2f3-39e9520224ff", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142944459600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "586173ab-985a-4a7e-8ae3-f812214b06fa", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142944793700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976a1725-166f-40ef-a4b3-8c3cccbf2296", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142945315600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2f29b02-0079-4387-adfc-311b97490943", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:34 ms .", "description": "", "type": "log"}, "body": {"pid": 21476, "tid": "Main Thread", "startTime": 13142945721500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}