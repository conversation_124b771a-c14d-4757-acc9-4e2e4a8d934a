{"version": "2.0", "ppid": 13468, "events": [{"head": {"id": "94d11118-12cf-4ac5-bc35-8a30fe1b3a87", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11483095440600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db2d162d-35d9-464a-8c17-4b60e6f0b3c7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11483098095000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c236d204-2528-4656-8c60-42719a8bf996", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11483098405700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32760939-a094-437e-a0b1-b7ffe8e7f188", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11483099161600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f47e06aa-9680-4a4d-9e8d-e43f2b5828cf", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11483099613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac37299c-a584-4f09-98d0-4c40f8fed36b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549871121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549879576300, "endTime": 11550073414800}, "additional": {"children": ["617fa770-a8db-403d-9e6b-f0404a033e27", "9a0e3a05-f466-4984-91ca-6a2809f8942a", "7392790f-0be2-41e3-8b1e-9a73640b7bb0", "f129e73b-e5ed-4491-b479-6655cba46fa4", "da2fd98d-a16c-4382-83f0-6b12a5480f41", "6c3e21bb-717f-43f2-8efa-efe9b9aee756", "f1d4fa44-2e0c-41b5-a143-ea8923319e32"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "617fa770-a8db-403d-9e6b-f0404a033e27", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549879584300, "endTime": 11549898188400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "b03938a2-4386-4b59-99aa-ff5cab41336e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549898217200, "endTime": 11550072075700}, "additional": {"children": ["b3cb9b6b-41fb-4f4d-9dbb-beaaff8ffb3e", "d78b23c9-6c09-4a16-86d8-7405d16b071b", "8e3d4804-ea34-4fce-a049-1663d47f7e11", "f628c20b-fc0c-4036-8c82-358cfd1a3da5", "ffba4ae9-2574-42e5-8b52-9f202ad2edf2", "56ab69a8-9732-44d4-a360-09dddf025fb1", "083fe22e-9f61-4770-b93c-63b8ba6d2ef7", "29dbd3ec-ca44-49ee-a1ab-91d21f9c0dde", "613a3cf2-a362-410c-9693-d6a8ee44b5a4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7392790f-0be2-41e3-8b1e-9a73640b7bb0", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550072104300, "endTime": 11550073382700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "990dc6fe-2b60-434d-933f-ec5561c43f38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f129e73b-e5ed-4491-b479-6655cba46fa4", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550073388900, "endTime": 11550073408700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "7310c95c-1303-4f97-950d-21d1e18e77e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da2fd98d-a16c-4382-83f0-6b12a5480f41", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549883270100, "endTime": 11549883324000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "9e96adc2-7db4-4867-808d-07a4cb9389e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e96adc2-7db4-4867-808d-07a4cb9389e2", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549883270100, "endTime": 11549883324000}, "additional": {"logType": "info", "children": [], "durationId": "da2fd98d-a16c-4382-83f0-6b12a5480f41", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "6c3e21bb-717f-43f2-8efa-efe9b9aee756", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549892393300, "endTime": 11549892423000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "79041afc-b716-454c-944d-5663ee21fcb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79041afc-b716-454c-944d-5663ee21fcb8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549892393300, "endTime": 11549892423000}, "additional": {"logType": "info", "children": [], "durationId": "6c3e21bb-717f-43f2-8efa-efe9b9aee756", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "404cd4a6-1aa9-486f-8e5c-212610b2bb7c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549892547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af05e343-a91a-46e8-9644-a44ade72a12a", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549897998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03938a2-4386-4b59-99aa-ff5cab41336e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549879584300, "endTime": 11549898188400}, "additional": {"logType": "info", "children": [], "durationId": "617fa770-a8db-403d-9e6b-f0404a033e27", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "b3cb9b6b-41fb-4f4d-9dbb-beaaff8ffb3e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549907737300, "endTime": 11549907749700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "f3abdfdd-ce6b-40c6-87a8-4bf170606afd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d78b23c9-6c09-4a16-86d8-7405d16b071b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549907772600, "endTime": 11549912385300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "fc376a26-4fc1-4ef0-b633-59a52f149c37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e3d4804-ea34-4fce-a049-1663d47f7e11", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549912420600, "endTime": 11550002597200}, "additional": {"children": ["3f6343d6-531b-411c-8ce0-b51ace263cc1", "a7049211-73fb-4bd5-8e3e-9934348fe062", "e26630f0-e50c-4553-8021-01cd264f872c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "4edc6eec-ed54-4916-b530-a0faa14b4dc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f628c20b-fc0c-4036-8c82-358cfd1a3da5", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550002614500, "endTime": 11550027547400}, "additional": {"children": ["1f1f3274-9cd6-42fd-80e0-18919b2389bf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "bcf1dedc-f93d-4559-a9e6-8d268228234e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffba4ae9-2574-42e5-8b52-9f202ad2edf2", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550027556000, "endTime": 11550042966900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "a3110af1-5a6b-44a9-925b-1c0b4e6258d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56ab69a8-9732-44d4-a360-09dddf025fb1", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550044871600, "endTime": 11550054819000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "40eccfbe-33dd-43c4-baf5-49c5f14d54c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "083fe22e-9f61-4770-b93c-63b8ba6d2ef7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550054847700, "endTime": 11550071900800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "c12d0cae-0953-4277-96b6-6c6d3d5222ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29dbd3ec-ca44-49ee-a1ab-91d21f9c0dde", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550071923200, "endTime": 11550072061500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "7663170e-8531-4ce0-8112-cbce3b8eba0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3abdfdd-ce6b-40c6-87a8-4bf170606afd", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549907737300, "endTime": 11549907749700}, "additional": {"logType": "info", "children": [], "durationId": "b3cb9b6b-41fb-4f4d-9dbb-beaaff8ffb3e", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "fc376a26-4fc1-4ef0-b633-59a52f149c37", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549907772600, "endTime": 11549912385300}, "additional": {"logType": "info", "children": [], "durationId": "d78b23c9-6c09-4a16-86d8-7405d16b071b", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "3f6343d6-531b-411c-8ce0-b51ace263cc1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549913150000, "endTime": 11549913169300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e3d4804-ea34-4fce-a049-1663d47f7e11", "logId": "e605730c-6e1a-4154-a1d7-235863cb571e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e605730c-6e1a-4154-a1d7-235863cb571e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549913150000, "endTime": 11549913169300}, "additional": {"logType": "info", "children": [], "durationId": "3f6343d6-531b-411c-8ce0-b51ace263cc1", "parent": "4edc6eec-ed54-4916-b530-a0faa14b4dc0"}}, {"head": {"id": "a7049211-73fb-4bd5-8e3e-9934348fe062", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549916441600, "endTime": 11550001579100}, "additional": {"children": ["0873136b-54bf-4709-b813-db549942b063", "4719d1b2-10bf-4aff-b9f1-362e5f8e17e6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e3d4804-ea34-4fce-a049-1663d47f7e11", "logId": "61b67712-3e9b-4418-ab24-c53002d71262"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0873136b-54bf-4709-b813-db549942b063", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549916443000, "endTime": 11549923547300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7049211-73fb-4bd5-8e3e-9934348fe062", "logId": "c2d6dbe5-aa97-4c9e-9ed0-f5361f097f09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4719d1b2-10bf-4aff-b9f1-362e5f8e17e6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549923573900, "endTime": 11550001566200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7049211-73fb-4bd5-8e3e-9934348fe062", "logId": "199c89bd-025b-4c19-ba07-621843aa9303"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9023c16-e360-40f5-bbab-998e6b112798", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549916451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7279e2b-53bc-4ffb-8e74-0bd823fd636b", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549923358900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d6dbe5-aa97-4c9e-9ed0-f5361f097f09", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549916443000, "endTime": 11549923547300}, "additional": {"logType": "info", "children": [], "durationId": "0873136b-54bf-4709-b813-db549942b063", "parent": "61b67712-3e9b-4418-ab24-c53002d71262"}}, {"head": {"id": "ed1a9543-59d4-43d6-bf06-9625ed4b94e6", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549923596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2adf0bd7-28c2-48d7-8bcb-20b091bc735b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549932641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb854b4d-6a97-434a-809c-250b82922018", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549932778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d751633c-6296-4a0c-bc24-b483b20341fa", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549933120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e88255-7ae9-4146-b1f9-9a42f3ed23cd", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549933406100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "637181d6-f80c-4adf-898d-a7c047923025", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549935194500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45708a0-65cb-404c-8863-0c05e24d3293", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549940246300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f8e4e67-22f0-45f6-95f4-b8e8e083076c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549952848400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdeefb86-27ff-429c-bb8b-65fa9eed643b", "name": "Sdk init in 37 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549977978200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6b0edf-fcdd-4043-abf2-49215ead740a", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549978125000}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 51}, "markType": "other"}}, {"head": {"id": "fedfcf51-faf3-4536-b7ea-0304e821b87b", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549978170300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 51}, "markType": "other"}}, {"head": {"id": "018ab637-13ac-46e0-b761-7ae6ecbf3317", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550001252600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3923242-ce60-4552-ac67-6c9708989640", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550001393400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f7b33f-59d9-4b8c-998f-b9ffc98011a1", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550001462000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526b2312-b84c-4861-bf1f-14b6cda26a23", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550001517700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199c89bd-025b-4c19-ba07-621843aa9303", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549923573900, "endTime": 11550001566200}, "additional": {"logType": "info", "children": [], "durationId": "4719d1b2-10bf-4aff-b9f1-362e5f8e17e6", "parent": "61b67712-3e9b-4418-ab24-c53002d71262"}}, {"head": {"id": "61b67712-3e9b-4418-ab24-c53002d71262", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549916441600, "endTime": 11550001579100}, "additional": {"logType": "info", "children": ["c2d6dbe5-aa97-4c9e-9ed0-f5361f097f09", "199c89bd-025b-4c19-ba07-621843aa9303"], "durationId": "a7049211-73fb-4bd5-8e3e-9934348fe062", "parent": "4edc6eec-ed54-4916-b530-a0faa14b4dc0"}}, {"head": {"id": "e26630f0-e50c-4553-8021-01cd264f872c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550002379800, "endTime": 11550002573000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e3d4804-ea34-4fce-a049-1663d47f7e11", "logId": "623fc16e-8427-4ab0-9a76-404710d0ffe1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "623fc16e-8427-4ab0-9a76-404710d0ffe1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550002379800, "endTime": 11550002573000}, "additional": {"logType": "info", "children": [], "durationId": "e26630f0-e50c-4553-8021-01cd264f872c", "parent": "4edc6eec-ed54-4916-b530-a0faa14b4dc0"}}, {"head": {"id": "4edc6eec-ed54-4916-b530-a0faa14b4dc0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549912420600, "endTime": 11550002597200}, "additional": {"logType": "info", "children": ["e605730c-6e1a-4154-a1d7-235863cb571e", "61b67712-3e9b-4418-ab24-c53002d71262", "623fc16e-8427-4ab0-9a76-404710d0ffe1"], "durationId": "8e3d4804-ea34-4fce-a049-1663d47f7e11", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "1f1f3274-9cd6-42fd-80e0-18919b2389bf", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550003337700, "endTime": 11550027534500}, "additional": {"children": ["66e812d5-6820-44d3-9a30-493c32b83815", "f2eda22f-07ff-4338-a2b3-791b49bff207", "ab398870-b163-4f10-be93-dfd8e31c3dc9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f628c20b-fc0c-4036-8c82-358cfd1a3da5", "logId": "471adb54-d7e3-4618-9fbd-4fd51d9d471b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66e812d5-6820-44d3-9a30-493c32b83815", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550006839200, "endTime": 11550006857700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f1f3274-9cd6-42fd-80e0-18919b2389bf", "logId": "29030ce8-db49-49cb-9b21-b27e3c8d908a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29030ce8-db49-49cb-9b21-b27e3c8d908a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550006839200, "endTime": 11550006857700}, "additional": {"logType": "info", "children": [], "durationId": "66e812d5-6820-44d3-9a30-493c32b83815", "parent": "471adb54-d7e3-4618-9fbd-4fd51d9d471b"}}, {"head": {"id": "f2eda22f-07ff-4338-a2b3-791b49bff207", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550009537500, "endTime": 11550026101600}, "additional": {"children": ["ef08d77f-b06e-48d4-807e-8ce25b0f1efd", "6dcbeb7e-face-4cea-8d9e-72e243b89bfa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f1f3274-9cd6-42fd-80e0-18919b2389bf", "logId": "ae0a7350-1c45-47d0-a0ee-33bc23c51edb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef08d77f-b06e-48d4-807e-8ce25b0f1efd", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550009538600, "endTime": 11550013788800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2eda22f-07ff-4338-a2b3-791b49bff207", "logId": "c23da6d7-fad9-4d24-9309-af3d00a88c5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dcbeb7e-face-4cea-8d9e-72e243b89bfa", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550013804700, "endTime": 11550026089900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2eda22f-07ff-4338-a2b3-791b49bff207", "logId": "960daf27-372f-4fea-8ff4-af2d87301d05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cacb9b11-6f61-43bf-93a8-f94d6ad78da0", "name": "hvigorfile, resolving D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550009576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01112d4-b782-4fe6-8dfb-aca16005ba31", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550013670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c23da6d7-fad9-4d24-9309-af3d00a88c5d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550009538600, "endTime": 11550013788800}, "additional": {"logType": "info", "children": [], "durationId": "ef08d77f-b06e-48d4-807e-8ce25b0f1efd", "parent": "ae0a7350-1c45-47d0-a0ee-33bc23c51edb"}}, {"head": {"id": "4e5d3894-01ff-4fb4-ae75-e892f6cb00c2", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550013820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f5203c-09a9-4fb9-b8a0-f9abaf198ed9", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550021778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6260cb0c-d089-4e32-89ea-7a5ed3b190b4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550021936200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5ed2524-d363-4ede-bf61-cc5fa011f5f9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550022414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25527bf4-4a41-44de-a6cc-08bb153bce43", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550022591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756e6a6f-21c1-4a9f-8cb2-b62507f8a0e6", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550022661000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b42c564-f13a-4c1b-9731-7df03a0cc0aa", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550022712800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25f81d68-9b2d-4b5e-9e83-fac3d2d47137", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550022770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "941c4c60-04d8-4ef0-be68-cadb79a5b612", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550025810500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165c70ef-4615-4fe5-b7b7-f85ff5b5a080", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550025928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0eac50a-29a7-4f32-8a4e-f4a90f8d4046", "name": "hvigorfile, no custom plugins were found in D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550025990000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07eeadda-9705-4a7d-b5ef-f264cadf3f97", "name": "hvigorfile, resolve finished D:\\HarmonyOSProject\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550026040800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "960daf27-372f-4fea-8ff4-af2d87301d05", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550013804700, "endTime": 11550026089900}, "additional": {"logType": "info", "children": [], "durationId": "6dcbeb7e-face-4cea-8d9e-72e243b89bfa", "parent": "ae0a7350-1c45-47d0-a0ee-33bc23c51edb"}}, {"head": {"id": "ae0a7350-1c45-47d0-a0ee-33bc23c51edb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550009537500, "endTime": 11550026101600}, "additional": {"logType": "info", "children": ["c23da6d7-fad9-4d24-9309-af3d00a88c5d", "960daf27-372f-4fea-8ff4-af2d87301d05"], "durationId": "f2eda22f-07ff-4338-a2b3-791b49bff207", "parent": "471adb54-d7e3-4618-9fbd-4fd51d9d471b"}}, {"head": {"id": "ab398870-b163-4f10-be93-dfd8e31c3dc9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550027501500, "endTime": 11550027517500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f1f3274-9cd6-42fd-80e0-18919b2389bf", "logId": "8a175f25-b277-485f-a7d0-11b8e11c7ca1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a175f25-b277-485f-a7d0-11b8e11c7ca1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550027501500, "endTime": 11550027517500}, "additional": {"logType": "info", "children": [], "durationId": "ab398870-b163-4f10-be93-dfd8e31c3dc9", "parent": "471adb54-d7e3-4618-9fbd-4fd51d9d471b"}}, {"head": {"id": "471adb54-d7e3-4618-9fbd-4fd51d9d471b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550003337700, "endTime": 11550027534500}, "additional": {"logType": "info", "children": ["29030ce8-db49-49cb-9b21-b27e3c8d908a", "ae0a7350-1c45-47d0-a0ee-33bc23c51edb", "8a175f25-b277-485f-a7d0-11b8e11c7ca1"], "durationId": "1f1f3274-9cd6-42fd-80e0-18919b2389bf", "parent": "bcf1dedc-f93d-4559-a9e6-8d268228234e"}}, {"head": {"id": "bcf1dedc-f93d-4559-a9e6-8d268228234e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550002614500, "endTime": 11550027547400}, "additional": {"logType": "info", "children": ["471adb54-d7e3-4618-9fbd-4fd51d9d471b"], "durationId": "f628c20b-fc0c-4036-8c82-358cfd1a3da5", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "8c0b7926-7a1c-418a-99e0-cf19718c5fbe", "name": "watch files: [\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550042223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c891258-8aca-438f-902e-fb4274f39263", "name": "hvigorfile, resolve hvigorfile dependencies in 16 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550042840100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3110af1-5a6b-44a9-925b-1c0b4e6258d1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550027556000, "endTime": 11550042966900}, "additional": {"logType": "info", "children": [], "durationId": "ffba4ae9-2574-42e5-8b52-9f202ad2edf2", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "613a3cf2-a362-410c-9693-d6a8ee44b5a4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550044505700, "endTime": 11550044843200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "logId": "3975921b-f3a7-4fd5-b540-526b468f9d76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f61eb9a2-e40a-422a-9fbb-f64c548cd400", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550044555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3975921b-f3a7-4fd5-b540-526b468f9d76", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550044505700, "endTime": 11550044843200}, "additional": {"logType": "info", "children": [], "durationId": "613a3cf2-a362-410c-9693-d6a8ee44b5a4", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "1d30c2af-9fb2-4cdb-a79f-a862e9551cce", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550046852400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b3292b-b101-4081-9626-474e55c6284b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550053884800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40eccfbe-33dd-43c4-baf5-49c5f14d54c1", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550044871600, "endTime": 11550054819000}, "additional": {"logType": "info", "children": [], "durationId": "56ab69a8-9732-44d4-a360-09dddf025fb1", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "72bb73c6-2595-4336-94c5-537c18549ccd", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550054868700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01cd2b5-9115-40f7-ada6-985a6391f339", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550061801000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c762db-2065-46b1-bbc3-035795308988", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550061934100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03bc272-ddcb-4d85-b67d-873d47eee53e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550062198700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08771021-114d-4ca4-8e96-4a18ed1204b2", "name": "Module entry Collected Dependency: D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550068424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d43df5-91d6-497e-a1f4-bab3f767b081", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550068577600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12d0cae-0953-4277-96b6-6c6d3d5222ea", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550054847700, "endTime": 11550071900800}, "additional": {"logType": "info", "children": [], "durationId": "083fe22e-9f61-4770-b93c-63b8ba6d2ef7", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "081917db-292d-46e6-ad0f-b84bb3778b54", "name": "Configuration phase cost:165 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550071959300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7663170e-8531-4ce0-8112-cbce3b8eba0a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550071923200, "endTime": 11550072061500}, "additional": {"logType": "info", "children": [], "durationId": "29dbd3ec-ca44-49ee-a1ab-91d21f9c0dde", "parent": "1e5acc99-b185-49f8-b90e-d9d0026e9654"}}, {"head": {"id": "1e5acc99-b185-49f8-b90e-d9d0026e9654", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549898217200, "endTime": 11550072075700}, "additional": {"logType": "info", "children": ["f3abdfdd-ce6b-40c6-87a8-4bf170606afd", "fc376a26-4fc1-4ef0-b633-59a52f149c37", "4edc6eec-ed54-4916-b530-a0faa14b4dc0", "bcf1dedc-f93d-4559-a9e6-8d268228234e", "a3110af1-5a6b-44a9-925b-1c0b4e6258d1", "40eccfbe-33dd-43c4-baf5-49c5f14d54c1", "c12d0cae-0953-4277-96b6-6c6d3d5222ea", "7663170e-8531-4ce0-8112-cbce3b8eba0a", "3975921b-f3a7-4fd5-b540-526b468f9d76"], "durationId": "9a0e3a05-f466-4984-91ca-6a2809f8942a", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "f1d4fa44-2e0c-41b5-a143-ea8923319e32", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550073352200, "endTime": 11550073365800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3cce834c-a041-44d8-aac1-fc84fa6df5bf", "logId": "9aed9ef7-ffa1-41f1-a57b-28543b0abaab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9aed9ef7-ffa1-41f1-a57b-28543b0abaab", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550073352200, "endTime": 11550073365800}, "additional": {"logType": "info", "children": [], "durationId": "f1d4fa44-2e0c-41b5-a143-ea8923319e32", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "990dc6fe-2b60-434d-933f-ec5561c43f38", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550072104300, "endTime": 11550073382700}, "additional": {"logType": "info", "children": [], "durationId": "7392790f-0be2-41e3-8b1e-9a73640b7bb0", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "7310c95c-1303-4f97-950d-21d1e18e77e9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550073388900, "endTime": 11550073408700}, "additional": {"logType": "info", "children": [], "durationId": "f129e73b-e5ed-4491-b479-6655cba46fa4", "parent": "3faeba83-8fa3-42bd-81a9-cd30224cfb08"}}, {"head": {"id": "3faeba83-8fa3-42bd-81a9-cd30224cfb08", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549879576300, "endTime": 11550073414800}, "additional": {"logType": "info", "children": ["b03938a2-4386-4b59-99aa-ff5cab41336e", "1e5acc99-b185-49f8-b90e-d9d0026e9654", "990dc6fe-2b60-434d-933f-ec5561c43f38", "7310c95c-1303-4f97-950d-21d1e18e77e9", "9e96adc2-7db4-4867-808d-07a4cb9389e2", "79041afc-b716-454c-944d-5663ee21fcb8", "9aed9ef7-ffa1-41f1-a57b-28543b0abaab"], "durationId": "3cce834c-a041-44d8-aac1-fc84fa6df5bf"}}, {"head": {"id": "49fda6b9-9678-4571-b2a6-05f5188aca12", "name": "Configuration task cost before running: 199 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550073828200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2839cd0e-cb8a-40f9-a30c-c7d294f5e69d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550081929100, "endTime": 11550095674600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e4b362b5-2957-4056-b7ef-4614923c99a2", "logId": "c0466be4-f5d1-4cce-b7f8-a1f16d25320a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4b362b5-2957-4056-b7ef-4614923c99a2", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550075811800}, "additional": {"logType": "detail", "children": [], "durationId": "2839cd0e-cb8a-40f9-a30c-c7d294f5e69d"}}, {"head": {"id": "2e09d296-1aaa-4988-a5f1-b33088f8c8b4", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550076821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a876cdb-a69c-4048-91c5-0bdd2306cbef", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550076935100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df561a4b-64cd-4184-ab01-00c042ad04b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550077001300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a258224c-ddaf-4771-bc41-ee72911d4688", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550081944900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df2aefe1-fb91-4958-accd-135c82942abc", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550095303100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed93e60-b002-4a91-a467-b41507586d1e", "name": "entry : default@PreBuild cost memory 0.4188079833984375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550095536000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0466be4-f5d1-4cce-b7f8-a1f16d25320a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550081929100, "endTime": 11550095674600}, "additional": {"logType": "info", "children": [], "durationId": "2839cd0e-cb8a-40f9-a30c-c7d294f5e69d"}}, {"head": {"id": "d6071c15-61ef-4f13-b440-83c1032f3a72", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550102478600, "endTime": 11550107140500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1cb1b2aa-6a79-41f7-a234-dc9be8b96b8b", "logId": "faaa3689-1afc-4301-bf95-b1ef1a0cbe44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cb1b2aa-6a79-41f7-a234-dc9be8b96b8b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550100382400}, "additional": {"logType": "detail", "children": [], "durationId": "d6071c15-61ef-4f13-b440-83c1032f3a72"}}, {"head": {"id": "6f10ea9a-a994-4ac8-b9d5-34966fb3c089", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550101073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a74e056-2809-4e99-8e86-0f5351c4f51b", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550101229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "181cc876-67c1-4c9f-8429-f3f64c2c51cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550101320100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6f944c-fa9b-47bd-8fcf-51d1952b68c7", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550102497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebac2f22-321e-4880-9378-bd9e7969506b", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550106931100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008ce34d-ced5-4dc1-bd1e-be811fd52732", "name": "entry : default@MergeProfile cost memory 0.1342315673828125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550107060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faaa3689-1afc-4301-bf95-b1ef1a0cbe44", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550102478600, "endTime": 11550107140500}, "additional": {"logType": "info", "children": [], "durationId": "d6071c15-61ef-4f13-b440-83c1032f3a72"}}, {"head": {"id": "25560d69-361c-4b5e-8d7d-5d13e896e736", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550110786000, "endTime": 11550113770500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "61514ba6-b7e0-4729-851e-6d39261382c8", "logId": "dbf597f2-7e43-4ffd-b694-ed8f0b07b8f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61514ba6-b7e0-4729-851e-6d39261382c8", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550109012400}, "additional": {"logType": "detail", "children": [], "durationId": "25560d69-361c-4b5e-8d7d-5d13e896e736"}}, {"head": {"id": "df759074-427d-48c7-ba6a-6c915b2e9f18", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550109590800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2195b48d-cf6f-4a6a-a13b-5d699f4af869", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550109701000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2cb1207-1de3-404a-8aab-7bac0a79f955", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550109761200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51c9cdf-99d7-45fe-aece-6af9c7b41c48", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550110799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2dedbc3-7350-46c1-8d5f-ed34523bf28b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550112115500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89d459ff-9ebc-4a24-90c7-33a6447a0062", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550113591200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a3bac3-8a68-4f9c-a352-29b8e7a3ab7f", "name": "entry : default@CreateBuildProfile cost memory 0.10507965087890625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550113698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf597f2-7e43-4ffd-b694-ed8f0b07b8f8", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550110786000, "endTime": 11550113770500}, "additional": {"logType": "info", "children": [], "durationId": "25560d69-361c-4b5e-8d7d-5d13e896e736"}}, {"head": {"id": "26c3bbdc-9d4c-4b5d-b024-4c6703455621", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550116893100, "endTime": 11550117563100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d43a2329-0d2a-4075-9e92-2bff46f3e230", "logId": "250f1a53-2866-4717-b060-d598fd700bd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d43a2329-0d2a-4075-9e92-2bff46f3e230", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550115472300}, "additional": {"logType": "detail", "children": [], "durationId": "26c3bbdc-9d4c-4b5d-b024-4c6703455621"}}, {"head": {"id": "b88ce3cb-a49c-4f22-a487-b682ddfce76a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550116009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca712f3-01fa-43ee-95c5-3717f746e470", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550116093800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "accc673c-7654-42fb-8666-d1c9d762d8a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550116148800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037a5153-f766-4a86-9333-89f34376e67a", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550116905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9acd3084-f6cf-4126-9a3e-1babb7b103e0", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550117094200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751f131f-e465-45dd-a3b7-7197f434b5ef", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550117161100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "520b5b3c-439a-4967-afd6-8e4a186e472d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550117209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3704c9cd-8f59-4280-91a5-ba9dd444b423", "name": "entry : default@PreCheckSyscap cost memory 0.05062103271484375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550117400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9058a040-580c-4258-b1d2-b2236d82c06c", "name": "runTaskFromQueue task cost before running: 243 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550117504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250f1a53-2866-4717-b060-d598fd700bd5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550116893100, "endTime": 11550117563100, "totalTime": 591000}, "additional": {"logType": "info", "children": [], "durationId": "26c3bbdc-9d4c-4b5d-b024-4c6703455621"}}, {"head": {"id": "6f4c0d97-bb7c-4388-a4be-6af6a4078445", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550132734200, "endTime": 11550134357700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "780d66af-e59e-4814-b4e6-a884f8d8856e", "logId": "b703c977-6f34-4c9b-9b07-fcbfb60abd7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "780d66af-e59e-4814-b4e6-a884f8d8856e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550119286700}, "additional": {"logType": "detail", "children": [], "durationId": "6f4c0d97-bb7c-4388-a4be-6af6a4078445"}}, {"head": {"id": "f848ff22-4aa6-471a-a0ac-a1e6bd0b1fd6", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550119834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae53be1-242d-4d2d-b8f8-6f6f58b9fe36", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550119921700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6343ab30-b6d7-4220-aa13-9aae31e85b1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550119977900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "183a88dd-7d6c-456a-ae51-8cba5588f96e", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550132763200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eaeba7c-4d5e-4576-9394-d358784c02e5", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550133133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3abc9e5c-bf23-40b1-b522-92151eb8e86d", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550134110600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c45929-4e27-49b5-ad62-ba6e43fb817f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0708160400390625", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550134260400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b703c977-6f34-4c9b-9b07-fcbfb60abd7f", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550132734200, "endTime": 11550134357700}, "additional": {"logType": "info", "children": [], "durationId": "6f4c0d97-bb7c-4388-a4be-6af6a4078445"}}, {"head": {"id": "88313043-eaa0-44b9-904a-56525df48d65", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550139987900, "endTime": 11550141491800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9eeec723-dd18-4749-9be8-a0df8a4955f2", "logId": "9e929c0f-a032-4553-86c6-eb8ef4d5e5dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9eeec723-dd18-4749-9be8-a0df8a4955f2", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550137463600}, "additional": {"logType": "detail", "children": [], "durationId": "88313043-eaa0-44b9-904a-56525df48d65"}}, {"head": {"id": "c43671c7-a553-4913-93ff-255368c6cac5", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550138066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "515bd0be-8efb-4f02-9f51-5cb37b85a543", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550138168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3152dcb6-72cd-4bde-8a34-a2c1875f9d5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550138228800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "184c46b3-f97e-4d62-b09a-f3481d81450b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550139999700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1124fcb-f1c0-4196-8292-4e9b69e2977a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550141283100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45aeff17-999e-4d66-b633-82c87ad9f880", "name": "entry : default@ProcessProfile cost memory 0.0594482421875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550141385800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e929c0f-a032-4553-86c6-eb8ef4d5e5dc", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550139987900, "endTime": 11550141491800}, "additional": {"logType": "info", "children": [], "durationId": "88313043-eaa0-44b9-904a-56525df48d65"}}, {"head": {"id": "fc30bcf8-af62-4489-ba37-7511a00023e8", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550146197100, "endTime": 11550153664700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7be4f97d-ac7d-4697-ba86-25a711d86c0e", "logId": "8f0b0191-ef40-4947-b660-612a96a35e4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7be4f97d-ac7d-4697-ba86-25a711d86c0e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550143467400}, "additional": {"logType": "detail", "children": [], "durationId": "fc30bcf8-af62-4489-ba37-7511a00023e8"}}, {"head": {"id": "5f0e5dbe-cdad-4637-8422-11380ee3fcf7", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550144095200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c887f9f-0c45-4a67-a730-a185fbbc90c3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550144216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212ed3d0-a078-4efe-96c0-ede13fdaba93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550144281100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1d65c2-bb1d-4922-9015-3fd7ccf9b9d6", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550146213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa56df5-547c-469a-8669-c47b2ad680d2", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550153413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2b47c6-4f69-448e-b6c9-f734669b6de3", "name": "entry : default@ProcessRouterMap cost memory 0.21765899658203125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550153567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0b0191-ef40-4947-b660-612a96a35e4a", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550146197100, "endTime": 11550153664700}, "additional": {"logType": "info", "children": [], "durationId": "fc30bcf8-af62-4489-ba37-7511a00023e8"}}, {"head": {"id": "af8e4256-277e-4993-826c-4a976f1d3cf0", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550162225900, "endTime": 11550166517500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "44ed60dd-86aa-49e3-b40d-9b223e5e66b0", "logId": "2e1ad5a4-2241-4464-a1fe-49583e111c71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44ed60dd-86aa-49e3-b40d-9b223e5e66b0", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550157105500}, "additional": {"logType": "detail", "children": [], "durationId": "af8e4256-277e-4993-826c-4a976f1d3cf0"}}, {"head": {"id": "c1be25ba-f25d-4e8a-b783-917e5bd76ad9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550157699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c92dd2d-70c6-434d-a8c0-d7eb70af20f3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550157807400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f90ebd3c-4a74-4dd7-87c0-16c2e8972b13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550157866000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab632bfc-6bf7-4eb9-a3ce-4366c66a727a", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550159352300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cbab41-4d46-493b-b741-521706edb9e0", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550164416700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "577f3445-ed86-4fec-9b3b-b8161ac60aff", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550164595200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c236ef-13b9-4605-be30-fa19e78c344d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550164661700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0620d6-83fa-419c-a97e-b9c7d4f3c45c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550164713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a7367b-34e6-44a4-bac9-928c219056ca", "name": "entry : default@PreviewProcessResource cost memory 0.09613037109375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550164805800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f17030-6bb1-411f-b5bf-c83cd2e58343", "name": "runTaskFromQueue task cost before running: 291 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550166420900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1ad5a4-2241-4464-a1fe-49583e111c71", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550162225900, "endTime": 11550166517500, "totalTime": 2650800}, "additional": {"logType": "info", "children": [], "durationId": "af8e4256-277e-4993-826c-4a976f1d3cf0"}}, {"head": {"id": "61113ba9-dba6-456c-bce1-a947d2ad4f4e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550175501600, "endTime": 11550201826400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4a17bd46-ba2d-4821-b55c-6e9862454ef7", "logId": "73991b96-52ef-4d95-994a-7efdf588ab4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a17bd46-ba2d-4821-b55c-6e9862454ef7", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550170080600}, "additional": {"logType": "detail", "children": [], "durationId": "61113ba9-dba6-456c-bce1-a947d2ad4f4e"}}, {"head": {"id": "8a323a42-1c22-40e8-9574-735c64680f5f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550170695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5986d3c4-2ce5-4c6c-9a53-285ac6c6a1f8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550170801800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316204dc-6498-4e48-ada2-4fb08e89fab5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550170866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "682e72c2-f789-4d40-a1b5-88bc0d30abca", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550175521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c229830b-ead8-4555-a346-8fcb4f7dea24", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550201559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ea8a65-4659-4f6c-86ae-87710d869ed7", "name": "entry : default@GenerateLoaderJson cost memory -0.7922134399414062", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550201736200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73991b96-52ef-4d95-994a-7efdf588ab4f", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550175501600, "endTime": 11550201826400}, "additional": {"logType": "info", "children": [], "durationId": "61113ba9-dba6-456c-bce1-a947d2ad4f4e"}}, {"head": {"id": "bc093b88-e57d-4f82-b01e-53abdf7bd003", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550215951200, "endTime": 11550951077400}, "additional": {"children": ["9391483c-098a-4d3c-a4e4-68f9fc7db796", "35ad62c9-5faa-4df1-bda6-24611a5fd9b4", "619408af-1ed2-454a-a892-fc194cdf5e91", "1feda2d6-f5aa-44f5-9a08-709ce8e6fc68", "d7aa5ca8-3956-4421-8436-6538c9dc8f0b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "ce11e747-dcc4-4238-adb9-c513751ebf36", "logId": "8fa87b28-dfa8-4baa-8b8b-1e7834950157"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce11e747-dcc4-4238-adb9-c513751ebf36", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550210629600}, "additional": {"logType": "detail", "children": [], "durationId": "bc093b88-e57d-4f82-b01e-53abdf7bd003"}}, {"head": {"id": "0dddca2b-1f9c-48b8-8a02-c5806798c17e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550211305000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3735fd50-4dba-4dd2-b6ab-9a5e3dfc610d", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550211451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f880c22b-89f6-4997-82eb-719e5850a5c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550211567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f1b27c-8bde-4ebe-b210-cd8d162fdbe9", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550212939900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb97e96-970b-4a9d-8d8e-9c928f5b44b0", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550216070000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc79ecdc-a9e7-44ac-8643-d1e539f4acb5", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550277205500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc22fe97-e65b-46a3-b46a-175cdc3ded33", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 61 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550277419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9391483c-098a-4d3c-a4e4-68f9fc7db796", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550278746100, "endTime": 11550309999700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc093b88-e57d-4f82-b01e-53abdf7bd003", "logId": "260336d5-d8c3-4951-bfa5-e77288281ab8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "260336d5-d8c3-4951-bfa5-e77288281ab8", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550278746100, "endTime": 11550309999700}, "additional": {"logType": "info", "children": [], "durationId": "9391483c-098a-4d3c-a4e4-68f9fc7db796", "parent": "8fa87b28-dfa8-4baa-8b8b-1e7834950157"}}, {"head": {"id": "05806777-2e1f-4123-a9b0-0799ff5ad6c1", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550310837900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35ad62c9-5faa-4df1-bda6-24611a5fd9b4", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550312099500, "endTime": 11550453272300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc093b88-e57d-4f82-b01e-53abdf7bd003", "logId": "372b18e1-d111-4d3a-baf7-e46856f8eb21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e587516-e25d-4719-a72b-e37ee1675399", "name": "current process  memoryUsage: {\n  rss: 161021952,\n  heapTotal: 114888704,\n  heapUsed: 107118288,\n  external: 3099655,\n  arrayBuffers: 93522\n} os memoryUsage :11.735076904296875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550313669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04622a2-12ac-4281-a51f-b4c58fbcf643", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550450597400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372b18e1-d111-4d3a-baf7-e46856f8eb21", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550312099500, "endTime": 11550453272300}, "additional": {"logType": "info", "children": [], "durationId": "35ad62c9-5faa-4df1-bda6-24611a5fd9b4", "parent": "8fa87b28-dfa8-4baa-8b8b-1e7834950157"}}, {"head": {"id": "2e5b2ca4-7251-4967-ae91-4a32c3142391", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550454298200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "619408af-1ed2-454a-a892-fc194cdf5e91", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550455695900, "endTime": 11550621904100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc093b88-e57d-4f82-b01e-53abdf7bd003", "logId": "167ec6a2-f367-46e5-b016-fc784b928898"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4123147-46c8-457a-97af-d79d5d2afa6d", "name": "current process  memoryUsage: {\n  rss: 161271808,\n  heapTotal: 115412992,\n  heapUsed: 107997968,\n  external: 3147101,\n  arrayBuffers: 85258\n} os memoryUsage :11.64046859741211", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550456724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f6368db-6b59-4d5f-b8d5-2a9bc7f24abc", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550618932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167ec6a2-f367-46e5-b016-fc784b928898", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550455695900, "endTime": 11550621904100}, "additional": {"logType": "info", "children": [], "durationId": "619408af-1ed2-454a-a892-fc194cdf5e91", "parent": "8fa87b28-dfa8-4baa-8b8b-1e7834950157"}}, {"head": {"id": "5f96c71a-a545-4f82-a1aa-418a95d2f786", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550622280800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1feda2d6-f5aa-44f5-9a08-709ce8e6fc68", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550623586400, "endTime": 11550759322100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc093b88-e57d-4f82-b01e-53abdf7bd003", "logId": "a7894a24-9314-4db8-a61c-7419f3c144f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21b9e5cd-5bf6-46bb-941c-b51b7a3b5ad5", "name": "current process  memoryUsage: {\n  rss: 161320960,\n  heapTotal: 115412992,\n  heapUsed: 108494048,\n  external: 3099694,\n  arrayBuffers: 93640\n} os memoryUsage :11.590560913085938", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550624608300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8b936f-d29a-4c72-b11c-8a454aefc822", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550756957600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7894a24-9314-4db8-a61c-7419f3c144f3", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550623586400, "endTime": 11550759322100}, "additional": {"logType": "info", "children": [], "durationId": "1feda2d6-f5aa-44f5-9a08-709ce8e6fc68", "parent": "8fa87b28-dfa8-4baa-8b8b-1e7834950157"}}, {"head": {"id": "95a3ed22-d938-4ad6-af5c-5bd115d1b281", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\HarmonyOSProject\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550760141000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7aa5ca8-3956-4421-8436-6538c9dc8f0b", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550761746100, "endTime": 11550948476600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc093b88-e57d-4f82-b01e-53abdf7bd003", "logId": "eda59d03-5b75-48e9-a9c2-5bb7f3225117"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44e0cee9-10e7-4ef0-bd5d-28597252547d", "name": "current process  memoryUsage: {\n  rss: 161378304,\n  heapTotal: 115412992,\n  heapUsed: 108806760,\n  external: 3099820,\n  arrayBuffers: 94685\n} os memoryUsage :11.58780288696289", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550762736900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac36bf95-5743-42f2-b3c3-125620807d81", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550945277400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda59d03-5b75-48e9-a9c2-5bb7f3225117", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550761746100, "endTime": 11550948476600}, "additional": {"logType": "info", "children": [], "durationId": "d7aa5ca8-3956-4421-8436-6538c9dc8f0b", "parent": "8fa87b28-dfa8-4baa-8b8b-1e7834950157"}}, {"head": {"id": "218fec6c-27f6-48b9-9741-83b09e0830e6", "name": "entry : default@PreviewCompileResource cost memory 0.7925262451171875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550950612800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af28ab7-ee8c-4434-9265-653ac8c474de", "name": "runTaskFromQueue task cost before running: 1 s 76 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550950927900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa87b28-dfa8-4baa-8b8b-1e7834950157", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550215951200, "endTime": 11550951077400, "totalTime": 734873000}, "additional": {"logType": "info", "children": ["260336d5-d8c3-4951-bfa5-e77288281ab8", "372b18e1-d111-4d3a-baf7-e46856f8eb21", "167ec6a2-f367-46e5-b016-fc784b928898", "a7894a24-9314-4db8-a61c-7419f3c144f3", "eda59d03-5b75-48e9-a9c2-5bb7f3225117"], "durationId": "bc093b88-e57d-4f82-b01e-53abdf7bd003"}}, {"head": {"id": "b041b8ef-26fd-48a5-9f24-55d20c9fa83f", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955433700, "endTime": 11550955947400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "5e4c84b2-495f-49d8-97a5-b663e38e1351", "logId": "7af70451-b1c6-4bc8-8a74-1825cd0dd05d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e4c84b2-495f-49d8-97a5-b663e38e1351", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550954558700}, "additional": {"logType": "detail", "children": [], "durationId": "b041b8ef-26fd-48a5-9f24-55d20c9fa83f"}}, {"head": {"id": "5389c80c-c815-4039-801d-44c02a86c067", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955149300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cadc065a-db0b-40ba-8392-5b8867d2136e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955256300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "466ee2f8-b057-4a45-b564-82a6954dbfb3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955317800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80c20d8-ec35-4c7f-81de-90592986cfa1", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955447400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e270fbc9-946d-4811-8705-c44987e5bb46", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955553200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d246cf92-d866-489c-90d1-3a0716ee1237", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955607100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0575d527-8346-410e-8b2c-2cb40e344684", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955656900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baec19f3-5b0c-4c7d-b127-365eeacdff33", "name": "entry : default@PreviewHookCompileResource cost memory 0.05193328857421875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc816e4-b3d7-45c7-aa20-76fb7e54f7df", "name": "runTaskFromQueue task cost before running: 1 s 81 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955880800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af70451-b1c6-4bc8-8a74-1825cd0dd05d", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550955433700, "endTime": 11550955947400, "totalTime": 416800}, "additional": {"logType": "info", "children": [], "durationId": "b041b8ef-26fd-48a5-9f24-55d20c9fa83f"}}, {"head": {"id": "bfa56702-dc3b-4f67-afe7-6ee8aa018da7", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550961332300, "endTime": 11550970486900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "1e027710-476e-47ae-9e3b-d937b1178895", "logId": "362cf781-7fbb-42b2-b3cf-da263331bf28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e027710-476e-47ae-9e3b-d937b1178895", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550959340200}, "additional": {"logType": "detail", "children": [], "durationId": "bfa56702-dc3b-4f67-afe7-6ee8aa018da7"}}, {"head": {"id": "f1facbee-d99e-45bc-8a32-657857f72f4c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550960096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8099eb2b-eb74-4094-a25e-2f9cc80c92df", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550960239500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e08a71c-41e0-4533-9ea0-9a544adc52b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550960341900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa674c0-ac59-4535-a682-12d9d6bcf860", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550961351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e776fe6-ca3e-41cf-b11b-61290433d40e", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550963012600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14be854a-7020-4a79-b681-618b2cdd69d7", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550963136800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293ae56e-9e52-4da6-bc60-9e4ce1e4a98c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550963224100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba3e5c7-0e74-44aa-95c5-95671435cf17", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550963279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c35698c-cd2d-4721-92eb-99496d8e6bb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550963327200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09ff0915-4412-4e0b-99a7-902f6c97a69e", "name": "entry : default@CopyPreviewProfile cost memory 0.2299652099609375", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550970198600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d23db8-3667-4e19-9ee5-29bfbda6037b", "name": "runTaskFromQueue task cost before running: 1 s 95 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550970400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362cf781-7fbb-42b2-b3cf-da263331bf28", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550961332300, "endTime": 11550970486900, "totalTime": 9025300}, "additional": {"logType": "info", "children": [], "durationId": "bfa56702-dc3b-4f67-afe7-6ee8aa018da7"}}, {"head": {"id": "d2a198b5-041e-42f3-9860-efc2267f5962", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550974393500, "endTime": 11550975498700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "1cbfffa7-8f99-46cf-8eb9-eff7d8842a37", "logId": "58fcb158-c972-4930-a61f-6285ae7a35c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cbfffa7-8f99-46cf-8eb9-eff7d8842a37", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550972830100}, "additional": {"logType": "detail", "children": [], "durationId": "d2a198b5-041e-42f3-9860-efc2267f5962"}}, {"head": {"id": "6046c49f-ebd4-4d5e-8235-7e0bf609c732", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550973415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daae153b-0395-41e7-a30b-ea0acba09974", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550973526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e473fd3-ba93-4a43-8dba-3484df7969e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550973592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f690b6-44fc-486d-86c0-3ca6e573fe8b", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550974406300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5c92485-a40d-4948-9b23-46e42637becf", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550974557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53650949-04f6-4f79-8e96-291df1672ec8", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550974663900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c89761-6134-4cf8-bad8-20d88ef3c7d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550974777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5463ebc-fe96-431c-b43c-dd7ebef7584d", "name": "entry : default@ReplacePreviewerPage cost memory 0.05194854736328125", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550975208000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb9533f-5756-4e8d-a208-0794f0a95ce4", "name": "runTaskFromQueue task cost before running: 1 s 100 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550975382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58fcb158-c972-4930-a61f-6285ae7a35c8", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550974393500, "endTime": 11550975498700, "totalTime": 945500}, "additional": {"logType": "info", "children": [], "durationId": "d2a198b5-041e-42f3-9860-efc2267f5962"}}, {"head": {"id": "402da574-7946-4d32-9fa0-ef96ec4a705e", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550978210300, "endTime": 11550978618800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0d4890ed-a0e3-4b5f-8712-fdc6c9858dba", "logId": "ae938d7e-aa14-432f-b6b7-f6a4f8cfc85f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d4890ed-a0e3-4b5f-8712-fdc6c9858dba", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550978124700}, "additional": {"logType": "detail", "children": [], "durationId": "402da574-7946-4d32-9fa0-ef96ec4a705e"}}, {"head": {"id": "f3f34dcd-4d16-49f5-a8a9-078c5be514f3", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550978223400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "486bfc92-db74-452a-b76a-7985c3e8e40c", "name": "entry : buildPreviewerResource cost memory 0.01213836669921875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550978431400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ed939d-5e87-4008-a7af-4f82ed5c0543", "name": "runTaskFromQueue task cost before running: 1 s 104 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550978550000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae938d7e-aa14-432f-b6b7-f6a4f8cfc85f", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550978210300, "endTime": 11550978618800, "totalTime": 309500}, "additional": {"logType": "info", "children": [], "durationId": "402da574-7946-4d32-9fa0-ef96ec4a705e"}}, {"head": {"id": "38f9fdb9-0710-48ac-a9f9-5e51d594d413", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550982902700, "endTime": 11550987640700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "f4846e49-c2d6-4862-85a0-d18e3176b31c", "logId": "fd594f64-ad94-46ab-b228-bfe895b82cbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4846e49-c2d6-4862-85a0-d18e3176b31c", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550980836700}, "additional": {"logType": "detail", "children": [], "durationId": "38f9fdb9-0710-48ac-a9f9-5e51d594d413"}}, {"head": {"id": "28722ae7-bc94-4787-85e4-1d5424434208", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550981560200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d67b0eb1-025c-4a99-8577-11f5a0b2ea19", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550981832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "217772ef-e879-47da-beb0-47375988defb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550981935600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25981af4-c18f-47d1-98e8-6e14b04601d6", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550982929100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bdbf64-ecf7-4fa4-a9e6-127ed6b84813", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550985886600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122a4bcd-c216-49d4-9b41-12553a7c07f5", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550986034600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f4e6fc-b01a-42eb-b251-4e421ef94058", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550986148300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a2915b-06dd-454f-a60a-1798b5b8be0c", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550986217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87f260a-1bf1-4e2e-bda9-d493f3effc4d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550986272000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f297598-4a07-4442-993e-112ac011e324", "name": "entry : default@PreviewUpdateAssets cost memory 0.14666748046875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550987417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db2e370-56d8-4f11-ac15-fb59b5833cd7", "name": "runTaskFromQueue task cost before running: 1 s 113 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550987554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd594f64-ad94-46ab-b228-bfe895b82cbf", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550982902700, "endTime": 11550987640700, "totalTime": 4622700}, "additional": {"logType": "info", "children": [], "durationId": "38f9fdb9-0710-48ac-a9f9-5e51d594d413"}}, {"head": {"id": "965724dc-fbf2-4b4c-840a-998f57a15bcd", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550996880600, "endTime": 11562587205400}, "additional": {"children": ["03d1ea3d-0455-4cb4-90b7-ea85fb8fb336"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "b024dfd8-6cf4-4780-9893-0a2a1e26bada", "logId": "bbc53168-aa30-4bfb-8754-8f81c2b930cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b024dfd8-6cf4-4780-9893-0a2a1e26bada", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550990574000}, "additional": {"logType": "detail", "children": [], "durationId": "965724dc-fbf2-4b4c-840a-998f57a15bcd"}}, {"head": {"id": "2b576d83-4c7e-4a77-90cf-a990a305208f", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550991128100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f9b67e-b5d9-440a-bc8c-25ff68499b21", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550991251700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b683dc4-3cfc-4e32-96ab-451f11e02154", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550991313400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6dcb9e6-a726-4102-a100-1d62acbd94fd", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550996896900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d69a4c97-db5c-40db-bfa6-b00f109c8422", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11551032558000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac7b0af-18ca-4ab0-8749-ee48a3fff0a9", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11551032772500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11551052945400, "endTime": 11562582813100}, "additional": {"children": ["ec9adb62-de53-4914-814d-051bf79ef8bf", "970b228f-5d77-4998-ad9c-daa5c4d0d9e3", "351ddf4f-8694-4ee4-96ce-23c1da148473", "e7440b4b-2373-4dcc-bd08-45256b6b567f", "9b563769-8b64-41df-b9c8-a23bdc773a2f", "17a049bf-c3f8-4aae-8c6e-cfe19ed83b0e", "ba0c1541-75c0-423d-987f-5f1ca0129a8f", "58ff8f51-e923-4cc4-937e-beeb694bc21d", "6715178e-d673-4916-bcd5-33b9c95f061e", "efda55be-9446-46d6-9547-759953d32d39", "425b49fd-7207-488a-b10b-34b08bcf288d", "72ee70bc-40e2-4fcd-824a-1b0c8ea0f7c2", "e7674fe7-b02e-4bc6-a149-cf9f8fa4bcc5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "965724dc-fbf2-4b4c-840a-998f57a15bcd", "logId": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "721d923b-accf-4079-8e66-a56de22298bc", "name": "entry : default@PreviewArkTS cost memory -1.1902313232421875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11551056876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a9b3ea-bd16-4524-9080-4b37d85da7ff", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11554755675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9adb62-de53-4914-814d-051bf79ef8bf", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11554756809800, "endTime": 11554756871000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "2a2a5a13-f4d9-4bdd-82a8-d305217509ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a2a5a13-f4d9-4bdd-82a8-d305217509ad", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11554756809800, "endTime": 11554756871000}, "additional": {"logType": "info", "children": [], "durationId": "ec9adb62-de53-4914-814d-051bf79ef8bf", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "bfc41fef-1292-4974-9708-a79abfdfd2d2", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11559729103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "970b228f-5d77-4998-ad9c-daa5c4d0d9e3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11559730064700, "endTime": 11559730084500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "e182d895-6341-468b-98a1-415f30253355"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e182d895-6341-468b-98a1-415f30253355", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11559730064700, "endTime": 11559730084500}, "additional": {"logType": "info", "children": [], "durationId": "970b228f-5d77-4998-ad9c-daa5c4d0d9e3", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "f1732a75-10da-4deb-9f01-a31c76dff7e4", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11559956571900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "351ddf4f-8694-4ee4-96ce-23c1da148473", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11559957867100, "endTime": 11559957890500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "67c8def6-827e-48b9-97fa-12c462b6665e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67c8def6-827e-48b9-97fa-12c462b6665e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11559957867100, "endTime": 11559957890500}, "additional": {"logType": "info", "children": [], "durationId": "351ddf4f-8694-4ee4-96ce-23c1da148473", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "806def06-f0a9-4d45-9d74-ac4a927c5617", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560065455700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7440b4b-2373-4dcc-bd08-45256b6b567f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11560066659700, "endTime": 11560066689000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "354235c0-815c-4b27-a35c-28d896468577"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "354235c0-815c-4b27-a35c-28d896468577", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560066659700, "endTime": 11560066689000}, "additional": {"logType": "info", "children": [], "durationId": "e7440b4b-2373-4dcc-bd08-45256b6b567f", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "32064dca-3c10-41d0-b69b-21b668d6a96c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560226625400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b563769-8b64-41df-b9c8-a23bdc773a2f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11560227719400, "endTime": 11560227746700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "fc898ec5-bde6-4fea-9515-22f18ff8ca47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc898ec5-bde6-4fea-9515-22f18ff8ca47", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560227719400, "endTime": 11560227746700}, "additional": {"logType": "info", "children": [], "durationId": "9b563769-8b64-41df-b9c8-a23bdc773a2f", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "dd9a5444-7d82-47ea-8de7-f605b3b7a07a", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560362135000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a049bf-c3f8-4aae-8c6e-cfe19ed83b0e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11560363512100, "endTime": 11560363538600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "882e19f7-0d4e-4ed2-9559-e0d9f36ef248"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "882e19f7-0d4e-4ed2-9559-e0d9f36ef248", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560363512100, "endTime": 11560363538600}, "additional": {"logType": "info", "children": [], "durationId": "17a049bf-c3f8-4aae-8c6e-cfe19ed83b0e", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "1c4f3dd9-b58d-4fcb-9179-3b6e30ec2638", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560429946100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0c1541-75c0-423d-987f-5f1ca0129a8f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11560431092600, "endTime": 11560431133000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "4008bc25-a74c-463e-af26-293c7a1e0f59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4008bc25-a74c-463e-af26-293c7a1e0f59", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560431092600, "endTime": 11560431133000}, "additional": {"logType": "info", "children": [], "durationId": "ba0c1541-75c0-423d-987f-5f1ca0129a8f", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "6d0ec3d0-5e8c-4558-9cd0-a2e0955f8fc3", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560514168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ff8f51-e923-4cc4-937e-beeb694bc21d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11560515351500, "endTime": 11560515377300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "fc216252-3e31-4760-a91c-5845ed36b099"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc216252-3e31-4760-a91c-5845ed36b099", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11560515351500, "endTime": 11560515377300}, "additional": {"logType": "info", "children": [], "durationId": "58ff8f51-e923-4cc4-937e-beeb694bc21d", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "36ef3d99-8315-44a1-b626-e1e25d978586", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562581229700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6715178e-d673-4916-bcd5-33b9c95f061e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11562582485300, "endTime": 11562582514800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "fcb60530-a962-4446-b22c-51419fa55c1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcb60530-a962-4446-b22c-51419fa55c1c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562582485300, "endTime": 11562582514800}, "additional": {"logType": "info", "children": [], "durationId": "6715178e-d673-4916-bcd5-33b9c95f061e", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "30657374-6cb3-45ee-b3ca-52c13fdd0735", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11551052945400, "endTime": 11562582813100}, "additional": {"logType": "info", "children": ["2a2a5a13-f4d9-4bdd-82a8-d305217509ad", "e182d895-6341-468b-98a1-415f30253355", "67c8def6-827e-48b9-97fa-12c462b6665e", "354235c0-815c-4b27-a35c-28d896468577", "fc898ec5-bde6-4fea-9515-22f18ff8ca47", "882e19f7-0d4e-4ed2-9559-e0d9f36ef248", "4008bc25-a74c-463e-af26-293c7a1e0f59", "fc216252-3e31-4760-a91c-5845ed36b099", "fcb60530-a962-4446-b22c-51419fa55c1c", "881b59af-4c93-40c3-a30d-59f4abc84d23", "a9dba281-0b02-41c4-8d0e-50a734e08161", "14c4863f-5a9c-46da-b72a-345c35a83a4c", "bc3a57f2-eabd-46b3-8da7-98b25582f2d8"], "durationId": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "parent": "bbc53168-aa30-4bfb-8754-8f81c2b930cf"}}, {"head": {"id": "efda55be-9446-46d6-9547-759953d32d39", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11553647012000, "endTime": 11554725027800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "881b59af-4c93-40c3-a30d-59f4abc84d23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "881b59af-4c93-40c3-a30d-59f4abc84d23", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11553647012000, "endTime": 11554725027800}, "additional": {"logType": "info", "children": [], "durationId": "efda55be-9446-46d6-9547-759953d32d39", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "425b49fd-7207-488a-b10b-34b08bcf288d", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11554725228800, "endTime": 11554729639600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "a9dba281-0b02-41c4-8d0e-50a734e08161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9dba281-0b02-41c4-8d0e-50a734e08161", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11554725228800, "endTime": 11554729639600}, "additional": {"logType": "info", "children": [], "durationId": "425b49fd-7207-488a-b10b-34b08bcf288d", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "72ee70bc-40e2-4fcd-824a-1b0c8ea0f7c2", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11554729731500, "endTime": 11554729737000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "14c4863f-5a9c-46da-b72a-345c35a83a4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14c4863f-5a9c-46da-b72a-345c35a83a4c", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11554729731500, "endTime": 11554729737000}, "additional": {"logType": "info", "children": [], "durationId": "72ee70bc-40e2-4fcd-824a-1b0c8ea0f7c2", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "e7674fe7-b02e-4bc6-a149-cf9f8fa4bcc5", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Worker1", "startTime": 11554729793600, "endTime": 11562581292800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "03d1ea3d-0455-4cb4-90b7-ea85fb8fb336", "logId": "bc3a57f2-eabd-46b3-8da7-98b25582f2d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc3a57f2-eabd-46b3-8da7-98b25582f2d8", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11554729793600, "endTime": 11562581292800}, "additional": {"logType": "info", "children": [], "durationId": "e7674fe7-b02e-4bc6-a149-cf9f8fa4bcc5", "parent": "30657374-6cb3-45ee-b3ca-52c13fdd0735"}}, {"head": {"id": "bbc53168-aa30-4bfb-8754-8f81c2b930cf", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11550996880600, "endTime": 11562587205400, "totalTime": 11590263400}, "additional": {"logType": "info", "children": ["30657374-6cb3-45ee-b3ca-52c13fdd0735"], "durationId": "965724dc-fbf2-4b4c-840a-998f57a15bcd"}}, {"head": {"id": "a85cbffb-896d-484f-93b6-06d43d1e79c1", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562593824700, "endTime": 11562594126800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ddb4e6cb-b97d-429f-94a1-d64f7faa78fd", "logId": "5eb3f19c-c9e3-4ddf-98fe-b4a9e2ef668c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddb4e6cb-b97d-429f-94a1-d64f7faa78fd", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562593774700}, "additional": {"logType": "detail", "children": [], "durationId": "a85cbffb-896d-484f-93b6-06d43d1e79c1"}}, {"head": {"id": "a8b69a43-5964-4bc8-ac04-e286abcd8f07", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562593837300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd1b5972-42d8-455a-bd25-a7a1747a5845", "name": "entry : PreviewBuild cost memory 0.01201629638671875", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562593966500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b05e28c7-1d48-45df-be54-d2aae4fde0b9", "name": "runTaskFromQueue task cost before running: 12 s 719 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562594064100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eb3f19c-c9e3-4ddf-98fe-b4a9e2ef668c", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562593824700, "endTime": 11562594126800, "totalTime": 213700}, "additional": {"logType": "info", "children": [], "durationId": "a85cbffb-896d-484f-93b6-06d43d1e79c1"}}, {"head": {"id": "7a36190c-360a-414f-8ff8-9ef30d0af82a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562604769900, "endTime": 11562604789800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0c88610-8e8b-4658-ac8e-bd0c035212af", "logId": "4b227133-0d1a-49dd-8a7e-7323eda384ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b227133-0d1a-49dd-8a7e-7323eda384ff", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562604769900, "endTime": 11562604789800}, "additional": {"logType": "info", "children": [], "durationId": "7a36190c-360a-414f-8ff8-9ef30d0af82a"}}, {"head": {"id": "add7ecb4-8762-4bad-bc4f-ed67cccab7a9", "name": "BUILD SUCCESSFUL in 12 s 730 ms ", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562604876900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "4ac62e3b-b2cf-4cf5-ba79-58c7431e640b", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11549875421700, "endTime": 11562605366800}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 11, "minute": 51}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "b92fc156-0dc1-48ef-87f7-ee4af0662267", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605602100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6bff945-e053-4179-8180-ca5e63edf75a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605684900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4572a2b-f91e-4608-a660-696fc1e29282", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a189836-5ce2-48f6-9072-295bf5ae0785", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605783000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04707d99-5379-459c-aa97-f8b47da0fed4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605826500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c0e7ea6-930a-42cb-93e9-84a0e14515d3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605868300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f38d072-e461-4023-9aad-58c0f388188a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562605916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd058c7a-6bd9-4b80-b005-d1f18db44cfe", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562606802500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47186b9e-12d2-42c4-991b-2c5aba68ddee", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562622058200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd8150d-fbfd-406d-91c7-ef8eb44ca122", "name": "Update task entry:default@PreviewCompileResource input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562624866300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3212584f-d468-4d1a-9d29-b155a33ad26e", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562625332100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7ad97b-0133-47e5-aeb9-111b314b5a63", "name": "Update task entry:default@PreviewCompileResource output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562644638800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a9f9d8-0ece-4b64-9f43-166f94e9f20d", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:40 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562645469300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56feb9c-caa0-4e50-9580-28d652aa8148", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562645696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29dbbffd-e5a4-4b65-b959-fe000286ba2d", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562646480600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "721171f5-5d24-499c-b3f1-a3658f04cee8", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562647324500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "176441d2-0c90-41c3-85ce-9cd270b4ed71", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562647715300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01f32b50-06f9-4d61-be6c-ba3acc7e0e94", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562648007900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0da534fb-dfc8-4e44-99f0-0b83f80deeb0", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562648459200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c01f7a-d8ba-4ed7-9e1d-bcab66395707", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562651601500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e287ab2-3438-4258-81c1-b714c34336eb", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562652499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b4b68a-7c1f-4d0b-b67f-b4eb956a4993", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562652807800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff658224-b40e-4f60-a49a-0294c96f792e", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562669051400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f45b6f-aa24-435f-b823-361bc12c17bf", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562670061100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf649a26-3e55-4ebe-906c-28e5bed3530b", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562670164000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b510f5-8688-4be2-bc79-911e7d48d1df", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562670440700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bab5a7d-8dd9-45f3-bc5b-a38578472e35", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562671204300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fccea519-58e1-4774-9644-3f136d0f2011", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562675067800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a3c6fdc-57d5-4500-aa59-63a5be8c2e9d", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562675381900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f92f1f63-7aee-4fbf-9c2a-a5f65f76ca0c", "name": "Update task entry:default@PreviewArkTS input file:D:\\HarmonyOSProject\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562675668700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6bc4ddd-0e59-4405-81b4-ceaae4631e78", "name": "Update task entry:default@PreviewArkTS output file:D:\\HarmonyOSProject\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562675987500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67d73f4-09cf-4c54-8364-487da166446e", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:28 ms .", "description": "", "type": "log"}, "body": {"pid": 9644, "tid": "Main Thread", "startTime": 11562676337300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}