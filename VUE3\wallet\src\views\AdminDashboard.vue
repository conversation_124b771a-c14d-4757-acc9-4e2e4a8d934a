<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <h1>管理员仪表板</h1>
      <div class="admin-info">
        <span>欢迎，{{ adminInfo?.realName || adminInfo?.username }}</span>
        <el-button @click="logout" type="danger" size="small">退出登录</el-button>
      </div>
    </div>
    
    <div class="dashboard-content">
      <el-card class="info-card">
        <h3>登录信息</h3>
        <p><strong>用户名:</strong> {{ adminInfo?.username }}</p>
        <p><strong>真实姓名:</strong> {{ adminInfo?.realName }}</p>
        <p><strong>手机号:</strong> {{ adminInfo?.phone }}</p>
        <p><strong>角色:</strong> {{ adminInfo?.role }}</p>
        <p><strong>状态:</strong> {{ adminInfo?.status === 1 ? '正常' : '禁用' }}</p>
        <p><strong>最后登录时间:</strong> {{ adminInfo?.lastLoginTime }}</p>
      </el-card>
      
      <el-card class="token-card">
        <h3>Token信息</h3>
        <p><strong>Token:</strong> {{ token }}</p>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const adminInfo = ref(null)
const token = ref('')

onMounted(() => {
  // 获取存储的管理员信息
  const storedAdminInfo = localStorage.getItem('adminInfo')
  const storedToken = localStorage.getItem('adminToken')
  const userType = localStorage.getItem('userType')
  
  if (!storedAdminInfo || !storedToken || userType !== 'admin') {
    ElMessage.error('请先登录')
    router.push('/login')
    return
  }
  
  try {
    adminInfo.value = JSON.parse(storedAdminInfo)
    token.value = storedToken
  } catch (error) {
    console.error('解析管理员信息失败:', error)
    ElMessage.error('登录信息异常，请重新登录')
    logout()
  }
})

const logout = () => {
  // 清除存储的信息
  localStorage.removeItem('adminToken')
  localStorage.removeItem('adminInfo')
  localStorage.removeItem('userType')
  
  ElMessage.success('已退出登录')
  router.push('/login')
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0;
  color: #333;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-card, .token-card {
  padding: 20px;
}

.info-card h3, .token-card h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.info-card p, .token-card p {
  margin: 10px 0;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
