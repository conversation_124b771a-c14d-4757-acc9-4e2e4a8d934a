package com.icss.wallet.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.icss.wallet.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据手机号查询用户信息
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM user WHERE phone = #{phone}")
    User findByPhone(@Param("phone") String phone);

    /**
     * 更新用户最后登录时间
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @return 影响行数
     */
    @Update("UPDATE user SET last_login_time = #{loginTime} WHERE user_id = #{userId}")
    int updateLoginTime(@Param("userId") Long userId, @Param("loginTime") Date loginTime);

    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    @Select("SELECT COUNT(1) FROM user WHERE phone = #{phone}")
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 更新用户支付密码
     * @param userId 用户ID
     * @param payPassword 支付密码
     * @return 影响行数
     */
    @Update("UPDATE user SET pay_password = #{payPassword}, update_time = NOW() WHERE user_id = #{userId}")
    int updatePayPassword(@Param("userId") Long userId, @Param("payPassword") String payPassword);

    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 状态(0-禁用,1-正常)
     * @return 影响行数
     */
    @Update("UPDATE user SET status = #{status}, update_time = NOW() WHERE user_id = #{userId}")
    int updateStatus(@Param("userId") Long userId, @Param("status") Integer status);
}
