package com.icss.wallet.controller;

import com.icss.wallet.result.R;
import com.icss.wallet.entity.Payment;
import com.icss.wallet.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付测试控制器 - 用于诊断支付功能问题
 */
@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/payment-test")
public class PaymentTestController {

    @Autowired(required = false)
    private PaymentService paymentService;

    /**
     * 测试支付服务是否可用
     */
    @GetMapping("/service-status")
    public R<Map<String, Object>> testServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("paymentServiceAvailable", paymentService != null);
        status.put("timestamp", System.currentTimeMillis());
        
        if (paymentService != null) {
            status.put("message", "PaymentService注入成功");
        } else {
            status.put("message", "PaymentService注入失败");
        }
        
        return R.success("服务状态检查完成", status);
    }

    /**
     * 测试数据库连接
     */
    @GetMapping("/db-test/{userId}")
    public R<Object> testDatabase(@PathVariable Long userId) {
        if (paymentService == null) {
            return R.failure("PaymentService未注入，无法测试数据库");
        }
        
        try {
            List<Payment> payments = paymentService.getPaymentHistory(userId);
            Map<String, Object> result = new HashMap<>();
            result.put("recordCount", payments.size());
            result.put("records", payments);
            result.put("message", "数据库连接正常");
            
            return R.success("数据库测试成功", result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            error.put("cause", e.getCause() != null ? e.getCause().getMessage() : "无");
            error.put("message", "数据库连接失败");
            
            return R.failure("数据库测试失败: " + e.getMessage());
        }
    }

    /**
     * 返回模拟支付记录
     */
    @GetMapping("/mock-history/{userId}")
    public R<List<Map<String, Object>>> getMockHistory(@PathVariable Long userId) {
        List<Map<String, Object>> mockData = new ArrayList<>();
        
        // 创建模拟数据
        for (int i = 1; i <= 5; i++) {
            Map<String, Object> payment = new HashMap<>();
            payment.put("paymentId", i);
            payment.put("paymentNo", "PAY" + System.currentTimeMillis() + i);
            payment.put("userId", userId);
            payment.put("amount", 100.00 * i);
            payment.put("merchantName", "测试商户" + i);
            payment.put("paymentType", i % 2 + 1);
            payment.put("status", 1);
            payment.put("createTime", System.currentTimeMillis());
            payment.put("description", "测试支付" + i);
            mockData.add(payment);
        }
        
        return R.success("获取模拟支付记录成功", mockData);
    }

    /**
     * 检查payment表是否存在
     */
    @GetMapping("/table-check")
    public R<Map<String, Object>> checkTable() {
        Map<String, Object> result = new HashMap<>();
        
        if (paymentService == null) {
            result.put("serviceStatus", "PaymentService未注入");
            result.put("tableExists", false);
            return R.failure("无法检查表结构");
        }
        
        try {
            // 尝试查询一个不存在的用户ID，如果表不存在会抛出异常
            paymentService.getPaymentHistory(999999L);
            result.put("tableExists", true);
            result.put("message", "payment表存在且可访问");
            return R.success("表检查成功", result);
        } catch (Exception e) {
            result.put("tableExists", false);
            result.put("error", e.getMessage());
            result.put("message", "payment表可能不存在或无法访问");
            return R.failure("表检查失败: " + e.getMessage());
        }
    }

    /**
     * 完整诊断
     */
    @GetMapping("/full-diagnostic/{userId}")
    public R<Map<String, Object>> fullDiagnostic(@PathVariable Long userId) {
        Map<String, Object> diagnostic = new HashMap<>();
        
        // 1. 检查服务注入
        diagnostic.put("serviceInjected", paymentService != null);
        
        // 2. 检查表存在
        boolean tableExists = false;
        String tableError = null;
        if (paymentService != null) {
            try {
                paymentService.getPaymentHistory(999999L);
                tableExists = true;
            } catch (Exception e) {
                tableError = e.getMessage();
            }
        }
        diagnostic.put("tableExists", tableExists);
        diagnostic.put("tableError", tableError);
        
        // 3. 检查数据
        int recordCount = 0;
        String dataError = null;
        if (paymentService != null && tableExists) {
            try {
                List<Payment> payments = paymentService.getPaymentHistory(userId);
                recordCount = payments.size();
            } catch (Exception e) {
                dataError = e.getMessage();
            }
        }
        diagnostic.put("recordCount", recordCount);
        diagnostic.put("dataError", dataError);
        
        // 4. 总结
        if (paymentService != null && tableExists && dataError == null) {
            diagnostic.put("status", "正常");
            diagnostic.put("message", "支付功能完全正常");
            return R.success("诊断完成", diagnostic);
        } else {
            diagnostic.put("status", "异常");
            diagnostic.put("message", "支付功能存在问题");
            return R.failure("诊断发现问题");
        }
    }
}
