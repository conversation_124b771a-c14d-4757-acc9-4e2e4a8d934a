[{"time": "2025-06-26T03:56:22.437Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/WalletPage.ets:64:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/WalletPage.ets"}, {"time": "2025-06-26T03:56:22.741Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/TransactionPage.ets:75:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/TransactionPage.ets"}, {"time": "2025-06-26T03:56:22.864Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/HomePage.ets:59:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/HomePage.ets"}, {"time": "2025-06-26T03:56:22.998Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankCardPage.ets:62:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankCardPage.ets"}, {"time": "2025-06-26T03:56:23.136Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/SettingsPage.ets:49:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/SettingsPage.ets"}, {"time": "2025-06-26T03:56:23.259Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankAccountTransferPage.ets:58:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/BankAccountTransferPage.ets"}, {"time": "2025-06-26T03:56:23.338Z", "category": "etsTransform", "level": "warn", "msg": "\u001b[33mArkTS:WARN File: D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/ChangePasswordPage.ets:13:1\n It's not a recommended way to export struct with @Entry decorator, which may cause ACE Engine error in component preview mode.", "type": "plugin_log", "fileId": "D:/HarmonyOSProject/Wallet/entry/src/main/ets/pages/ChangePasswordPage.ets"}]