import {HomePage} from './HomePage';
import {WalletPage} from './WalletPage';
import {TransactionPage} from './TransactionPage';
import {BankCardPage} from './BankCardPage';
import {SettingsPage} from './SettingsPage';
@Entry
@Component
struct BarPage {
  @State currentIndex: number = 0
  tabController: TabsController = new TabsController()

  @Builder
  TabWidget(title: string, targetIndex: number, currentImage: Resource, selectImage: Resource) {
    Column() {
      Image(targetIndex == this.currentIndex ? selectImage : currentImage)
        .width(25)
        .height(25)
        .margin({ top: 5 })
      Text(title)
        .fontSize(16)
        .fontColor(this.currentIndex == targetIndex ? '#ee9725cb' : '#333333')
        .margin({ top: 6 })
    }
    .height(55)
    .onClick(() => {
      this.currentIndex = targetIndex
      this.tabController.changeIndex(this.currentIndex)
    })
  }

  build() {
    Column() {
      Tabs({
        barPosition: BarPosition.End,
        index: 0,
        controller: this.tabController
      }) {
        TabContent() {
          HomePage()
        }.tabBar(this.TabWidget('首页', 0, $r('app.media.home'), $r('app.media.home_selected')))

        TabContent() {
          WalletPage()
        }.tabBar(this.TabWidget('钱包', 1, $r('app.media.wallet'), $r('app.media.wallet_selected')))

        TabContent() {
          TransactionPage()
        }.tabBar(this.TabWidget('交易', 2, $r('app.media.transaction'), $r('app.media.transaction_selected')))

        TabContent() {
          BankCardPage()
        }.tabBar(this.TabWidget('银行卡', 3, $r('app.media.card'), $r('app.media.card_selected')))

        TabContent() {
          SettingsPage()
        }.tabBar(this.TabWidget('我的', 4, $r('app.media.profile'), $r('app.media.profile_selected')))
      }
      .animationDuration(0)
      .barHeight(55)
    }
    .width('100%')
    .height('100%')
  }
}