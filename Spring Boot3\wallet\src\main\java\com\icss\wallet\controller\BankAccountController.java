package com.icss.wallet.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.BankAccount;
import com.icss.wallet.result.R;
import com.icss.wallet.service.BankAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
@CrossOrigin
@RestController
@RequestMapping("/bankAccounts")
public class BankAccountController {
    @Autowired
    private BankAccountService bankAccountService;

    @PostMapping("/add")
    public R add(@RequestBody BankAccount bankAccount) {
        bankAccountService.addBankAccount(bankAccount);
        return R.success("银行账户添加成功");
    }

    @DeleteMapping("/{accountId}")
    public R delete(@PathVariable Long accountId) {
        bankAccountService.deleteBankAccount(accountId);
        return R.success("银行账户删除成功");
    }

    @PutMapping("/update")
    public R update(@RequestBody BankAccount bankAccount) {
        bankAccountService.updateBankAccount(bankAccount);
        return R.success("银行账户更新成功");
    }

    @GetMapping("/{accountId}")
    public R getById(@PathVariable Long accountId) {
        BankAccount bankAccount = bankAccountService.getById(accountId);
        return R.success("查询成功", bankAccount);
    }

    @GetMapping("/page")
    public R page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) Integer status) {
        IPage<BankAccount> page = bankAccountService.page(new Page<>(pageNum, pageSize), phone, status);
        return R.success("分页查询成功", page);
    }

    /**
     * 根据用户ID查询银行账户
     */
    @GetMapping("/user/{userId}")
    public R getByUserId(@PathVariable Long userId) {
        try {
            java.util.List<BankAccount> accounts = bankAccountService.getByUserId(userId);
            return R.success("查询成功", accounts);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * HarmonyOS前端专用：根据用户ID查询活跃银行账户
     */
    @GetMapping("/user/{userId}/active")
    public R getActiveByUserId(@PathVariable Long userId) {
        try {
            java.util.List<BankAccount> accounts = bankAccountService.getActiveByUserId(userId);
            return R.success("查询成功", accounts);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }
}