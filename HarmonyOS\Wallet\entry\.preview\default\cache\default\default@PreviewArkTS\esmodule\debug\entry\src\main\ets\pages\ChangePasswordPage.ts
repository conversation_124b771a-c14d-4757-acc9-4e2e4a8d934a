if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ChangePasswordPage_Params {
    oldPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
    isLoading?: boolean;
    preferencesStore?: preferences.Preferences | null;
    userId?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import axios from "@normalized:N&&&@ohos/axios/index&2.2.6";
import type { AxiosResponse } from "@normalized:N&&&@ohos/axios/index&2.2.6";
import preferences from "@ohos:data.preferences";
// 定义API响应类型
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}
export class ChangePasswordPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__oldPassword = new ObservedPropertySimplePU('', this, "oldPassword");
        this.__newPassword = new ObservedPropertySimplePU('', this, "newPassword");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__preferencesStore = new ObservedPropertyObjectPU(null, this, "preferencesStore");
        this.__userId = new ObservedPropertySimplePU(0, this, "userId");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ChangePasswordPage_Params) {
        if (params.oldPassword !== undefined) {
            this.oldPassword = params.oldPassword;
        }
        if (params.newPassword !== undefined) {
            this.newPassword = params.newPassword;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.preferencesStore !== undefined) {
            this.preferencesStore = params.preferencesStore;
        }
        if (params.userId !== undefined) {
            this.userId = params.userId;
        }
    }
    updateStateVars(params: ChangePasswordPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__oldPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__newPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__preferencesStore.purgeDependencyOnElmtId(rmElmtId);
        this.__userId.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__oldPassword.aboutToBeDeleted();
        this.__newPassword.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__preferencesStore.aboutToBeDeleted();
        this.__userId.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __oldPassword: ObservedPropertySimplePU<string>;
    get oldPassword() {
        return this.__oldPassword.get();
    }
    set oldPassword(newValue: string) {
        this.__oldPassword.set(newValue);
    }
    private __newPassword: ObservedPropertySimplePU<string>;
    get newPassword() {
        return this.__newPassword.get();
    }
    set newPassword(newValue: string) {
        this.__newPassword.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __preferencesStore: ObservedPropertyObjectPU<preferences.Preferences | null>;
    get preferencesStore() {
        return this.__preferencesStore.get();
    }
    set preferencesStore(newValue: preferences.Preferences | null) {
        this.__preferencesStore.set(newValue);
    }
    private __userId: ObservedPropertySimplePU<number>;
    get userId() {
        return this.__userId.get();
    }
    set userId(newValue: number) {
        this.__userId.set(newValue);
    }
    aboutToAppear() {
        this.initPreferences();
    }
    // 初始化preferences并获取用户ID
    async initPreferences() {
        try {
            this.preferencesStore = await preferences.getPreferences(getContext(), 'user_prefs');
            this.userId = await this.preferencesStore.get('userId', 0) as number;
            console.log('当前用户ID:', this.userId);
        }
        catch (error) {
            console.error('初始化preferences失败:', error);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(39:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        // 标题栏
        this.TitleBar.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主内容区域
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(44:7)", "entry");
            // 主内容区域
            Column.width('100%');
            // 主内容区域
            Column.padding({ left: 20, right: 20 });
            // 主内容区域
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 页面标题
            Text.create('修改登录密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(46:9)", "entry");
            // 页面标题
            Text.fontSize(24);
            // 页面标题
            Text.fontWeight(FontWeight.Bold);
            // 页面标题
            Text.fontColor('#333333');
            // 页面标题
            Text.margin({ top: 30, bottom: 40 });
        }, Text);
        // 页面标题
        Text.pop();
        // 密码输入表单
        this.PasswordForm.bind(this)();
        // 修改按钮
        this.ChangeButton.bind(this)();
        // 温馨提示
        this.Tips.bind(this)();
        // 主内容区域
        Column.pop();
        Column.pop();
    }
    TitleBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(72:5)", "entry");
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 16, right: 16 });
            Row.backgroundColor('#ffffff');
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777260, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(73:7)", "entry");
            Image.width(24);
            Image.height(24);
            Image.onClick(() => {
                router.back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('修改密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(80:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持标题居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(88:7)", "entry");
            // 占位，保持标题居中
            Row.width(24);
            // 占位，保持标题居中
            Row.height(24);
        }, Row);
        // 占位，保持标题居中
        Row.pop();
        Row.pop();
    }
    PasswordForm(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(100:5)", "entry");
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 原密码输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(102:7)", "entry");
            // 原密码输入
            Column.width('100%');
            // 原密码输入
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('原密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(103:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入原密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(109:9)", "entry");
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.backgroundColor('#ffffff');
            TextInput.borderRadius(8);
            TextInput.border({ width: 1, color: '#e0e0e0' });
            TextInput.onChange((value: string) => {
                this.oldPassword = value;
            });
        }, TextInput);
        // 原密码输入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 新密码输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(124:7)", "entry");
            // 新密码输入
            Column.width('100%');
            // 新密码输入
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('新密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(125:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入新密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(131:9)", "entry");
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.backgroundColor('#ffffff');
            TextInput.borderRadius(8);
            TextInput.border({ width: 1, color: '#e0e0e0' });
            TextInput.onChange((value: string) => {
                this.newPassword = value;
            });
        }, TextInput);
        // 新密码输入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 确认密码输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(146:7)", "entry");
            // 确认密码输入
            Column.width('100%');
            // 确认密码输入
            Column.margin({ bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认新密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(147:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请再次输入新密码' });
            TextInput.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(153:9)", "entry");
            TextInput.width('100%');
            TextInput.height(50);
            TextInput.type(InputType.Password);
            TextInput.backgroundColor('#ffffff');
            TextInput.borderRadius(8);
            TextInput.border({ width: 1, color: '#e0e0e0' });
            TextInput.onChange((value: string) => {
                this.confirmPassword = value;
            });
        }, TextInput);
        // 确认密码输入
        Column.pop();
        Column.pop();
    }
    ChangeButton(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(172:5)", "entry");
            Button.width('100%');
            Button.height(50);
            Button.backgroundColor('#1f98e5');
            Button.borderRadius(8);
            Button.enabled(!this.isLoading);
            Button.onClick(() => {
                this.changePassword();
            });
            Button.margin({ bottom: 30 });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(174:9)", "entry");
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(175:11)", "entry");
                        LoadingProgress.width(20);
                        LoadingProgress.height(20);
                        LoadingProgress.color('#ffffff');
                        LoadingProgress.margin({ right: 8 });
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('修改中...');
                        Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(180:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#ffffff');
                    }, Text);
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('确认修改');
                        Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(185:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#ffffff');
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Button.pop();
    }
    Tips(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(203:5)", "entry");
            Column.width('100%');
            Column.padding(16);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(8);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('温馨提示');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(204:7)", "entry");
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 10 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 密码长度至少6位');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(211:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 建议使用字母、数字组合');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(217:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 5 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('• 请妥善保管您的密码');
            Text.debugLine("entry/src/main/ets/pages/ChangePasswordPage.ets(223:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
    }
    // 修改密码
    async changePassword() {
        // 表单验证
        if (!this.validateForm()) {
            return;
        }
        this.isLoading = true;
        try {
            const response: AxiosResponse<ApiResponse<void>> = await axios({
                url: 'http://localhost:8091/auth/changePassword',
                method: 'post',
                params: {
                    userId: this.userId,
                    oldPassword: this.oldPassword,
                    newPassword: this.newPassword
                }
            });
            console.log('修改密码响应:', JSON.stringify(response.data));
            if (response.data.code === 0) {
                promptAction.showToast({
                    message: '密码修改成功',
                    duration: 2000
                });
                // 延迟返回上一页
                setTimeout(() => {
                    router.back();
                }, 1000);
            }
            else {
                promptAction.showToast({
                    message: response.data.msg || '密码修改失败',
                    duration: 2000
                });
            }
        }
        catch (error) {
            console.error('修改密码错误:', error);
            promptAction.showToast({
                message: '网络错误，请稍后重试',
                duration: 2000
            });
        }
        finally {
            this.isLoading = false;
        }
    }
    // 表单验证
    validateForm(): boolean {
        if (!this.oldPassword.trim()) {
            promptAction.showToast({
                message: '请输入原密码',
                duration: 2000
            });
            return false;
        }
        if (!this.newPassword.trim()) {
            promptAction.showToast({
                message: '请输入新密码',
                duration: 2000
            });
            return false;
        }
        if (this.newPassword.length < 6) {
            promptAction.showToast({
                message: '新密码长度至少6位',
                duration: 2000
            });
            return false;
        }
        if (!this.confirmPassword.trim()) {
            promptAction.showToast({
                message: '请确认新密码',
                duration: 2000
            });
            return false;
        }
        if (this.newPassword !== this.confirmPassword) {
            promptAction.showToast({
                message: '两次输入的密码不一致',
                duration: 2000
            });
            return false;
        }
        if (this.oldPassword === this.newPassword) {
            promptAction.showToast({
                message: '新密码不能与原密码相同',
                duration: 2000
            });
            return false;
        }
        return true;
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ChangePasswordPage";
    }
}
registerNamedRoute(() => new ChangePasswordPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/ChangePasswordPage", pageFullPath: "entry/src/main/ets/pages/ChangePasswordPage", integratedHsp: "false", moduleType: "followWithHap" });
