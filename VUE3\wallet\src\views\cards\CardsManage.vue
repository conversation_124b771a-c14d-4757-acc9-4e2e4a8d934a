<template>
  <div class="bank-card-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><CreditCard /></el-icon>
            银行卡管理
          </h2>
          <p>管理系统银行卡信息，包括查看、绑定/解绑、设置默认卡等操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计面板 -->
    <el-row :gutter="20" class="statistics-panel">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ cardStatistics.totalCards }}</div>
            <div class="stat-label">总银行卡</div>
          </div>
          <el-icon class="stat-icon total"><CreditCard /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ cardStatistics.boundCards }}</div>
            <div class="stat-label">已绑定</div>
          </div>
          <el-icon class="stat-icon bound"><Check /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ cardStatistics.unboundCards }}</div>
            <div class="stat-label">未绑定</div>
          </div>
          <el-icon class="stat-icon unbound"><Close /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ cardStatistics.defaultCards }}</div>
            <div class="stat-label">默认卡</div>
          </div>
          <el-icon class="stat-icon default"><Star /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <el-card>
      <div class="card-header">
        <h2>银行卡管理</h2>
        <div class="header-actions">
          <el-button
            v-if="selectedCards.length > 0"
            type="warning"
            @click="handleBatchOperation"
          >
            批量操作 ({{ selectedCards.length }})
          </el-button>
          <el-button type="primary" @click="showAddDialog">添加银行卡</el-button>
        </div>
      </div>

      <!-- 快速筛选按钮 -->
      <div class="quick-filters">
        <el-button-group>
          <el-button
            :type="searchForm.status === '' ? 'primary' : ''"
            @click="quickFilter('')"
          >
            全部卡片
          </el-button>
          <el-button
            :type="searchForm.status === 1 ? 'success' : ''"
            @click="quickFilter(1)"
          >
            <el-icon><Check /></el-icon>
            已绑定
          </el-button>
          <el-button
            :type="searchForm.status === 0 ? 'info' : ''"
            @click="quickFilter(0)"
          >
            <el-icon><Close /></el-icon>
            未绑定
          </el-button>
        </el-button-group>
      </div>

      <el-form :model="searchForm" class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="银行名称">
              <el-input v-model="searchForm.bankName" placeholder="请输入银行名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="卡号">
              <el-input v-model="searchForm.cardNumber" placeholder="请输入卡号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="已绑定" :value="1" />
                <el-option label="未绑定" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="applyFilters">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <el-tabs v-model="activeTab" type="card" class="bank-tabs">
        <el-tab-pane v-for="bank in groupedCards" :key="bank.name">
          <template #label>
            <div class="tab-label">
              <BankCardIcon :bank-name="bank.name" size="small" />
              <span>{{ bank.name }} ({{ bank.cards.length }})</span>
            </div>
          </template>

          <div class="bank-cards-grid">
            <div
              v-for="card in bank.cards"
              :key="card.cardId"
              class="bank-card-item"
              :class="{ 'selected': selectedCards.some(c => c.cardId === card.cardId) }"
              @click="toggleCardSelection(card)"
            >
              <div class="card-header">
                <div class="card-info">
                  <BankCardIcon :bank-name="card.bankName" size="medium" />
                  <div class="card-details">
                    <h4>{{ card.bankName }}</h4>
                    <p class="card-type">{{ card.cardType === 1 ? '借记卡' : '信用卡' }}</p>
                  </div>
                </div>
                <div class="card-status">
                  <el-icon v-if="card.isDefault === 1" class="default-star">
                    <StarFilled />
                  </el-icon>
                  <el-tag
                    :type="card.status === 1 ? 'success' : 'info'"
                    size="small"
                    class="status-tag"
                  >
                    {{ card.status === 1 ? '已绑定' : '未绑定' }}
                  </el-tag>
                </div>
              </div>

              <div class="card-number">
                <div class="sensitive-data">
                  <span class="card-number-text">
                    {{ formatCardNumber(card.cardNumber, showFullCardData[card.cardId]?.cardNumber) }}
                  </span>
                  <el-button
                    type="text"
                    size="small"
                    @click.stop="toggleCardDataVisibility(card.cardId, 'cardNumber')"
                    class="toggle-btn"
                  >
                    <el-icon>
                      <View v-if="!showFullCardData[card.cardId]?.cardNumber" />
                      <Hide v-else />
                    </el-icon>
                  </el-button>
                </div>
              </div>

              <div class="card-holder">
                <span class="label">持卡人：</span>
                <span>{{ card.cardHolder }}</span>
              </div>

              <div class="card-phone">
                <span class="label">手机号：</span>
                <span>{{ card.phone }}</span>
              </div>

              <div class="card-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click.stop="viewDetail(card.cardId)"
                  class="action-btn"
                >
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button
                  v-if="card.status === 1"
                  type="danger"
                  size="small"
                  @click.stop="handleUnbind(card.cardId)"
                  class="action-btn"
                >
                  解绑
                </el-button>
                <el-button
                  v-else
                  type="success"
                  size="small"
                  @click.stop="handleBind(card.cardId)"
                  class="action-btn"
                >
                  绑定
                </el-button>
                <el-button
                  v-if="card.isDefault === 0 && card.status === 1"
                  type="warning"
                  size="small"
                  @click.stop="setAsDefault(card.cardId)"
                  class="action-btn"
                >
                  <el-icon><Star /></el-icon>
                  设默认
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click.stop="handleDelete(card)"
                  class="action-btn delete-btn"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 添加银行卡对话框 -->
    <el-dialog v-model="addDialogVisible" title="添加银行卡" width="50%">
      <el-form :model="newCardForm" label-width="120px" :rules="rules" ref="addFormRef">
        <el-form-item label="银行名称" prop="bankName">
          <el-select v-model="newCardForm.bankName" placeholder="请选择银行" filterable>
            <el-option
              v-for="bank in bankList"
              :key="bank.value"
              :label="bank.label"
              :value="bank.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="银行卡号" prop="cardNumber">
          <el-input v-model="newCardForm.cardNumber" placeholder="请输入银行卡号" />
        </el-form-item>
        <el-form-item label="卡类型" prop="cardType">
          <el-radio-group v-model="newCardForm.cardType">
            <el-radio :label="1">借记卡</el-radio>
            <el-radio :label="2">信用卡</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="持卡人姓名" prop="cardHolder">
          <el-input v-model="newCardForm.cardHolder" placeholder="请输入持卡人姓名" />
        </el-form-item>
        <el-form-item label="预留手机号" prop="phone">
          <el-input v-model="newCardForm.phone" placeholder="请输入预留手机号" />
        </el-form-item>
        <el-form-item v-if="newCardForm.cardType === 2" label="有效期" prop="expiryDate">
          <el-input v-model="newCardForm.expiryDate" placeholder="MM/YY" />
        </el-form-item>
        <el-form-item v-if="newCardForm.cardType === 2" label="安全码" prop="cvv">
          <el-input v-model="newCardForm.cvv" placeholder="请输入安全码" show-password />
        </el-form-item>
        <el-form-item label="立即绑定">
          <el-switch v-model="newCardForm.autoBind" />
        </el-form-item>
        <el-form-item label="设为默认卡" v-if="newCardForm.autoBind">
          <el-switch v-model="newCardForm.isDefault" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddCard">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check, Close, View, Hide, CreditCard, Star, Plus, Edit, Delete, StarFilled, Refresh
} from '@element-plus/icons-vue'
import BankCardIcon from '@/components/BankCardIcon.vue'

const router = useRouter()

// 数据
const bankCards = ref([])
const allBankCards = ref([]) // 存储所有银行卡数据
const searchForm = ref({
  bankName: '',
  cardNumber: '',
  status: ''
})
const activeTab = ref('')
const addDialogVisible = ref(false)
const newCardForm = ref({
  userId: 1, // 实际项目中应从用户登录信息获取
  bankName: '',
  cardNumber: '',
  cardType: 1,
  cardHolder: '',
  phone: '',
  expiryDate: '',
  cvv: '',
  autoBind: false,
  isDefault: 0
})
const addFormRef = ref(null)

// 批量操作相关
const selectedCards = ref([])

// 银行卡数据脱敏显示控制
const showFullCardData = ref({})

// 银行列表
const bankList = ref([
  { value: '中国工商银行', label: '中国工商银行' },
  { value: '中国建设银行', label: '中国建设银行' },
  { value: '中国银行', label: '中国银行' },
  { value: '中国农业银行', label: '中国农业银行' },
  { value: '招商银行', label: '招商银行' },
  { value: '交通银行', label: '交通银行' },
  { value: '中信银行', label: '中信银行' },
  { value: '光大银行', label: '光大银行' },
  { value: '民生银行', label: '民生银行' },
  { value: '兴业银行', label: '兴业银行' }
])

// 验证规则
const rules = {
  bankName: [{ required: true, message: '请选择银行名称', trigger: 'change' }],
  cardNumber: [
    { required: true, message: '请输入银行卡号', trigger: 'blur' },
    { pattern: /^\d{16,19}$/, message: '银行卡号应为16-19位数字', trigger: 'blur' }
  ],
  cardType: [{ required: true, message: '请选择卡类型', trigger: 'change' }],
  cardHolder: [
    { required: true, message: '请输入持卡人姓名', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5]{2,}$/, message: '请输入正确的持卡人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入预留手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  expiryDate: [
    { 
      required: true, 
      message: '请输入有效期(MM/YY)', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (newCardForm.value.cardType === 2 && !value) {
          callback(new Error('请输入有效期(MM/YY)'))
        } else if (value && !/^(0[1-9]|1[0-2])\/?([0-9]{2})$/.test(value)) {
          callback(new Error('有效期格式应为MM/YY'))
        } else {
          callback()
        }
      }
    }
  ],
  cvv: [
    { 
      required: true, 
      message: '请输入安全码', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (newCardForm.value.cardType === 2 && !value) {
          callback(new Error('请输入安全码'))
        } else if (value && !/^\d{3,4}$/.test(value)) {
          callback(new Error('安全码应为3或4位数字'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 计算属性 - 按银行分组
const groupedCards = computed(() => {
  const groups = {}
  
  bankCards.value.forEach(card => {
    if (!groups[card.bankName]) {
      groups[card.bankName] = {
        name: card.bankName,
        cards: []
      }
    }
    groups[card.bankName].cards.push(card)
  })
  
  // 转换为数组并按银行名称排序
  const result = Object.values(groups).sort((a, b) => a.name.localeCompare(b.name))
  
  // 设置默认激活的tab
  if (result.length > 0 && !activeTab.value) {
    activeTab.value = result[0].name
  }
  
  return result
})

// 银行卡统计计算
const cardStatistics = computed(() => {
  return {
    totalCards: bankCards.value.length,
    boundCards: bankCards.value.filter(card => card.status === 1).length,
    unboundCards: bankCards.value.filter(card => card.status === 0).length,
    defaultCards: bankCards.value.filter(card => card.isDefault === 1).length
  }
})

// 银行卡号脱敏格式化
const formatCardNumber = (cardNumber, showFull = false) => {
  if (!cardNumber) return ''
  if (showFull) {
    return cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ')
  }
  // 脱敏显示：显示前4位和后4位，中间用*代替
  return cardNumber.replace(/(\d{4})\d{8}(\d{4})/, '$1 **** **** $2')
}

// 切换银行卡数据显示状态
const toggleCardDataVisibility = (cardId, dataType) => {
  if (!showFullCardData.value[cardId]) {
    showFullCardData.value[cardId] = {}
  }
  showFullCardData.value[cardId][dataType] = !showFullCardData.value[cardId][dataType]
}

// 批量选择处理
const handleSelectionChange = (selection) => {
  selectedCards.value = selection
}

// 切换卡片选择状态
const toggleCardSelection = (card) => {
  const index = selectedCards.value.findIndex(c => c.cardId === card.cardId)
  if (index > -1) {
    selectedCards.value.splice(index, 1)
  } else {
    selectedCards.value.push(card)
  }
}

// 快速筛选
const quickFilter = (status) => {
  searchForm.value.status = status
  applyFilters()
}

// 批量操作
const handleBatchOperation = () => {
  if (selectedCards.value.length === 0) {
    ElMessage.warning('请先选择要操作的银行卡')
    return
  }

  ElMessageBox.confirm(
    `确定要批量操作选中的 ${selectedCards.value.length} 张银行卡吗？`,
    '批量操作确认',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 显示批量操作选项
    showBatchOperationDialog()
  }).catch(() => {
    ElMessage.info('已取消批量操作')
  })
}

// 显示批量操作对话框
const showBatchOperationDialog = () => {
  ElMessageBox.confirm(
    '请选择批量操作类型：',
    '批量操作',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: '批量绑定',
      cancelButtonText: '批量解绑',
      type: 'info'
    }
  ).then(() => {
    // 批量绑定
    batchUpdateCardStatus(1)
  }).catch((action) => {
    if (action === 'cancel') {
      // 批量解绑
      batchUpdateCardStatus(0)
    }
  })
}

// 批量更新银行卡状态
const batchUpdateCardStatus = async (status) => {
  try {
    const promises = selectedCards.value.map(card => {
      if (status === 1) {
        return axios.post(`http://localhost:8091/bankCards/bind/${card.cardId}`)
      } else {
        return axios.post(`http://localhost:8091/bankCards/unbind/${card.cardId}`)
      }
    })

    await Promise.all(promises)
    ElMessage.success(`批量${status === 1 ? '绑定' : '解绑'}成功`)
    selectedCards.value = []
    fetchBankCards()
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  }
}

// 方法
const fetchBankCards = async () => {
  try {
    // 获取所有银行卡数据，不传筛选参数
    const response = await axios.get('http://localhost:8091/bankCards/user/1')
    if (response.data && response.data.code === 0) {
      allBankCards.value = response.data.data || []
      // 应用当前的筛选条件
      applyFilters()
    } else {
      ElMessage.error(response.data?.msg || '获取银行卡列表失败')
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error)
    let errorMsg = '获取银行卡列表失败'
    if (error.response) {
      if (error.response.data && error.response.data.msg) {
        errorMsg = error.response.data.msg
      } else {
        errorMsg = error.response.statusText
      }
    }
    ElMessage.error(errorMsg)
  }
}

// 应用筛选条件
const applyFilters = () => {
  let filteredCards = [...allBankCards.value]

  // 银行名称筛选
  if (searchForm.value.bankName) {
    filteredCards = filteredCards.filter(card =>
      card.bankName.includes(searchForm.value.bankName)
    )
  }

  // 卡号筛选
  if (searchForm.value.cardNumber) {
    filteredCards = filteredCards.filter(card =>
      card.cardNumber.includes(searchForm.value.cardNumber)
    )
  }

  // 状态筛选 - 确保数据类型匹配
  if (searchForm.value.status !== '') {
    // 将筛选值转换为数字进行比较，因为数据库中status是tinyint类型
    const statusValue = parseInt(searchForm.value.status)
    filteredCards = filteredCards.filter(card =>
      card.status === statusValue
    )
  }

  bankCards.value = filteredCards
}

const resetSearch = () => {
  searchForm.value = {
    bankName: '',
    cardNumber: '',
    status: ''
  }
  applyFilters()
}



const showAddDialog = () => {
  newCardForm.value = {
    userId: 1,
    bankName: '',
    cardNumber: '',
    cardType: 1,
    cardHolder: '',
    phone: '',
    expiryDate: '',
    cvv: '',
    autoBind: false,
    isDefault: 0
  }
  addDialogVisible.value = true
}

// 添加银行卡方法
const handleAddCard = async () => {
  try {
    await addFormRef.value.validate()
    
    // 准备提交数据
    const formData = {
      ...newCardForm.value,
      status: newCardForm.value.autoBind ? 1 : 0, // 根据autoBind设置状态
      createTime: new Date(),
      updateTime: new Date()
    }
    
    // 清理不需要的字段
    if (formData.cardType === 1) {
      delete formData.expiryDate
      delete formData.cvv
    }
    
    // 如果不是自动绑定，则不需要isDefault字段
    if (!formData.autoBind) {
      delete formData.isDefault
    }
    
    const response = await axios.post('http://localhost:8091/bankCards/add', formData)
    
    if (response.data && response.data.code === 0) {
      ElMessage.success('添加银行卡成功')
      addDialogVisible.value = false
      await fetchBankCards()
      
      // 如果是自动绑定且添加成功，且不是默认卡，则调用绑定接口
      if (formData.autoBind && formData.isDefault !== 1) {
        await handleBindCard(response.data.data.cardId)
      }
    } else {
      throw new Error(response.data?.msg || '添加银行卡失败')
    }
  } catch (error) {
    console.error('添加银行卡失败:', error)
    let errorMsg = '添加银行卡失败'
    if (error.response) {
      if (error.response.data) {
        if (error.response.data.msg) {
          errorMsg = error.response.data.msg
        } else if (error.response.data.message) {
          errorMsg = error.response.data.message
        }
      }
    } else if (error.message) {
      errorMsg = error.message
    }
    ElMessage.error(errorMsg)
  }
}

// 绑定银行卡方法
const handleBindCard = async (cardId) => {
  try {
    const response = await axios.post(`http://localhost:8091/bankCards/bind/${cardId}`)
    
    if (response.data && response.data.code === 0) {
      ElMessage.success('绑定银行卡成功')
      await fetchBankCards()
    } else {
      throw new Error(response.data?.msg || '绑定银行卡失败')
    }
  } catch (error) {
    console.error('绑定银行卡失败:', error)
    let errorMsg = '绑定银行卡失败'
    if (error.response) {
      if (error.response.data) {
        if (error.response.data.msg) {
          errorMsg = error.response.data.msg
        } else if (error.response.data.message) {
          errorMsg = error.response.data.message
        }
      }
    } else if (error.message) {
      errorMsg = error.message
    }
    ElMessage.error(errorMsg)
  }
}

const handleUnbind = (cardId) => {
  ElMessageBox.confirm('确定要解绑该银行卡吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const response = await axios.post(`http://localhost:8091/bankCards/unbind/${cardId}`)
        if (response.data && response.data.code === 0) {
          ElMessage.success('解绑成功')
          await fetchBankCards()
        } else {
          throw new Error(response.data?.msg || '解绑失败')
        }
      } catch (error) {
        console.error('解绑银行卡失败:', error)
        let errorMsg = '解绑银行卡失败'
        if (error.response) {
          if (error.response.data && error.response.data.msg) {
            errorMsg = error.response.data.msg
          }
        } else if (error.message) {
          errorMsg = error.message
        }
        ElMessage.error(errorMsg)
      }
    })
    .catch(() => {
      ElMessage.info('已取消解绑')
    })
}

const handleBind = (cardId) => {
  ElMessageBox.confirm('确定要绑定该银行卡吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  .then(() => handleBindCard(cardId))
  .catch(() => {
    ElMessage.info('已取消绑定')
  })
}

const setAsDefault = async (cardId) => {
  try {
    const response = await axios.put(`http://localhost:8091/bankCards/${cardId}/default`)
    if (response.data && response.data.code === 0) {
      ElMessage.success('设置默认卡成功')
      await fetchBankCards()
    } else {
      throw new Error(response.data?.msg || '设置默认卡失败')
    }
  } catch (error) {
    console.error('设置默认卡失败:', error)
    let errorMsg = '设置默认卡失败'
    if (error.response) {
      if (error.response.data && error.response.data.msg) {
        errorMsg = error.response.data.msg
      }
    } else if (error.message) {
      errorMsg = error.message
    }
    ElMessage.error(errorMsg)
  }
}

// 删除银行卡
const handleDelete = (row) => {
  // 检查是否为默认卡
  if (row.isDefault === 1) {
    ElMessage.warning('默认银行卡不能删除，请先设置其他卡为默认卡')
    return
  }

  // 检查是否为已绑定的卡
  if (row.status === 1) {
    ElMessage.warning('已绑定的银行卡不能直接删除，请先解绑')
    return
  }

  ElMessageBox.confirm(
    `确定要删除银行卡 "${row.bankName} ${formatCardNumber(row.cardNumber)}" 吗？此操作不可恢复！`,
    '删除银行卡',
    {
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      type: 'error'
    }
  )
  .then(async () => {
    try {
      const response = await axios.delete(`http://localhost:8091/bankCards/${row.cardId}`)
      if (response.data && response.data.code === 0) {
        ElMessage.success('删除银行卡成功')
        await fetchBankCards()
      } else {
        throw new Error(response.data?.msg || '删除银行卡失败')
      }
    } catch (error) {
      console.error('删除银行卡失败:', error)
      let errorMsg = '删除银行卡失败'
      if (error.response) {
        if (error.response.data && error.response.data.msg) {
          errorMsg = error.response.data.msg
        }
      } else if (error.message) {
        errorMsg = error.message
      }
      ElMessage.error(errorMsg)
    }
  })
  .catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 查看详情
const viewDetail = (cardId) => {
  console.log('点击查看详情，cardId:', cardId)
  if (!cardId) {
    ElMessage.error('银行卡ID无效')
    return
  }
  const targetPath = `/home/<USER>/${cardId}`
  console.log('跳转到:', targetPath)
  router.push(targetPath)
}

// 刷新数据
const refreshData = () => {
  fetchBankCards()
}

// 生命周期
onMounted(() => {
  fetchBankCards()
})
</script>

<style scoped>
.bank-card-manage {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.statistics-panel {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: white;
  color: #2c3e50;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  position: relative;
  z-index: 2;
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 40px;
  opacity: 0.2;
  z-index: 1;
}

.stat-icon.total {
  color: #3498db;
}

.stat-icon.bound {
  color: #27ae60;
}

.stat-icon.unbound {
  color: #e74c3c;
}

.stat-icon.default {
  color: #f39c12;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.quick-filters {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 银行卡标签页样式 */
.bank-tabs {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 银行卡网格布局 */
.bank-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  padding: 20px;
}

.bank-card-item {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out;
}

.bank-card-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.bank-card-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.bank-card-item:hover::before {
  transform: scaleX(1);
}

.bank-card-item.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
}

.bank-card-item.selected::before {
  transform: scaleX(1);
}

.card-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.card-type {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
}

.card-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-star {
  color: #f39c12;
  font-size: 18px;
  animation: sparkle 2s infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.status-tag {
  font-size: 11px;
  padding: 2px 8px;
}

.card-number {
  margin-bottom: 12px;
}

.sensitive-data {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-number-text {
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 1px;
}

.toggle-btn {
  padding: 4px !important;
  min-height: auto !important;
  color: #667eea !important;
}

.card-holder,
.card-phone {
  margin-bottom: 8px;
  font-size: 14px;
  color: #5a6c7d;
}

.label {
  font-weight: 500;
  color: #7f8c8d;
}

.card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ecf0f1;
}

.action-btn {
  flex: 1;
  min-width: 70px;
  height: 32px;
  font-size: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-btn:hover {
  background: #e74c3c !important;
  border-color: #e74c3c !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .bank-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .bank-card-manage {
    padding: 10px;
  }

  .statistics-panel .el-col {
    margin-bottom: 10px;
  }

  .bank-cards-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }

  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100% !important;
    margin-bottom: 15px;
  }

  .card-actions {
    flex-direction: column;
  }

  .action-btn {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stat-number {
    font-size: 24px;
  }

  .stat-icon {
    font-size: 30px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .bank-card-item {
    padding: 15px;
  }

  .card-number-text {
    font-size: 14px;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同索引的卡片添加延迟动画 */
.bank-card-item:nth-child(1) { animation-delay: 0.1s; }
.bank-card-item:nth-child(2) { animation-delay: 0.2s; }
.bank-card-item:nth-child(3) { animation-delay: 0.3s; }
.bank-card-item:nth-child(4) { animation-delay: 0.4s; }
.bank-card-item:nth-child(5) { animation-delay: 0.5s; }
.bank-card-item:nth-child(6) { animation-delay: 0.6s; }
</style>