<template>
  <div class="cards-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>
            <el-icon class="header-icon"><CreditCard /></el-icon>
            银行卡管理
          </h2>
          <p>管理系统银行卡信息，包括查看、绑定/解绑、设置默认卡等操作</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><CreditCard /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.totalCards }}</div>
                <div class="stat-label">总银行卡</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon bound">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.boundCards }}</div>
                <div class="stat-label">已绑定</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon unbound">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.unboundCards }}</div>
                <div class="stat-label">未绑定</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon default">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.defaultCards }}</div>
                <div class="stat-label">默认卡</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20" class="search-row">
        <el-col :span="6">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="searchForm.cardNumber"
            placeholder="请输入银行卡号"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><CreditCard /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.isBound"
            placeholder="请选择绑定状态"
            clearable
            style="width: 100%"
          >
            <el-option label="已绑定" :value="1" />
            <el-option label="未绑定" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            添加银行卡
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 银行卡列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="phone" label="用户手机" width="120" />
        <el-table-column prop="cardHolder" label="持卡人姓名" width="120" />
        <el-table-column prop="bankName" label="银行名称" width="120" />
        <el-table-column prop="cardNumber" label="银行卡号" width="180">
          <template #default="{ row }">
            <div class="card-number">
              <span v-if="!row.showFullNumber">
                {{ maskCardNumber(row.cardNumber) }}
              </span>
              <span v-else>{{ row.cardNumber }}</span>
              <el-button
                type="text"
                size="small"
                @click="toggleCardNumber(row)"
                style="margin-left: 8px"
              >
                <el-icon v-if="row.showFullNumber"><Hide /></el-icon>
                <el-icon v-else><View /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="cardType" label="卡片类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.cardType === 1 ? 'primary' : 'success'">
              {{ row.cardType === 1 ? '储蓄卡' : '信用卡' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="绑定状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'info'">
              {{ row.status === 1 ? '已绑定' : '未绑定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认卡" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isDefault === 1" type="warning">
              <el-icon><Star /></el-icon>
              默认
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                :type="row.status === 1 ? 'warning' : 'success'"
                size="small"
                @click="toggleBind(row)"
              >
                <el-icon v-if="row.status === 1"><Close /></el-icon>
                <el-icon v-else><Check /></el-icon>
                {{ row.status === 1 ? '解绑' : '绑定' }}
              </el-button>
              <el-button
                v-if="row.status === 1 && row.isDefault !== 1"
                type="primary"
                size="small"
                @click="setDefault(row)"
              >
                <el-icon><Star /></el-icon>
                设为默认
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteCard(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加银行卡对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加银行卡"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        label-width="100px"
      >
        <el-form-item label="用户ID" required>
          <el-input-number
            v-model="addForm.userId"
            :min="1"
            style="width: 100%"
            placeholder="请输入用户ID"
          />
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
            <el-icon style="margin-right: 4px;"><InfoFilled /></el-icon>
            若没有用户ID，请先前往 系统设置 → 用户管理 添加用户后再添加银行卡
          </div>
        </el-form-item>
        <el-form-item label="银行卡号" required>
          <el-input
            v-model="addForm.cardNumber"
            placeholder="请输入银行卡号"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="银行名称" required>
          <el-input
            v-model="addForm.bankName"
            placeholder="请输入银行名称"
          />
        </el-form-item>
        <el-form-item label="卡片类型" required>
          <el-radio-group v-model="addForm.cardType">
            <el-radio :label="1">储蓄卡</el-radio>
            <el-radio :label="2">信用卡</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="持卡人姓名" required>
          <el-input
            v-model="addForm.cardHolder"
            placeholder="请输入持卡人姓名"
          />
        </el-form-item>
        <el-form-item label="预留手机号" required>
          <el-input
            v-model="addForm.phone"
            placeholder="请输入预留手机号"
            maxlength="11"
          />
        </el-form-item>
        <el-form-item label="绑定状态">
          <el-radio-group v-model="addForm.status">
            <el-radio :label="0">未绑定</el-radio>
            <el-radio :label="1">已绑定</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CreditCard, Check, Close, Star, Plus, Refresh, Search, View, Hide, InfoFilled
} from '@element-plus/icons-vue'

// 配置axios基础URL
axios.defaults.baseURL = 'http://localhost:8091'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const showAddDialog = ref(false)

// 统计数据
const statistics = ref({
  totalCards: 0,
  boundCards: 0,
  unboundCards: 0,
  defaultCards: 0
})

// 搜索表单
const searchForm = reactive({
  phone: '',
  cardNumber: '',
  isBound: null
})

// 添加表单
const addForm = reactive({
  userId: null,
  cardNumber: '',
  bankName: '',
  cardType: 1,
  cardHolder: '',
  phone: '',
  status: 1
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await axios.get('/bankCards/admin/statistics')
    if (response.data.code === 0) {
      statistics.value = response.data.data
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

const fetchCards = async () => {
  try {
    loading.value = true
    const response = await axios.get('/bankCards/admin/page', {
      params: {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        phone: searchForm.phone,
        cardNumber: searchForm.cardNumber,
        isBound: searchForm.isBound
      }
    })

    if (response.data.code === 0) {
      tableData.value = response.data.data.records.map(item => ({
        ...item,
        showFullNumber: false
      }))
      total.value = response.data.data.total
    } else {
      ElMessage.error(response.data.msg || '获取银行卡列表失败')
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error)
    ElMessage.error('获取银行卡列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchCards()
}

// 重置
const handleReset = () => {
  searchForm.phone = ''
  searchForm.cardNumber = ''
  searchForm.isBound = null
  currentPage.value = 1
  fetchCards()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchCards()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchCards()
}

// 切换卡号显示
const toggleCardNumber = (row) => {
  row.showFullNumber = !row.showFullNumber
}

// 卡号脱敏
const maskCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  return cardNumber.replace(/(\d{4})\d{8,12}(\d{4})/, '$1****$2')
}

// 切换绑定状态
const toggleBind = async (row) => {
  const action = row.status === 1 ? '解绑' : '绑定'
  try {
    await ElMessageBox.confirm(`确定要${action}该银行卡吗？`, '确认操作')

    const url = row.status === 1 ? `/bankCards/admin/unbind/${row.cardId}` : `/bankCards/admin/bind/${row.cardId}`
    await axios.post(url)

    ElMessage.success(`${action}成功`)
    fetchCards()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('绑定状态切换失败:', error)
      ElMessage.error('绑定状态切换失败')
    }
  }
}

// 设为默认
const setDefault = async (row) => {
  try {
    await axios.put(`/bankCards/admin/${row.cardId}/default`)
    ElMessage.success('设置默认卡片成功')
    fetchCards()
  } catch (error) {
    console.error('设置默认卡片失败:', error)
    ElMessage.error('设置默认卡片失败')
  }
}

// 删除银行卡
const deleteCard = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该银行卡吗？此操作不可恢复！', '确认删除', {
      type: 'warning'
    })

    await axios.delete(`/bankCards/admin/${row.cardId}`)
    ElMessage.success('删除成功')
    fetchCards()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除银行卡失败:', error)
      ElMessage.error('删除银行卡失败')
    }
  }
}

// 确认添加
const confirmAdd = async () => {
  if (!addForm.userId || !addForm.cardNumber || !addForm.bankName || !addForm.cardHolder || !addForm.phone) {
    ElMessage.warning('请填写完整信息')
    return
  }

  try {
    await axios.post('/bankCards/admin/add', addForm)
    ElMessage.success('添加银行卡成功')
    showAddDialog.value = false
    refreshData()

    // 重置表单
    Object.assign(addForm, {
      userId: null,
      cardNumber: '',
      bankName: '',
      cardType: 1,
      cardHolder: '',
      phone: '',
      status: 1
    })
  } catch (error) {
    console.error('添加银行卡失败:', error)
    ElMessage.error('添加银行卡失败')
  }
}

// 刷新数据
const refreshData = () => {
  loadStatistics()
  fetchCards()
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
  fetchCards()
})
</script>

<style scoped>
.cards-manage {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 12px;
}

.header-left p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  font-size: 16px;
  padding: 12px 24px;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.bound {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.unbound {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.default {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.search-card, .table-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-row {
  align-items: center;
}

.card-number {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .cards-manage {
    padding: 12px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-left h2 {
    font-size: 24px;
  }

  .statistics-cards .el-col {
    margin-bottom: 12px;
  }

  .search-row .el-col {
    margin-bottom: 12px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
    width: 56px;
    height: 56px;
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 16px;
  }

  .header-left h2 {
    font-size: 20px;
    gap: 8px;
  }

  .header-icon {
    font-size: 24px;
    padding: 6px;
  }
}
</style>
