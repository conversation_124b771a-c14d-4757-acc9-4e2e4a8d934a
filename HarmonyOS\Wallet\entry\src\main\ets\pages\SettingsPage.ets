import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

interface UserInfo {
  name: string;
  phone: string;
  avatar: Resource;
}

@Entry
@Component
export struct SettingsPage {
  @State userInfo: UserInfo = {
    name: '张三',
    phone: '138****1234',
    avatar: $r('app.media.icon_avatar')
  };
  @State showPasswordDialog: boolean = false;
  @State showLimitDialog: boolean = false;
  @State showLogoutDialog: boolean = false;

  build() {
    // 根容器
    Column() {
      // 主内容区域
      Column() {
        // 用户信息卡片
        this.UserInfoCard()

        // 设置选项列表
        this.SettingsList()

        // 退出登录按钮
        this.LogoutButton()
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#f5f5f5')

      // 弹窗区域（覆盖在主内容之上）
      if (this.showPasswordDialog) {
        this.PasswordDialog()
      }

      if (this.showLimitDialog) {
        this.LimitDialog()
      }

      if (this.showLogoutDialog) {
        this.LogoutDialog()
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  UserInfoCard() {
    Row() {
      Image(this.userInfo.avatar)
        .width(60)
        .height(60)
        .borderRadius(30)
        .margin({right: 15})

      Column() {
        Text(this.userInfo.name)
          .fontSize(18)
          .fontColor('#333333')
        Text(this.userInfo.phone)
          .fontSize(14)
          .fontColor('#666666')
          .margin({top: 4})
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .margin({top: 20})
    .shadow({radius: 4, color: '#00000010', offsetX: 0, offsetY: 2})
  }

  @Builder
  SettingsList() {
    Column() {
      this.SettingItem('支付密码设置', $r('app.media.password'), () => {
        this.showPasswordDialog = true;
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('支付限额设置', $r('app.media.setting'), () => {
        this.showLimitDialog = true;
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('修改登录密码', $r('app.media.edit'), () => {
        router.pushUrl({url: 'pages/ChangePasswordPage'});
      })

      Divider()
        .strokeWidth(0.5)
        .color('#f0f0f0')

      this.SettingItem('关于我们', $r('app.media.about'), () => {
        router.pushUrl({url: 'pages/AboutPage'});
      })
    }
    .width('90%')
    .margin({top: 20})
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .shadow({radius: 4, color: '#00000010', offsetX: 0, offsetY: 2})
  }

  @Builder
  SettingItem(title: string, icon: Resource, onClick: () => void) {
    Row() {
      Image(icon)
        .width(20)
        .height(20)
        .margin({right: 15})

      Text(title)
        .fontSize(16)
        .fontColor('#333333')
        .layoutWeight(1)

      Image($r('app.media.arrow'))
        .width(16)
        .height(16)
    }
    .width('100%')
    .height(60)
    .padding({left: 15, right: 15})
    .onClick(onClick)
  }

  @Builder
  LogoutButton() {
    Button() {
      Text('退出登录')
        .fontSize(16)
        .fontColor('#FF5252')
    }
    .width('90%')
    .height(50)
    .backgroundColor('#ffffff')
    .margin({top: 30})
    .onClick(() => {
      this.showLogoutDialog = true;
    })
  }

  @Builder
  PasswordDialog() {
    Column() {
      Text('设置支付密码')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Text('请输入6位数字支付密码')
        .fontSize(14)
        .fontColor('#666666')
        .margin({bottom: 10})

      TextInput({placeholder: '新支付密码'})
        .width('80%')
        .height(50)
        .type(InputType.Password)
        .maxLength(6)
        .margin({bottom: 10})

      TextInput({placeholder: '确认支付密码'})
        .width('80%')
        .height(50)
        .type(InputType.Password)
        .maxLength(6)
        .margin({bottom: 20})

      Row() {
        Button() {
          Text('取消')
            .fontSize(16)
            .fontColor('#333333')
        }
        .width('40%')
        .height(50)
        .backgroundColor('#f0f0f0')
        .onClick(() => {
          this.showPasswordDialog = false;
        })

        Button() {
          Text('确认')
            .fontSize(16)
            .fontColor(Color.White)
        }
        .width('40%')
        .height(50)
        .backgroundColor('#ee1f98e5')
        .margin({left: 10})
        .onClick(() => {
          this.showPasswordDialog = false;
          promptAction.showToast({message: '支付密码设置成功', duration: 1000});
        })
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '20%'})
    .zIndex(1)
  }

  @Builder
  LimitDialog() {
    Column() {
      Text('支付限额设置')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Text('单笔支付限额')
        .fontSize(16)
        .fontColor('#333333')
          // .alignSelf(HorizontalAlign.Start)
        .margin({left: '10%', bottom: 5})

      TextInput({placeholder: '请输入单笔限额'})
        .width('80%')
        .height(50)
        .type(InputType.Number)
        .margin({bottom: 10})

      Text('每日支付限额')
        .fontSize(16)
        .fontColor('#333333')
          // .alignSelf(HorizontalAlign.Start)
        .margin({left: '10%', bottom: 5})

      TextInput({placeholder: '请输入每日限额'})
        .width('80%')
        .height(50)
        .type(InputType.Number)
        .margin({bottom: 20})

      Row() {
        Button() {
          Text('取消')
            .fontSize(16)
            .fontColor('#333333')
        }
        .width('40%')
        .height(50)
        .backgroundColor('#f0f0f0')
        .onClick(() => {
          this.showLimitDialog = false;
        })

        Button() {
          Text('确认')
            .fontSize(16)
            .fontColor(Color.White)
        }
        .width('40%')
        .height(50)
        .backgroundColor('#ee1f98e5')
        .margin({left: 10})
        .onClick(() => {
          this.showLimitDialog = false;
          promptAction.showToast({message: '限额设置成功', duration: 1000});
        })
      }
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '15%'})
    .zIndex(1)
  }

  @Builder
  LogoutDialog() {
    Column() {
      Text('确认退出登录？')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin({bottom: 20})

      Button() {
        Text('确认退出')
          .fontSize(16)
          .fontColor(Color.White)
      }
      .width('80%')
      .height(50)
      .backgroundColor('#FF5252')
      .onClick(() => {
        this.showLogoutDialog = false;
        router.replaceUrl({url: 'pages/LoginPage'});
      })

      Button() {
        Text('取消')
          .fontSize(16)
          .fontColor('#333333')
      }
      .width('80%')
      .height(50)
      .backgroundColor('#f0f0f0')
      .margin({top: 10})
      .onClick(() => {
        this.showLogoutDialog = false;
      })
    }
    .width('90%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .position({x: '5%', y: '30%'})
    .zIndex(1)
  }
}