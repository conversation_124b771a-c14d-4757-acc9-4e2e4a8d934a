package com.icss.wallet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Admin;
import com.icss.wallet.result.R;
import com.icss.wallet.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
@CrossOrigin
@RestController
@RequestMapping("/admin")
public class AdminController {
    @Autowired
    private AdminService adminService;

    @PostMapping
    public R add(@RequestBody Admin admin) {
        try {
            adminService.addAdmin(admin);
            return R.success("管理员添加成功");
        } catch (RuntimeException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("添加管理员失败");
        }
    }

    @DeleteMapping("/{id}")
    public R delete(@PathVariable Long id) {
        try {
            adminService.deleteAdmin(id);
            return R.success("管理员删除成功");
        } catch (Exception e) {
            return R.failure("删除管理员失败");
        }
    }

    @PutMapping
    public R update(@RequestBody Admin admin) {
        try {
            adminService.updateAdmin(admin);
            return R.success("管理员更新成功");
        } catch (Exception e) {
            return R.failure("更新管理员失败");
        }
    }

    @GetMapping("/page")
    public R page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        IPage<Admin> page = adminService.page(new Page<>(pageNum, pageSize));
        return R.success("分页查询成功", page);
    }

    @GetMapping("/{id}")
    public R getById(@PathVariable Long id) {
        try {
            Admin admin = adminService.getById(id);
            if (admin == null) {
                return R.failure("管理员不存在");
            }
            return R.success("查询成功", admin);
        } catch (Exception e) {
            return R.failure("查询管理员失败");
        }
    }

    /**
     * 获取管理员基本信息列表
     */
    @GetMapping("/basic-info")
    public R getBasicInfo() {
        try {
            return R.success("查询成功", adminService.getBasicInfo());
        } catch (Exception e) {
            return R.failure("查询管理员基本信息失败");
        }
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public R checkUsername(@RequestParam String username) {
        try {
            boolean exists = adminService.isUsernameExists(username);
            if (exists) {
                return R.failure("用户名已存在");
            }
            return R.success("用户名可用");
        } catch (Exception e) {
            return R.failure("检查用户名失败");
        }
    }

    /**
     * 启用/禁用管理员
     */
    @PutMapping("/{id}/status")
    public R toggleStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            Admin admin = adminService.getById(id);
            if (admin == null) {
                return R.failure("管理员不存在");
            }

            admin.setStatus(status);
            adminService.updateAdmin(admin);

            String action = status == 1 ? "启用" : "禁用";
            return R.success(action + "成功");
        } catch (Exception e) {
            return R.failure("操作失败");
        }
    }

}