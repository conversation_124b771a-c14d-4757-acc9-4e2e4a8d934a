package com.icss.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.BankCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface BankCardMapper extends BaseMapper<BankCard> {
    @Update("UPDATE bank_card SET status = #{status}, update_time = NOW() WHERE card_id = #{cardId}")
    int updateCardStatus(@Param("cardId") Long cardId, @Param("status") Integer status);

    @Update("UPDATE bank_card SET is_default = #{isDefault}, update_time = NOW() WHERE card_id = #{cardId}")
    int updateDefaultStatus(@Param("cardId") Long cardId, @Param("isDefault") Integer isDefault);

    // ==================== 管理员专用方法 ====================

    /**
     * 管理员分页查询银行卡信息（包含用户信息）
     */
    @Select("<script>" +
            "SELECT bc.*, u.phone, u.real_name " +
            "FROM bank_card bc " +
            "LEFT JOIN user u ON bc.user_id = u.user_id " +
            "WHERE 1=1 " +
            "<if test='phone != null and phone != \"\"'>" +
            "AND u.phone LIKE CONCAT('%', #{phone}, '%') " +
            "</if>" +
            "<if test='cardNumber != null and cardNumber != \"\"'>" +
            "AND bc.card_number LIKE CONCAT('%', #{cardNumber}, '%') " +
            "</if>" +
            "<if test='isBound != null'>" +
            "AND bc.status = #{isBound} " +
            "</if>" +
            "ORDER BY bc.create_time DESC" +
            "</script>")
    IPage<BankCard> selectBankCardsWithUserInfo(Page<BankCard> page,
                                              @Param("phone") String phone,
                                              @Param("cardNumber") String cardNumber,
                                              @Param("isBound") Integer isBound);

    /**
     * 管理员获取银行卡统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalCards, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as boundCards, " +
            "COUNT(CASE WHEN status = 0 THEN 1 END) as unboundCards, " +
            "COUNT(CASE WHEN is_default = 1 THEN 1 END) as defaultCards " +
            "FROM bank_card")
    java.util.Map<String, Object> getBankCardStatistics();
}