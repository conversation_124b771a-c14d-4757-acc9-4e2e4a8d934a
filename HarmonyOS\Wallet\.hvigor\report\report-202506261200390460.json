{"version": "2.0", "ppid": 31540, "events": [{"head": {"id": "d74c829e-a38e-4058-8b7a-e5f52db4b135", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11947736045800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8af17c-2d1b-45d5-bb03-e060b5e479ff", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11952206148300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3372d0e9-2b87-47c5-b377-622d44a50eea", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11952207525000, "endTime": 11952207553000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "144305e9-d9eb-40c6-bf5c-b18f603629bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "144305e9-d9eb-40c6-bf5c-b18f603629bc", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11952207525000, "endTime": 11952207553000}, "additional": {"logType": "info", "children": [], "durationId": "3372d0e9-2b87-47c5-b377-622d44a50eea"}}, {"head": {"id": "f68c6722-6692-4dfc-b968-e35883daa928", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953613106500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693bc5cb-e728-4bd5-98a9-154cf094d48d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953614467900, "endTime": 11953614492500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "e4ae2dd4-3782-4851-93a8-31dde304961d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4ae2dd4-3782-4851-93a8-31dde304961d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953614467900, "endTime": 11953614492500}, "additional": {"logType": "info", "children": [], "durationId": "693bc5cb-e728-4bd5-98a9-154cf094d48d"}}, {"head": {"id": "ca03eed9-f3a2-4b83-bfc6-c65d3f72aa13", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953614594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd13126-adc4-438c-b1f8-0d838651d553", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953615448400, "endTime": 11953615463100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "d84e0962-8d3d-42f9-83cc-ca7179d4cd8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d84e0962-8d3d-42f9-83cc-ca7179d4cd8a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953615448400, "endTime": 11953615463100}, "additional": {"logType": "info", "children": [], "durationId": "0cd13126-adc4-438c-b1f8-0d838651d553"}}, {"head": {"id": "9c3470cf-c45b-404b-8cdb-21e78897ee01", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953615534800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ec4988-2c00-41e4-9a93-29247ee37b8e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953616483000, "endTime": 11953616518300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "3cb74b96-61cb-4fb0-84e2-d80504736633"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb74b96-61cb-4fb0-84e2-d80504736633", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953616483000, "endTime": 11953616518300}, "additional": {"logType": "info", "children": [], "durationId": "d3ec4988-2c00-41e4-9a93-29247ee37b8e"}}, {"head": {"id": "0f0f5f3d-451a-4388-add3-741e30070cf0", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953616644600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "179ff84f-8c6a-410b-b671-6c274c856074", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953618166300, "endTime": 11953618216400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "8496cd62-1c3c-432a-9853-ad0aafd890e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8496cd62-1c3c-432a-9853-ad0aafd890e8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953618166300, "endTime": 11953618216400}, "additional": {"logType": "info", "children": [], "durationId": "179ff84f-8c6a-410b-b671-6c274c856074"}}, {"head": {"id": "08130825-f9e6-4b52-a2f5-1223a2917790", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953618370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e37a4dc-8bbf-4e14-a106-ab54cc78055c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953619843800, "endTime": 11953619869900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "8d176551-cef1-4783-9070-5a471b61037f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d176551-cef1-4783-9070-5a471b61037f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953619843800, "endTime": 11953619869900}, "additional": {"logType": "info", "children": [], "durationId": "9e37a4dc-8bbf-4e14-a106-ab54cc78055c"}}, {"head": {"id": "d032342b-b3e2-4f2c-86c4-628fe09c9736", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953619972300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e2d56c1-fa98-4467-9860-c4ab951725d3", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953621267800, "endTime": 11953621292200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "d7c49de4-7976-4930-b67f-f95f07237e63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7c49de4-7976-4930-b67f-f95f07237e63", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 11953621267800, "endTime": 11953621292200}, "additional": {"logType": "info", "children": [], "durationId": "9e2d56c1-fa98-4467-9860-c4ab951725d3"}}, {"head": {"id": "b86492b3-c7ee-42c1-9ab9-203dc62376b2", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12067230138800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd2496b3-d509-45af-9179-16b3f407e6e7", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12067233037600, "endTime": 12067233093200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "ee5b793d-7c05-44b8-92ad-d53183bc2bd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee5b793d-7c05-44b8-92ad-d53183bc2bd6", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12067233037600, "endTime": 12067233093200}, "additional": {"logType": "info", "children": [], "durationId": "cd2496b3-d509-45af-9179-16b3f407e6e7"}}, {"head": {"id": "1f86d995-8b31-4601-97e7-91f8926c3253", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069022929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed533be1-18df-4f42-a954-3bae2efc9a8e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069024089900, "endTime": 12069024116000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "238c6425-e0be-46b7-8990-e500f3d7734d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "238c6425-e0be-46b7-8990-e500f3d7734d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069024089900, "endTime": 12069024116000}, "additional": {"logType": "info", "children": [], "durationId": "ed533be1-18df-4f42-a954-3bae2efc9a8e"}}, {"head": {"id": "9ce282ec-1356-4b1a-be6c-6dcc9220c8b7", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069024209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63ae1580-52d5-47d9-a92f-7305724a5e47", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069025083300, "endTime": 12069025289500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "c5070a1c-bd87-482f-a39c-d1af0c63c645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5070a1c-bd87-482f-a39c-d1af0c63c645", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069025083300, "endTime": 12069025289500}, "additional": {"logType": "info", "children": [], "durationId": "63ae1580-52d5-47d9-a92f-7305724a5e47"}}, {"head": {"id": "11a5e4a1-2f8a-43b5-a28b-f4dfcc8560cf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069025392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b53696-3968-4e60-9fe2-17d6a338728e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069026255200, "endTime": 12069026270400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "e37f8521-95e6-4025-8398-dd86a2396a2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e37f8521-95e6-4025-8398-dd86a2396a2b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069026255200, "endTime": 12069026270400}, "additional": {"logType": "info", "children": [], "durationId": "94b53696-3968-4e60-9fe2-17d6a338728e"}}, {"head": {"id": "4a85ee76-8281-41fa-a00b-bcc31f997007", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069026344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae67b9a6-b42c-4e96-9b34-58ef09bb6ac9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069027195900, "endTime": 12069027231900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "cb12a8d0-87c8-4944-9d08-783488363f03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb12a8d0-87c8-4944-9d08-783488363f03", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069027195900, "endTime": 12069027231900}, "additional": {"logType": "info", "children": [], "durationId": "ae67b9a6-b42c-4e96-9b34-58ef09bb6ac9"}}, {"head": {"id": "abac8f90-e9b9-4e7c-9b5c-633f9a93b18b", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069027327700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe9838c-8baa-4190-9dc5-d9270683062d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069028153600, "endTime": 12069028168700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "4221d620-e772-4ec1-ab10-5e13fd6675f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4221d620-e772-4ec1-ab10-5e13fd6675f1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069028153600, "endTime": 12069028168700}, "additional": {"logType": "info", "children": [], "durationId": "ffe9838c-8baa-4190-9dc5-d9270683062d"}}, {"head": {"id": "603f6190-430e-4cd5-9807-f692e429fdad", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069028236100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed2c3e52-8bbe-42f0-8339-592469d56a36", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069029161600, "endTime": 12069029181300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "7ed07711-f9da-4aac-959f-59ca7d67e87b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ed07711-f9da-4aac-959f-59ca7d67e87b", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12069029161600, "endTime": 12069029181300}, "additional": {"logType": "info", "children": [], "durationId": "ed2c3e52-8bbe-42f0-8339-592469d56a36"}}, {"head": {"id": "d0bcae6e-6a25-4a3b-838e-28904332f99c", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12086187356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa237441-aee4-4c39-8b9d-beab19162306", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12086188542900, "endTime": 12086188565200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "6c41aa35-7ce7-4a38-a53f-de35a9fe169f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c41aa35-7ce7-4a38-a53f-de35a9fe169f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12086188542900, "endTime": 12086188565200}, "additional": {"logType": "info", "children": [], "durationId": "aa237441-aee4-4c39-8b9d-beab19162306"}}, {"head": {"id": "149f2c65-4c53-4fdc-892c-fe68d2219e93", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087795263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3198cc5-0dc9-4b50-a307-30f1c11c72fd", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087796400600, "endTime": 12087796419500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "26c4c8d4-d6bf-4a63-82bf-c4d45b90ad30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26c4c8d4-d6bf-4a63-82bf-c4d45b90ad30", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087796400600, "endTime": 12087796419500}, "additional": {"logType": "info", "children": [], "durationId": "e3198cc5-0dc9-4b50-a307-30f1c11c72fd"}}, {"head": {"id": "80fd92ac-9056-4f72-95c7-b558d5ce1c16", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087796499400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec75cb38-f2db-4cf3-a800-d28eda3b1c43", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087797266600, "endTime": 12087797279100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "8713cf0e-abcb-420f-a201-7ab7d90c591d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8713cf0e-abcb-420f-a201-7ab7d90c591d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087797266600, "endTime": 12087797279100}, "additional": {"logType": "info", "children": [], "durationId": "ec75cb38-f2db-4cf3-a800-d28eda3b1c43"}}, {"head": {"id": "b17aa800-40e0-4458-9ebc-0c7fa6b51ead", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087797945300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db216bbc-c417-4d0b-be7a-5fce1bb3803f", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087798729700, "endTime": 12087798743900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "82a0880f-92dc-4893-8519-4832bba1004c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82a0880f-92dc-4893-8519-4832bba1004c", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087798729700, "endTime": 12087798743900}, "additional": {"logType": "info", "children": [], "durationId": "db216bbc-c417-4d0b-be7a-5fce1bb3803f"}}, {"head": {"id": "1cccf65d-6bb6-4e65-89c4-ec0d6836acd5", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087798827300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ff6b6fa-d3b2-4928-b864-724ad5894ce1", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087799595200, "endTime": 12087799610300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "2ba9e227-6738-4262-9c1b-e6e923ba620d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ba9e227-6738-4262-9c1b-e6e923ba620d", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087799595200, "endTime": 12087799610300}, "additional": {"logType": "info", "children": [], "durationId": "3ff6b6fa-d3b2-4928-b864-724ad5894ce1"}}, {"head": {"id": "d1665a05-0b8b-4157-8b60-28b01006a947", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087799684200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7e284d-cef1-4006-b7ac-9874025e03f8", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087800764800, "endTime": 12087800789400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "757c6de5-cf44-4428-8d4a-f98a850d2eaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "757c6de5-cf44-4428-8d4a-f98a850d2eaa", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087800764800, "endTime": 12087800789400}, "additional": {"logType": "info", "children": [], "durationId": "2d7e284d-cef1-4006-b7ac-9874025e03f8"}}, {"head": {"id": "eb2bf77b-d80c-49dc-acf6-2238a52e470c", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087800871100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd0e6b4-4934-4877-a008-f02fe38ccd40", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087801991400, "endTime": 12087802026700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "660f92bc-9e3c-4e0b-bce2-05c97809c1c7", "logId": "057633e9-1761-4ce3-a7e0-ec88dd1d5240"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "057633e9-1761-4ce3-a7e0-ec88dd1d5240", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12087801991400, "endTime": 12087802026700}, "additional": {"logType": "info", "children": [], "durationId": "dbd0e6b4-4934-4877-a008-f02fe38ccd40"}}, {"head": {"id": "5115e444-02c9-490f-9a8d-b8a58b418e94", "name": "watch worker: worker is ready to be terminated.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12097529726400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6425a894-01e6-4257-8348-f62df63a39d6", "name": "Server currently has 0 watch-worker", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12097530174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc1cadb-2d85-4b56-ab1f-3bfb287b776b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098911240900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47142f6-a892-416c-865d-b1397137b497", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098918920700, "endTime": 12099096486600}, "additional": {"children": ["58021bb2-0bf5-4927-9fdc-4db54539d506", "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "6c8a3cc4-8097-4d8f-9c47-65eb810a5f18", "cf859389-2657-4202-b175-ad97c918777b", "426ceff3-8644-41f6-83f8-7174568797a7", "8b533f6e-cfd5-44d3-bf23-34295851ea56", "d277ca53-8136-4ebd-b57d-b54f6be44bd7"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58021bb2-0bf5-4927-9fdc-4db54539d506", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098918923900, "endTime": 12098935041500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "5c9ec283-cb28-4f47-a881-a17ca928c083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098935102200, "endTime": 12099095253900}, "additional": {"children": ["57735ef8-d854-44b0-83a0-cd78c59ba6fd", "c2581b09-edb4-44e3-9d59-dde200f0ffbc", "3fd2458c-9161-4f94-bdd1-087d6a194efc", "9b8ccf04-825e-472c-9b9a-4c27139bb4a3", "9547c933-1c23-4add-bf1c-7b3d38120f89", "48a8576d-f565-4420-8f78-c2f181e09386", "1089ef6e-98e2-4786-9bed-5114a461ee86", "74a24cd5-3884-4814-8fa6-5c3e16a69cce", "06ba22e9-bb3a-428d-9672-4923681cc15c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c8a3cc4-8097-4d8f-9c47-65eb810a5f18", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099095280800, "endTime": 12099096451400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "e06c4705-2441-4330-9ff1-1432b6db1e7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf859389-2657-4202-b175-ad97c918777b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099096458100, "endTime": 12099096481100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "5261f1ea-63b6-4fbc-a31d-ed0068af7953"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "426ceff3-8644-41f6-83f8-7174568797a7", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098922900200, "endTime": 12098922928900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "c3441844-d492-4e40-8e64-c652a0a03fc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3441844-d492-4e40-8e64-c652a0a03fc8", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098922900200, "endTime": 12098922928900}, "additional": {"logType": "info", "children": [], "durationId": "426ceff3-8644-41f6-83f8-7174568797a7", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "8b533f6e-cfd5-44d3-bf23-34295851ea56", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098928098900, "endTime": 12098928114600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "097e0ca5-f166-4dc6-898c-0b5f7a9cd552"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "097e0ca5-f166-4dc6-898c-0b5f7a9cd552", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098928098900, "endTime": 12098928114600}, "additional": {"logType": "info", "children": [], "durationId": "8b533f6e-cfd5-44d3-bf23-34295851ea56", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "10452dde-35b6-4323-a1e7-4c48b9a03fd0", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098928187000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed98fd3-038d-4232-bdf8-6a694b8e82eb", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098934842100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c9ec283-cb28-4f47-a881-a17ca928c083", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098918923900, "endTime": 12098935041500}, "additional": {"logType": "info", "children": [], "durationId": "58021bb2-0bf5-4927-9fdc-4db54539d506", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "57735ef8-d854-44b0-83a0-cd78c59ba6fd", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098941485600, "endTime": 12098941499000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "2eb2e033-d616-4aaa-833b-b61e45a8e809"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2581b09-edb4-44e3-9d59-dde200f0ffbc", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098941520700, "endTime": 12098945443200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "c2483fb4-5582-46f0-8d14-932b61a92491"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fd2458c-9161-4f94-bdd1-087d6a194efc", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098945455300, "endTime": 12099029946800}, "additional": {"children": ["e933c7cc-51e9-4b8f-a04d-daa40ccb0832", "36a2ae55-c3cd-4a83-86e8-5b3c8a8abb4f", "61330602-fdd5-4e1a-bc61-fd1682a80c3a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "3ae3a343-3ad8-45d9-b485-89f4bc8edf2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b8ccf04-825e-472c-9b9a-4c27139bb4a3", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099029962400, "endTime": 12099053376800}, "additional": {"children": ["90885509-d551-4460-b515-4d51c67d3446"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "0de4f00f-b4b0-43ab-b625-0d01909614aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9547c933-1c23-4add-bf1c-7b3d38120f89", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099053384400, "endTime": 12099070256100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "df5b8932-e129-4acf-879f-8e2aa42a4dd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48a8576d-f565-4420-8f78-c2f181e09386", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099071377000, "endTime": 12099080557100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "5332dbd1-41fe-4275-beeb-71b3093ee9d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1089ef6e-98e2-4786-9bed-5114a461ee86", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099080580800, "endTime": 12099095096900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "7f9b3c6c-7867-49d0-8d24-d7d0928a1630"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74a24cd5-3884-4814-8fa6-5c3e16a69cce", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099095118200, "endTime": 12099095239600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "d17d7904-775a-4212-b96e-8665d41c51b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2eb2e033-d616-4aaa-833b-b61e45a8e809", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098941485600, "endTime": 12098941499000}, "additional": {"logType": "info", "children": [], "durationId": "57735ef8-d854-44b0-83a0-cd78c59ba6fd", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "c2483fb4-5582-46f0-8d14-932b61a92491", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098941520700, "endTime": 12098945443200}, "additional": {"logType": "info", "children": [], "durationId": "c2581b09-edb4-44e3-9d59-dde200f0ffbc", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "e933c7cc-51e9-4b8f-a04d-daa40ccb0832", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098946058800, "endTime": 12098946076900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3fd2458c-9161-4f94-bdd1-087d6a194efc", "logId": "6f662a09-9edc-4d2d-8777-029da9bf0218"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f662a09-9edc-4d2d-8777-029da9bf0218", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098946058800, "endTime": 12098946076900}, "additional": {"logType": "info", "children": [], "durationId": "e933c7cc-51e9-4b8f-a04d-daa40ccb0832", "parent": "3ae3a343-3ad8-45d9-b485-89f4bc8edf2f"}}, {"head": {"id": "36a2ae55-c3cd-4a83-86e8-5b3c8a8abb4f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098947910300, "endTime": 12099028696200}, "additional": {"children": ["47d8323a-f250-4a16-8997-df4b8b1c296a", "f37ac436-3240-4b2f-9272-fa2843c3263d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3fd2458c-9161-4f94-bdd1-087d6a194efc", "logId": "ec19c1bd-bf49-4041-b99e-10661c7e229b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47d8323a-f250-4a16-8997-df4b8b1c296a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098947911400, "endTime": 12098955639500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36a2ae55-c3cd-4a83-86e8-5b3c8a8abb4f", "logId": "bb54826c-7094-4342-938b-594f6e8c4aab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f37ac436-3240-4b2f-9272-fa2843c3263d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098955655000, "endTime": 12099028681000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36a2ae55-c3cd-4a83-86e8-5b3c8a8abb4f", "logId": "827a19b0-5798-4194-b08d-d64188bb3b1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a86575e-3bcd-42aa-9beb-63c1fe637250", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098947917000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89c460d-8d00-43d1-a76a-5af3634984ca", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098955506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb54826c-7094-4342-938b-594f6e8c4aab", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098947911400, "endTime": 12098955639500}, "additional": {"logType": "info", "children": [], "durationId": "47d8323a-f250-4a16-8997-df4b8b1c296a", "parent": "ec19c1bd-bf49-4041-b99e-10661c7e229b"}}, {"head": {"id": "97972053-4df3-4506-be0d-be9e82beac5a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098955670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47a145e-b549-4e6a-93aa-33f0af864cf5", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098962499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53089208-cce7-4952-8a80-602930cfd9d3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098962624200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856c61ef-3efd-4c42-9b13-b37f573e8ecf", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098963656900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad39367-9395-4576-be5b-e9a68efd6659", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098963906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d05c48d-917c-4ee4-923e-fa5ce68bdcd0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098965451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d2dd41-7b62-44f7-b571-92dbc4e476a6", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098969997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f3665b-50af-4e93-802e-8244cc2947f5", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098980127600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb0f4339-7255-4596-84c3-ba669a938e37", "name": "Sdk init in 36 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099006699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f22339-825f-47a6-a8a7-1249c1f17afa", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099006841600}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 0}, "markType": "other"}}, {"head": {"id": "19a238bd-7b85-4960-af83-b605b9488b17", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099006883700}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 0}, "markType": "other"}}, {"head": {"id": "c0d79d17-fd23-48ea-8416-b17018b8fc2a", "name": "Project task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099028379400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ccf95e9-8e50-4690-adaf-acb332f657df", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099028509400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b787a19e-4ce9-4629-a651-b4f32683d4ea", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099028576700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d531d9f1-ab63-4a12-8c01-fadda4ea7f30", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099028629100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827a19b0-5798-4194-b08d-d64188bb3b1a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098955655000, "endTime": 12099028681000}, "additional": {"logType": "info", "children": [], "durationId": "f37ac436-3240-4b2f-9272-fa2843c3263d", "parent": "ec19c1bd-bf49-4041-b99e-10661c7e229b"}}, {"head": {"id": "ec19c1bd-bf49-4041-b99e-10661c7e229b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098947910300, "endTime": 12099028696200}, "additional": {"logType": "info", "children": ["bb54826c-7094-4342-938b-594f6e8c4aab", "827a19b0-5798-4194-b08d-d64188bb3b1a"], "durationId": "36a2ae55-c3cd-4a83-86e8-5b3c8a8abb4f", "parent": "3ae3a343-3ad8-45d9-b485-89f4bc8edf2f"}}, {"head": {"id": "61330602-fdd5-4e1a-bc61-fd1682a80c3a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099029916400, "endTime": 12099029930000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3fd2458c-9161-4f94-bdd1-087d6a194efc", "logId": "52a9d43a-3f5f-4620-a03b-a59236c81e3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52a9d43a-3f5f-4620-a03b-a59236c81e3b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099029916400, "endTime": 12099029930000}, "additional": {"logType": "info", "children": [], "durationId": "61330602-fdd5-4e1a-bc61-fd1682a80c3a", "parent": "3ae3a343-3ad8-45d9-b485-89f4bc8edf2f"}}, {"head": {"id": "3ae3a343-3ad8-45d9-b485-89f4bc8edf2f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098945455300, "endTime": 12099029946800}, "additional": {"logType": "info", "children": ["6f662a09-9edc-4d2d-8777-029da9bf0218", "ec19c1bd-bf49-4041-b99e-10661c7e229b", "52a9d43a-3f5f-4620-a03b-a59236c81e3b"], "durationId": "3fd2458c-9161-4f94-bdd1-087d6a194efc", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "90885509-d551-4460-b515-4d51c67d3446", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099030562000, "endTime": 12099053363500}, "additional": {"children": ["3b94d771-dd25-4b0f-8c72-6cd69c9ce20e", "70f70ecc-44af-44e7-ba56-ecac42760f5f", "582d4a0a-448d-45fb-b116-2c9730dabae5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b8ccf04-825e-472c-9b9a-4c27139bb4a3", "logId": "347ed132-7f66-44e7-a96a-781cdea4d020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b94d771-dd25-4b0f-8c72-6cd69c9ce20e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099033554400, "endTime": 12099033570000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90885509-d551-4460-b515-4d51c67d3446", "logId": "28a9906c-3e69-42bb-ae34-eacafc435f29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28a9906c-3e69-42bb-ae34-eacafc435f29", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099033554400, "endTime": 12099033570000}, "additional": {"logType": "info", "children": [], "durationId": "3b94d771-dd25-4b0f-8c72-6cd69c9ce20e", "parent": "347ed132-7f66-44e7-a96a-781cdea4d020"}}, {"head": {"id": "70f70ecc-44af-44e7-ba56-ecac42760f5f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099035731900, "endTime": 12099051863100}, "additional": {"children": ["c1805576-2dff-4c19-b79a-7f4ee95a9bc2", "833c91f2-d018-4409-962c-81ae95819628"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90885509-d551-4460-b515-4d51c67d3446", "logId": "ecc1d90d-f97d-4630-ba20-034efe10cf65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1805576-2dff-4c19-b79a-7f4ee95a9bc2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099035733200, "endTime": 12099039591700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70f70ecc-44af-44e7-ba56-ecac42760f5f", "logId": "e2326f4c-110b-4933-b2dc-e1ad61582bdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "833c91f2-d018-4409-962c-81ae95819628", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099039606100, "endTime": 12099051851000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70f70ecc-44af-44e7-ba56-ecac42760f5f", "logId": "940c47d6-1ce3-46e7-8161-ac82ba1c5117"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2d94222-5da0-4396-8f2b-c36970e87b88", "name": "hvigorfile, resolving D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099035740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d654bf-dc63-4e86-bf11-f854abc2667b", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099039479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2326f4c-110b-4933-b2dc-e1ad61582bdf", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099035733200, "endTime": 12099039591700}, "additional": {"logType": "info", "children": [], "durationId": "c1805576-2dff-4c19-b79a-7f4ee95a9bc2", "parent": "ecc1d90d-f97d-4630-ba20-034efe10cf65"}}, {"head": {"id": "644f27dd-e681-46a3-b191-b33fab0b9b70", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099039624500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94208c7-7c0a-41a6-bd5e-7473b768db4d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047100900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2e7b18-9fec-4027-ba19-be7ae189c235", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047254300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ce6edf-f3a4-4904-b527-5f8b6a54776a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047509200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f8405e-76d4-48d9-8d65-7d4c95e71313", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047695000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb5977d-741c-4d50-9fae-16b64757ae97", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047772400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bafec8e-f76b-4845-a1b7-9a2973cb3e50", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745b7a26-3659-4afc-85db-d278b39199d4", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099047896800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d861e3-e909-4182-8dbd-c68dc716831e", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099051493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c47c20e-ce1c-4303-af7c-d14b4c730332", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099051677700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a893b41-fc3b-4dba-9b91-b3f1b81f5c18", "name": "hvigorfile, no custom plugins were found in D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099051752400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23fc267-abb3-438d-bda8-9f1767327b45", "name": "hvigorfile, resolve finished D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099051804400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940c47d6-1ce3-46e7-8161-ac82ba1c5117", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099039606100, "endTime": 12099051851000}, "additional": {"logType": "info", "children": [], "durationId": "833c91f2-d018-4409-962c-81ae95819628", "parent": "ecc1d90d-f97d-4630-ba20-034efe10cf65"}}, {"head": {"id": "ecc1d90d-f97d-4630-ba20-034efe10cf65", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099035731900, "endTime": 12099051863100}, "additional": {"logType": "info", "children": ["e2326f4c-110b-4933-b2dc-e1ad61582bdf", "940c47d6-1ce3-46e7-8161-ac82ba1c5117"], "durationId": "70f70ecc-44af-44e7-ba56-ecac42760f5f", "parent": "347ed132-7f66-44e7-a96a-781cdea4d020"}}, {"head": {"id": "582d4a0a-448d-45fb-b116-2c9730dabae5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099053334800, "endTime": 12099053346900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "90885509-d551-4460-b515-4d51c67d3446", "logId": "dfc63652-6f9f-4a1e-8cf8-01eecbb36433"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfc63652-6f9f-4a1e-8cf8-01eecbb36433", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099053334800, "endTime": 12099053346900}, "additional": {"logType": "info", "children": [], "durationId": "582d4a0a-448d-45fb-b116-2c9730dabae5", "parent": "347ed132-7f66-44e7-a96a-781cdea4d020"}}, {"head": {"id": "347ed132-7f66-44e7-a96a-781cdea4d020", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099030562000, "endTime": 12099053363500}, "additional": {"logType": "info", "children": ["28a9906c-3e69-42bb-ae34-eacafc435f29", "ecc1d90d-f97d-4630-ba20-034efe10cf65", "dfc63652-6f9f-4a1e-8cf8-01eecbb36433"], "durationId": "90885509-d551-4460-b515-4d51c67d3446", "parent": "0de4f00f-b4b0-43ab-b625-0d01909614aa"}}, {"head": {"id": "0de4f00f-b4b0-43ab-b625-0d01909614aa", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099029962400, "endTime": 12099053376800}, "additional": {"logType": "info", "children": ["347ed132-7f66-44e7-a96a-781cdea4d020"], "durationId": "9b8ccf04-825e-472c-9b9a-4c27139bb4a3", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "8d3236ce-89bf-4888-b324-10befca61bd4", "name": "watch files: [\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\hvigorfile.ts',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099069330100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46faeb9b-2928-45a4-9e6c-8f7e62647572", "name": "hvigorfile, resolve hvigorfile dependencies in 17 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099070145000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5b8932-e129-4acf-879f-8e2aa42a4dd8", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099053384400, "endTime": 12099070256100}, "additional": {"logType": "info", "children": [], "durationId": "9547c933-1c23-4add-bf1c-7b3d38120f89", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "06ba22e9-bb3a-428d-9672-4923681cc15c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099071153200, "endTime": 12099071360500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "logId": "fa7e3fd7-46f4-4b92-adf3-3fd8ad1dd315"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "905620aa-5400-4994-8b70-5e3c53970044", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099071179700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa7e3fd7-46f4-4b92-adf3-3fd8ad1dd315", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099071153200, "endTime": 12099071360500}, "additional": {"logType": "info", "children": [], "durationId": "06ba22e9-bb3a-428d-9672-4923681cc15c", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "b56a6d1c-4e3b-4ef7-8f0f-74af2f3bfd4d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099072878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a273f91-eb31-4f55-8d76-f5b084835588", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099079792400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5332dbd1-41fe-4275-beeb-71b3093ee9d2", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099071377000, "endTime": 12099080557100}, "additional": {"logType": "info", "children": [], "durationId": "48a8576d-f565-4420-8f78-c2f181e09386", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "8af7cb6b-c507-4f03-b27e-ab399078b237", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099080600900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6501b81-9bac-4506-b3fe-b440d5e5d4ef", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099086272400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d932a070-4ede-4a25-a50f-d1b8c927cfd2", "name": "<PERSON><PERSON><PERSON> Wall<PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099086404800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "416cc25b-9543-430b-98e0-433dc80f068e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099086915700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "169a48c2-c15d-4107-9318-1143207618b4", "name": "Module entry Collected Dependency: D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099091830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe71451b-c00f-439b-b9ba-4c1de461dac5", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099091965100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f9b3c6c-7867-49d0-8d24-d7d0928a1630", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099080580800, "endTime": 12099095096900}, "additional": {"logType": "info", "children": [], "durationId": "1089ef6e-98e2-4786-9bed-5114a461ee86", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "b4fe15b3-975f-4236-af97-d2a6326a643e", "name": "Configuration phase cost:154 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099095143500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d17d7904-775a-4212-b96e-8665d41c51b3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099095118200, "endTime": 12099095239600}, "additional": {"logType": "info", "children": [], "durationId": "74a24cd5-3884-4814-8fa6-5c3e16a69cce", "parent": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7"}}, {"head": {"id": "ac127db0-c7f3-42d9-a3b3-7fba200eacc7", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098935102200, "endTime": 12099095253900}, "additional": {"logType": "info", "children": ["2eb2e033-d616-4aaa-833b-b61e45a8e809", "c2483fb4-5582-46f0-8d14-932b61a92491", "3ae3a343-3ad8-45d9-b485-89f4bc8edf2f", "0de4f00f-b4b0-43ab-b625-0d01909614aa", "df5b8932-e129-4acf-879f-8e2aa42a4dd8", "5332dbd1-41fe-4275-beeb-71b3093ee9d2", "7f9b3c6c-7867-49d0-8d24-d7d0928a1630", "d17d7904-775a-4212-b96e-8665d41c51b3", "fa7e3fd7-46f4-4b92-adf3-3fd8ad1dd315"], "durationId": "e1c2a92f-a1fd-4afa-a04d-66ca6b4deaff", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "d277ca53-8136-4ebd-b57d-b54f6be44bd7", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099096427300, "endTime": 12099096439800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a47142f6-a892-416c-865d-b1397137b497", "logId": "966a4254-27b5-4781-ab2d-b3aa54db063d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "966a4254-27b5-4781-ab2d-b3aa54db063d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099096427300, "endTime": 12099096439800}, "additional": {"logType": "info", "children": [], "durationId": "d277ca53-8136-4ebd-b57d-b54f6be44bd7", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "e06c4705-2441-4330-9ff1-1432b6db1e7f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099095280800, "endTime": 12099096451400}, "additional": {"logType": "info", "children": [], "durationId": "6c8a3cc4-8097-4d8f-9c47-65eb810a5f18", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "5261f1ea-63b6-4fbc-a31d-ed0068af7953", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099096458100, "endTime": 12099096481100}, "additional": {"logType": "info", "children": [], "durationId": "cf859389-2657-4202-b175-ad97c918777b", "parent": "a842f8ba-6a34-4824-9686-2cc36c35e5f6"}}, {"head": {"id": "a842f8ba-6a34-4824-9686-2cc36c35e5f6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098918920700, "endTime": 12099096486600}, "additional": {"logType": "info", "children": ["5c9ec283-cb28-4f47-a881-a17ca928c083", "ac127db0-c7f3-42d9-a3b3-7fba200eacc7", "e06c4705-2441-4330-9ff1-1432b6db1e7f", "5261f1ea-63b6-4fbc-a31d-ed0068af7953", "c3441844-d492-4e40-8e64-c652a0a03fc8", "097e0ca5-f166-4dc6-898c-0b5f7a9cd552", "966a4254-27b5-4781-ab2d-b3aa54db063d"], "durationId": "a47142f6-a892-416c-865d-b1397137b497"}}, {"head": {"id": "39670145-5269-4c9a-b79c-881b7bd5c379", "name": "Configuration task cost before running: 182 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099096627800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb23998c-eeea-406b-997a-8e1a5fba241f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099101709000, "endTime": 12099110261800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7f50fb23-fb3a-43f2-b6bb-890efb9953e7", "logId": "03b292f0-f33f-4e88-8539-0ea33c4d42a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f50fb23-fb3a-43f2-b6bb-890efb9953e7", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099097960700}, "additional": {"logType": "detail", "children": [], "durationId": "cb23998c-eeea-406b-997a-8e1a5fba241f"}}, {"head": {"id": "b7a250a7-9ac6-40cf-b2e8-ae39543aabed", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099098806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f727f2-0374-4bbf-942b-d3a819c2b055", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099098891400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef8778f-b183-4cd7-9a26-7be54bf6edee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099098948100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b3cda8-f843-4770-b4fa-7536a0d343fb", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099101726600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1c97fb-2bb3-4e8f-bdac-38e9679f0238", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099109958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c959fc-1b97-40a0-a906-70f40e24abd3", "name": "entry : default@PreBuild cost memory 0.3159637451171875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099110166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b292f0-f33f-4e88-8539-0ea33c4d42a5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099101709000, "endTime": 12099110261800}, "additional": {"logType": "info", "children": [], "durationId": "cb23998c-eeea-406b-997a-8e1a5fba241f"}}, {"head": {"id": "4fe8f915-86e5-4840-8280-b9edb3815f45", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099115133400, "endTime": 12099118589200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "007e0016-579c-4cba-a3f8-57df17cb3bda", "logId": "6295ac4b-3839-4f7a-b29b-53c64aeeee87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "007e0016-579c-4cba-a3f8-57df17cb3bda", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099113853100}, "additional": {"logType": "detail", "children": [], "durationId": "4fe8f915-86e5-4840-8280-b9edb3815f45"}}, {"head": {"id": "cede6d30-d241-4da6-b632-321baea6f149", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099114328700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fad2ae7-022d-460b-b187-ae3010e54605", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099114418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11fe87d4-0855-4be4-90fd-cc94ad8eb1f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099114478100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ab8fc4-678c-4ae2-8b8b-0b74e1148dbc", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099115143500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f08241f-1af4-4290-b22a-bf54b5e779b0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099118338000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d936fe9e-1d4c-489c-9969-738c8a1380f0", "name": "entry : default@MergeProfile cost memory 0.144927978515625", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099118498600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6295ac4b-3839-4f7a-b29b-53c64aeeee87", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099115133400, "endTime": 12099118589200}, "additional": {"logType": "info", "children": [], "durationId": "4fe8f915-86e5-4840-8280-b9edb3815f45"}}, {"head": {"id": "f8df2400-6103-472f-8c9d-f2f823a78686", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099123081800, "endTime": 12099127081100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8a4b7945-01e2-482f-8f66-c99a2e036c20", "logId": "217e96d8-dc57-4ed0-bb4d-490d11083d6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a4b7945-01e2-482f-8f66-c99a2e036c20", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099121061700}, "additional": {"logType": "detail", "children": [], "durationId": "f8df2400-6103-472f-8c9d-f2f823a78686"}}, {"head": {"id": "2a513466-5dea-44d4-8339-a58b4228ec3a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099121705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318a4c81-20bd-4dc8-b8f5-2c187ec7c62e", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099121825400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3359f5f-3bfb-4875-ad07-60e5e10ca111", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099121922400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44a6c48-7aa3-471b-83c0-b98963901758", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099123095300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20dfbd1-d43d-4513-98ce-20f68a7af4e3", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099124366500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e46b1e-1351-4592-ac52-053d189c0e53", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099126560100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad8cee2b-1936-45b5-a327-bf08948f1aae", "name": "entry : default@CreateBuildProfile cost memory 0.34381103515625", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099126929700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "217e96d8-dc57-4ed0-bb4d-490d11083d6e", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099123081800, "endTime": 12099127081100}, "additional": {"logType": "info", "children": [], "durationId": "f8df2400-6103-472f-8c9d-f2f823a78686"}}, {"head": {"id": "c41b7687-f9ca-4dc1-b13c-4a12b44be0d8", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131425600, "endTime": 12099132102100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a5531b77-894f-42e2-b9ff-3155ec3a3de0", "logId": "52eb31b9-f914-447e-93eb-982d1eb7bc5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5531b77-894f-42e2-b9ff-3155ec3a3de0", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099129413000}, "additional": {"logType": "detail", "children": [], "durationId": "c41b7687-f9ca-4dc1-b13c-4a12b44be0d8"}}, {"head": {"id": "eba048d6-08de-44a5-af60-c9c2bfae60a3", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099130087800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa593e2f-c567-4067-934a-0b80df0f7e65", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099130198800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46900087-f98e-4e16-97dc-71d1faf4f7fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099130291500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcaf159a-665b-471d-95cf-441b4e8f1a16", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131437100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12b2bb5-6404-477e-91e8-564b09bca125", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b549bb6e-039e-4888-9468-3cf5de990f56", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131675200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0cf7f04-e9a9-4016-9128-90303906b753", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64bbd108-70b5-4d1d-8c0e-a46fda0251e4", "name": "entry : default@PreCheckSyscap cost memory 0.05063629150390625", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131851900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca3b67e0-587a-4df2-92fe-9601a0ce7ce8", "name": "runTaskFromQueue task cost before running: 218 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131992100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52eb31b9-f914-447e-93eb-982d1eb7bc5f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099131425600, "endTime": 12099132102100, "totalTime": 526700}, "additional": {"logType": "info", "children": [], "durationId": "c41b7687-f9ca-4dc1-b13c-4a12b44be0d8"}}, {"head": {"id": "381cc853-29d8-4ef5-9a0a-70bf7fe68d7a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099145388500, "endTime": 12099146628700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5eb3a869-4983-49b9-a451-c85de7f57dd1", "logId": "7497617c-e5d2-4d64-a9e6-048371f82d33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5eb3a869-4983-49b9-a451-c85de7f57dd1", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099135759500}, "additional": {"logType": "detail", "children": [], "durationId": "381cc853-29d8-4ef5-9a0a-70bf7fe68d7a"}}, {"head": {"id": "e4800287-9b11-4b26-8c54-73bbaa311b0c", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099136406200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "537930f9-e32e-4fef-a253-984dae5d3575", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099136528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ff9d17-919c-462b-ba28-f7a2ee42d0af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099136623400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a193ab-e5e5-4603-936e-f085a0a0519f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099145407900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4336c88f-03cb-4d36-b913-759b6f76b2b7", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099145772500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a236fd-79d7-4a62-8b76-b13ee05bea7b", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099146432200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac9d8ea-cea8-4a67-b8fe-827c5595c50a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07085418701171875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099146549500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7497617c-e5d2-4d64-a9e6-048371f82d33", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099145388500, "endTime": 12099146628700}, "additional": {"logType": "info", "children": [], "durationId": "381cc853-29d8-4ef5-9a0a-70bf7fe68d7a"}}, {"head": {"id": "02218389-4e27-402d-9619-276ebc631cd4", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099150367600, "endTime": 12099152161500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "75d8b407-b9fd-43e3-aa2c-df595a19e3c6", "logId": "e2466ced-3b30-4df6-aa25-0b0c8983f1b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75d8b407-b9fd-43e3-aa2c-df595a19e3c6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099148363900}, "additional": {"logType": "detail", "children": [], "durationId": "02218389-4e27-402d-9619-276ebc631cd4"}}, {"head": {"id": "db2e013d-9e47-432e-a047-f04b8591a3b1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099148917600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3060c4ac-0af9-4393-bd51-9d4c4dba5697", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099149021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df124390-ea85-45dd-aac5-ff7b974301e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099149084900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b389603-917b-41a8-831a-d99169df062d", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099150390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972e25c1-3098-4772-8bc4-52b33b9827c3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099151923400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6fbc0b-c9e1-4566-8d61-08b50975adc9", "name": "entry : default@ProcessProfile cost memory 0.06038665771484375", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099152081200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2466ced-3b30-4df6-aa25-0b0c8983f1b9", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099150367600, "endTime": 12099152161500}, "additional": {"logType": "info", "children": [], "durationId": "02218389-4e27-402d-9619-276ebc631cd4"}}, {"head": {"id": "16bf8195-c6c8-48c2-b406-32898190a96a", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099156354900, "endTime": 12099163241300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5c5c67c3-9775-4ce5-9cc7-b8108a4129a1", "logId": "32ced215-0848-4b36-beb8-2937a620a0f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c5c67c3-9775-4ce5-9cc7-b8108a4129a1", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099153901100}, "additional": {"logType": "detail", "children": [], "durationId": "16bf8195-c6c8-48c2-b406-32898190a96a"}}, {"head": {"id": "8c0fe6c1-7dae-4adf-8072-0e88934376a8", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099154435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf5eafb-5633-4474-8daf-49711a29bbae", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099154533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "366c8e9d-1cc4-45a7-93a9-087f38c618fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099154599400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6747c523-f235-4cdb-9641-5ca3dedb32ff", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099156367700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04c31c71-7103-4320-adf6-9f0be4234ea6", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099162976800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee3e3b9-f26e-401c-bd01-b2eae09ac9b2", "name": "entry : default@ProcessRouterMap cost memory 0.23809051513671875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099163142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ced215-0848-4b36-beb8-2937a620a0f2", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099156354900, "endTime": 12099163241300}, "additional": {"logType": "info", "children": [], "durationId": "16bf8195-c6c8-48c2-b406-32898190a96a"}}, {"head": {"id": "b8953f57-cfbd-46c5-b04e-384fd0f99b79", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099171240400, "endTime": 12099175037800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d6193694-6787-49a7-8932-f9b97643d778", "logId": "e9686f8a-dc89-4880-9655-63f4e7eabfe3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6193694-6787-49a7-8932-f9b97643d778", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099166482000}, "additional": {"logType": "detail", "children": [], "durationId": "b8953f57-cfbd-46c5-b04e-384fd0f99b79"}}, {"head": {"id": "bc406877-bf74-4c7f-b034-f7f660258672", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099167090200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6925d0e3-74ff-45db-894d-5edb3091ebbd", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099167223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a64b9a-a3d6-4122-8e7b-5b1f9c432a2b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099167351500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029858fe-cd8f-43d5-9b03-b40631c83421", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099168550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b139ecb-f5a4-4529-ac3c-82f8a9fb0a54", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099173161400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feb41c98-c74f-4ff9-979e-8d166c66e3f9", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099173321500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "722c8a5f-bbad-41f1-a369-eb741717f4db", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099173389200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "161bc7a5-014a-49f3-ad7a-a10d41ac75e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099173444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e7bd529-184e-48da-944e-367728e2fc00", "name": "entry : default@PreviewProcessResource cost memory -1.5944900512695312", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099173532000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10953123-cf2c-4007-b1c2-8d563fcde357", "name": "runTaskFromQueue task cost before running: 261 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099174935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9686f8a-dc89-4880-9655-63f4e7eabfe3", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099171240400, "endTime": 12099175037800, "totalTime": 2360500}, "additional": {"logType": "info", "children": [], "durationId": "b8953f57-cfbd-46c5-b04e-384fd0f99b79"}}, {"head": {"id": "66391b2c-1fca-4f22-a3cf-1dac2984eec9", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099183112400, "endTime": 12099206649000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6ab115f4-f84c-48d1-a07f-b73187c1f213", "logId": "f121d4b7-9992-44eb-8fa0-bf6cbc737525"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ab115f4-f84c-48d1-a07f-b73187c1f213", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099178243700}, "additional": {"logType": "detail", "children": [], "durationId": "66391b2c-1fca-4f22-a3cf-1dac2984eec9"}}, {"head": {"id": "800445e6-336c-40df-9ab7-1de1272b3445", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099178787500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bc5720-e457-4c56-9447-b6788e44c438", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099178892100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a82c75-e47f-46c1-b9a4-90890170e011", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099178956200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1581139f-1233-460d-aeb4-e620cbccb899", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099183132500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39cc64ac-3129-41e7-8afb-b45c3295da27", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099206395200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5cdedcb-b8bb-48d4-8b04-43e034eee4fc", "name": "entry : default@GenerateLoaderJson cost memory 0.**********46875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099206561600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f121d4b7-9992-44eb-8fa0-bf6cbc737525", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099183112400, "endTime": 12099206649000}, "additional": {"logType": "info", "children": [], "durationId": "66391b2c-1fca-4f22-a3cf-1dac2984eec9"}}, {"head": {"id": "cef3e22d-d543-45c5-9598-d20eb8ba3651", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099222490700, "endTime": 12099934743900}, "additional": {"children": ["deef56a0-9428-4472-afe7-3718e79f2a4b", "acac25ca-92df-4453-96c4-bfb8af10cb2d", "e95015c0-6395-4f86-98f7-2089a9a9bd22", "862003b9-c807-480a-87d7-986402a6725f", "e6fb334a-f118-4b37-807b-1bae3ca23423"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed."], "detailId": "f628356a-c9b2-4774-8fb6-7d9a64e304d3", "logId": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f628356a-c9b2-4774-8fb6-7d9a64e304d3", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099216358600}, "additional": {"logType": "detail", "children": [], "durationId": "cef3e22d-d543-45c5-9598-d20eb8ba3651"}}, {"head": {"id": "7144a9c8-aff0-47a9-bebc-a000ba54558a", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099216996600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e497d345-08ae-4d1b-ac86-cc6ed59a61b5", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099217146800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a913053-0f57-42cc-938b-230280fb5c76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099217241100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c627ac6c-37cf-4a0b-97b5-51244da92455", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099218605300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "086af5f8-8955-4801-be5d-8b61d3684b3f", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099222528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4d3bb4-e08e-4d5f-bf0d-f2045f09ac56", "name": "entry:default@PreviewCompileResource is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099270203900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2646a1-b874-42fd-8e6d-24426f8b0591", "name": "Incremental task entry:default@PreviewCompileResource pre-execution cost: 47 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099270362800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deef56a0-9428-4472-afe7-3718e79f2a4b", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099271742700, "endTime": 12099298181300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cef3e22d-d543-45c5-9598-d20eb8ba3651", "logId": "95616171-d800-475b-a6cd-7329d0f2fe25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95616171-d800-475b-a6cd-7329d0f2fe25", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099271742700, "endTime": 12099298181300}, "additional": {"logType": "info", "children": [], "durationId": "deef56a0-9428-4472-afe7-3718e79f2a4b", "parent": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72"}}, {"head": {"id": "805a1edb-36aa-4caa-8a9b-1c3f9a5b57d3", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099299065900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acac25ca-92df-4453-96c4-bfb8af10cb2d", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099300188900, "endTime": 12099447780700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cef3e22d-d543-45c5-9598-d20eb8ba3651", "logId": "b9205016-bfa5-4d17-847b-4300956f0d09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90d04dc5-83a8-45ec-986b-676f98efa0ba", "name": "current process  memoryUsage: {\n  rss: 155000832,\n  heapTotal: 116641792,\n  heapUsed: 108841632,\n  external: 3109182,\n  arrayBuffers: 103079\n} os memoryUsage :12.677101135253906", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099301685400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67047c3-8e92-4509-b923-2ded1acd72d5", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099445934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9205016-bfa5-4d17-847b-4300956f0d09", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099300188900, "endTime": 12099447780700}, "additional": {"logType": "info", "children": [], "durationId": "acac25ca-92df-4453-96c4-bfb8af10cb2d", "parent": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72"}}, {"head": {"id": "7420bf3a-e5d6-40d9-b6d7-6814b3c4ba2b", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099448037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e95015c0-6395-4f86-98f7-2089a9a9bd22", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099449188400, "endTime": 12099605921500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cef3e22d-d543-45c5-9598-d20eb8ba3651", "logId": "a0d49e35-865a-43aa-87a2-85326baf9935"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d6fe23a-0b0d-45b2-9935-5a311ba5c610", "name": "current process  memoryUsage: {\n  rss: 155353088,\n  heapTotal: 120836096,\n  heapUsed: 109417720,\n  external: 3091502,\n  arrayBuffers: 85414\n} os memoryUsage :12.688514709472656", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099450161500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e978c06e-9b01-4047-9621-36c6c8bf5728", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099603475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d49e35-865a-43aa-87a2-85326baf9935", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099449188400, "endTime": 12099605921500}, "additional": {"logType": "info", "children": [], "durationId": "e95015c0-6395-4f86-98f7-2089a9a9bd22", "parent": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72"}}, {"head": {"id": "138a4e68-462b-4ad2-b07e-4c6b10d2be76", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\oh_modules\\\\.ohpm\\\\@ohos+axios@2.2.6\\\\oh_modules\\\\@ohos\\\\axios\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099606510900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "862003b9-c807-480a-87d7-986402a6725f", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099607587600, "endTime": 12099740742900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cef3e22d-d543-45c5-9598-d20eb8ba3651", "logId": "56f24d7e-9ac7-4221-9994-8824b2177d9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b1f4c87-e055-4091-a08b-1360ceebc1b6", "name": "current process  memoryUsage: {\n  rss: 154578944,\n  heapTotal: 120836096,\n  heapUsed: 109682216,\n  external: 3099820,\n  arrayBuffers: 93796\n} os memoryUsage :12.690673828125", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099608701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f8f1d2-3de6-4336-9eaf-3d47c8a40abf", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099738552400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f24d7e-9ac7-4221-9994-8824b2177d9f", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099607587600, "endTime": 12099740742900}, "additional": {"logType": "info", "children": [], "durationId": "862003b9-c807-480a-87d7-986402a6725f", "parent": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72"}}, {"head": {"id": "8758402a-c0ce-4079-8d4d-272364c24acc", "name": "Use tool [D:\\HarmonyOS\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\HarmonyOS\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***t',\n  '-r',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '--defined-ids',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map\\\\id_defined.json',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-i',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\har_compiled',\n  '-o',\n  'D:\\\\AAAqimo\\\\wallet\\\\HarmonyOS\\\\Wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099741613300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6fb334a-f118-4b37-807b-1bae3ca23423", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099743293900, "endTime": 12099932145000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cef3e22d-d543-45c5-9598-d20eb8ba3651", "logId": "fd0e7d36-ab34-4d1f-bc77-c06afa17a31e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff37213a-0dba-43a3-ac5d-9985942423c9", "name": "current process  memoryUsage: {\n  rss: 154624000,\n  heapTotal: 120836096,\n  heapUsed: 109981056,\n  external: 3099946,\n  arrayBuffers: 94925\n} os memoryUsage :12.695716857910156", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099744367700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d323b371-c965-4212-8110-a7c84111414b", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099928818300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0e7d36-ab34-4d1f-bc77-c06afa17a31e", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099743293900, "endTime": 12099932145000}, "additional": {"logType": "info", "children": [], "durationId": "e6fb334a-f118-4b37-807b-1bae3ca23423", "parent": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72"}}, {"head": {"id": "3d96b5da-ab3a-44b4-8493-35aa328188c9", "name": "entry : default@PreviewCompileResource cost memory 1.7320175170898438", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099934242800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f1bde6-d1a3-4b9f-85e8-c7603ea9b5fd", "name": "runTaskFromQueue task cost before running: 1 s 20 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099934562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64da5efb-61d5-4f78-a1a1-f058c8dd7e72", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099222490700, "endTime": 12099934743900, "totalTime": 711954600}, "additional": {"logType": "info", "children": ["95616171-d800-475b-a6cd-7329d0f2fe25", "b9205016-bfa5-4d17-847b-4300956f0d09", "a0d49e35-865a-43aa-87a2-85326baf9935", "56f24d7e-9ac7-4221-9994-8824b2177d9f", "fd0e7d36-ab34-4d1f-bc77-c06afa17a31e"], "durationId": "cef3e22d-d543-45c5-9598-d20eb8ba3651"}}, {"head": {"id": "beb04911-a8c1-47f4-b4bc-025977d37bb7", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938495500, "endTime": 12099939120600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8afbda7b-72e1-4c5f-b6c3-92030f1c35e6", "logId": "b8f6b4cc-c89d-497e-8215-a9ffbed87239"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8afbda7b-72e1-4c5f-b6c3-92030f1c35e6", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099937717800}, "additional": {"logType": "detail", "children": [], "durationId": "beb04911-a8c1-47f4-b4bc-025977d37bb7"}}, {"head": {"id": "82b2c751-5e6e-45e9-8b65-1d51d69c3b1d", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0793cf-e58f-47f1-a05f-3d19b44e3aa1", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938319100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3946e9bd-5522-4b28-98df-df844644b52e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938378600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3281cf2c-1031-476f-9b73-44e08fc36109", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29250347-793c-46d0-bf17-d8153dc6bb41", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a02d7d07-b56f-40b5-a558-50eb424b397a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938832700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d16aeaa7-b561-4d8a-8101-e1aed53f9e52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938887800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c0b0e21-3314-4f57-935c-c096b9188b7f", "name": "entry : default@PreviewHookCompileResource cost memory 0.05171966552734375", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938963500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b715793e-7a47-40dc-8ad3-0c5604c8066b", "name": "runTaskFromQueue task cost before running: 1 s 25 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099939057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f6b4cc-c89d-497e-8215-a9ffbed87239", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099938495500, "endTime": 12099939120600, "totalTime": 540800}, "additional": {"logType": "info", "children": [], "durationId": "beb04911-a8c1-47f4-b4bc-025977d37bb7"}}, {"head": {"id": "c1c512d8-0180-4143-8902-c73b32704513", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099942142100, "endTime": 12099952153000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist."], "detailId": "860bb12e-a971-4a79-9334-c2faf2ccd73a", "logId": "2a6b05a7-71a9-4416-9e2f-8f39ac0e763a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "860bb12e-a971-4a79-9334-c2faf2ccd73a", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099940769000}, "additional": {"logType": "detail", "children": [], "durationId": "c1c512d8-0180-4143-8902-c73b32704513"}}, {"head": {"id": "2915d207-bbda-43fa-9a48-bf3bff1321d1", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099941257000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad771c8-e4e5-4448-b5c2-09bc44fbe87a", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099941346500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d5e4dc-825d-428d-b024-27132dfcbcc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099941411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c724f549-10b6-4362-a546-be57ce7d66af", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099942162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b88e618d-1bd8-462f-b56b-58fe47eb66a1", "name": "entry:default@CopyPreviewProfile is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile' does not exist.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099944217200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b121272f-922b-4ab4-9610-da715a527dde", "name": "Incremental task entry:default@CopyPreviewProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099944415600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64978833-f725-41fa-a605-9fe049ff426e", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099944593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37f08d8-4bb0-44b4-8e61-d6966466abfb", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099944683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d37d8a18-15c5-44ea-823a-c485ce42052e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099944759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07868f2f-02e8-463c-8200-b3130ce6fe10", "name": "entry : default@CopyPreviewProfile cost memory 0.2532196044921875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099951873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f693689b-5fe7-45e9-9146-92854410a158", "name": "runTaskFromQueue task cost before running: 1 s 38 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099952061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a6b05a7-71a9-4416-9e2f-8f39ac0e763a", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099942142100, "endTime": 12099952153000, "totalTime": 9885900}, "additional": {"logType": "info", "children": [], "durationId": "c1c512d8-0180-4143-8902-c73b32704513"}}, {"head": {"id": "0c241d01-d2a3-4740-a8a0-0145e23151b9", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955488900, "endTime": 12099955990500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "0b526a9c-7c73-4ec8-9d0d-7e13316bb514", "logId": "708f53a3-a905-4a22-a646-83abd4cd3c74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b526a9c-7c73-4ec8-9d0d-7e13316bb514", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099954107300}, "additional": {"logType": "detail", "children": [], "durationId": "0c241d01-d2a3-4740-a8a0-0145e23151b9"}}, {"head": {"id": "c37b6159-f839-444e-9bdf-f34b09897a14", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099954610100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6787c205-5ce1-456e-8ebc-039f734c8fa0", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099954695500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b13570d-da58-4104-ac3d-b2d7ab3c8990", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099954756200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a784999-c9bf-4f80-85d9-9d6f11a660d9", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955498500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c312c03e-58ab-4378-bf41-f0090310f5ba", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955620700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14c6b22-c454-4bf2-aa32-d8f1d96c49f3", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "610e792c-f99f-4539-a649-b3244ea1c86e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955737800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb89c48-66cc-49ea-abb9-8b03f54eea9c", "name": "entry : default@ReplacePreviewerPage cost memory 0.051544189453125", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955838900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a235106-a96c-4bf8-9c5a-606a0f424c5a", "name": "runTaskFromQueue task cost before running: 1 s 42 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955927100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "708f53a3-a905-4a22-a646-83abd4cd3c74", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099955488900, "endTime": 12099955990500, "totalTime": 415300}, "additional": {"logType": "info", "children": [], "durationId": "0c241d01-d2a3-4740-a8a0-0145e23151b9"}}, {"head": {"id": "3e1ac44e-f62f-4b5d-802e-31796444bbb9", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099957563600, "endTime": 12099957890400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2ece4a24-6a43-45cb-aae9-fba230990fa0", "logId": "e8c9d187-8fb1-4ace-88e3-5021e9f37299"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ece4a24-6a43-45cb-aae9-fba230990fa0", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099957512800}, "additional": {"logType": "detail", "children": [], "durationId": "3e1ac44e-f62f-4b5d-802e-31796444bbb9"}}, {"head": {"id": "f3af2559-f525-425b-87ee-cb8c6955a560", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099957571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6600924f-e8a1-4d2d-ae21-25a90b18acd6", "name": "entry : buildPreviewerResource cost memory 0.01186370849609375", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099957688500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f8cc2a-d34a-40b9-8575-3fbfbf30f91b", "name": "runTaskFromQueue task cost before running: 1 s 44 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099957783000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c9d187-8fb1-4ace-88e3-5021e9f37299", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099957563600, "endTime": 12099957890400, "totalTime": 193900}, "additional": {"logType": "info", "children": [], "durationId": "3e1ac44e-f62f-4b5d-802e-31796444bbb9"}}, {"head": {"id": "40f160b5-d0fe-4aa3-afeb-bdb5dfe69095", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099961411900, "endTime": 12099964921900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "1f95b8af-fba2-4679-a018-925c81499ce6", "logId": "8f17a18b-7af9-4059-aa6b-9453d7aa6734"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f95b8af-fba2-4679-a018-925c81499ce6", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099959931100}, "additional": {"logType": "detail", "children": [], "durationId": "40f160b5-d0fe-4aa3-afeb-bdb5dfe69095"}}, {"head": {"id": "53f9fe5d-423c-445b-b5a2-6f14ae4298de", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099960472600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feac496d-4b0d-4d34-bc46-671aeef33fd9", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099960584100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "befd747e-a83c-45a7-9323-aef81ac10b0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099960674800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0cee096-ca4a-4001-a09d-adc2c77978c1", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099961421300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ca28e3-a8a8-48d6-b76d-11314696ecd3", "name": "entry:default@PreviewUpdateAssets is not up-to-date, since the output file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099963359900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53da353b-0372-4226-80d8-f531a7eab5c4", "name": "Incremental task entry:default@PreviewUpdateAssets pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099963479600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "865eb4d5-5cbb-42bf-a83e-b2aaf43d1efc", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099963581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49f0a62e-e67a-40af-95f6-aee0205ef5d6", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099963637300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3aba09-eb36-4d48-abf8-7ee58163748a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099963692000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d81c099e-a38b-46aa-ad05-925527954ae1", "name": "entry : default@PreviewUpdateAssets cost memory 0.15319061279296875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099964745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2748adb1-2574-4ed7-a8dc-8b0e04bab96d", "name": "runTaskFromQueue task cost before running: 1 s 51 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099964857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f17a18b-7af9-4059-aa6b-9453d7aa6734", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099961411900, "endTime": 12099964921900, "totalTime": 3421400}, "additional": {"logType": "info", "children": [], "durationId": "40f160b5-d0fe-4aa3-afeb-bdb5dfe69095"}}, {"head": {"id": "ac4b7a5f-009e-4b50-91ba-c3b8299f91ae", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099975704400, "endTime": 12111745264600}, "additional": {"children": ["6089c90c-1741-4c99-b915-8c345394b559"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "d4d33a01-4750-4d9c-a99f-266f29812fbe", "logId": "2660fff9-07e1-4bbc-b4c8-4a320b919c2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4d33a01-4750-4d9c-a99f-266f29812fbe", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099967459700}, "additional": {"logType": "detail", "children": [], "durationId": "ac4b7a5f-009e-4b50-91ba-c3b8299f91ae"}}, {"head": {"id": "500e35e4-072a-4233-bff9-1099bb7418ea", "name": "jsonObjWithoutParam {\"@ohos/axios\":\"^2.0.9\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099968371800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0629d6b5-29dd-4aa9-8af1-d5bffdb85a56", "name": "jsonObjWithoutParam {\"@ohos/hypium\":\"1.0.6\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099968573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "016fc4ae-7128-467c-98cc-a2e94794fdb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"@ohos/axios\":\"^2.0.9\"},\"devDependencies\":{\"@ohos/hypium\":\"1.0.6\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099968679500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff37a7d-5b6a-4379-a428-342af6ee972a", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099975728600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb2d7af8-7a14-4a6c-8eaf-dcd17f41a6f3", "name": "entry:default@PreviewArkTS is not up-to-date, since the input file 'D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12100016230600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29df5ed4-e388-448b-b900-b14868d0fe05", "name": "Incremental task entry:default@PreviewArkTS pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12100016453400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6089c90c-1741-4c99-b915-8c345394b559", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12100036935400, "endTime": 12111742153400}, "additional": {"children": ["d121cd1c-eacd-4666-9085-3ce8c0d7b788", "f111bbaa-4c2b-4f20-ad4e-d4dc75553e13", "1cbb51c7-c553-4902-9662-cb5ffd33bc04", "bda5be5c-b7d3-45c7-820c-ee703a1ca484", "6475ad75-0936-4bf0-b06d-7d680558dab9", "84170f98-66d4-4437-bf1e-b58e2e401ddb", "dd660b3c-ffbd-4226-9cf5-a10b5c2eda48", "d1374b2b-9349-4b7f-b59a-83c2ffba4905", "76e2718a-5f04-4122-ac7b-feaecf049ea3", "37bce0e9-1f00-4691-954e-8e74c048a54f", "af1f5bc5-9137-4ac7-9d0f-85338ccec7b2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ac4b7a5f-009e-4b50-91ba-c3b8299f91ae", "logId": "88853515-a048-426d-a0a2-91f51888d391"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8aa3910-ea61-47ef-ac25-70728d1ee4c0", "name": "entry : default@PreviewArkTS cost memory 2.2648773193359375", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12100039245600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a592398-94e6-4c25-a224-7bcf75b01841", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12104327938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d121cd1c-eacd-4666-9085-3ce8c0d7b788", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12104329157600, "endTime": 12104329178000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "d38617a6-a67c-443c-b792-a821ced33053"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d38617a6-a67c-443c-b792-a821ced33053", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12104329157600, "endTime": 12104329178000}, "additional": {"logType": "info", "children": [], "durationId": "d121cd1c-eacd-4666-9085-3ce8c0d7b788", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "4a692641-598d-4dd0-a46f-29b36536e703", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111735768100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f111bbaa-4c2b-4f20-ad4e-d4dc75553e13", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12111736911400, "endTime": 12111736930700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "12cb81ad-defd-4c77-b6cd-7e65a6326d07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12cb81ad-defd-4c77-b6cd-7e65a6326d07", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111736911400, "endTime": 12111736930700}, "additional": {"logType": "info", "children": [], "durationId": "f111bbaa-4c2b-4f20-ad4e-d4dc75553e13", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "64e48915-5216-45d5-a7d7-e22cae9c1496", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111737011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cbb51c7-c553-4902-9662-cb5ffd33bc04", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12111737849700, "endTime": 12111737866300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "988efa0e-eec7-4f4c-8e7a-1a87d59204be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "988efa0e-eec7-4f4c-8e7a-1a87d59204be", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111737849700, "endTime": 12111737866300}, "additional": {"logType": "info", "children": [], "durationId": "1cbb51c7-c553-4902-9662-cb5ffd33bc04", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "fb6094ae-e46d-4e3d-84be-de14afc34fee", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111737935600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda5be5c-b7d3-45c7-820c-ee703a1ca484", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12111738768700, "endTime": 12111738783200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "82140c26-4dc7-4e64-acdf-5d09f2ea1007"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82140c26-4dc7-4e64-acdf-5d09f2ea1007", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111738768700, "endTime": 12111738783200}, "additional": {"logType": "info", "children": [], "durationId": "bda5be5c-b7d3-45c7-820c-ee703a1ca484", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "b5ef5283-7f39-46fb-b5c3-307a867dde6c", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111738848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6475ad75-0936-4bf0-b06d-7d680558dab9", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12111739686100, "endTime": 12111739701200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "ce5e8721-734e-4183-bdfd-afbbbf8b875a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce5e8721-734e-4183-bdfd-afbbbf8b875a", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111739686100, "endTime": 12111739701200}, "additional": {"logType": "info", "children": [], "durationId": "6475ad75-0936-4bf0-b06d-7d680558dab9", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "5586dc1b-21a7-4802-8b96-acfe0b6f2ccf", "name": "watch worker: send response to session manager. Response type: WatchLog", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111739766400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84170f98-66d4-4437-bf1e-b58e2e401ddb", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12111740614800, "endTime": 12111740636700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "46c6eb59-194c-4d11-989f-e6165b552001"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46c6eb59-194c-4d11-989f-e6165b552001", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111740614800, "endTime": 12111740636700}, "additional": {"logType": "info", "children": [], "durationId": "84170f98-66d4-4437-bf1e-b58e2e401ddb", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "e397eda4-70c4-4704-aa18-d4cd461b90ae", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111740729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd660b3c-ffbd-4226-9cf5-a10b5c2eda48", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12111741901100, "endTime": 12111741935400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "c62a177a-f2d4-4aaa-a4af-04e8b0719bd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c62a177a-f2d4-4aaa-a4af-04e8b0719bd5", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111741901100, "endTime": 12111741935400}, "additional": {"logType": "info", "children": [], "durationId": "dd660b3c-ffbd-4226-9cf5-a10b5c2eda48", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "88853515-a048-426d-a0a2-91f51888d391", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12100036935400, "endTime": 12111742153400}, "additional": {"logType": "info", "children": ["d38617a6-a67c-443c-b792-a821ced33053", "12cb81ad-defd-4c77-b6cd-7e65a6326d07", "988efa0e-eec7-4f4c-8e7a-1a87d59204be", "82140c26-4dc7-4e64-acdf-5d09f2ea1007", "ce5e8721-734e-4183-bdfd-afbbbf8b875a", "46c6eb59-194c-4d11-989f-e6165b552001", "c62a177a-f2d4-4aaa-a4af-04e8b0719bd5", "88cb1a63-2235-4be6-bfd6-f3f5da7ea344", "c848301b-e686-4d69-8d84-a902a701cf96", "07fee718-d973-4681-aafc-5848206f38f0", "33352553-65c8-4c4c-969b-e664012df941"], "durationId": "6089c90c-1741-4c99-b915-8c345394b559", "parent": "2660fff9-07e1-4bbc-b4c8-4a320b919c2f"}}, {"head": {"id": "d1374b2b-9349-4b7f-b59a-83c2ffba4905", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12102865397200, "endTime": 12104232563900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "88cb1a63-2235-4be6-bfd6-f3f5da7ea344"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88cb1a63-2235-4be6-bfd6-f3f5da7ea344", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12102865397200, "endTime": 12104232563900}, "additional": {"logType": "info", "children": [], "durationId": "d1374b2b-9349-4b7f-b59a-83c2ffba4905", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "76e2718a-5f04-4122-ac7b-feaecf049ea3", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12104232758000, "endTime": 12104300144800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "c848301b-e686-4d69-8d84-a902a701cf96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c848301b-e686-4d69-8d84-a902a701cf96", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12104232758000, "endTime": 12104300144800}, "additional": {"logType": "info", "children": [], "durationId": "76e2718a-5f04-4122-ac7b-feaecf049ea3", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "37bce0e9-1f00-4691-954e-8e74c048a54f", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12104300285100, "endTime": 12104300644200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "07fee718-d973-4681-aafc-5848206f38f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07fee718-d973-4681-aafc-5848206f38f0", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12104300285100, "endTime": 12104300644200}, "additional": {"logType": "info", "children": [], "durationId": "37bce0e9-1f00-4691-954e-8e74c048a54f", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "af1f5bc5-9137-4ac7-9d0f-85338ccec7b2", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Worker2", "startTime": 12104300746100, "endTime": 12111735982800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6089c90c-1741-4c99-b915-8c345394b559", "logId": "33352553-65c8-4c4c-969b-e664012df941"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33352553-65c8-4c4c-969b-e664012df941", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12104300746100, "endTime": 12111735982800}, "additional": {"logType": "info", "children": [], "durationId": "af1f5bc5-9137-4ac7-9d0f-85338ccec7b2", "parent": "88853515-a048-426d-a0a2-91f51888d391"}}, {"head": {"id": "2660fff9-07e1-4bbc-b4c8-4a320b919c2f", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12099975704400, "endTime": 12111745264600, "totalTime": 11769551900}, "additional": {"logType": "info", "children": ["88853515-a048-426d-a0a2-91f51888d391"], "durationId": "ac4b7a5f-009e-4b50-91ba-c3b8299f91ae"}}, {"head": {"id": "27eb5fd6-2c85-43d8-b615-6fa98c1b8f87", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111750437600, "endTime": 12111750763500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a826c9d8-3b11-4830-b234-1a1f3a57bebc", "logId": "e9b7bf85-1ead-47e6-9358-a5b5dcddf347"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a826c9d8-3b11-4830-b234-1a1f3a57bebc", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111750381600}, "additional": {"logType": "detail", "children": [], "durationId": "27eb5fd6-2c85-43d8-b615-6fa98c1b8f87"}}, {"head": {"id": "9e56cc52-e0b0-45a7-b638-cc21ecd62798", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111750453800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448178de-1da7-49ed-a362-318466580673", "name": "entry : PreviewBuild cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111750603500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f827d9-76cb-4dc1-8f6b-f8e58b2d544d", "name": "runTaskFromQueue task cost before running: 12 s 836 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111750697500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9b7bf85-1ead-47e6-9358-a5b5dcddf347", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111750437600, "endTime": 12111750763500, "totalTime": 233500}, "additional": {"logType": "info", "children": [], "durationId": "27eb5fd6-2c85-43d8-b615-6fa98c1b8f87"}}, {"head": {"id": "64ae42cd-3b19-4483-86bc-d3bf852e368d", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111761762600, "endTime": 12111761786000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "601ab6ed-fc5d-48f9-aba6-ec062e3b877b", "logId": "601b50fc-057c-41a2-a9bf-7824dfad9506"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "601b50fc-057c-41a2-a9bf-7824dfad9506", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111761762600, "endTime": 12111761786000}, "additional": {"logType": "info", "children": [], "durationId": "64ae42cd-3b19-4483-86bc-d3bf852e368d"}}, {"head": {"id": "929194a6-aef0-4449-a190-dd065ee43437", "name": "BUILD SUCCESSFUL in 12 s 848 ms ", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111761833100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "87566953-f747-4b9e-8889-41eb32b1729e", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12098914713600, "endTime": 12111762083300}, "additional": {"time": {"year": 2025, "month": 6, "day": 26, "hour": 12, "minute": 0}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f28ad6bb-18c0-4660-9f29-e2b5b1409ff0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762119900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "389f0e32-6bc7-4d78-b613-d6711dc3442a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762186000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e2e0ddc-4393-4e12-ae82-dcd97bee0580", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762233800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d765177-9775-4041-b8d2-d82a87237106", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762284300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd4ce61e-0bff-4852-8b55-275148fb8364", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762328300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b297511-44be-4d0e-9494-26214ab6f13c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762370300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4d91d9-1328-4e16-ace0-e433efbd9a71", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111762411600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee7c4727-628f-4f49-8951-99baf22b3a2f", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111763120900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5715f88-df3f-4e75-ab84-db7b3c446a37", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111778563800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964594d1-0961-46fc-8570-fed71fcfe49d", "name": "Update task entry:default@PreviewCompileResource input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111781476600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53cd6301-ef70-4f27-bc1b-ff5222002f31", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111781815700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcf29a0d-45c7-4097-9347-bf7f95ca2a90", "name": "Update task entry:default@PreviewCompileResource output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111802113000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b94f7c-6ea5-4b95-a88f-46f58fc5d557", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:41 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111802839700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a911c51-887a-46fc-9121-905e03b46271", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111803075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eedf1c7e-a28a-474d-adbb-48e1196379dc", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111804048700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b450efb5-6e4a-46ef-88dd-af8efcdd3694", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111804929300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63650d14-8f48-4c88-87c3-763c56d3a75d", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111805331300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9bc54f3-7ec1-490d-aa7f-6400e83ff64c", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111805639100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258333fb-1ac3-4be5-8da9-b992be2ab487", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111805966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c18afc-4cf3-4cd0-a517-f7fe6cb584f5", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111809611500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e2deba-d07f-46bd-8a2c-b86351afb0f8", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111810561700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d46f91-5701-4e7e-aef6-57a5f34def11", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\oh_modules\\.ohpm\\@ohos+axios@2.2.6\\oh_modules\\@ohos\\axios\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111810893700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6c80b5-d2cc-4584-87ba-6958d50768a6", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111828637700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2efbfbea-8763-458d-87d9-8e80580132b0", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111829741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c21d0b-ec2e-473a-9c3a-a2f824f3968e", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111829853600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d191e7-f6d2-4d87-83cd-9b1a583da7c9", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111830156600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "528407b9-962b-4397-a1b3-2fae1ff68d2b", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111831016400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34acd706-ba1c-47db-8081-eb545da59ad4", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111835239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea983b90-9541-4b25-a7c4-ea02bc6aeb40", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111835589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19118e9e-d38d-4e1f-ad24-41b02c08a6c6", "name": "Update task entry:default@PreviewArkTS input file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111835974500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207ad310-98ad-4095-8ca3-9aec6e7c9685", "name": "Update task entry:default@PreviewArkTS output file:D:\\AAAqimo\\wallet\\HarmonyOS\\Wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111836344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6895c523-b46b-4127-a980-8279c90378b3", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:31 ms .", "description": "", "type": "log"}, "body": {"pid": 14452, "tid": "Main Thread", "startTime": 12111836692200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}