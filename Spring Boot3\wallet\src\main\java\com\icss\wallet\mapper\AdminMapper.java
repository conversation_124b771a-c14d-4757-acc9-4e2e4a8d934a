package com.icss.wallet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.wallet.entity.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;


@Mapper
@MapperScan("com.icss.wallet.mapper")
public interface AdminMapper extends BaseMapper<Admin> {
    @Select("SELECT * FROM admin WHERE username = #{username}")
    Admin findByUsername(@Param("username") String username);

    @Select("SELECT * FROM admin WHERE phone = #{phone}")
    Admin findByPhone(@Param("phone") String phone);

    @Select("SELECT admin_id, username, phone, real_name, role, status ,phone"+
            "FROM admin ORDER BY admin_id DESC")
    List<Admin> selectBasicInfo();

    @Insert("INSERT INTO admin (username, password, phone, real_name, role, status, create_time, update_time) " +
            "VALUES (#{username}, #{password}, #{phone}, #{realName}, #{role}, #{status}, #{createTime}, #{updateTime})")
    int insertAdmin(Admin admin);

}