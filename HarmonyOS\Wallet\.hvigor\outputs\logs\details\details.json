{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"init": 954400, "PreCheckSyscap": 763800, "PreviewProcessResource": 5875800, "PreviewCompileResource": 973172300, "PreviewHookCompileResource": 479500, "CopyPreviewProfile": 8179800, "ReplacePreviewerPage": 645100, "buildPreviewerResource": 321100, "PreviewUpdateAssets": 4290600, "PreviewArkTS": 11022890900, "PreviewBuild": 265100}, "APP": {"init": 404800}}, "BUILD_ID": "202506261225171990", "TOTAL_TIME": 12360115500}}