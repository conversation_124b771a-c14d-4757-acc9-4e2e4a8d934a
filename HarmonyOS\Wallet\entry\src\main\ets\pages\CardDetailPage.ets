import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

interface RouterParams {
  cardId?: number;
  bankName?: string;
  cardNumber?: string;
  cardType?: number; // 1-储蓄卡, 2-信用卡
  cardHolder?: string;
  phone?: string;
  status?: number; // 0-未绑定, 1-已绑定
  isDefault?: number; // 0-非默认, 1-默认
  expiryDate?: string;
  cvv?: string;
  createTime?: string;
}

@Entry
@Component
struct CardDetailPage {
  private params: RouterParams = router.getParams() as RouterParams;

  @State cardId: number = this.params.cardId || 0;
  @State bankName: string = this.params.bankName || '';
  @State cardNumber: string = this.params.cardNumber || '';
  @State cardType: number = this.params.cardType || 1;
  @State cardHolder: string = this.params.cardHolder || '';
  @State phone: string = this.params.phone || '';
  @State status: number = this.params.status || 0;
  @State isDefault: number = this.params.isDefault || 0;
  @State expiryDate: string = this.params.expiryDate || '';
  @State cvv: string = this.params.cvv || '';
  @State createTime: string = this.params.createTime || '';
  @State showFullCardNumber: boolean = false;

  build() {
    Column() {
      // 顶部导航栏
      this.buildHeader()

      // 银行卡展示区域
      this.buildCardDisplay()

      // 银行卡信息区域
      this.buildCardInfo()

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f7fa')
  }

  @Builder
  buildHeader() {
    Row() {
      Image($r('app.media.back'))
        .width(24)
        .height(24)
        .onClick(() => {
          router.back();
        })

      Text('银行卡详情')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 占位符保持居中
      Row()
        .width(24)
        .height(24)
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor('#ffffff')
  }

  @Builder
  buildCardDisplay() {
    Column() {
      // 银行卡样式展示
      Stack() {
        // 卡片背景
        Column() {
          // 银行名称和卡类型
          Row() {
            Text(this.bankName)
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .fontColor('#ffffff')
              .layoutWeight(1)

            Text(this.getCardTypeText())
              .fontSize(14)
              .fontColor('#ffffff')
              .backgroundColor('rgba(255,255,255,0.2)')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
          }
          .width('100%')
          .margin({ bottom: 40 })

          // 卡号显示
          Text(this.getDisplayCardNumber())
            .fontSize(20)
            .fontWeight(FontWeight.Medium)
            .fontColor('#ffffff')
            .letterSpacing(2)
            .margin({ bottom: 20 })

          // 持卡人姓名和状态
          Row() {
            Text(this.cardHolder)
              .fontSize(16)
              .fontColor('#ffffff')
              .layoutWeight(1)

            Text(this.status === 1 ? '已绑定' : '未绑定')
              .fontSize(12)
              .fontColor('#ffffff')
              .backgroundColor('rgba(255,255,255,0.2)')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
          }
          .width('100%')
        }
        .width('100%')
        .height(200)
        .padding(24)
        .backgroundColor(this.getCardColor())
        .borderRadius(16)
        .justifyContent(FlexAlign.SpaceBetween)
      }
      .margin(16)
    }
    .width('100%')
  }

  @Builder
  buildCardInfo() {
    Column() {
      // 标题
      Row() {
        Text('银行卡信息')
          .fontSize(16)
          .fontWeight(FontWeight.Bold)
          .fontColor('#1a1a1a')
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 信息列表
      Column() {
        this.buildInfoItem('银行名称', this.bankName)
        this.buildInfoItem('卡片类型', this.getCardTypeText())
        this.buildInfoItem('持卡人姓名', this.cardHolder)
        this.buildInfoItem('卡号', this.getDisplayCardNumber(), true)
        this.buildInfoItem('绑定状态', this.status === 1 ? '已绑定' : '未绑定')
        if (this.createTime) {
          this.buildInfoItem('绑定时间', this.formatTime(this.createTime))
        }
      }
      .width('100%')
    }
    .width('100%')
    .backgroundColor('#ffffff')
    .borderRadius(12)
    .padding(20)
    .margin(16)
  }

  @Builder
  buildInfoItem(label: string, value: string, isCardNumber: boolean = false) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)

      Text(value)
        .fontSize(14)
        .fontColor('#1a1a1a')
        .layoutWeight(1)
        .textAlign(TextAlign.End)

      if (isCardNumber) {
        Image(this.showFullCardNumber ? $r('app.media.eye_open') : $r('app.media.eye_close'))
          .width(20)
          .height(20)
          .margin({ left: 8 })
          .onClick(() => {
            this.showFullCardNumber = !this.showFullCardNumber;
          })
      }
    }
    .width('100%')
    .height(48)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
    .border({
      width: { bottom: 1 },
      color: '#f0f0f0'
    })
  }

  // 获取卡片类型文本
  getCardTypeText(): string {
    return this.cardType === 1 ? '储蓄卡' : '信用卡';
  }

  // 获取显示的卡号
  getDisplayCardNumber(): string {
    if (this.showFullCardNumber) {
      return this.cardNumber;
    }
    if (this.cardNumber.length >= 4) {
      return `**** **** **** ${this.cardNumber.slice(-4)}`;
    }
    return this.cardNumber;
  }

  // 获取卡片颜色
  getCardColor(): string {
    switch (this.bankName) {
      case '中国工商银行': return '#c41e3a';
      case '中国建设银行': return '#003e7e';
      case '中国银行': return '#b8860b';
      case '中国农业银行': return '#00a651';
      case '招商银行': return '#d32f2f';
      case '交通银行': return '#1976d2';
      case '中信银行': return '#e53935';
      case '光大银行': return '#7b1fa2';
      case '华夏银行': return '#f57c00';
      case '民生银行': return '#388e3c';
      case '兴业银行': return '#303f9f';
      case '浦发银行': return '#1976d2';
      default: return '#1274ca';
    }
  }

  // 格式化时间
  formatTime(timeStr: string): string {
    try {
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}/${month}/${day} ${hours}:${minutes}`;
    } catch (error) {
      return timeStr;
    }
  }

  // 获取银行图标
  getBankIcon(): Resource {
    switch (this.bankName) {
      case '中国银行': return $r('app.media.boc');
      case '建设银行': return $r('app.media.ccb');
      case '工商银行': return $r('app.media.icbc');
      case '农业银行': return $r('app.media.abc');
      case '招商银行': return $r('app.media.cmb');
      case '交通银行': return $r('app.media.bcm');
      case '中信银行': return $r('app.media.citic');
      case '光大银行': return $r('app.media.ceb');
      case '华夏银行': return $r('app.media.hxb');
      case '民生银行': return $r('app.media.cmbc');
      case '兴业银行': return $r('app.media.cib');
      case '浦发银行': return $r('app.media.spdb');
      default: return $r('app.media.bank');
    }
  }
}