if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CardsPage_Params {
    cards?: BankCard[];
    showAddCard?: boolean;
}
import promptAction from "@ohos:promptAction";
import router from "@ohos:router";
interface BankCard {
    id: number;
    bankName: string;
    cardNumber: string;
    fullCardNumber: string;
    cardType: 'debit' | 'credit';
    isDefault: boolean;
    backgroundColor: string;
    showDetails: boolean;
}
export class CardsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__cards = new ObservedPropertyObjectPU([
            {
                id: 1,
                bankName: '中国银行',
                cardNumber: '**** **** **** 1234',
                fullCardNumber: '6217 8801 2345 6789 1234',
                cardType: 'debit',
                isDefault: true,
                backgroundColor: ' #d5c3170e',
                showDetails: false
            },
            {
                id: 2,
                bankName: '建设银行',
                cardNumber: '**** **** **** 5678',
                fullCardNumber: '6227 0001 2345 6789 5678',
                cardType: 'credit',
                isDefault: false,
                backgroundColor: '#ff318bde',
                showDetails: false
            },
            {
                id: 3,
                bankName: '工商银行',
                cardNumber: '**** **** **** 9012',
                fullCardNumber: '6222 0201 2345 6789 9012',
                cardType: 'debit',
                isDefault: false,
                backgroundColor: '#d5c3170e',
                showDetails: false
            }
        ], this, "cards");
        this.__showAddCard = new ObservedPropertySimplePU(false, this, "showAddCard");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CardsPage_Params) {
        if (params.cards !== undefined) {
            this.cards = params.cards;
        }
        if (params.showAddCard !== undefined) {
            this.showAddCard = params.showAddCard;
        }
    }
    updateStateVars(params: CardsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__cards.purgeDependencyOnElmtId(rmElmtId);
        this.__showAddCard.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__cards.aboutToBeDeleted();
        this.__showAddCard.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __cards: ObservedPropertyObjectPU<BankCard[]>;
    get cards() {
        return this.__cards.get();
    }
    set cards(newValue: BankCard[]) {
        this.__cards.set(newValue);
    }
    private __showAddCard: ObservedPropertySimplePU<boolean>;
    get showAddCard() {
        return this.__showAddCard.get();
    }
    set showAddCard(newValue: boolean) {
        this.__showAddCard.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/CardsPage.ets(54:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(55:7)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加顶部返回栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardsPage.ets(57:9)", "entry");
            // 添加顶部返回栏
            Row.width('100%');
            // 添加顶部返回栏
            Row.height(56);
            // 添加顶部返回栏
            Row.justifyContent(FlexAlign.Start);
            // 添加顶部返回栏
            Row.alignItems(VerticalAlign.Center);
            // 添加顶部返回栏
            Row.backgroundColor('#ffffff');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777260, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardsPage.ets(58:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.margin({ left: 12, right: 8 });
            Image.onClick(() => {
                this.navigateToHome();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的银行卡');
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(65:11)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 添加顶部返回栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 添加银行卡按钮
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/CardsPage.ets(76:9)", "entry");
            // 添加银行卡按钮
            Button.width('90%');
            // 添加银行卡按钮
            Button.height(50);
            // 添加银行卡按钮
            Button.backgroundColor('#e90f6cc4');
            // 添加银行卡按钮
            Button.fontColor(Color.White);
            // 添加银行卡按钮
            Button.margin({ top: 20 });
            // 添加银行卡按钮
            Button.onClick(() => {
                this.showAddCard = true;
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardsPage.ets(77:11)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777252, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardsPage.ets(78:13)", "entry");
            Image.width(20);
            Image.height(20);
            Image.margin({ right: 8 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡');
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(82:13)", "entry");
        }, Text);
        Text.pop();
        Row.pop();
        // 添加银行卡按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡列表
            List.create({ space: 15 });
            List.debugLine("entry/src/main/ets/pages/CardsPage.ets(95:9)", "entry");
            // 银行卡列表
            List.width('100%');
            // 银行卡列表
            List.layoutWeight(1);
            // 银行卡列表
            List.margin({ top: 20 });
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const card = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.debugLine("entry/src/main/ets/pages/CardsPage.ets(97:13)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.BankCardItem.bind(this)(card);
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.cards, forEachItemGenFunction, (card: BankCard) => card.id.toString(), false, false);
        }, ForEach);
        ForEach.pop();
        // 银行卡列表
        List.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 添加银行卡弹窗
            if (this.showAddCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(112:9)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.backgroundColor('rgba(0,0,0,0.5)');
                        Column.onClick(() => {
                            this.showAddCard = false;
                        });
                    }, Column);
                    this.AddCardDialog.bind(this)();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
    }
    // 添加导航到首页的方法
    navigateToHome() {
        // router.back();
        // // 或者使用具体路径返回首页
        router.replaceUrl({ url: 'pages/HomePage' });
    }
    BankCardItem(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(136:5)", "entry");
            Column.width('90%');
            Column.padding(15);
            Column.backgroundColor(card.backgroundColor);
            Column.borderRadius(8);
            Column.margin({ left: '5%', right: '5%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡头部
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardsPage.ets(138:7)", "entry");
            // 银行卡头部
            Row.width('100%');
            // 银行卡头部
            Row.onClick(() => {
                this.toggleCardDetails(card.id);
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 圆形银行图标
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/CardsPage.ets(140:9)", "entry");
            // 圆形银行图标
            Stack.margin({ right: 15 });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.debugLine("entry/src/main/ets/pages/CardsPage.ets(141:11)", "entry");
            Circle.width(40);
            Circle.height(40);
            Circle.fill(Color.White);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.getBankIcon(card.bankName));
            Image.debugLine("entry/src/main/ets/pages/CardsPage.ets(145:11)", "entry");
            Image.width(30);
            Image.height(30);
        }, Image);
        // 圆形银行图标
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(151:9)", "entry");
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(152:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.cardNumber);
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(155:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#ffe5e5e5');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(163:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#ffffff');
                        Text.backgroundColor('#eea7a6a6');
                        Text.padding(4);
                        Text.borderRadius(4);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 银行卡头部
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡详细信息
            if (card.showDetails) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(178:9)", "entry");
                        Column.width('100%');
                        Column.margin({ top: 10 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`完整卡号: ${card.fullCardNumber}`);
                        Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(179:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#ffffff');
                        Text.margin({ top: 10 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`卡片类型: ${card.cardType === 'debit' ? '借记卡' : '信用卡'}`);
                        Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(183:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#ffffff');
                        Text.margin({ top: 5 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardsPage.ets(191:7)", "entry");
            Row.margin({ top: 15 });
            Row.justifyContent(FlexAlign.SpaceAround);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 查账单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(193:9)", "entry");
            // 查账单
            Column.width(70);
            // 查账单
            Column.height(60);
            // 查账单
            Column.backgroundColor('#00000000');
            // 查账单
            Column.onClick(() => {
                this.navigateToBillPage(card);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777259, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardsPage.ets(194:11)", "entry");
            Image.width(20);
            Image.height(20);
            Image.objectFit(ImageFit.Contain);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查账单');
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(198:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#ffd4d4d4');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 查账单
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 查卡号
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(211:9)", "entry");
            // 查卡号
            Column.width(70);
            // 查卡号
            Column.height(60);
            // 查卡号
            Column.backgroundColor('#00000000');
            // 查卡号
            Column.margin({ left: 15 });
            // 查卡号
            Column.onClick(() => {
                this.navigateToCardDetailPage(card);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777257, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardsPage.ets(212:11)", "entry");
            Image.width(20);
            Image.height(20);
            Image.objectFit(ImageFit.Contain);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查卡号');
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(216:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#ffd4d4d4');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 查卡号
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(230:9)", "entry");
            // 转入
            Column.width(70);
            // 转入
            Column.height(60);
            // 转入
            Column.backgroundColor('#00000000');
            // 转入
            Column.margin({ left: 15 });
            // 转入
            Column.onClick(() => {
                this.navigateToTransferPage(card);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777258, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/CardsPage.ets(231:11)", "entry");
            Image.width(20);
            Image.height(20);
            Image.objectFit(ImageFit.Contain);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转入');
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(235:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#ffd4d4d4');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 转入
        Column.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 管理按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardsPage.ets(252:7)", "entry");
            // 管理按钮
            Row.margin({ top: 10 });
            // 管理按钮
            Row.justifyContent(FlexAlign.End);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('设为默认', { type: ButtonType.Capsule, stateEffect: true });
            Button.debugLine("entry/src/main/ets/pages/CardsPage.ets(253:9)", "entry");
            Button.width(100);
            Button.height(36);
            Button.fontSize(12);
            Button.fontColor(card.isDefault ? '#999999' : '#666666');
            Button.backgroundColor(card.isDefault ? '#f0f0f0' : '#f5f5f5');
            Button.enabled(!card.isDefault);
            Button.onClick(() => {
                this.setDefaultCard(card.id);
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('解绑', { type: ButtonType.Capsule, stateEffect: true });
            Button.debugLine("entry/src/main/ets/pages/CardsPage.ets(264:9)", "entry");
            Button.width(100);
            Button.height(36);
            Button.fontSize(12);
            Button.fontColor('#FF5252');
            Button.backgroundColor('#f5f5f5');
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.unbindCard(card.id);
            });
        }, Button);
        Button.pop();
        // 管理按钮
        Row.pop();
        Column.pop();
    }
    AddCardDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardsPage.ets(287:5)", "entry");
            Column.width('90%');
            Column.padding(20);
            Column.backgroundColor('#ffffff');
            Column.borderRadius(12);
            Column.position({ x: '5%', y: '20%' });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('添加银行卡');
            Text.debugLine("entry/src/main/ets/pages/CardsPage.ets(288:7)", "entry");
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入银行卡号' });
            TextInput.debugLine("entry/src/main/ets/pages/CardsPage.ets(293:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Number);
            TextInput.margin({ bottom: 10 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入持卡人姓名' });
            TextInput.debugLine("entry/src/main/ets/pages/CardsPage.ets(299:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.margin({ bottom: 10 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入银行预留手机号' });
            TextInput.debugLine("entry/src/main/ets/pages/CardsPage.ets(304:7)", "entry");
            TextInput.width('80%');
            TextInput.height(50);
            TextInput.type(InputType.Number);
            TextInput.margin({ bottom: 20 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardsPage.ets(310:7)", "entry");
            Row.width('80%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消', { type: ButtonType.Capsule });
            Button.debugLine("entry/src/main/ets/pages/CardsPage.ets(311:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#f0f0f0');
            Button.fontColor('#666666');
            Button.onClick(() => {
                this.showAddCard = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确认添加', { type: ButtonType.Capsule });
            Button.debugLine("entry/src/main/ets/pages/CardsPage.ets(320:9)", "entry");
            Button.width('40%');
            Button.height(50);
            Button.backgroundColor('#ff1274ca');
            Button.fontColor(Color.White);
            Button.margin({ left: 10 });
            Button.onClick(() => {
                this.showAddCard = false;
                promptAction.showToast({ message: '添加成功', duration: 1000 });
            });
        }, Button);
        Button.pop();
        Row.pop();
        Column.pop();
    }
    // 导航到账单页面
    navigateToBillPage(card: BankCard) {
        router.pushUrl({
            url: 'pages/BillPage',
            params: {
                bankName: card.bankName,
                cardNumber: card.cardNumber,
                cardType: card.cardType
            }
        });
    }
    // 导航到卡号详情页面
    navigateToCardDetailPage(card: BankCard) {
        router.pushUrl({
            url: 'pages/CardDetailPage',
            params: {
                bankName: card.bankName,
                fullCardNumber: card.fullCardNumber,
                cardType: card.cardType
            }
        });
    }
    // 导航到转账页面
    navigateToTransferPage(card: BankCard) {
        router.pushUrl({
            url: 'pages/TransactionPage',
            params: {
                bankName: card.bankName,
                cardNumber: card.cardNumber,
                fullCardNumber: card.fullCardNumber
            }
        });
    }
    toggleCardDetails(cardId: number) {
        this.cards = this.cards.map(card => {
            if (card.id === cardId) {
                card.showDetails = !card.showDetails;
            }
            return card;
        });
    }
    getBankIcon(bankName: string): Resource {
        switch (bankName) {
            case '中国银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '建设银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            case '工商银行': return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
            default: return { "id": ********, "type": 20000, params: [], "bundleName": "com.icss.wallet", "moduleName": "entry" };
        }
    }
    setDefaultCard(cardId: number) {
        this.cards = this.cards.map(card => {
            card.isDefault = card.id === cardId;
            return card;
        });
        promptAction.showToast({ message: '设置默认卡成功', duration: 1000 });
    }
    unbindCard(cardId: number) {
        promptAction.showDialog({
            title: '确认解绑',
            message: '确定要解绑这张银行卡吗？',
            buttons: [
                { text: '取消', color: '#666666' },
                { text: '确定', color: '#FF5252' }
            ]
        }).then(result => {
            if (result.index === 1) {
                this.cards = this.cards.filter(card => card.id !== cardId);
                promptAction.showToast({ message: '解绑成功', duration: 1000 });
            }
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CardsPage";
    }
}
registerNamedRoute(() => new CardsPage(undefined, {}), "", { bundleName: "com.icss.wallet", moduleName: "entry", pagePath: "pages/CardsPage", pageFullPath: "entry/src/main/ets/pages/CardsPage", integratedHsp: "false", moduleType: "followWithHap" });
